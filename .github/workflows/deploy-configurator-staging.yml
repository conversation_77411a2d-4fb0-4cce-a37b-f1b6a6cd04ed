name: Deploy @somo/configurator (staging)

on:
  push:
    branches: [staging]
    paths:
      - "packages/configurator/**"
      - "packages/shared/**"
      - "pnpm-lock.yaml"
      - ".github/workflows/deploy-configurator-staging.yml"

jobs:
  deploy:
    name: Deploy configurator (staging)
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: ./.github/actions/prepare

      - name: Install Fly CLI
        uses: superfly/flyctl-actions/setup-flyctl@master

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and deploy to Fly.io
        uses: docker/build-push-action@v6
        with:
          context: .
          file: packages/configurator/Dockerfile
          push: false
          load: true
          tags: configurator-staging:latest
          cache-from: type=gha,scope=configurator-staging-build
          cache-to: type=gha,mode=max,scope=configurator-staging-build
          build-args: |
            VITE_ENVIRONMENT=staging
            VITE_API_URL=${{ vars.VITE_API_URL_STAGING }}
            VITE_CLERK_PUBLISHABLE_KEY=${{ vars.VITE_CLERK_PUBLISHABLE_KEY_STAGING }}
            VITE_WS_URL=${{ vars.VITE_WS_URL_STAGING }}
            VITE_LAUNCH_DARKLY_CLIENT_SIDE_ID=${{ vars.VITE_LAUNCH_DARKLY_CLIENT_SIDE_ID_STAGING }}
            VITE_SENTRY_DSN=${{ vars.VITE_SENTRY_DSN }}
            VITE_SENTRY_AUTH_TOKEN=${{ vars.VITE_SENTRY_AUTH_TOKEN }}

      - name: Deploy to Fly.io
        run: |
          # Tag the local image with the Fly.io registry format
          docker tag configurator-staging:latest registry.fly.io/somo-configurator-staging:latest
          # Push the image to Fly.io registry
          flyctl auth docker
          docker push registry.fly.io/somo-configurator-staging:latest
          # Deploy using the pushed image
          flyctl deploy --config packages/configurator/fly.staging.toml --image registry.fly.io/somo-configurator-staging:latest
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
