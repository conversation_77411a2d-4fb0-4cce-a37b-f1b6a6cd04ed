require("dotenv").config();

import bodyParser from "body-parser";
import cors from "cors";
import express, { NextFunction, type Request, type Response } from "express";
import expressWinston from "express-winston";
import winston from "winston";
import expressWs from "express-ws";
import WebSocketClient from "./WebSocketClient";
import SerialPortClient from "./SerialPortClient";
import { buildDmxDataFrame, dmxDataFrameToSerialBuffer } from "./DMX";
import { updateAnalog } from "./AnalogLightControl";
import { InputSchema } from "./InputSchema";
import {
  Light,
  loadLightsFromRedis,
  RfSwitch,
  RfDimmer,
  RfSwitchSchema,
  saveLightsToRedis,
  saveLightToRedis,
  RfDimmerSchema,
} from "./Lights";
import { Button } from "./Buttons";
import MQTTClient from "./MQTTClient";
import { WebSocket } from "ws";
import getMAC from "getmac";
import ip from "ip";
import { z } from "zod/v4";
import { buttonFromNodes, lightsFromNodes, SomoDimmerNodeSchema, SomoSwitchNodeSchema } from "./lightsFromNodes";
import redis from "./redisClient";
import { wait } from "./wait";
import { runSudoCommand } from "./runSudoCommand";
import { RFController, RFDimmerCommands, RFResponse, RFSwitchTable, RFTables, RFTestCommands } from "./RFController";

const { app } = expressWs(express());
const port = Number.parseInt(process.env.PORT || "3000");

const baseStationNodeId = 1;

let shuttingDown = false;

const configurationSchema = z.object({
  rf: z.object({
    channel: z.number().prefault(15),
    network: z.number().prefault(15),
  }),
  wifi: z.object({
    ssid: z.string().prefault(""),
    password: z.string().prefault(""),
  }),
  somoSwitchIdToQRCodeMapping: z
    .array(
      z.object({
        id: z.string(),
        qrCode: z.string(),
      })
    )
    .prefault([]),
  somoDimmerIdToQRCodeMapping: z
    .array(
      z.object({
        id: z.string(),
        qrCode: z.string(),
      })
    ).prefault([]),
});

const updateSchema = z
  .object({
    mac: z.string(),
    version: z.string(),
    nodes: z.array(
      z.any()
    ),
    edges: z.array(z.any()),
    name: z.string().prefault(""),
    configuration: configurationSchema,
  })


// ws client to connect to the unipi. This will get replaced by reading from our own
// board via i2c
const unipiClient = new WebSocketClient(process.env.UNIPI_WS_URL || "ws://************:8080/ws");

// connection to the DMX interface via serial port
const serialPortClient = new SerialPortClient("/dev/ttyACM0");

// connection to the MQTT broker
const mqttClient = new MQTTClient("mqtt://127.0.0.1:1883");
mqttClient.on("connect", () => {
  mqttClient.subscribe("somo/actions");
});

const somoCloud = new WebSocketClient(
  process.env.SOMO_CLOUD_WS_URL || "ws://localhost:4000/ws/service"
);

const rf = new RFController("/dev/ttyUSB0", false);
rf.on("rfIncoming", async (data: RFResponse) => {
  console.log("[rf|incoming] ", data);
  // Size Destination Source Table Command
  const size = data.data[0];
  const destination = data.data[1];
  const source = data.data[2];
  const table = data.data[3];
  const command = data.data[4];
  const otherData = data.data.slice(5);

  if (destination !== baseStationNodeId) {
    console.log("[rf|incoming] not for us", data);
    return;
  }

  if (table === RFTables.TABLE_RF_SWITCH) {
    const rfSwitch = rfSwitches.find((rfSwitch) => rfSwitch.nodeId === source);
    if (!rfSwitch) {
      console.error("[rf|incoming] rfSwitch not found", source);
      return;
    }
    rfSwitch.lastRssi = data.rssi;
    rfSwitch.lastModifiedTime = Date.now();
    switch (command) {
      case RFSwitchTable.RF_SWITCH_VIA1_ON:
        rfSwitch.via1 = { on: true };
        break;
      case RFSwitchTable.RF_SWITCH_VIA1_OFF:
        rfSwitch.via1 = { on: false };
        break;
      case RFSwitchTable.RF_SWITCH_VIA2_ON:
        rfSwitch.via2 = { on: true };
        break;
      case RFSwitchTable.RF_SWITCH_VIA2_OFF:
        rfSwitch.via2 = { on: false };
        break;
      case RFSwitchTable.RF_SWITCH_VIA3_ON:
        rfSwitch.via3 = { on: true };
        break;
      case RFSwitchTable.RF_SWITCH_VIA3_OFF:
        rfSwitch.via3 = { on: false };
        break;
      default:
        console.log("[rf|command] ", command);
    }
    console.log("[rf|switch] ", rfSwitch);
    await redis.set("rfSwitches", JSON.stringify(rfSwitches));
    await broadcastRfSwitches();
  } else if (table === RFTables.TABLE_RF_DIMMER) {
    const rfDimmer = rfDimmers.find((rfDimmer) => rfDimmer.nodeId === source);
    if (!rfDimmer) {
      console.error("[rf|incoming] rfDimmer not found", source);
      return;
    }
    if (command === RFDimmerCommands.RF_DIMMER_GET_ACK) {
      const brightness = otherData[0];
      rfDimmer.brightness = brightness;
      rfDimmer.lastRssi = data.rssi;
      rfDimmer.lastModifiedTime = Date.now();
      console.log("[rf|dimmer] ", rfDimmer);
      await redis.set("rfDimmers", JSON.stringify(rfDimmers));
      await broadcastRfDimmers();
    }
  }
});

rf.on('error', (err) => { });
unipiClient.on("error", (err) => { });
mqttClient.on("error", (err) => { });
serialPortClient.on("error", (err) => { });
somoCloud.on("error", (err) => { });

let lights: Light[] = [];
let rfSwitches: RfSwitch[] = [];
let rfDimmers: RfDimmer[] = [];
let buttons: Button[] = [];
let configuration: z.infer<typeof configurationSchema> | null = null;

let cleanupAdvertisingInterval: () => void = () => { };

const connectedWebsocketClients: WebSocket[] = [];

let controllerVersion = "unknown";
redis.get("version").then((v) => {
  controllerVersion = v || "unknown";
  console.log("[version]", controllerVersion);
});

redis.get("nodes").then(async (nodesString: string | null) => {
  const loadedConfiguration = await redis.get("configuration");
  configuration = loadedConfiguration ? configurationSchema.safeParse(JSON.parse(loadedConfiguration)).data ?? null : null;

  const rfSwitchesStr = await redis.get("rfSwitches");
  rfSwitches = rfSwitchesStr ? z.array(RfSwitchSchema).parse(JSON.parse(rfSwitchesStr)) : [];

  const rfDimmersStr = await redis.get("rfDimmers");
  rfDimmers = rfDimmersStr ? z.array(RfDimmerSchema).parse(JSON.parse(rfDimmersStr)) : [];

  if (rf.isInitialized()) {
    console.log("[rf|waitForInitialization] already initialized");
  } else {
    console.log("[rf|waitForInitialization] waiting for initialization");
    await rf.waitForInitialization();
  }

  if (configuration) {
    console.log("[rf|changeChannel] changing channel to", configuration.rf.channel, configuration.rf.network);
    await rf.changeChannel(configuration.rf.channel, configuration.rf.network);
  }

  console.log("[configuration]", configuration);

  const nodes = nodesString ? JSON.parse(nodesString) : [];
  lights = await loadLightsFromRedis();
  buttons = buttonFromNodes(nodes, lights, mqttClient);

  await pingRfSwitches();
  await pingRfDimmers();

  startRfSwitchOfflineLoop();
  startRfDimmerOfflineLoop();

  console.log("[rfSwitches]", rfSwitches);
  console.log("[rfDimmers]", rfDimmers);
  console.log("[lights]", lights);
  console.log("[buttons]", buttons);
});

redis.get("name").then((name) => {
  console.log("[name]", name);
});

async function pingRfDimmers(offlineOnly: boolean = false) {
  if (!rf.isInitialized()) {
    console.log("[rf|waitForInitialization] waiting for initialization");
    await rf.waitForInitialization();
  }

  let rfDimmersToPing = rfDimmers;
  if (offlineOnly) {
    rfDimmersToPing = rfDimmers.filter((rfDimmer) => !rfDimmer.online);
  }
  if (rfDimmersToPing.length === 0) {
    return;
  }

  for (const rfDimmer of rfDimmersToPing) {
    rfDimmer.lastModifiedTime = Date.now();
    try {
      const statusResponse = await rf.sendRFCommand(
        rfDimmer.nodeId,
        baseStationNodeId,
        RFTables.TABLE_RF_DIMMER,
        RFDimmerCommands.RF_DIMMER_GET_ACK,
        {
          data: Buffer.from([0, 0x01]), // 0% brightness
          waitForResponse: true,
        }
      );
      rfDimmer.lastRssi = statusResponse?.rssi;
      rfDimmer.online = true;
      rfDimmer.brightness = 0;
      console.log("[rf|status] response", statusResponse);
    } catch (err) {
      console.error("[rf|ping] error", err);
      rfDimmer.online = false;
    }
  }

  await redis.set(`rfDimmers`, JSON.stringify(rfDimmers));
  await broadcastRfDimmers();
}

async function pingRfSwitches(offlineOnly: boolean = false) {
  if (!rf.isInitialized()) {
    console.log("[rf|waitForInitialization] waiting for initialization");
    await rf.waitForInitialization();
  }

  let rfSwitchesToPing = rfSwitches;
  if (offlineOnly) {
    rfSwitchesToPing = rfSwitches.filter((rfSwitch) => !rfSwitch.online);
  }
  if (rfSwitchesToPing.length === 0) {
    return;
  }

  // ping all switches to ensure they are online and set the via states
  for (const rfSwitch of rfSwitchesToPing) {
    rfSwitch.lastModifiedTime = Date.now();
    try {
      const statusResponse = await rf.sendRFCommand(
        rfSwitch.nodeId,
        baseStationNodeId,
        RFTables.TABLE_RF_TEST,
        RFTestCommands.RF_TEST_STATUS,
      )
      if (!statusResponse) {
        console.error("[rf|ping] switch not online", rfSwitch);
        rfSwitch.online = false;
        continue;
      }
      const last3fromResponse = statusResponse.data.slice(-3);
      if (rfSwitch.via1) {
        rfSwitch.via1.on = last3fromResponse[0] === 1;
      }
      if (rfSwitch.via2) {
        rfSwitch.via2.on = last3fromResponse[1] === 1;
      }
      if (rfSwitch.via3) {
        rfSwitch.via3.on = last3fromResponse[2] === 1;
      }

      rfSwitch.lastRssi = statusResponse?.rssi;
      rfSwitch.online = true;
      console.log("[rf|status] response", statusResponse);
    } catch (err) {
      console.error("[rf|ping] error", err);
      rfSwitch.online = false;
    }
  }

  // save to redis
  await redis.set("rfSwitches", JSON.stringify(rfSwitches));
  await broadcastRfSwitches();
}


somoCloud.on("message", async (msg: string) => {
  console.log(`Received message from somo cloud: |${msg}|`);
  let parsed: any = {};
  try {
    parsed = JSON.parse(msg);
  } catch (err) {
    console.error("[somo|message] error", err);
    return;
  }

  switch (parsed.action) {
    case "update": {
      console.log("[somo|update] ", parsed);

      try {
        const update = updateSchema
          .parse(parsed);

        console.log("[somo|update] version", update.version);
        controllerVersion = update.version;
        redis.set("version", update.version);
        redis.set("nodes", JSON.stringify(update.nodes));
        redis.set("edges", JSON.stringify(update.edges));
        redis.set("name", update.name);
        redis.set("configuration", JSON.stringify(update.configuration));

        if (!configuration) {
          if (update.configuration) {
            configuration = update.configuration;
          } else {
            console.error("[rf|changeChannel] no configuration");
            return;
          }
        }

        console.log("[rf|changeChannel] changing channel to", configuration.rf.channel, configuration.rf.network);
        await rf.changeChannel(configuration.rf.channel, configuration.rf.network);

        let nodeId = 35;
        const newRfSwitches = (configuration.somoSwitchIdToQRCodeMapping).map(({ id, qrCode }) => {
          const node = update.nodes.find((node) => node.id === id);
          if (!node) {
            console.error("[rf] node not found", id);
            return null;
          }

          const switchNode = SomoSwitchNodeSchema.parse(node);

          const rfSwitch: RfSwitch = {
            id,
            qrCode,
            provisioned: false,
            nodeId: nodeId++,
            lastModifiedTime: Date.now(),
            online: false,
            name: switchNode.data.title,
            via1: switchNode.data.via1.enabled ? { on: false } : undefined,
            via2: switchNode.data.via2.enabled ? { on: false } : undefined,
            via3: switchNode.data.via3.enabled ? { on: false } : undefined,
          }
          return rfSwitch;
        });

        const newRfDimmers = (configuration.somoDimmerIdToQRCodeMapping).map(({ id, qrCode }) => {
          const node = update.nodes.find((node) => node.id === id);
          if (!node) {
            console.error("[rf] node not found", id);
            return null;
          }
          const dimmerNode = SomoDimmerNodeSchema.parse(node);
          const rfDimmer: RfDimmer = {
            id,
            qrCode,
            provisioned: false,
            nodeId: nodeId++,
            lastModifiedTime: Date.now(),
            online: false,
            name: dimmerNode.data.title,
            brightness: 0,
          }
          return rfDimmer;
        });

        console.log("[rf|update] newRfDimmers", newRfDimmers);

        // dumb first approach: first deprovision all existing rf switches
        for (const rfSwitch of rfSwitches) {
          if (!rfSwitch.provisioned) {
            continue;
          }
          try {
            const response = await rf.removeDevice(rfSwitch.qrCode, rfSwitch.nodeId, configuration.rf.channel, configuration.rf.network);
            console.log("[rf|removeDevice] response", response);
          } catch (err) {
            console.error("[rf|removeDevice] error", err);
          }
        }
        // remove all existing rf dimmers
        for (const rfDimmer of rfDimmers) {
          if (!rfDimmer.provisioned) {
            continue;
          }
          try {
            const response = await rf.removeDevice(rfDimmer.qrCode, rfDimmer.nodeId, configuration.rf.channel, configuration.rf.network);
            console.log("[rf|removeDevice] response", response);
          } catch (err) {
            console.error("[rf|removeDevice] error", err);
          }
        }

        // update the configuration
        configuration = update.configuration;

        // now provision the new rf switches
        rfSwitches = [];
        rfDimmers = [];

        for (const rfSwitch of newRfSwitches) {
          if (!rfSwitch) {
            continue;
          }
          const now = Date.now();
          try {
            console.log("[rf|changeChannel] changing channel to", 15, 15);
            await rf.changeChannel(15, 15);
            const response = await rf.adoptDevice(rfSwitch.qrCode, rfSwitch.nodeId, configuration.rf.channel, configuration.rf.network);
            console.log("[rf|adoptDevice] response", response);
            rfSwitch.provisioned = true;
            rfSwitch.lastRssi = response?.rssi;
            rfSwitches.push(rfSwitch);
            await rf.changeChannel(configuration.rf.channel, configuration.rf.network);

            // check how many vias are enabled as a number and enable the correct one
            const viasEnabled = [rfSwitch.via1, rfSwitch.via2, rfSwitch.via3].filter((via) => via !== undefined).length;
            if (viasEnabled === 1) {
              const response = await rf.sendRFCommand(
                rfSwitch.nodeId,
                baseStationNodeId,
                RFTables.TABLE_RF_SWITCH,
                RFSwitchTable.RF_SWITCH_SET_1_GANG,
                {
                  timeout: 2000,
                }
              )
              rfSwitch.lastRssi = response?.rssi;
              console.log("[rf|sendRFCommand] Enable 1 gang", response);
            } else if (viasEnabled === 2) {
              const response = await rf.sendRFCommand(
                rfSwitch.nodeId,
                baseStationNodeId,
                RFTables.TABLE_RF_SWITCH,
                RFSwitchTable.RF_SWITCH_SET_2_GANG,
              )
              rfSwitch.lastRssi = response?.rssi;
              console.log("[rf|sendRFCommand] Enable 2 gang", response);
            } else if (viasEnabled === 3) {
              const response = await rf.sendRFCommand(
                rfSwitch.nodeId,
                baseStationNodeId,
                RFTables.TABLE_RF_SWITCH,
                RFSwitchTable.RF_SWITCH_SET_3_GANG,
              )
              rfSwitch.lastRssi = response?.rssi;
              console.log("[rf|sendRFCommand] Enable 3 gang", response);
            }

            // once a ping
            const statusResponse = await rf.sendRFCommand(
              rfSwitch.nodeId,
              baseStationNodeId,
              RFTables.TABLE_RF_TEST,
              RFTestCommands.RF_TEST_STATUS,
            )
            rfSwitch.lastRssi = statusResponse?.rssi;
            rfSwitch.online = true;
            console.log("[rf|status] response", statusResponse);
          } catch (err) {
            console.error("[rf|adoptDevice] error", err);
          } finally {
            const dx = Date.now() - now;
            console.log("[rf|adoptDevice] time", dx);
            rfSwitch.lastModifiedTime = now;
          }
        }

        // now provision the new rf dimmers
        for (const rfDimmer of newRfDimmers) {
          if (!rfDimmer) {
            continue;
          }
          const now = Date.now();
          try {
            console.log("[rf|changeChannel] changing channel to", 15, 15);
            await rf.changeChannel(15, 15);
            const response = await rf.adoptDevice(rfDimmer.qrCode, rfDimmer.nodeId, configuration.rf.channel, configuration.rf.network);
            console.log("[rf|adoptDevice] response", response);
            rfDimmer.provisioned = true;
            rfDimmer.lastRssi = response?.rssi;
            rfDimmers.push(rfDimmer);
            await rf.changeChannel(configuration.rf.channel, configuration.rf.network);
            const statusResponse = await rf.sendRFCommand(
              rfDimmer.nodeId,
              baseStationNodeId,
              RFTables.TABLE_RF_DIMMER,
              RFDimmerCommands.RF_DIMMER_GET_ACK,
              {
                data: Buffer.from([100, 0x01]), // 100% brightness
                waitForResponse: true,
              }
            );
            rfDimmer.lastRssi = statusResponse?.rssi;
            rfDimmer.online = true;
            rfDimmer.brightness = 100;
            console.log("[rf|status] response", statusResponse);
          } catch (err) {
            console.error("[rf|adoptDevice] error", err);
          } finally {
            const dx = Date.now() - now;
            console.log("[rf|adoptDevice] time", dx);
            rfDimmer.lastModifiedTime = now;
          }
        }


        // save rf switches to redis
        await redis.set("rfSwitches", JSON.stringify(rfSwitches));
        await broadcastRfSwitches();

        // save rf dimmers to redis
        await redis.set("rfDimmers", JSON.stringify(rfDimmers));
        await broadcastRfDimmers();

        // update lights and buttons
        lights = lightsFromNodes(update.nodes);
        buttons = buttonFromNodes(update.nodes, lights, mqttClient);
        console.log("lights", lights);
        console.log("rfSwitches", rfSwitches);
        console.log("buttons", buttons);
        await saveLightsToRedis(lights);
        await broadcastLights();
        somoCloud.send(
          JSON.stringify({
            action: "update-confirmed",
            mac: update.mac,
            version: update.version,
          })
        );
      } catch (err) {
        console.error("[somo|update] error", err);
      }
      break;
    }
    case "reboot": {
      try {
        somoCloud.close();
        await wait(1000);
        await runSudoCommand("reboot now");
      } catch (err) {
        console.error("[somo|reboot] error", err);
      }
      break;
    }
  }
});

// lcd
// const lcd = new LCDInterface({
//   resetPin: 27,
//   dcPin: 25,
//   spiBus: 0,
//   spiDevice: 0,
// });
// lcd.init().then(async () => {
//   await lcd.clear();
//   const image = await loadImage("images/logo.png");
//   setInterval(async () => {
//     lcd.drawImage(image, 0, 0, image.width, image.height);
//     // ensure controller version is never longer than 6 characters
//     const version = controllerVersion.slice(-6);
//     lcd.drawSections([
//       { heading: "MAC", content: getMAC() },
//       { heading: "IP", content: ip.address() },
//       {
//         heading: "Lights/Buttons",
//         content: `${lights.length}/${buttons.length}`,
//       },
//       { heading: "Version", content: "#" + version },
//     ]);
//     await lcd.drawContext();
//   }, 100);
// });

function startRfSwitchOfflineLoop(options: { offlineInterval: number, allInterval: number } = { offlineInterval: 5000, allInterval: 60000 * 15 }) {
  const pingOfflineLoop = async () => {
    if (shuttingDown) {
      return;
    }
    await pingRfSwitches(true);
    setTimeout(pingOfflineLoop, options.offlineInterval);
  }
  setTimeout(pingOfflineLoop, options.offlineInterval);

  const pingAllLoop = async () => {
    if (shuttingDown) {
      return;
    }
    await pingRfSwitches();
    setTimeout(pingAllLoop, options.allInterval);
  }
  setTimeout(pingAllLoop, options.allInterval);
}

function startRfDimmerOfflineLoop(options: { offlineInterval: number, allInterval: number } = { offlineInterval: 5000, allInterval: 60000 * 15 }) {
  const pingOfflineLoop = async () => {
    if (shuttingDown) {
      return;
    }
    await pingRfDimmers(true);
    setTimeout(pingOfflineLoop, options.offlineInterval);
  }
  pingOfflineLoop();

  const pingAllLoop = async () => {
    if (shuttingDown) {
      return;
    }
    await pingRfDimmers();
    setTimeout(pingAllLoop, options.allInterval);
  }
  pingAllLoop();
}

// this loop advertises the controller to the somo cloud
function startAdvertisingLoop(advertisingInterval: number = 5000) {
  const advertise = () => {
    if (!somoCloud.isConnected) {
      console.log("not connected to somo cloud");
      return;
    }
    somoCloud.send(
      JSON.stringify({
        action: "advertise",
        mac: getMAC(),
        lights,
        buttons: buttons.map((b) => b.toJSON()),
        ip: ip.address(),
        version: controllerVersion,
      })
    );
  };
  advertise();
  const interval = setInterval(advertise, advertisingInterval);
  return () => clearInterval(interval);
}

// this is the main loop that updates the lights and sends the data to the serial port, websocket clients and mqtt
function startLightLoop() {
  let previousLightStates = lights.map((light) => ({
    brightness: light.brightness,
    targetBrightness: light.targetBrightness,
  }));

  const lightLoop = async () => {
    if (!serialPortClient.isOpen) {
      console.log("DMX serial port not connected. Skipping light loop.");
      return;
    }
    const currentTime = Date.now();
    const dmxData = await buildDmxDataFrame(lights);
    const buffer = dmxDataFrameToSerialBuffer(dmxData.data);

    // write dmx data to serial port
    const spStart = Date.now();
    await serialPortClient.write(buffer);
    const spEnd = Date.now();

    // update analog lights
    const analogStart = Date.now();
    await updateAnalog(lights, unipiClient);
    const analogEnd = Date.now();

    // Check if any lights changed
    const lightsChanged = lights.some((light, index) => {
      const prevState = previousLightStates[index];
      if (!prevState) return true;
      return (
        light.brightness !== prevState.brightness ||
        light.targetBrightness !== prevState.targetBrightness
      );
    });

    // Only broadcast if lights changed
    let clientsStart = 0,
      clientsEnd = 0;
    if (lightsChanged) {
      clientsStart = Date.now();
      await broadcastLights();
      clientsEnd = Date.now();

      // Update previous states
      previousLightStates = lights.map((light) => ({
        brightness: light.brightness,
        targetBrightness: light.targetBrightness,
      }));
    }

    // time check
    const dx = Date.now() - currentTime;
    if (dx > 1000 / 60) {
      console.error("DMX update too slow for loop", dx, {
        required: 1000 / 60,
        serialPort: spEnd - spStart,
        analog: analogEnd - analogStart,
        broadcastLights: lightsChanged ? clientsEnd - clientsStart : -1,
      });
    }

    const nextTime = Math.max(1, 1000 / 60 - dx);
    setTimeout(lightLoop, nextTime);
  };

  // save lights to redis every 10 seconds
  setInterval(async () => {
    await saveLightsToRedis(lights);
  }, 10000);

  lightLoop();
}

async function broadcastStatusToWebsocketClients() {
  for (const client of connectedWebsocketClients) {
    await new Promise<void>((resolve) => {
      const state = {
        type: "status",
        lights,
        buttons: buttons.map((b) => b.toJSON()),
        rfSwitches: rfSwitches,
      };
      console.log("broadcasting state", state);
      client.send(
        JSON.stringify(state),
        () => resolve
      );
    });
  }
}

async function broadcastRfSwitches() {
  await broadcastStatusToWebsocketClients();

  for (const rfSwitch of rfSwitches) {
    await mqttClient.publish(
      `somo/rfSwitches/${rfSwitch.id}`,
      JSON.stringify(rfSwitch),
      { retain: true }
    );
  }
}

async function broadcastRfDimmers() {
  await broadcastStatusToWebsocketClients();

  for (const rfDimmer of rfDimmers) {
    await mqttClient.publish(
      `somo/rfDimmers/${rfDimmer.id}`,
      JSON.stringify(rfDimmer),
      { retain: true }
    );
  }
}


async function broadcastLights() {
  await broadcastStatusToWebsocketClients();

  // publish lights to mqtt
  for (const light of lights) {
    await mqttClient.publish(
      `somo/lights/${light.id}`,
      JSON.stringify({
        id: light.id,
        brightness: Math.round(light.brightness),
        targetBrightness: Math.round(light.targetBrightness),
        on: light.brightness > 0,
      }),
      { retain: true }
    );
  }
}

unipiClient.on("message", (msg: string) => {
  const parsed = JSON.parse(msg);
  if (!Array.isArray(parsed)) {
    return;
  }
  parsed.forEach((item) => {
    if (item.dev === "input") {
      const input = InputSchema.parse(item);
      const button = buttons.find((b) => b.id === input.circuit);
      console.log("button", input.circuit);
      if (button) {
        button.pressed = input.value === 1;
      }
    }
  });
});

mqttClient.on("message", async (topic, message) => {
  try {
    switch (topic) {
      case "somo/actions":
        const action = JSON.parse(message.toString());
        console.log("action", action);

        if ("action" in action) {
          switch (action.action) {
            case "getCurrentDeviceState":
              broadcastLights();
              break;
            case "changeLight":
              for (const light of lights) {
                if (light.id === action.id) {
                  light.targetBrightness = action.brightness;
                  light.dimSpeedMsec = action.dimSpeed;
                  light.lastModifiedTime = Date.now();
                  await saveLightToRedis(light);
                  console.log("saved light", light);
                  return;
                }
              }
            case "changeRfDimmer": {
              for (const rfDimmer of rfDimmers) {
                if (rfDimmer.id === action.id && rfDimmer.provisioned && rfDimmer.online) {
                  rfDimmer.brightness = action.brightness;
                  const response = await rf.sendRFCommand(
                    rfDimmer.nodeId,
                    baseStationNodeId,
                    RFTables.TABLE_RF_DIMMER,
                    RFDimmerCommands.RF_DIMMER_GET_ACK,
                    {
                      data: Buffer.from([action.brightness, 0x01]),
                      waitForResponse: true,
                    }
                  );
                  rfDimmer.lastRssi = response?.rssi;
                  rfDimmer.lastModifiedTime = Date.now();
                  // if (action.brightness === 0) {
                  //   await rf.sendRFCommand(
                  //     rfDimmer.nodeId,
                  //     baseStationNodeId,
                  //     RFTables.TABLE_RF_DIMMER,
                  //     RFDimmerCommands.RF_DIMMER_LEVEL_1,
                  //     {
                  //       waitForResponse: false,
                  //     }
                  //   );

                  // } else {
                  //   await rf.sendRFCommand(
                  //     rfDimmer.nodeId,
                  //     baseStationNodeId,
                  //     RFTables.TABLE_RF_DIMMER,
                  //     RFDimmerCommands.RF_DIMMER_LEVEL_5,
                  //     {
                  //       waitForResponse: false,
                  //     }
                  //   );
                  // }
                  await redis.set(`rfDimmers`, JSON.stringify(rfDimmers));
                  await broadcastRfDimmers();
                }
              }
            }
            case "changeRfSwitch":
              for (const rfSwitch of rfSwitches) {
                if (rfSwitch.id === action.id && rfSwitch.provisioned && rfSwitch.online) {
                  let cmd: number;
                  switch (action.command) {
                    case "RF_SWITCH_VIA1_ON":
                      cmd = RFSwitchTable.RF_SWITCH_VIA1_ON;
                      if (rfSwitch.via1) {
                        rfSwitch.via1.on = true;
                      }
                      break;
                    case "RF_SWITCH_VIA2_ON":
                      cmd = RFSwitchTable.RF_SWITCH_VIA2_ON;
                      if (rfSwitch.via2) {
                        rfSwitch.via2.on = true;
                      }
                      break;
                    case "RF_SWITCH_VIA3_ON":
                      cmd = RFSwitchTable.RF_SWITCH_VIA3_ON;
                      if (rfSwitch.via3) {
                        rfSwitch.via3.on = true;
                      }
                      break;
                    case "RF_SWITCH_VIA1_OFF":
                      cmd = RFSwitchTable.RF_SWITCH_VIA1_OFF;
                      if (rfSwitch.via1) {
                        rfSwitch.via1.on = false;
                      }
                      break;
                    case "RF_SWITCH_VIA2_OFF":
                      cmd = RFSwitchTable.RF_SWITCH_VIA2_OFF;
                      if (rfSwitch.via2) {
                        rfSwitch.via2.on = false;
                      }
                      break;
                    case "RF_SWITCH_VIA3_OFF":
                      cmd = RFSwitchTable.RF_SWITCH_VIA3_OFF;
                      if (rfSwitch.via3) {
                        rfSwitch.via3.on = false;
                      }
                      break;
                    default:
                      console.error(`Invalid RF switch command: ${action.command}`);
                      return;
                  }
                  await rf.sendRFCommand(
                    rfSwitch.nodeId,
                    baseStationNodeId,
                    RFTables.TABLE_RF_SWITCH,
                    cmd,
                  )
                  await redis.set(`rfSwitches`, JSON.stringify(rfSwitches));
                  await broadcastRfSwitches();
                }
              }
              break;
            default:
              console.log(`Received unhandled action: ${action.action}`);
              break;
          }
        }
        break;
      default:
        console.log(`Received unhandled message on topic ${topic}`);
        break;
    }
  } catch (error) {
    console.error("[mqtt|error] error", error);
  }
});

// parse request bodies
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

app.use(
  expressWinston.logger({
    transports: [new winston.transports.Console()],
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    ),
    colorize: true,
  })
);

app.use(
  cors({
    origin: "*",
  })
);

app.get("/health", async (_req: Request, res: Response) => {
  res.json({ status: "ok", lights, buttons: buttons.map((b) => b.toJSON()) });
});

serialPortClient.on("connected", async () => {
  startLightLoop();
});

// Final error handler to prevent unhandled errors from crashing the server
app.use((err: Error, _req: Request, res: Response, _next: NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ message: "Unknown error" });
});

app.listen(port, () => {
  console.log(`[server]: Server is running at http://localhost:${port}`);
  cleanupAdvertisingInterval = startAdvertisingLoop();

});

// turn on a light or all lights
app.get("/on", async (req: Request, res: Response) => {
  if (req.query.id) {
    const light = lights.find((l) => l.id === req.query.id);
    if (!light) {
      res.status(404).json({ status: "error", message: "Light not found" });
      return;
    }
    mqttClient.publish(
      "somo/actions",
      JSON.stringify({
        action: "changeLight",
        id: light.id,
        brightness: 100,
        dimSpeed: light.nonDimmingSpeedMsec,
      })
    );
  } else {
    // turn on all lights
    for (const light of lights) {
      mqttClient.publish(
        "somo/actions",
        JSON.stringify({
          action: "changeLight",
          id: light.id,
          brightness: 100,
          dimSpeed: light.nonDimmingSpeedMsec,
        })
      );
    }
  }
  res.json({ status: "ok" });
});

// turn off a light or all lights
app.get("/off", async (req: Request, res: Response) => {
  if (req.query.id) {
    const light = lights.find((l) => l.id === req.query.id);
    if (!light) {
      res.status(404).json({ status: "error", message: "Light not found" });
      return;
    }
    mqttClient.publish(
      "somo/actions",
      JSON.stringify({
        action: "changeLight",
        id: light.id,
        brightness: 0,
        dimSpeed: light.nonDimmingSpeedMsec,
      })
    );
  } else {
    for (const light of lights) {
      mqttClient.publish(
        "somo/actions",
        JSON.stringify({
          action: "changeLight",
          id: light.id,
          brightness: 0,
          dimSpeed: light.nonDimmingSpeedMsec,
        })
      );
    }
  }
  res.json({ status: "ok" });
});

// Global error handler for API routes
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error("API Error:", err);

  // Don't expose internal error details to client
  res.status(500).json({
    status: "error",
    message: "An internal server error occurred",
  });
});

// cleanup function to handle server shutdown
function cleanup() {
  shuttingDown = true;
  console.log("Closing HTTP server");
  cleanupAdvertisingInterval();

  serialPortClient.close();
  // lcd.close();
  process.exit(0);
}

process.on("SIGTERM", cleanup);
process.on("SIGINT", cleanup);
