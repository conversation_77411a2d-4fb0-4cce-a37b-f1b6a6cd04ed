import { z } from "zod/v4";
import redis from "./redisClient";
import fs from "fs";

export enum FixtureType {
  WDLED8 = "WDLED8",
  EST = "EST",
  DF_12 = "DF_12",
  ELV = "ELV",
  D4 = "D4",
  Analog = "Analog",
}


export const LightSchema = z.object({
  id: z.string(),
  dimSpeedMsec: z.number(),
  lastModifiedTime: z.number(),
  targetBrightness: z.number(),
  brightness: z.number(),
  fixtures: z.array(
    z.object({
      type: z.enum(FixtureType),
      channel: z.array(z.number()),
      params: z.record(z.string(), z.number()).optional(),
      minBrightness: z.number().optional().prefault(0),
      maxBrightness: z.number().optional().prefault(100),
    })
  ),
  dimmingSpeedMsec: z.number().optional().prefault(0.02),
  nonDimmingSpeedMsec: z.number().optional().prefault(0.2),
});

export const RfLightSchema = z.object({
  on: z.boolean(),
});

export const RfSwitchSchema = z.object({
  id: z.string(),
  qrCode: z.string(),
  name: z.string(),
  provisioned: z.boolean(),
  online: z.boolean().optional().prefault(false),
  nodeId: z.number(),
  lastModifiedTime: z.number(),
  lastRssi: z.number().optional(),
  via1: RfLightSchema.optional(),
  via2: RfLightSchema.optional(),
  via3: RfLightSchema.optional(),
});

export const RfDimmerSchema = z.object({
  id: z.string(),
  qrCode: z.string(),
  name: z.string(),
  provisioned: z.boolean(),
  online: z.boolean().optional().prefault(false),
  nodeId: z.number(),
  lastModifiedTime: z.number(),
  lastRssi: z.number().optional(),
  brightness: z.number().optional().prefault(0),
});

export type RfSwitch = z.infer<typeof RfSwitchSchema>;
export type RfDimmer = z.infer<typeof RfDimmerSchema>;
export type Light = z.infer<typeof LightSchema>;
export type RfLight = z.infer<typeof RfLightSchema>;

interface DefaultParams {
  min1: number;
  max1: number;
  gamma1: number;
  min2?: number;
  max2?: number;
  gamma2?: number;
}

export const DefaultParams: Record<FixtureType, DefaultParams> = {
  [FixtureType.WDLED8]: {
    min1: 0.0,
    max1: 1.0,
    gamma1: 1.2,
    min2: 0.0,
    max2: 1.0,
    gamma2: 2.5,
  },
  [FixtureType.EST]: { min1: 0.02, max1: 1.0, gamma1: 1.5 },
  [FixtureType.DF_12]: { min1: 0.0, max1: 1.0, gamma1: 1.5 },
  [FixtureType.ELV]: { min1: 0.0, max1: 1.0, gamma1: 1.5 },
  [FixtureType.D4]: {
    min1: 0.0,
    max1: 1.0,
    gamma1: 1.0,
    min2: 0.0,
    max2: 1.0,
    gamma2: 1.0,
  },
  [FixtureType.Analog]: { min1: 0.0, max1: 1.0, gamma1: 1.0 },
} as const;

export const loadLightsFromJson = (path: string) => {
  const lights = JSON.parse(fs.readFileSync(path, "utf8"));
  return LightSchema.array().parse(lights);
};

export const loadLightsFromRedis = async (): Promise<Light[]> => {
  const keys = await redis.keys("light:*");
  const lights = await Promise.all(
    keys.map((key) => loadLightFromRedis(key.split(":")[1]))
  );
  return lights.filter((light) => light !== null);
};

export const loadLightFromRedis = async (id: string): Promise<Light | null> => {
  const lightData = await redis.get(`light:${id}`);
  if (!lightData) {
    throw new Error(`No light data found in Redis for ID: ${id}`);
  }
  const res = LightSchema.safeParse(JSON.parse(lightData));
  if (!res.success) {
    return null;
  }
  return res.data;
};

export const saveLightToRedis = async (light: Light): Promise<void> => {
  await redis.set(`light:${light.id}`, JSON.stringify(light));
};

export const saveLightsToRedis = async (lights: Light[]): Promise<void> => {
  const keys = await redis.keys("light:*");
  await Promise.all(keys.map((key) => redis.del(key)));
  await Promise.all(lights.map((light) => saveLightToRedis(light)));
};
