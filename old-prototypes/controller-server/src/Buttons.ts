import { z } from "zod/v4";
import { EventEmitter } from "events";

export const ButtonSchema = z.object({
  id: z.string(),
  pressed: z.boolean(),
});

export type ButtonType = z.infer<typeof ButtonSchema>;

interface ButtonEvents {
  on(
    event: "up" | "down" | "holdStart" | "holdEnd" | "click",
    listener: () => void
  ): this;
  off(
    event: "up" | "down" | "holdStart" | "holdEnd" | "click",
    listener: () => void
  ): this;
}

export class Button extends EventEmitter implements ButtonEvents {
  id: string;
  private _pressed: boolean;
  private lastPressedTime: number;
  private releaseTimer: NodeJS.Timeout | null = null;

  get pressed(): boolean {
    return this._pressed;
  }

  set pressed(value: boolean) {
    if (this.releaseTimer) {
      clearTimeout(this.releaseTimer);
      this.releaseTimer = null;
    }

    if (this._pressed !== value) {
      const now = Date.now();
      const pressDelta = now - this.lastPressedTime;
      if (!value && this._pressed && pressDelta <= 300) {
        this.emit("click");
      }
      if (!value && this._pressed && pressDelta > 300) {
        this.emit("holdEnd", pressDelta);
      }
      this._pressed = value;
      this.lastPressedTime = now;
      this.emit(value ? "down" : "up");
    }

    if (value) {
      this.releaseTimer = setTimeout(() => {
        this.emit("holdStart");
      }, 10);
    }
  }

  constructor(id: string) {
    super();
    this.id = id;
    this._pressed = false;
    this.lastPressedTime = 0;
  }

  static fromJson(json: unknown): Button {
    const parsed = ButtonSchema.parse(json);
    const b = new Button(parsed.id);
    b.pressed = parsed.pressed;
    return b;
  }

  toJSON(): ButtonType {
    return {
      id: this.id,
      pressed: this.pressed,
    };
  }
}
