import { z } from "zod/v4";

export const InputSchema = z.object({
  counter_modes: z.array(z.enum(["Enabled", "Disabled"])),
  glob_dev_id: z.number(),
  modes: z.array(z.enum(["Simple", "DirectSwitch"])),
  value: z.number(),
  circuit: z.string(),
  debounce: z.number(),
  counter: z.number(),
  counter_mode: z.enum(["Enabled", "Disabled"]),
  dev: z.string(),
  mode: z.enum(["Simple", "DirectSwitch"]),
});
