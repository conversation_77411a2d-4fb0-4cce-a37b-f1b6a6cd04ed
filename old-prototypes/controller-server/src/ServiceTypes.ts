import { z } from "zod/v4";

// Define the FixtureType enum using Zod
export const FixtureTypeSchema = z.enum([
  "WDLED8",
  "EST",
  "DF_12",
  "ELV",
  "D4",
  "Analog",
]);

// Define CurveType schema
export const CurveTypeSchema = z.enum(["linear", "bezier"]);

// Define CurvePoint schema
export const CurvePointSchema = z.object({
  x: z.number(),
  y: z.number(),
});

// Define the base Device schema
const BaseDeviceSchema = z.object({
  id: z.string(),
  type: z.string().optional(),
  sortIndex: z.number(),
});

// Define the Fixture schema
const FixtureSchema = z.object({
  id: z.string(),
  sortIndex: z.number(),
  type: FixtureTypeSchema,
  channel: z.number(),
  minBrightness: z.number(),
  maxBrightness: z.number(),
});

// Define the DmxDevice schema
export const DmxDeviceSchema = BaseDeviceSchema.extend({
  type: z.literal("dmx"),
  name: z.string(),
  icon: z.string().optional(),
  showLabel: z.boolean(),
  fixtures: z.record(z.string(), FixtureSchema),
  defaultDimmingSpeed: z.number(),
  isDimmable: z.boolean(),
  dimmingCurve: z.array(CurvePointSchema).optional(),
  dimmingCurveType: CurveTypeSchema.optional(),
});

// Define the SomoSwitchDevice schema
export const SomoSwitchDeviceSchema = BaseDeviceSchema.extend({
  type: z.literal("somoSwitch"),
  nodeId: z.string(),
  viaId: z.string(),
  showLabel: z.boolean(),
});

// Define the SomoDimmerDevice schema
export const SomoDimmerDeviceSchema = BaseDeviceSchema.extend({
  type: z.literal("somoDimmer"),
  nodeId: z.string(),
  viaId: z.string(),
  showLabel: z.boolean(),
  dimmingCurve: z.array(CurvePointSchema).optional(),
  dimmingCurveType: CurveTypeSchema.optional(),
  defaultDimmingSpeed: z.number().optional(),
});

// Define the Device union schema
export const DeviceSchema = z.discriminatedUnion("type", [
  DmxDeviceSchema,
  SomoSwitchDeviceSchema,
  SomoDimmerDeviceSchema,
]);

// Define the SectionNodeData schema using Zod
export const SectionNodeDataSchema = z.object({
  title: z.string(),
  direction: z.enum(["left", "right"]).optional(),
  devices: z.record(z.string(), DeviceSchema),
});

// Export the inferred types from the Zod schemas
export type FixtureType = z.infer<typeof FixtureTypeSchema>;
export type CurveType = z.infer<typeof CurveTypeSchema>;
export type CurvePoint = z.infer<typeof CurvePointSchema>;
export type Device = z.infer<typeof DeviceSchema>;
export type DmxDevice = z.infer<typeof DmxDeviceSchema>;
export type SomoSwitchDevice = z.infer<typeof SomoSwitchDeviceSchema>;
export type SomoDimmerDevice = z.infer<typeof SomoDimmerDeviceSchema>;
export type SectionNodeData = z.infer<typeof SectionNodeDataSchema>;