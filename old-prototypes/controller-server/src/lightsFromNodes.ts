import { Device, SectionNodeDataSchema, DmxDevice } from "./ServiceTypes";
import { FixtureType, Light } from "./Lights";
import { z } from "zod/v4";
import { Button } from "./Buttons";
import MQTTClient from "./MQTTClient";

export const AnyNodeSchema = z.object({
  type: z.string(),
  data: z.any(),
  id: z.string(),
});

export const DeviceControlSettingsSchema = z.object({
  id: z.string(),
  dimSpeed: z.number(),
  sortIndex: z.number(),
  deviceId: z.string(),
  targetValue: z.number(),
  onValue: z.number(),
  offValue: z.number(),
});

export const SomoSwitchViaSchema = z.object({
  icon: z.string().optional(),
  enabled: z.boolean(),
  lightIcon: z.string().optional(),
  name: z.string(),
  hasLoad: z.boolean(),
  showLabel: z.boolean(),
  lightName: z.string(),
  onUpClick: z.record(z.string(), DeviceControlSettingsSchema)
});

export const SomoSwitchContainerNodeDataSchema = z.object({
  title: z.string(),
  via1: SomoSwitchViaSchema,
  via2: SomoSwitchViaSchema,
  via3: SomoSwitchViaSchema
});

export type SomoSwitchVia = z.infer<typeof SomoSwitchViaSchema>;
export type SomoSwitchContainerNodeData = z.infer<typeof SomoSwitchContainerNodeDataSchema>;

export const SomoDimmerViaSchema = z.object({
  icon: z.string().optional(),
  lightIcon: z.string().optional(),
  name: z.string(),
  showLabel: z.boolean(),
  lightName: z.string(),
  onUpClick: z.record(z.string(), DeviceControlSettingsSchema)
});

export const SomoDimmerButtonSchema = z.object({
  icon: z.string().optional(),
  name: z.string(),
  showLabel: z.boolean(),
  enabled: z.boolean(),
  onUpClick: z.record(z.string(), DeviceControlSettingsSchema)
});

export const SomoDimmerContainerNodeDataSchema = z.object({
  title: z.string(),
  via: SomoDimmerViaSchema,
  viaUp: SomoDimmerButtonSchema,
  viaDown: SomoDimmerButtonSchema,
  dimSpeed: z.number(),
  dimmingCurve: z.object({
    points: z.array(z.object({
      x: z.number(),
      y: z.number()
    })),
    type: z.string()
  })
});

export type SomoDimmerVia = z.infer<typeof SomoDimmerViaSchema>;
export type SomoDimmerButton = z.infer<typeof SomoDimmerButtonSchema>;
export type SomoDimmerContainerNodeData = z.infer<typeof SomoDimmerContainerNodeDataSchema>;

export const SomoSwitchNodeSchema = AnyNodeSchema.extend({
  type: z.literal("somoSwitchContainer"),
  data: SomoSwitchContainerNodeDataSchema,
});

export const SomoDimmerNodeSchema = AnyNodeSchema.extend({
  type: z.literal("somoDimmerContainer"),
  data: SomoDimmerContainerNodeDataSchema,
});

const SectionNodeSchema = AnyNodeSchema.extend({
  type: z.literal("section"),
  data: SectionNodeDataSchema,
});

const AbstractControllerSchema = z.object({
  id: z.string(),
  label: z.string(),
  sortIndex: z.number(),
});

export const MomentaryControllerSchema = AbstractControllerSchema.extend({
  type: z.literal("momentary"),
  channel: z.string(),
  onUpClick: z.record(z.string(), DeviceControlSettingsSchema),
  onUpHold: z.record(z.string(), DeviceControlSettingsSchema),
});

export const ToggleControllerSchema = AbstractControllerSchema.extend({
  type: z.literal("toggle"),
  upChannel: z.string(),
  downChannel: z.string(),
  onUpClick: z.record(z.string(), DeviceControlSettingsSchema),
  onUpHold: z.record(z.string(), DeviceControlSettingsSchema),
  onDownClick: z.record(z.string(), DeviceControlSettingsSchema),
  onDownHold: z.record(z.string(), DeviceControlSettingsSchema),
});

export const ControllerContainerNodeDataSchema = z.object({
  title: z.string(),
  controllers: z.record(
    z.string(), z.union([MomentaryControllerSchema, ToggleControllerSchema])
  ),
});

const ControllerContainerNodeSchema = AnyNodeSchema.extend({
  type: z.literal("controllerContainer"),
  data: ControllerContainerNodeDataSchema,
});

type AnyNode = z.infer<typeof AnyNodeSchema>;
type SectionNode = z.infer<typeof SectionNodeSchema>;
type ControllerContainerNode = z.infer<typeof ControllerContainerNodeSchema>;


// export function rfLightsFromNodes(nodes: (AnyNode | SectionNode)[]): RfLight[] {
//   const sections = nodes.filter((n) => n.type === "section") as SectionNode[];
//   const allDevices = sections.flatMap((s) => Object.values(s.data.devices));

//   const rfDevices = allDevices.filter((device): device is SomoSwitchDevice =>
//     device.type === "somoSwitch"
//   );
//   console.log('[rfDevices]', rfDevices);
//   const lights: RfLight[] = rfDevices.map((device) => ({
//     id: device.id,
//     nodeId: device.nodeId,
//     on: false,
//     type: RfLightType.SomoSwitch,
//     lastModifiedTime: Date.now(),
//   } as RfLight));

//   // remove duplicates
//   return lights.filter((light, index, self) =>
//     index === self.findIndex((t) => t.id === light.id)
//   );
// }

export function lightsFromNodes(nodes: (AnyNode | SectionNode)[]): Light[] {
  const sections = nodes.filter((n) => n.type === "section") as SectionNode[];
  const allDevices = sections.flatMap((s) => Object.values(s.data.devices));

  // Filter only DMX devices
  const dmxDevices = allDevices.filter((device): device is DmxDevice =>
    device.type === "dmx" || device.type === undefined
  );

  const newLights: Light[] = dmxDevices.map((device) => ({
    id: device.id,
    dimSpeedMsec: device.defaultDimmingSpeed,
    lastModifiedTime: Date.now(),
    targetBrightness: 0,
    brightness: 0,
    fixtures: Object.values(device.fixtures).map((fixture) => ({
      type: fixture.type as FixtureType,
      channel: [fixture.channel],
      minBrightness: fixture.minBrightness,
      maxBrightness: fixture.maxBrightness,
    })),
    dimmingSpeedMsec: device.defaultDimmingSpeed,
    nonDimmingSpeedMsec: device.defaultDimmingSpeed,
  }));

  return newLights;
}

export function buttonFromNodes(
  nodes: (AnyNode | ControllerContainerNode)[],
  lights: Light[],
  mqttClient: MQTTClient
): Button[] {
  const controllers = nodes.filter(
    (n) => n.type === "controllerContainer"
  ) as ControllerContainerNode[];
  const buttons: Button[] = [];
  // convert now controllers into new buttons
  for (const controller of controllers) {
    for (const input of Object.values(controller.data.controllers)) {
      console.log("input", input);
      if (input.type === "toggle") {
        const upButton = new Button(input.upChannel);
        if (input.onUpClick) {
          upButton.on("click", () => {
            for (const setting of Object.values(input.onUpClick)) {
              const light = lights.find((l) => l.id === setting.deviceId);
              if (!light) {
                console.error(`Light ${setting.deviceId} not found`);
                continue;
              }
              mqttClient.publish(
                "somo/actions",
                JSON.stringify({
                  action: "changeLight",
                  id: light.id,
                  brightness: setting.targetValue,
                  dimSpeed: light.nonDimmingSpeedMsec,
                })
              );
            }
          });
        }
        if (input.onUpHold) {
          upButton.on("holdStart", () => {
            for (const setting of Object.values(input.onUpHold)) {
              const light = lights.find((l) => l.id === setting.deviceId);
              if (!light) {
                console.error(`Light ${setting.deviceId} not found`);
                continue;
              }
              mqttClient.publish(
                "somo/actions",
                JSON.stringify({
                  action: "changeLight",
                  id: light.id,
                  brightness: setting.targetValue,
                  dimSpeed: light.dimmingSpeedMsec,
                })
              );
            }
          });
        }
        buttons.push(upButton);

        const downButton = new Button(input.downChannel);
        if (input.onDownClick) {
          downButton.on("click", () => {
            for (const setting of Object.values(input.onDownClick)) {
              const light = lights.find((l) => l.id === setting.deviceId);
              if (!light) {
                console.error(`Light ${setting.deviceId} not found`);
                continue;
              }
              mqttClient.publish(
                "somo/actions",
                JSON.stringify({
                  action: "changeLight",
                  id: light.id,
                  brightness: setting.targetValue,
                  dimSpeed: light.nonDimmingSpeedMsec,
                })
              );
            }
          });
        }
        if (input.onDownHold) {
          downButton.on("holdStart", () => {
            for (const setting of Object.values(input.onDownHold)) {
              const light = lights.find((l) => l.id === setting.deviceId);
              if (!light) {
                console.error(`Light ${setting.deviceId} not found`);
                continue;
              }
              mqttClient.publish(
                "somo/actions",
                JSON.stringify({
                  action: "changeLight",
                  id: light.id,
                  brightness: setting.targetValue,
                  dimSpeed: light.dimmingSpeedMsec,
                })
              );
            }
          });
        }
        buttons.push(downButton);
      }
      if (input.type === "momentary") {
        const button = new Button(input.channel);
        button.on("click", () => {
          for (const setting of Object.values(input.onUpClick)) {
            const light = lights.find((l) => l.id === setting.deviceId);
            if (!light) {
              console.error(`Light ${setting.deviceId} not found`);
              continue;
            }
            if (light.targetBrightness === setting.targetValue) {
              mqttClient.publish(
                "somo/actions",
                JSON.stringify({
                  action: "changeLight",
                  id: light.id,
                  brightness: 0,
                  dimSpeed: light.nonDimmingSpeedMsec,
                })
              );
            } else {
              mqttClient.publish(
                "somo/actions",
                JSON.stringify({
                  action: "changeLight",
                  id: light.id,
                  brightness: setting.targetValue,
                  dimSpeed: light.nonDimmingSpeedMsec,
                })
              );
            }
          }
        });
        button.on("holdStart", () => {
          for (const setting of Object.values(input.onUpHold)) {
            const light = lights.find((l) => l.id === setting.deviceId);
            if (!light) {
              console.error(`Light ${setting.deviceId} not found`);
              continue;
            }
            if (light.targetBrightness === setting.targetValue) {
              mqttClient.publish(
                "somo/actions",
                JSON.stringify({
                  action: "changeLight",
                  id: light.id,
                  brightness: 0,
                  dimSpeed: light.dimmingSpeedMsec,
                })
              );
            } else {
              mqttClient.publish(
                "somo/actions",
                JSON.stringify({
                  action: "changeLight",
                  id: light.id,
                  brightness: setting.targetValue,
                  dimSpeed: light.dimmingSpeedMsec,
                })
              );
            }
          }
        });
        buttons.push(button);
      }
    }
  }
  return buttons;
}
