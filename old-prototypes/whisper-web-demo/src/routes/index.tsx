import { createFileRoute } from "@tanstack/react-router";
import { useEffect, useRef, useState } from "react";
import { z } from "zod/v4";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  fa<PERSON><PERSON><PERSON><PERSON><PERSON>,
  fa<PERSON><PERSON>aker,
  faTemperatureList,
} from "@fortawesome/pro-solid-svg-icons";
import { motion, AnimatePresence } from "motion/react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import ollama from "ollama/browser";

const DOCKER_HOST = location.hostname; // "*************";

export const Route = createFileRoute("/")({
  component: App,
});

const float32ToPcm16 = (float32Array: Float32Array) => {
  const pcm16Array = new Int16Array(float32Array.length);
  for (let i = 0; i < float32Array.length; i++) {
    const s = Math.max(-1, Math.min(1, float32Array[i]));
    pcm16Array[i] = s < 0 ? s * 32768 : s * 32767;
  }
  return pcm16Array;
};

const WAKEWORD = "computer";

const wakewordSchema = z
  .object({
    activations: z.array(z.string()),
  })
  .or(
    z.object({
      loaded_models: z.array(z.string()),
    }),
  );

function App() {
  const [isAudioStarted, setIsAudioStarted] = useState(false);
  const currentStream = useRef<MediaStream | null>(null);
  const currentAudioInput = useRef<MediaStreamAudioSourceNode | null>(null);
  const currentScriptProcessorNode = useRef<ScriptProcessorNode | null>(null);
  const wakeWordWss = useRef<WebSocket | null>(null);
  const [speechActive, setSpeechActive] = useState(false);
  const [runningCommand, setRunningCommand] = useState(false);
  const whisperWss = useRef<WebSocket | null>(null);
  const [lastSentence, setLastSentence] = useState("");
  const [lastCommand, setLastCommand] = useState("");

  const lastSentenceRef = useRef("");

  const startTranscription = () => {
    const currentModel = "ctranslate2-4you/whisper-tiny-ct2-int8";

    const whisperWs = new WebSocket(
      `ws://${DOCKER_HOST}:8000/v1/audio/transcriptions?language=en&response_format=text&model=${currentModel}&vad_filter=True`,
    );
    whisperWss.current = whisperWs;
    whisperWs.onopen = function () {
      console.log("[whisperWss] WebSocket connection is open");
      lastSentenceRef.current = "";
    };

    whisperWs.onmessage = (event) => {
      console.log("[whisperWss] Received message:", event.data);
      if (typeof event.data === "string" || event.data instanceof String) {
        setLastSentence(event.data as string);
        lastSentenceRef.current = event.data as string;
      }
    };

    whisperWs.onclose = async function () {
      console.log("[whisperWss] WebSocket connection is closed");
      setSpeechActive(false);
      setRunningCommand(true);
      setLastCommand("");

      // try {
      //   const response = await ollama.chat({
      //     model: "llama3.2",
      //     messages: [{ role: "user", content: lastSentenceRef.current }],
      //     stream: true,
      //   });
      //   for await (const part of response) {
      //     setLastCommand((prev) => prev + part.message.content);
      //   }
      // } catch (error) {
      //   console.error(error);
      // }
      await new Promise((resolve) => setTimeout(resolve, 5000));

      setRunningCommand(false);
      whisperWss.current = null;
    };

    whisperWs.onerror = function (error) {
      console.log("[whisperWss] Error:", error);
    };
  };

  useEffect(() => {
    if (!whisperWss.current && speechActive) {
      setLastSentence("");
      startTranscription();
    }
  }, [speechActive]);

  // useEffect(() => {
  //   if (runningCommand) {
  //     setLastCommand("I opened the door for you.");
  //     const timeout = setTimeout(() => {
  //       setRunningCommand(false);
  //     }, 5000);
  //     return () => clearTimeout(timeout);
  //   }
  // }, [runningCommand]);

  useEffect(() => {
    if (runningCommand) {
      return;
    }
    const ws = new WebSocket(`ws://${DOCKER_HOST}:9000/ws`);

    // When the websocket connection is open
    ws.onopen = function () {
      console.log("[wakeWord] opened");
      wakeWordWss.current = ws;
    };

    ws.onmessage = (event) => {
      console.log("[wakeWord]", event.data);

      const data = wakewordSchema.parse(JSON.parse(event.data));
      if (
        "activations" in data &&
        data.activations.some((activation) => activation.includes(WAKEWORD))
      ) {
        setSpeechActive(true);
      }
    };

    return () => {
      ws.close();
      wakeWordWss.current = null;
    };
  }, [runningCommand]);

  const startAudio = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    const audioContext = new AudioContext({ sampleRate: 16000 });
    const volume = audioContext.createGain();
    const audioInput = audioContext.createMediaStreamSource(stream);
    audioInput.connect(volume);

    const bufferSize = 4096;
    const recorder = audioContext.createScriptProcessor(bufferSize, 1, 1);
    recorder.onaudioprocess = (event) => {
      const samples = event.inputBuffer.getChannelData(0);
      const pcm16 = float32ToPcm16(samples);
      if (
        wakeWordWss.current &&
        wakeWordWss.current.readyState === WebSocket.OPEN
      ) {
        wakeWordWss.current.send(pcm16.buffer);
      }
      if (
        whisperWss.current &&
        whisperWss.current.readyState === WebSocket.OPEN
      ) {
        whisperWss.current.send(pcm16.buffer);
      }
    };
    volume.connect(recorder);
    recorder.connect(audioContext.destination);

    currentStream.current = stream;
    currentAudioInput.current = audioInput;
    currentScriptProcessorNode.current = recorder;

    wakeWordWss.current?.send(`${audioContext.sampleRate}`);

    setIsAudioStarted(true);
  };

  const stopAudio = () => {
    setIsAudioStarted(false);
    setSpeechActive(false);

    // stop all nodes
    currentStream.current?.getTracks().forEach((track) => {
      if (track.readyState == "live") {
        track.stop();
      }
    });
    currentAudioInput.current?.disconnect();
    currentScriptProcessorNode.current?.disconnect();
  };

  const [currentTime, setCurrentTime] = useState(new Date());
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const timeout = setTimeout(() => {
      startAudio();
    }, 1000);
    return () => clearTimeout(timeout);
  }, []);

  return (
    <div className="fixed w-full h-full flex flex-row gap-4 justify-center inset-0 bg-[#F5F5F5] cursor-none">
      {/* <div className="flex flex-col items-center justify-center h-screen w-full gap-4">
        <div
          className={cn(
            "mb-10 h-8 font-semibold transition-colors duration-1000",
            lastSentence.length > 0 && !speechActive
              ? "text-black"
              : "text-gray-400"
          )}
        >
          {lastSentence} {speechActive && " ▋"}
        </div>
        <Button
          onClick={isAudioStarted ? stopAudio : startAudio}
          variant={isAudioStarted ? "outline" : "default"}
        >
          {isAudioStarted ? "Stop" : "Start"} Microphone
        </Button>
      </div> */}
      <div className="absolute top-0 inset-x-0 flex flex-row items-center justify-between p-4">
        <div className="text-sm font-semibold">{format(currentTime, "p")}</div>
        <div className="flex flex-row items-center gap-2">
          <div className="text-sm font-semibold">
            {format(currentTime, "MM/dd/yyyy")}
          </div>
        </div>
      </div>

      <div className="absolute inset-x-0 top-0 bottom-20 flex items-end justify-center pb-10">
        <div className="relative">
          <img
            src="../assets/room.png"
            className="h-[495px] w-[495px] object-cover"
          />
          <div className="absolute bottom-[303px] left-[100px] border-r border-black/30 h-[260px]">
            <div className="flex flex-col items-end pr-2 absolute min-w-[200px] top-0 left-[-200px]">
              <div className="text-sm text-gray-400">Bedroom</div>
              <div className="text-sm font-semibold">ON / 70%</div>
            </div>
          </div>
          <div className="absolute bottom-[423px] left-[196px] border-r border-black/30 h-[260px]">
            <div className="flex flex-col items-end pr-2 absolute min-w-[200px] top-0 left-[-200px]">
              <div className="text-sm text-gray-400">Door</div>
              <div className="text-sm font-semibold">Locked</div>
            </div>
          </div>
          <div className="absolute bottom-[160px] left-[270px] border-r border-black/30 h-[490px]">
            <div className="flex flex-col items-start pl-2 absolute min-w-[200px] top-0 left-0">
              <div className="text-sm text-gray-400">Livingroom</div>
              <div className="text-sm font-semibold">ON / 70%</div>
            </div>
          </div>
          <div className="absolute bottom-[240px] left-[380px] border-r border-black/30 h-[320px]">
            <div className="flex flex-col items-start pl-2 absolute min-w-[200px] top-0 left-0">
              <div className="text-sm text-gray-400">Bathroom</div>
              <div className="text-sm font-semibold">OFF</div>
            </div>
          </div>
        </div>
      </div>

      <div
        className={cn(
          "absolute inset-x-0 top-0 bottom-20 flex items-center justify-center bg-black/10 backdrop-blur-sm transition-opacity duration-1000",
          "flex flex-col items-center justify-center gap-4",
          speechActive || runningCommand ? "opacity-100" : "opacity-0",
        )}
      >
        <div className="flex flex-col min-w-[400px] items-start gap-2">
          <AnimatePresence>
            {(speechActive || lastSentence.length > 0) && (
              <motion.div
                initial={{ translateX: "-500px" }}
                animate={{ translateX: "0px" }}
                className="border border-white rounded-3xl flex items-center justify-center py-4 px-8 bg-[#EAEAEA] shadow-md max-w-[400px] -ml-10"
              >
                {lastSentence} {speechActive && " ▋"}
              </motion.div>
            )}
          </AnimatePresence>
          <AnimatePresence>
            {(runningCommand || lastCommand.length > 0) && (
              <div className="border border-white rounded-3xl flex items-center justify-center py-4 px-8 bg-[#D9DFE9] shadow-md max-w-[400px] self-end -mr-10">
                {lastCommand} {runningCommand && " ▋"}
              </div>
            )}
          </AnimatePresence>
        </div>
      </div>

      <div className="absolute bottom-0 inset-x-0 flex flex-row items-center justify-between px-8 bg-black h-20">
        <div className="flex flex-row gap-1 items-center text-gray-200 justify-center">
          <FontAwesomeIcon icon={faTemperatureList} className="h-7 w-7 " />
          <div className="text-4xl font-light">71°</div>
        </div>
        <div
          className={cn(
            "transition-colors duration-1000 mt-2",
            isAudioStarted
              ? "text-gray-200"
              : "text-gray-400 hover:text-gray-200",
          )}
          onClick={isAudioStarted ? stopAudio : startAudio}
        >
          <FontAwesomeIcon icon={faPowerOff} className="h-7 w-7 " />
        </div>
        <div className="flex flex-row gap-1 items-center text-gray-200 justify-center">
          <div className="flex flex-col -space-y-1 items-end">
            <div className="text-sm text-gray-400">Green Day</div>
            <div className="text-sm">Basket Case</div>
          </div>
          <FontAwesomeIcon icon={faSpeaker} className="h-7 w-7 " />
        </div>
      </div>
    </div>
  );
}
