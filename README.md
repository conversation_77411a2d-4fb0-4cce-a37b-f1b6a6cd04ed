# Somo AI

This is the root of Somo mono-repository.

## Pre-requisites

We use [Node.js](https://nodejs.org/en/) and [pnpm](https://pnpm.io/) to manage dependencies and run scripts.

> [!TIP]
>
> The version of node and pnpm are versioned in the `.tool-versions` file and can be automatically installed with [asdf](https://asdf-vm.com/). Alternatively, it's also set in `.nvmrc` so you can use [nvm](https://github.com/nvm-sh/nvm).

Then, if you want to run the configurator, make sure to do this first:

- Install [Docker](https://www.docker.com/)
- Run a Postgres instance with:

  ```sh
  docker run --name somo-postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 -d postgres
  ```

- From `./packages/admin-server/`:

  - Copy the `.env.example` file to `.env` and ask your coworkers to fill in the values.
  - Run `pnpm install`
  - Run `npx prisma generate`
  - Run migrations: `npx nx run @somo/admin-server:migrate`

- From `./packages/configurator/`:

  - Run `pnpm install`
  - Copy the `.env.example` file to `.env`. Default values are fine, but you may ask your coworkers for theirs if necessary.

- Run `pnpm dev-configurator`

- Open the configurator in your browser (typically http://localhost:3001). Log in with Clerk, using your Somo account (Google SSO). Once done, you should see an empty list of organizations.
  - **Then, ask a co-worker to add you as a user in an organization so you can access the configurator.**

## Apps you can run

We have top-level scripts to run the apps you may be interested in:

### Configurator

Launch the configurator and its dependencies with:

```sh
pnpm dev-configurator
```

### Website

Launch the website with:

```sh
pnpm dev-website
```

### Tablet Controller

Launch the tablet controller with:

```sh
pnpm dev-tablet-controller
```

## Manage the monorepo with Nx

We are using [Nx](https://nx.dev) to manage all of our packages. [Read the docs](https://nx.dev/getting-started/intro) to learn what it can do.

### Visualize packages

You can see details about a specific package by running:

```sh
npx nx show project package-name
```

To generate the project graph, use:

```sh
npx nx graph
```

[Learn more about Nx Graph &raquo;](https://nx.dev/features/explore-graph#explore-the-project-graph)

### Run tasks

To build a package, use:

```sh
npx nx build package-name
```

To run any task with Nx, use:

```sh
npx nx run package-name:task-name
```

These targets are either [inferred automatically](https://nx.dev/concepts/inferred-tasks) or defined in the `project.json` or `package.json` files.

[More about running tasks in the docs &raquo;](https://nx.dev/features/run-tasks)

### Generate a package

```sh
npx nx g @nx/js:lib packages/pkg-name --importPath=@somo/pkg-name
```

### Versioning and releasing

To version and release a package, use:

```sh
npx nx release
```

Pass `--dry-run` to see what would happen without actually releasing the package.

[Learn more about Nx release &raquo;](hhttps://nx.dev/features/manage-releases)

### Keep TypeScript project references up to date

Nx automatically updates TypeScript [project references](https://www.typescriptlang.org/docs/handbook/project-references.html) in `tsconfig.json` files to ensure they remain accurate based on your project dependencies (`import` or `require` statements). This sync is automatically done when running tasks such as `build` or `typecheck`, which require updated references to function correctly.

To manually trigger the process to sync the project graph dependencies information to the TypeScript project references, run the following command:

```sh
npx nx sync
```

You can enforce that the TypeScript project references are always in the correct state when running in CI by adding a step to your CI job configuration that runs the following command:

```sh
npx nx sync:check
```

[Learn more about nx sync](https://nx.dev/reference/nx-commands#sync)

## Automated formatting & fixes

Code formatting is automated with [Prettier](https://prettier.io/).

We use [Husky](https://typicode.github.io/husky/) and [lint-staged](https://github.com/okonet/lint-staged) to run Prettier on staged files when you commit. Thus, it should be transparent to you.

## Update configurator with latest firmware changes

### Pre-requisite

Make sure you have installed `protobuf` on your environment. On MacOS, you can do that with:

    brew install protobuf

Verify it's working by running:

    protoc --version

Make sure you have [**version 29.3**](https://github.com/protocolbuffers/protobuf/releases/tag/v29.3) so you compile the same code—otherwise, you'll notice the version change when you regenerate the WASM files anyway. It's fine to upgrade to a more recent version of protobuf, but **make sure everyone uses the same**. Current version was set based on the default that gets installed with Homebrew to date.

You also need:

- [Emscripten SDK](https://emscripten.org/docs/getting_started/downloads.html) (v4.0.10)
- [CMake](https://cmake.org/) v4.0.3

To install the correct version of Emscripten, checkout the `4.0.10` tag after you have cloned the git repository. From here, follow their README instructions to install and activate it.

Finally, make sure your Emscripten environment is setup.

On Unix/macOS:

    source /path/to/emsdk/emsdk_env.sh

On Windows:

    C:\path\to\emsdk\emsdk_env.bat

You can check your versions with the following commands:

```sh
▶ protoc --version
libprotoc 29.3

▶ cmake --version
cmake version 4.0.3

▶ emcc --check
emcc (Emscripten gcc/clang-like replacement + linker emulating GNU ld) 4.0.10 (b7dc6e5747465580df5984e723b9d1f10d8e804b)
```

### Regenerate the files

Whenever the basestation logic changes, we need to update the artifacts in the configurator to get the simulator in sync.

**TL;DR we have a script that does it all for you:**

    pnpm generate-wasm

### How it works

The core logic of the basestation lives in the [`firmware/basestation/common/`](./firmware/basestation/common/) folder. It is coded in C and flashed onto the basestation hardware.

We re-use the same logic in our configurator to avoid duplicating the logic and allow us to test the firmware logic before flashing the hardware, right from the browser (simulator)!

To do so, we compile the C code into Web Assembly and generate TypeScript types and code from the protobuf configuration. The types and WASM files artifacts are copied to the [Configurator](./packages/configurator/) package.

```mermaid
graph LR
  subgraph "Firmware"
    BS["Basestation Core Logic (C/C++)"]
    PB["Basestation Config (Protobuf)"]
  end

  subgraph "Configurator (web)"
    WASM["Basestation Core Logic (WASM)"]
    TS["Basestation Config (TS types)"]
    CF["Configurator Web App (TS)"]
  end

  G["pnpm generate-wasm"]

  Basestation --> BS
  Browser --> CF

  BS --uses--> PB

  BS -.-G-.-> WASM
  PB -.-G-.-> TS

  CF --uses--> TS
  CF --uses--> WASM

  classDef firmware fill:#bbf,stroke:#333,stroke-width:1px
  classDef artifacts fill:#f9f,stroke:#333,stroke-width:2px
  classDef webComponents fill:#fbb,stroke:#333,stroke-width:1px

  class BS,PB firmware
  class WASM,TS artifacts
  class CF webComponents
```

#### Build the basestation logic into WASM

The basestation code is compiled from C to Web Assembly, so we can embed the same logic and run it on the Node.js server.

To update the WASM files:

1. Go to `firmware/basestation/common`
2. Make sure to follow [the Readme instructions](./firmware/basestation/common/Readme.md) to have the necessary setup
3. Build the Web Assembly Module into the `common/build/` folder with:
   ```sh
   pnpm generate-wasm-build
   ```
4. Copy the `firmware/basestation/common/build/common.*` files to `packages/configurator` with:
   ```sh
   pnpm generate-wasm-copy
   ```

> ![TIP]
>
> In the future, since we want to version the WASM files to keep track of the changes, we may swap the `basestation-wasm/` folder with an actual npm package wrapping the WASM files, so that we can version and release.

#### Generate the protobuf configuration

The protobuf config lives in [firmware/basestation](./firmware/basestation/basestation-config.proto).

From that config, we generate TypeScript types and code that we can use in the [Configurator](./packages/configurator/) and the [Server](./packages/admin-server/) to generate the proper data for the basestation, based on the JSON configuration we craft there.

To do so, we use [ts-proto](https://github.com/stephenh/ts-proto). This depends on `protoc` to be available (it's a plugin). Ideally, we'd have used the [node protoc package](https://www.npmjs.com/package/protoc) but it has been broken for a while and none of the suggested alternatives worked for us.

To generate [the types and code](./packages/shared/src/proto/) from protobuf config, you can run:

    pnpm generate-wasm-proto
