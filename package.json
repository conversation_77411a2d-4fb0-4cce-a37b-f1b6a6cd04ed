{"name": "@somo/source", "version": "0.0.0", "private": true, "license": "MIT", "scripts": {"build-shared": "nx build shared", "dev": "nx dev", "dev-configurator": "nx run-many --target=dev --projects=@somo/admin-server,@somo/configurator --parallel --output-style=stream", "dev-tablet-controller": "nx run @somo/tablet-controller:dev", "dev-website": "nx run @somo/website:dev", "format": "prettier . --write", "format-check": "prettier . --list-different", "generate-protobuf": "protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_out=./packages/shared/src/proto --ts_proto_opt=esModuleInterop=true,outputJsonMethods=false,outputClientImpl=false firmware/basestation/basestation-config.proto -I firmware/basestation -I firmware/basestation/lib/nanopb-*******-macosx-x86/generator/proto", "generate-protobuf-commands": "protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_out=./packages/shared/src/proto --ts_proto_opt=esModuleInterop=true,outputJsonMethods=false,outputClientImpl=false firmware/basestation/commands.proto -I firmware/basestation -I firmware/basestation/lib/nanopb-*******-macosx-x86/generator/proto", "generate-wasm": "pnpm generate-wasm-build && pnpm generate-wasm-copy && pnpm generate-wasm-proto", "generate-wasm-build": "cd firmware/basestation/common/build && ../build-wasm.sh && cd -", "generate-wasm-copy": "cp firmware/basestation/common/build/common.{js,wasm} packages/configurator/public/basestation-wasm/ && cp firmware/basestation/common/build/common.d.ts packages/configurator/src/types/basestation.d.ts", "generate-wasm-proto": "pnpm generate-protobuf && pnpm generate-protobuf-commands", "lint": "nx lint", "lint-all": "nx run-many -t lint", "prepare": "husky", "test": "nx test", "test-all": "nx run-many -t test", "typecheck": "nx typecheck", "typecheck-all": "nx run-many -t typecheck"}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "dependencies": {"@radix-ui/react-progress": "^1.1.7"}, "devDependencies": {"@eslint/js": "9.17.0", "@nx/eslint": "20.6.4", "@nx/eslint-plugin": "20.6.4", "@nx/js": "20.6.4", "@nx/vite": "20.6.4", "@nx/web": "20.6.4", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/node": "22.10.3", "@vitest/coverage-v8": "3.0.5", "@vitest/ui": "3.0.5", "eslint": "9.8.0", "eslint-config-prettier": "9.0.0", "husky": "9.1.7", "jiti": "2.4.2", "jsdom": "~22.1.0", "jsonc-eslint-parser": "2.1.0", "lint-staged": "15.5.0", "nx": "20.6.4", "prettier": "3.5.3", "prettier-plugin-curly": "0.3.1", "prettier-plugin-packagejson": "2.5.10", "prettier-plugin-sh": "0.15.0", "ts-proto": "2.7.0", "tslib": "2.3.0", "typescript": "~5.7.2", "typescript-eslint": "8.19.0", "vite": "6.2.3", "vite-plugin-dts": "~4.5.0", "vitest": "3.0.5"}, "packageManager": "pnpm@10.13.1", "pnpm": {"onlyBuiltDependencies": ["@clerk/shared", "@prisma/client", "@prisma/engines", "@swc/core", "nx", "prisma"]}}