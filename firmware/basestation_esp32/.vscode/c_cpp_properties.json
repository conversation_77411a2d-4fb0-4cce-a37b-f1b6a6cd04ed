{"configurations": [{"name": "ESP-IDF", "compilerPath": "${config:idf.toolsPath}/tools/xtensa-esp-elf/esp-15.1.0_20250607/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc", "compileCommands": "${config:idf.buildPath}/compile_commands.json", "includePath": ["${config:idf.espIdfPath}/components/**", "${config:idf.espIdfPathWin}/components/**", "${workspaceFolder}/**"], "browse": {"path": ["${config:idf.espIdfPath}/components", "${config:idf.espIdfPathWin}/components", "${workspaceFolder}"], "limitSymbolsToIncludedHeaders": true}}], "version": 4}