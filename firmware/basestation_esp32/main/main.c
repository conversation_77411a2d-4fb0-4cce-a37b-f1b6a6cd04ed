#include "main.h"

static const char * FILE_TAG = FILE_TAG_MAIN;

void app_main( void ){
    // set debug levels to any file
    // this is done through the FILE_TAG for each print we want to make
        esp_log_level_set( FILE_TAG_MAIN,           ESP_LOG_VERBOSE );
        esp_log_level_set( FILE_TAG_UART,           ESP_LOG_VERBOSE );
        esp_log_level_set( FILE_TAG_NETIF_COMMON,   ESP_LOG_VERBOSE );
        esp_log_level_set( FILE_TAG_WIFI,           ESP_LOG_VERBOSE );
        esp_log_level_set( FILE_TAG_WEBSOCKET,      ESP_LOG_VERBOSE );
        esp_log_level_set( FILE_TAG_ETHERNET,       ESP_LOG_VERBOSE );
        // esp-idf libraries definitions
        esp_log_level_set( "uart",                          ESP_LOG_NONE );
        esp_log_level_set( "nvs",                           ESP_LOG_NONE );
        esp_log_level_set( "intr_alloc",                    ESP_LOG_NONE );
        esp_log_level_set( "event",                         ESP_LOG_NONE );
        esp_log_level_set( "cpu_start",                     ESP_LOG_NONE );
        esp_log_level_set( "spi_flash",                     ESP_LOG_NONE );
        esp_log_level_set( "memspi",                        ESP_LOG_NONE );
        esp_log_level_set( "heap_init",                     ESP_LOG_NONE );
        esp_log_level_set( "memory_layout",                 ESP_LOG_NONE );
        esp_log_level_set( "esp_adapter",                   ESP_LOG_NONE );
        esp_log_level_set( "system_api",                    ESP_LOG_NONE );
        esp_log_level_set( "efuse",                         ESP_LOG_NONE );
        esp_log_level_set( "gpio",                          ESP_LOG_ERROR );
        /*********************************************************************/
        esp_log_level_set( "wifi",                          ESP_LOG_NONE );
        esp_log_level_set( "wifi_init",                     ESP_LOG_NONE );
        esp_log_level_set( "wifi_init_default",             ESP_LOG_NONE );
        esp_log_level_set( "phy_init",                      ESP_LOG_NONE );
        /*********************************************************************/
        esp_log_level_set( "esp_eth",                       ESP_LOG_ERROR );
        esp_log_level_set( "esp.emac",                      ESP_LOG_ERROR );
        esp_log_level_set( "esp_eth.phy",                   ESP_LOG_ERROR );
        esp_log_level_set( "eth_phy_802_3",                 ESP_LOG_ERROR );

        esp_log_level_set( "w6100-mac",                     ESP_LOG_ERROR );
        esp_log_level_set( "w6100-phy",                     ESP_LOG_ERROR );
        esp_log_level_set( "spi_master",                    ESP_LOG_NONE );
        /*********************************************************************/
        esp_log_level_set( "websocket_client",              ESP_LOG_ERROR );
        esp_log_level_set( "mbedtls",                       ESP_LOG_NONE );
        esp_log_level_set( "HTTP_CLIENT",                   ESP_LOG_NONE );
        esp_log_level_set( "Dynamic Impl",                  ESP_LOG_NONE );
        esp_log_level_set( "SSL TLS",                       ESP_LOG_NONE );
        esp_log_level_set( "SSL client",                    ESP_LOG_NONE );
        esp_log_level_set( "esp-tls",                       ESP_LOG_NONE );
    // Initialize GPIOs and netif configuration as the device should be an always connected type
    // Initialize any configuration or setup tasks
    vTaskDelay( pdMS_TO_TICKS( 2000 ) );
    xTaskCreate( vUARTDebugRxTask, "UARTDebugTask", 4096, NULL, 1, &xUARTDebugTaskHandle );

    // vTaskDelay( pdMS_TO_TICKS( 250 ) );
    ESP_LOGD( FILE_TAG, ">>>>> APP START" );
    ESP_LOGD( FILE_TAG, "Time: %s", __TIME__ );
    ESP_LOGD( FILE_TAG, "Date: %s", __DATE__ );

    xNetifCommonStart();

    // Create business logic tasks
    xTaskCreate( vUARTPicoRxTask, "UARTPicoTask", 4096, NULL, 1, &xUARTPicoTaskHandle );
    // vTaskDelay( pdMS_TO_TICKS( 2000 ) );
    vTaskDelay( pdMS_TO_TICKS( 250 ) );
    // vWifiInit();
    xETHInit();
}