// File where all GPIO numeration is set
// All the following nums refer to ESP32-S3-DevKitM-1, most likely the number doesn't change in the bus GPIOs
#ifndef __PINOUT_H__
#define __PINOUT_H__

#include "types.h"

typedef enum {
    GPIO0_PIN = 0,
    <PERSON><PERSON>1_PIN,
    <PERSON><PERSON>2_PIN,
    GPIO<PERSON>_PIN,
    GPIO4_PIN,
    GPIO5_PIN,
    GPIO<PERSON>_PIN,
    GPIO7_PIN,
    GPIO8_PIN,
    GPIO<PERSON>_PIN,
    GPIO10_PIN,
    GPIO11_PIN,
    GPIO12_PIN,
    <PERSON><PERSON>13_PIN,
    GPIO14_PIN,
    GP<PERSON>15_PIN,
    <PERSON><PERSON><PERSON>_PIN,
    <PERSON><PERSON>17_PIN,
    GPIO18_PIN,
    GP<PERSON>19_PIN,
    GP<PERSON>20_PIN,
    GP<PERSON>21_PIN,
    GPIO22_PIN,
    GPIO23_PIN,
    GPIO24_PIN,
    GPIO25_PIN,
    GPIO26_PIN,
    GP<PERSON>27_PIN,
    GPIO28_PIN,
    GPIO29_PIN,
    GPIO30_PIN,
    GP<PERSON>31_PIN,
    GP<PERSON>32_PIN,
    <PERSON><PERSON><PERSON>_PIN,
    <PERSON><PERSON><PERSON>_PIN,
    <PERSON><PERSON><PERSON>_PIN,
    <PERSON><PERSON><PERSON>_PIN,
    <PERSON><PERSON><PERSON>_PIN,
    <PERSON><PERSON><PERSON>_PIN,
    <PERSON><PERSON><PERSON>_PIN,
    <PERSON><PERSON><PERSON>_PIN,
    GP<PERSON><PERSON>_PIN,
    GP<PERSON>42_PIN,
    GPIO43_PIN,
    GPIO44_PIN,
    GPIO45_PIN
} GPIOPin;

// UART definition for Pico comms
#define UART_CHANNELS 2

#define UART_PARAMS_DEFAULT() { \
    { \
        .rx_pin = GPIO44_PIN, \
        .tx_pin = GPIO43_PIN, \
        .port = UART_NUM_0, \
        .config = { \
            .baud_rate = 115200, \
            .data_bits = UART_DATA_8_BITS, \
            .parity = UART_PARITY_DISABLE, \
            .stop_bits = UART_STOP_BITS_1, \
            .flow_ctrl = UART_HW_FLOWCTRL_DISABLE, \
            .source_clk = UART_SCLK_DEFAULT, \
        }, \
    }, \
    { \
        .rx_pin = GPIO18_PIN, \
        .tx_pin = GPIO17_PIN, \
        .port = UART_NUM_1, \
        .config = { \
            .baud_rate = 921600, \
            .data_bits = UART_DATA_8_BITS, \
            .parity = UART_PARITY_DISABLE, \
            .stop_bits = UART_STOP_BITS_1, \
            .flow_ctrl = UART_HW_FLOWCTRL_DISABLE, \
            .source_clk = UART_SCLK_DEFAULT, \
        }, \
    }, \
};

#define SPI_ETH_PARAMS_DEFAULT() { \
    .int_pin = GPIO8_PIN, \
    .reset_pin = GPIO9_PIN, \
    .host = SPI2_HOST, \
    .miso_pin = GPIO13_PIN, \
    .mosi_pin = GPIO11_PIN, \
    .clk_pin = GPIO12_PIN, \
    .cs_pin = GPIO10_PIN, \
    .dev_cfg = { \
        .clock_speed_hz = 5000000, \
        .mode = 0, \
        .spics_io_num = GPIO10_PIN, \
        .queue_size = 20, \
    }, \
}

#endif