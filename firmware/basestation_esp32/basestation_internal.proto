// Compile me into c with
// python lib/nanopb-*******-macosx-x86/generator/nanopb_generator.py basestation-config.proto

syntax = "proto3";

import "nanopb.proto";

// incoming commands
message NetworkConfig {
    message MACConfig{
        bool use_mac_address = 1;
        string mac_address = 1 [(nanopb).max_length = 17];
    }

    message DHCPConfig{
        bool static_ip = 1;
        string ip_address = 2 [(nanopb).max_length = 15];
        string subnet_mask = 3 [(nanopb).max_length = 15];
        string gateway = 4 [(nanopb).max_length = 15];
        string dns_server = 5 [(nanopb).max_length = 15];
    }

    message WifiConfig {
        string ssid = 1 [(nanopb).max_length = 32];
        string password = 2 [(nanopb).max_length = 32];
    }
}