#!/usr/bin/env python3

import serial
import serial.tools.list_ports
import struct
import sys
import time
import argparse
from pathlib import Path

class PicoCanFlasher:
    USB_CMD_START_FLASH = 0x01
    USB_CMD_SEND_DATA = 0x02
    USB_CMD_GET_STATUS = 0x03
    USB_CMD_ABORT = 0x04
    USB_CMD_SET_NODE_ID = 0x05
    
    USB_RESP_ACK = 0x10
    USB_RESP_NACK = 0x11
    USB_RESP_STATUS = 0x12
    USB_RESP_COMPLETE = 0x13
    
    def __init__(self, port=None):
        self.port = port
        self.ser = None
        self.chunk_size = 1024
        
    def find_pico(self):
        """Find Pico running CAN flasher firmware"""
        ports = serial.tools.list_ports.comports()
        for port in ports:
            if "Pico" in port.description or "2e8a:0005" in port.hwid:
                return port.device
        return None
        
    def connect(self):
        """Connect to the Pico"""
        if not self.port:
            self.port = self.find_pico()
            if not self.port:
                raise Exception("No Pico found. Please specify port manually.")
                
        self.ser = serial.Serial(self.port, 115200, timeout=2)
        time.sleep(0.5)  # Give time for USB to stabilize
        self.ser.reset_input_buffer()
        print(f"Connected to {self.port}")
        
    def disconnect(self):
        """Disconnect from the Pico"""
        if self.ser:
            self.ser.close()
            
    def send_command(self, cmd, data=b''):
        """Send command to Pico"""
        length = len(data)
        packet = struct.pack('B', cmd) + struct.pack('>I', length) + data
        self.ser.write(packet)
        
    def read_response(self):
        """Read response from Pico"""
        # Read response header
        header = self.ser.read(5)
        if len(header) != 5:
            raise Exception("Timeout reading response header")
            
        resp_cmd = header[0]
        resp_len = struct.unpack('>I', header[1:5])[0]
        
        # Read response data
        data = self.ser.read(resp_len) if resp_len > 0 else b''
        if len(data) != resp_len:
            raise Exception("Timeout reading response data")
            
        return resp_cmd, data
        
    def set_node_id(self, node_id):
        """Set target CAN node ID"""
        print(f"Setting target node ID to {node_id}")
        self.send_command(self.USB_CMD_SET_NODE_ID, struct.pack('B', node_id))
        
        resp_cmd, resp_data = self.read_response()
        if resp_cmd != self.USB_RESP_ACK:
            raise Exception("Failed to set node ID")
            
    def flash_firmware(self, firmware_path, node_id):
        """Flash firmware to target node"""
        # Read firmware file
        with open(firmware_path, 'rb') as f:
            firmware_data = f.read()
            
        firmware_size = len(firmware_data)
        print(f"Firmware size: {firmware_size} bytes")
        
        # Set target node
        self.set_node_id(node_id)
        
        # Start flash process
        print("Starting flash process...")
        self.send_command(self.USB_CMD_START_FLASH, struct.pack('>I', firmware_size))
        
        resp_cmd, resp_data = self.read_response()
        if resp_cmd != self.USB_RESP_ACK:
            raise Exception("Failed to start flash process")
            
        # Send firmware data in chunks
        print("Sending firmware data...")
        bytes_sent = 0
        while bytes_sent < firmware_size:
            chunk = firmware_data[bytes_sent:bytes_sent + self.chunk_size]
            self.send_command(self.USB_CMD_SEND_DATA, chunk)
            
            resp_cmd, resp_data = self.read_response()
            if resp_cmd != self.USB_RESP_ACK:
                raise Exception(f"Failed to send data at offset {bytes_sent}")
                
            bytes_sent += len(chunk)
            progress = (bytes_sent * 100) // firmware_size
            print(f"\rUploading: {progress}%", end='', flush=True)
            
        print("\nFirmware uploaded, flashing target...")
        
        # Monitor flash progress
        while True:
            self.send_command(self.USB_CMD_GET_STATUS)
            resp_cmd, resp_data = self.read_response()
            
            if resp_cmd == self.USB_RESP_COMPLETE:
                print("\nFlash complete!")
                break
            elif resp_cmd == self.USB_RESP_STATUS:
                status = resp_data[0]
                progress = resp_data[1]
                print(f"\rFlashing: {progress}%", end='', flush=True)
            else:
                raise Exception("Unexpected response during flash")
                
            time.sleep(0.1)
            
    def abort_flash(self):
        """Abort ongoing flash operation"""
        print("Aborting flash operation...")
        self.send_command(self.USB_CMD_ABORT)
        
        resp_cmd, resp_data = self.read_response()
        if resp_cmd == self.USB_RESP_ACK:
            print("Flash aborted")
        else:
            print("Failed to abort flash")

def main():
    parser = argparse.ArgumentParser(description='Flash Pico firmware via CAN bus')
    parser.add_argument('firmware', help='Path to firmware binary file')
    parser.add_argument('node_id', type=int, help='Target CAN node ID (0-127)')
    parser.add_argument('-p', '--port', help='Serial port (auto-detect if not specified)')
    parser.add_argument('-c', '--chunk-size', type=int, default=1024, 
                        help='Data chunk size in bytes (default: 1024)')
    
    args = parser.parse_args()
    
    # Validate inputs
    if not Path(args.firmware).exists():
        print(f"Error: Firmware file '{args.firmware}' not found")
        sys.exit(1)
        
    if not 0 <= args.node_id <= 127:
        print("Error: Node ID must be between 0 and 127")
        sys.exit(1)
        
    # Create flasher instance
    flasher = PicoCanFlasher(args.port)
    flasher.chunk_size = args.chunk_size
    
    try:
        # Connect to Pico
        flasher.connect()
        
        # Flash firmware
        flasher.flash_firmware(args.firmware, args.node_id)
        
    except KeyboardInterrupt:
        print("\nInterrupted by user")
        flasher.abort_flash()
    except Exception as e:
        print(f"\nError: {e}")
        sys.exit(1)
    finally:
        flasher.disconnect()

if __name__ == "__main__":
    main()