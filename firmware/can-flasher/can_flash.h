#ifndef CAN_FLASH_H
#define CAN_FLASH_H

#include <stdint.h>
#include <stdbool.h>

#define CAN_FLASH_CMD_ERASE     0x01
#define CAN_FLASH_CMD_WRITE     0x02
#define CAN_FLASH_CMD_VERIFY    0x03
#define CAN_FLASH_CMD_REBOOT    0x04
#define CAN_FLASH_CMD_ACK       0x05
#define CAN_FLASH_CMD_NACK      0x06
#define CAN_FLASH_CMD_ENTER_BL  0x07

#define CAN_FLASH_CHUNK_SIZE    64
#define CAN_FLASH_TIMEOUT_MS    1000

typedef struct {
    uint8_t node_id;
    uint32_t flash_addr;
    uint32_t total_size;
    uint8_t *data;
    uint32_t bytes_written;
    bool in_progress;
} flash_session_t;

void can_init(void);
bool can_start_flash(uint8_t node_id, uint32_t size, uint8_t *data);
bool can_flash_chunk(void);
bool can_is_flash_complete(void);
void can_abort_flash(void);
uint32_t can_get_progress(void);

#endif