# CAN Bus Pico Flasher

This firmware and Python tool allows flashing Raspberry Pi Pico devices over CAN bus using another Pico as a USB-to-CAN bridge.

## Hardware Requirements

- Raspberry Pi Pico (for the flasher)
- MCP2515 CAN transceiver module
- Target Pico device with CAN bootloader

## Pin Connections

| Pico Pin | MCP2515 Pin |
|----------|-------------|
| GP16     | MISO        |
| GP17     | CS          |
| GP18     | SCK         |
| GP19     | MOSI        |

## Building the Firmware

```bash
cd firmware/can-flasher
mkdir build && cd build
cmake ..
make
```

Flash the resulting `can_flasher.uf2` to your Pico.

## Using the Python Tool

```bash
# Flash firmware.bin to node ID 5
python3 flash_pico.py firmware.bin 5

# Specify serial port manually
python3 flash_pico.py firmware.bin 5 -p /dev/ttyACM0

# Use smaller chunks for slower connections
python3 flash_pico.py firmware.bin 5 -c 512
```

## Protocol

The flasher uses a simple USB protocol:
- Commands are sent as: [CMD][LENGTH(4 bytes)][DATA]
- Responses follow the same format

CAN messages use ID 0x100+node_id for commands and 0x101+node_id for data.

## CAN Bootloader Commands

- `0x01`: Erase flash
- `0x02`: Write data
- `0x03`: Verify checksum
- `0x04`: Reboot
- `0x07`: Enter bootloader mode