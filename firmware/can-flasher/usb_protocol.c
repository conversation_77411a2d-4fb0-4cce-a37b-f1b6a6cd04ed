#include "usb_protocol.h"
#include "can_flash.h"
#include "pico/stdlib.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define USB_BUFFER_SIZE 4096

static uint8_t usb_buffer[USB_BUFFER_SIZE];
static uint8_t *firmware_buffer = NULL;
static uint32_t firmware_size = 0;
static uint32_t firmware_received = 0;
static uint8_t target_node_id = 0;

static void send_response(uint8_t cmd, uint8_t *data, uint32_t len) {
    putchar(cmd);
    putchar((len >> 24) & 0xFF);
    putchar((len >> 16) & 0xFF);
    putchar((len >> 8) & 0xFF);
    putchar(len & 0xFF);
    
    for (uint32_t i = 0; i < len; i++) {
        putchar(data[i]);
    }
}

static void handle_start_flash(uint32_t size) {
    if (firmware_buffer) {
        free(firmware_buffer);
        firmware_buffer = NULL;
    }
    
    firmware_buffer = malloc(size);
    if (!firmware_buffer) {
        uint8_t resp = 0;
        send_response(USB_RESP_NACK, &resp, 1);
        return;
    }
    
    firmware_size = size;
    firmware_received = 0;
    
    uint8_t resp = 1;
    send_response(USB_RESP_ACK, &resp, 1);
}

static void handle_send_data(uint8_t *data, uint32_t len) {
    if (!firmware_buffer || firmware_received + len > firmware_size) {
        uint8_t resp = 0;
        send_response(USB_RESP_NACK, &resp, 1);
        return;
    }
    
    memcpy(firmware_buffer + firmware_received, data, len);
    firmware_received += len;
    
    if (firmware_received >= firmware_size) {
        if (can_start_flash(target_node_id, firmware_size, firmware_buffer)) {
            uint8_t resp = 1;
            send_response(USB_RESP_ACK, &resp, 1);
        } else {
            uint8_t resp = 0;
            send_response(USB_RESP_NACK, &resp, 1);
        }
    } else {
        uint8_t resp = 1;
        send_response(USB_RESP_ACK, &resp, 1);
    }
}

static void handle_get_status(void) {
    uint8_t status[5];
    
    if (can_is_flash_complete()) {
        status[0] = 2;
        status[1] = 100;
        status[2] = 0;
        status[3] = 0;
        status[4] = 0;
        send_response(USB_RESP_COMPLETE, status, 5);
        
        if (firmware_buffer) {
            free(firmware_buffer);
            firmware_buffer = NULL;
        }
    } else {
        can_flash_chunk();
        
        uint32_t progress = can_get_progress();
        status[0] = 1;
        status[1] = progress;
        status[2] = 0;
        status[3] = 0;
        status[4] = 0;
        send_response(USB_RESP_STATUS, status, 5);
    }
}

static void handle_abort(void) {
    can_abort_flash();
    
    if (firmware_buffer) {
        free(firmware_buffer);
        firmware_buffer = NULL;
    }
    
    uint8_t resp = 1;
    send_response(USB_RESP_ACK, &resp, 1);
}

static void handle_set_node_id(uint8_t node_id) {
    target_node_id = node_id;
    uint8_t resp = 1;
    send_response(USB_RESP_ACK, &resp, 1);
}

void usb_process_commands(void) {
    int c = getchar_timeout_us(0);
    if (c == PICO_ERROR_TIMEOUT) return;
    
    uint8_t cmd = c;
    
    uint32_t len = 0;
    for (int i = 0; i < 4; i++) {
        c = getchar_timeout_us(1000);
        if (c == PICO_ERROR_TIMEOUT) return;
        len = (len << 8) | c;
    }
    
    if (len > USB_BUFFER_SIZE) return;
    
    for (uint32_t i = 0; i < len; i++) {
        c = getchar_timeout_us(1000);
        if (c == PICO_ERROR_TIMEOUT) return;
        usb_buffer[i] = c;
    }
    
    switch (cmd) {
        case USB_CMD_START_FLASH:
            if (len == 4) {
                uint32_t size = (usb_buffer[0] << 24) | (usb_buffer[1] << 16) | 
                               (usb_buffer[2] << 8) | usb_buffer[3];
                handle_start_flash(size);
            }
            break;
            
        case USB_CMD_SEND_DATA:
            handle_send_data(usb_buffer, len);
            break;
            
        case USB_CMD_GET_STATUS:
            handle_get_status();
            break;
            
        case USB_CMD_ABORT:
            handle_abort();
            break;
            
        case USB_CMD_SET_NODE_ID:
            if (len == 1) {
                handle_set_node_id(usb_buffer[0]);
            }
            break;
    }
}