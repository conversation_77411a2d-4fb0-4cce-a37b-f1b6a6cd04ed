#include "can_flash.h"
#include "pico/stdlib.h"
#include "hardware/spi.h"
#include "hardware/gpio.h"
#include <string.h>
#include <stdio.h>

#define CAN_SPI_PORT spi0
#define CAN_SCK_PIN  18
#define CAN_MOSI_PIN 19
#define CAN_MISO_PIN 16
#define CAN_CS_PIN   17

#define MCP2515_CMD_WRITE       0x02
#define MCP2515_CMD_READ        0x03
#define MCP2515_CMD_BIT_MODIFY  0x05
#define MCP2515_CMD_LOAD_TX0    0x40
#define MCP2515_CMD_RTS_TX0     0x81
#define MCP2515_CMD_READ_RX0    0x90
#define MCP2515_CMD_READ_STATUS 0xA0
#define MCP2515_CMD_RESET       0xC0

#define MCP2515_CANCTRL         0x0F
#define MCP2515_CANSTAT         0x0E
#define MCP2515_CNF3            0x28
#define MCP2515_CNF2            0x29
#define MCP2515_CNF1            0x2A
#define MCP2515_CANINTE         0x2B
#define MCP2515_CANINTF         0x2C
#define MCP2515_TXB0CTRL        0x30
#define MCP2515_RXB0CTRL        0x60

static flash_session_t flash_session = {0};

static void mcp2515_reset(void) {
    gpio_put(CAN_CS_PIN, 0);
    uint8_t cmd = MCP2515_CMD_RESET;
    spi_write_blocking(CAN_SPI_PORT, &cmd, 1);
    gpio_put(CAN_CS_PIN, 1);
    sleep_ms(10);
}

static void mcp2515_write_register(uint8_t reg, uint8_t value) {
    gpio_put(CAN_CS_PIN, 0);
    uint8_t buf[3] = {MCP2515_CMD_WRITE, reg, value};
    spi_write_blocking(CAN_SPI_PORT, buf, 3);
    gpio_put(CAN_CS_PIN, 1);
}

static uint8_t mcp2515_read_register(uint8_t reg) {
    gpio_put(CAN_CS_PIN, 0);
    uint8_t buf[3] = {MCP2515_CMD_READ, reg, 0};
    spi_write_read_blocking(CAN_SPI_PORT, buf, buf, 3);
    gpio_put(CAN_CS_PIN, 1);
    return buf[2];
}

static bool mcp2515_send_message(uint32_t id, uint8_t *data, uint8_t len) {
    gpio_put(CAN_CS_PIN, 0);
    uint8_t cmd = MCP2515_CMD_LOAD_TX0;
    spi_write_blocking(CAN_SPI_PORT, &cmd, 1);
    
    uint8_t buf[5];
    buf[0] = (id >> 3) & 0xFF;
    buf[1] = (id << 5) & 0xE0;
    buf[2] = 0;
    buf[3] = 0;
    buf[4] = len & 0x0F;
    
    spi_write_blocking(CAN_SPI_PORT, buf, 5);
    spi_write_blocking(CAN_SPI_PORT, data, len);
    gpio_put(CAN_CS_PIN, 1);
    
    gpio_put(CAN_CS_PIN, 0);
    cmd = MCP2515_CMD_RTS_TX0;
    spi_write_blocking(CAN_SPI_PORT, &cmd, 1);
    gpio_put(CAN_CS_PIN, 1);
    
    return true;
}

static bool mcp2515_read_message(uint32_t *id, uint8_t *data, uint8_t *len) {
    uint8_t status = mcp2515_read_register(MCP2515_CANINTF);
    if (!(status & 0x01)) return false;
    
    gpio_put(CAN_CS_PIN, 0);
    uint8_t cmd = MCP2515_CMD_READ_RX0;
    spi_write_blocking(CAN_SPI_PORT, &cmd, 1);
    
    uint8_t buf[5];
    spi_read_blocking(CAN_SPI_PORT, 0, buf, 5);
    
    *id = ((uint32_t)buf[0] << 3) | (buf[1] >> 5);
    *len = buf[4] & 0x0F;
    
    spi_read_blocking(CAN_SPI_PORT, 0, data, *len);
    gpio_put(CAN_CS_PIN, 1);
    
    mcp2515_write_register(MCP2515_CANINTF, 0x00);
    
    return true;
}

void can_init(void) {
    spi_init(CAN_SPI_PORT, 10000000);
    gpio_set_function(CAN_SCK_PIN, GPIO_FUNC_SPI);
    gpio_set_function(CAN_MOSI_PIN, GPIO_FUNC_SPI);
    gpio_set_function(CAN_MISO_PIN, GPIO_FUNC_SPI);
    
    gpio_init(CAN_CS_PIN);
    gpio_set_dir(CAN_CS_PIN, GPIO_OUT);
    gpio_put(CAN_CS_PIN, 1);
    
    mcp2515_reset();
    
    mcp2515_write_register(MCP2515_CNF1, 0x00);
    mcp2515_write_register(MCP2515_CNF2, 0x90);
    mcp2515_write_register(MCP2515_CNF3, 0x02);
    
    mcp2515_write_register(MCP2515_CANCTRL, 0x00);
    
    while ((mcp2515_read_register(MCP2515_CANSTAT) & 0xE0) != 0x00) {
        sleep_ms(1);
    }
    
    mcp2515_write_register(MCP2515_RXB0CTRL, 0x60);
    mcp2515_write_register(MCP2515_CANINTE, 0x01);
}

bool can_start_flash(uint8_t node_id, uint32_t size, uint8_t *data) {
    if (flash_session.in_progress) return false;
    
    flash_session.node_id = node_id;
    flash_session.flash_addr = 0x10020000;
    flash_session.total_size = size;
    flash_session.data = data;
    flash_session.bytes_written = 0;
    flash_session.in_progress = true;
    
    uint8_t cmd_data[8] = {CAN_FLASH_CMD_ENTER_BL, 0, 0, 0, 0, 0, 0, 0};
    mcp2515_send_message(0x100 | node_id, cmd_data, 8);
    sleep_ms(100);
    
    cmd_data[0] = CAN_FLASH_CMD_ERASE;
    cmd_data[1] = (flash_session.flash_addr >> 24) & 0xFF;
    cmd_data[2] = (flash_session.flash_addr >> 16) & 0xFF;
    cmd_data[3] = (flash_session.flash_addr >> 8) & 0xFF;
    cmd_data[4] = flash_session.flash_addr & 0xFF;
    cmd_data[5] = (size >> 24) & 0xFF;
    cmd_data[6] = (size >> 16) & 0xFF;
    cmd_data[7] = (size >> 8) & 0xFF;
    
    mcp2515_send_message(0x100 | node_id, cmd_data, 8);
    
    uint32_t start = to_ms_since_boot(get_absolute_time());
    while (to_ms_since_boot(get_absolute_time()) - start < CAN_FLASH_TIMEOUT_MS) {
        uint32_t rx_id;
        uint8_t rx_data[8];
        uint8_t rx_len;
        
        if (mcp2515_read_message(&rx_id, rx_data, &rx_len)) {
            if ((rx_id & 0xFF) == node_id && rx_data[0] == CAN_FLASH_CMD_ACK) {
                return true;
            }
        }
    }
    
    flash_session.in_progress = false;
    return false;
}

bool can_flash_chunk(void) {
    if (!flash_session.in_progress) return false;
    if (flash_session.bytes_written >= flash_session.total_size) return true;
    
    uint32_t chunk_size = flash_session.total_size - flash_session.bytes_written;
    if (chunk_size > CAN_FLASH_CHUNK_SIZE) chunk_size = CAN_FLASH_CHUNK_SIZE;
    
    uint8_t cmd_data[8];
    cmd_data[0] = CAN_FLASH_CMD_WRITE;
    cmd_data[1] = (flash_session.flash_addr >> 24) & 0xFF;
    cmd_data[2] = (flash_session.flash_addr >> 16) & 0xFF;
    cmd_data[3] = (flash_session.flash_addr >> 8) & 0xFF;
    cmd_data[4] = flash_session.flash_addr & 0xFF;
    cmd_data[5] = chunk_size;
    cmd_data[6] = 0;
    cmd_data[7] = 0;
    
    mcp2515_send_message(0x100 | flash_session.node_id, cmd_data, 8);
    sleep_us(100);
    
    for (uint32_t i = 0; i < chunk_size; i += 8) {
        uint8_t data_len = chunk_size - i;
        if (data_len > 8) data_len = 8;
        
        mcp2515_send_message(0x101 | flash_session.node_id, 
                           &flash_session.data[flash_session.bytes_written + i], data_len);
        sleep_us(100);
    }
    
    uint32_t start = to_ms_since_boot(get_absolute_time());
    while (to_ms_since_boot(get_absolute_time()) - start < CAN_FLASH_TIMEOUT_MS) {
        uint32_t rx_id;
        uint8_t rx_data[8];
        uint8_t rx_len;
        
        if (mcp2515_read_message(&rx_id, rx_data, &rx_len)) {
            if ((rx_id & 0xFF) == flash_session.node_id && rx_data[0] == CAN_FLASH_CMD_ACK) {
                flash_session.bytes_written += chunk_size;
                flash_session.flash_addr += chunk_size;
                return true;
            }
        }
    }
    
    return false;
}

bool can_is_flash_complete(void) {
    if (!flash_session.in_progress) return false;
    
    if (flash_session.bytes_written >= flash_session.total_size) {
        uint8_t cmd_data[8] = {CAN_FLASH_CMD_REBOOT, 0, 0, 0, 0, 0, 0, 0};
        mcp2515_send_message(0x100 | flash_session.node_id, cmd_data, 8);
        
        flash_session.in_progress = false;
        return true;
    }
    
    return false;
}

void can_abort_flash(void) {
    flash_session.in_progress = false;
}

uint32_t can_get_progress(void) {
    if (!flash_session.in_progress || flash_session.total_size == 0) return 0;
    return (flash_session.bytes_written * 100) / flash_session.total_size;
}