#ifndef USB_PROTOCOL_H
#define USB_PROTOCOL_H

#include <stdint.h>

#define USB_CMD_START_FLASH  0x01
#define USB_CMD_SEND_DATA    0x02
#define USB_CMD_GET_STATUS   0x03
#define USB_CMD_ABORT        0x04
#define USB_CMD_SET_NODE_ID  0x05

#define USB_RESP_ACK         0x10
#define USB_RESP_NACK        0x11
#define USB_RESP_STATUS      0x12
#define USB_RESP_COMPLETE    0x13

void usb_process_commands(void);

#endif