cmake_minimum_required(VERSION 3.12)

include($ENV{PICO_SDK_PATH}/external/pico_sdk_import.cmake)

project(can_flasher C CXX ASM)

set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 17)

pico_sdk_init()

add_executable(can_flasher
    main.c
    can_flash.c
    usb_protocol.c
)

target_link_libraries(can_flasher
    pico_stdlib
    pico_stdio_usb
    hardware_spi
    hardware_gpio
    hardware_dma
)

pico_enable_stdio_usb(can_flasher 1)
pico_enable_stdio_uart(can_flasher 0)

pico_add_extra_outputs(can_flasher)