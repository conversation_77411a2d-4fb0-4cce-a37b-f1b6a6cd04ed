# == DO NOT EDIT THE FOLLOWING LINES for the Raspberry Pi Pico VS Code Extension to work ==
if(WIN32)
    set(USERHOME $ENV{USERPROFILE})
else()
    set(USERHOME $ENV{HOME})
endif()
set(sdkVersion 2.1.1)
set(toolchainVersion 14_2_Rel1)
set(picotoolVersion 2.1.1)
set(picoVscode ${USERHOME}/.pico-sdk/cmake/pico-vscode.cmake)
if (EXISTS ${picoVscode})
    include(${picoVscode})
endif()
# ====================================================================================
set(PICO_BOARD pico CACHE STRING "Board type")

cmake_minimum_required(VERSION 3.13)

# Include the Pico SDK
include(pico_sdk_import.cmake)

project(uart_debug_bridge C CXX ASM)

set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 17)

# Initialize the Pico SDK
pico_sdk_init()

# Add executable
add_executable(uart_debug_bridge
    src/main.c
)

# Pull in our pico_stdlib, which aggregates commonly used features
target_link_libraries(uart_debug_bridge 
    pico_stdlib
    pico_unique_id
    hardware_uart
    pico_multicore
)

# Enable USB output, disable UART output
pico_enable_stdio_usb(uart_debug_bridge 1)
pico_enable_stdio_uart(uart_debug_bridge 0)

# Create map/bin/hex/uf2 file etc.
pico_add_extra_outputs(uart_debug_bridge)