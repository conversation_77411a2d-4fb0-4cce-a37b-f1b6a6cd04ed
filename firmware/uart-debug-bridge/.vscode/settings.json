{"cmake.showSystemKits": false, "cmake.options.statusBarVisibility": "hidden", "cmake.options.advanced": {"build": {"statusBarVisibility": "hidden"}, "launch": {"statusBarVisibility": "hidden"}, "debug": {"statusBarVisibility": "hidden"}}, "cmake.configureOnEdit": false, "cmake.automaticReconfigure": false, "cmake.configureOnOpen": false, "cmake.generator": "Ninja", "cmake.cmakePath": "${userHome}/.pico-sdk/cmake/v3.31.5/bin/cmake", "C_Cpp.debugShortcut": false, "terminal.integrated.env.windows": {"PICO_SDK_PATH": "${env:USERPROFILE}/.pico-sdk/sdk/2.1.1", "PICO_TOOLCHAIN_PATH": "${env:USERPROFILE}/.pico-sdk/toolchain/14_2_Rel1", "Path": "${env:USERPROFILE}/.pico-sdk/toolchain/14_2_Rel1/bin;${env:USERPROFILE}/.pico-sdk/picotool/2.1.1/picotool;${env:USERPROFILE}/.pico-sdk/cmake/v3.31.5/bin;${env:USERPROFILE}/.pico-sdk/ninja/v1.12.1;${env:PATH}"}, "terminal.integrated.env.osx": {"PICO_SDK_PATH": "${env:HOME}/.pico-sdk/sdk/2.1.1", "PICO_TOOLCHAIN_PATH": "${env:HOME}/.pico-sdk/toolchain/14_2_Rel1", "PATH": "${env:HOME}/.pico-sdk/toolchain/14_2_Rel1/bin:${env:HOME}/.pico-sdk/picotool/2.1.1/picotool:${env:HOME}/.pico-sdk/cmake/v3.31.5/bin:${env:HOME}/.pico-sdk/ninja/v1.12.1:${env:PATH}"}, "terminal.integrated.env.linux": {"PICO_SDK_PATH": "${env:HOME}/.pico-sdk/sdk/2.1.1", "PICO_TOOLCHAIN_PATH": "${env:HOME}/.pico-sdk/toolchain/14_2_Rel1", "PATH": "${env:HOME}/.pico-sdk/toolchain/14_2_Rel1/bin:${env:HOME}/.pico-sdk/picotool/2.1.1/picotool:${env:HOME}/.pico-sdk/cmake/v3.31.5/bin:${env:HOME}/.pico-sdk/ninja/v1.12.1:${env:PATH}"}, "raspberry-pi-pico.cmakeAutoConfigure": true, "raspberry-pi-pico.useCmakeTools": false, "raspberry-pi-pico.cmakePath": "${HOME}/.pico-sdk/cmake/v3.31.5/bin/cmake", "raspberry-pi-pico.ninjaPath": "${HOME}/.pico-sdk/ninja/v1.12.1/ninja"}