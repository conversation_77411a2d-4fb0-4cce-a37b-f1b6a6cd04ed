{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "<PERSON><PERSON> Debug (Cortex-Debug)",
            "cwd": "${userHome}/.pico-sdk/openocd/0.12.0+dev/scripts",
            "executable": "${command:raspberry-pi-pico.launchTargetPath}",
            "request": "launch",
            "type": "cortex-debug",
            "servertype": "openocd",
            "serverpath": "${userHome}/.pico-sdk/openocd/0.12.0+dev/openocd.exe",
            "gdbPath": "${command:raspberry-pi-pico.getGDBPath}",
            "device": "${command:raspberry-pi-pico.getChipUppercase}",
            "configFiles": [
                "interface/cmsis-dap.cfg",
                "target/${command:raspberry-pi-pico.getTarget}.cfg"
            ],
            "svdFile": "${userHome}/.pico-sdk/sdk/2.1.1/src/${command:raspberry-pi-pico.getChip}/hardware_regs/${command:raspberry-pi-pico.getChipUppercase}.svd",
            "runToEntryPoint": "main",
            // Fix for no_flash binaries, where monitor reset halt doesn't do what is expected
            // Also works fine for flash binaries
            "overrideLaunchCommands": [
                "monitor reset init",
                "load \"${command:raspberry-pi-pico.launchTargetPath}\""
            ],
            "openOCDLaunchCommands": [
                "adapter speed 5000"
            ]
        },
        {
            "name": "Pico Debug (Cortex-Debug with external OpenOCD)",
            "cwd": "${workspaceRoot}",
            "executable": "${command:raspberry-pi-pico.launchTargetPath}",
            "request": "launch",
            "type": "cortex-debug",
            "servertype": "external",
            "gdbTarget": "localhost:3333",
            "gdbPath": "${command:raspberry-pi-pico.getGDBPath}",
            "device": "${command:raspberry-pi-pico.getChipUppercase}",
            "svdFile": "${userHome}/.pico-sdk/sdk/2.1.1/src/${command:raspberry-pi-pico.getChip}/hardware_regs/${command:raspberry-pi-pico.getChipUppercase}.svd",
            "runToEntryPoint": "main",
            // Fix for no_flash binaries, where monitor reset halt doesn't do what is expected
            // Also works fine for flash binaries
            "overrideLaunchCommands": [
                "monitor reset init",
                "load \"${command:raspberry-pi-pico.launchTargetPath}\""
            ]
        },
    ]
}
