/*
 * UART Debug Bridge Firmware
 * 
 * Bridges USB CDC to UART for debugging external devices
 * 
 * Default pinout:
 * - UART TX: GPIO 0 (Pin 1)
 * - UART RX: GPIO 1 (Pin 2)
 * - GND: Pin 3, 8, 13, 18, 23, 28, 33, 38
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "pico/stdlib.h"
#include "pico/multicore.h"
#include "hardware/uart.h"
#include "hardware/gpio.h"
#include "hardware/irq.h"
#include "pico/unique_id.h"

// UART configuration
#define UART_INST uart0
#define UART_TX_PIN 0
#define UART_RX_PIN 1
#define UART_BAUD_RATE 115200

// Buffer sizes
#define USB_BUFFER_SIZE 1024
#define UART_BUFFER_SIZE 1024

// Ring buffer structure
typedef struct {
    uint8_t buffer[USB_BUFFER_SIZE];
    volatile uint16_t head;
    volatile uint16_t tail;
    volatile uint16_t count;
} ring_buffer_t;

// Global buffers for bidirectional communication
static ring_buffer_t usb_to_uart_buffer;
static ring_buffer_t uart_to_usb_buffer;

// Function to initialize a ring buffer
static void ring_buffer_init(ring_buffer_t *rb) {
    rb->head = 0;
    rb->tail = 0;
    rb->count = 0;
}

// Function to add data to ring buffer
static bool ring_buffer_put(ring_buffer_t *rb, uint8_t data) {
    if (rb->count >= USB_BUFFER_SIZE) {
        return false; // Buffer full
    }
    
    rb->buffer[rb->head] = data;
    rb->head = (rb->head + 1) % USB_BUFFER_SIZE;
    rb->count++;
    return true;
}

// Function to get data from ring buffer
static bool ring_buffer_get(ring_buffer_t *rb, uint8_t *data) {
    if (rb->count == 0) {
        return false; // Buffer empty
    }
    
    *data = rb->buffer[rb->tail];
    rb->tail = (rb->tail + 1) % USB_BUFFER_SIZE;
    rb->count--;
    return true;
}

// UART interrupt handler
void on_uart_rx() {
    while (uart_is_readable(UART_INST)) {
        uint8_t ch = uart_getc(UART_INST);
        ring_buffer_put(&uart_to_usb_buffer, ch);
    }
}

// Core 1 main function - handles UART to USB direction
void core1_main() {
    while (true) {
        uint8_t data;
        if (ring_buffer_get(&uart_to_usb_buffer, &data)) {
            putchar_raw(data); // Send to USB
        }
        tight_loop_contents();
    }
}

// Function to process USB commands for UART configuration
void process_usb_command(const char* cmd) {
    // Simple command parsing for baud rate changes
    if (strncmp(cmd, "BAUD:", 5) == 0) {
        uint32_t new_baud = atoi(cmd + 5);
        if (new_baud >= 300 && new_baud <= 3000000) {
            uart_set_baudrate(UART_INST, new_baud);
            printf("Baud rate set to %lu\r\n", new_baud);
        } else {
            printf("Invalid baud rate\r\n");
        }
    } else if (strcmp(cmd, "STATUS") == 0) {
        printf("UART Debug Bridge v1.0\r\n");
        printf("UART: TX=GPIO%d RX=GPIO%d\r\n", UART_TX_PIN, UART_RX_PIN);
        
        // Print unique ID
        pico_unique_board_id_t board_id;
        pico_get_unique_board_id(&board_id);
        printf("Board ID: ");
        for (int i = 0; i < PICO_UNIQUE_BOARD_ID_SIZE_BYTES; i++) {
            printf("%02X", board_id.id[i]);
        }
        printf("\r\n");
    }
}

int main() {
    // Initialize stdio (USB)
    stdio_init_all();
    
    // Wait a bit for USB to enumerate
    sleep_ms(1000);
    
    // Initialize ring buffers
    ring_buffer_init(&usb_to_uart_buffer);
    ring_buffer_init(&uart_to_usb_buffer);
    
    // Initialize UART
    uart_init(UART_INST, UART_BAUD_RATE);
    gpio_set_function(UART_TX_PIN, GPIO_FUNC_UART);
    gpio_set_function(UART_RX_PIN, GPIO_FUNC_UART);
    
    // Set UART format (8 data bits, 1 stop bit, no parity)
    uart_set_format(UART_INST, 8, 1, UART_PARITY_NONE);
    
    // Enable UART RX interrupt
    irq_set_exclusive_handler(UART0_IRQ, on_uart_rx);
    irq_set_enabled(UART0_IRQ, true);
    uart_set_irq_enables(UART_INST, true, false);
    
    // Start core 1 for UART to USB processing
    multicore_launch_core1(core1_main);
    
    printf("UART Debug Bridge Ready\r\n");
    printf("Commands: BAUD:<rate>, STATUS\r\n");
    printf("UART: TX=GPIO%d RX=GPIO%d Baud=%d\r\n", 
           UART_TX_PIN, UART_RX_PIN, UART_BAUD_RATE);
    
    char command_buffer[32];
    int cmd_pos = 0;
    bool in_command = false;
    
    while (true) {
        // Process USB to UART direction
        int ch = getchar_timeout_us(0);
        if (ch != PICO_ERROR_TIMEOUT) {
            uint8_t data = (uint8_t)ch;
            
            // Check for command mode (lines starting with ++)
            if (data == '+' && cmd_pos == 0) {
                in_command = true;
                cmd_pos = 0;
                continue;
            }
            
            if (in_command) {
                if (data == '\r' || data == '\n') {
                    command_buffer[cmd_pos] = '\0';
                    if (cmd_pos > 0) {
                        process_usb_command(command_buffer);
                    }
                    in_command = false;
                    cmd_pos = 0;
                } else if (cmd_pos < sizeof(command_buffer) - 1) {
                    command_buffer[cmd_pos++] = data;
                }
            } else {
                // Normal data - send to UART
                uart_putc_raw(UART_INST, data);
            }
        }
        
        tight_loop_contents();
    }
    
    return 0;
}