# UART Debug Bridge

A Raspberry Pi Pico firmware that creates a USB-to-UART bridge for debugging external devices.

## Features

- **USB CDC Interface**: Connect to your computer via USB for easy access
- **Bidirectional Communication**: Full duplex UART communication
- **Configurable Baud Rate**: Change UART settings via commands
- **Multi-core Processing**: Uses both cores for optimal performance
- **Ring Buffering**: Prevents data loss during high-speed communication

## Hardware Setup

### Default Pin Configuration
- **UART TX**: GPIO 0 (Pin 1)
- **UART RX**: GPIO 1 (Pin 2)  
- **GND**: Connect to target device ground

### Wiring Example
```
Pico          Target Device
GPIO 0 (TX) → RX
GPIO 1 (RX) ← TX
GND         → GND
```

## Usage

1. **Flash the firmware** to your Pico using the `.uf2` file
2. **Connect the Pico** to your computer via USB
3. **Connect UART pins** to your target device
4. **Open a serial terminal** (115200 baud default)

### Serial Terminal Setup
- **Baud Rate**: 115200 (or as configured)
- **Data Bits**: 8
- **Stop Bits**: 1
- **Parity**: None
- **Flow Control**: None

## Commands

Send commands over USB to configure the bridge:

- `+BAUD:<rate>` - Change UART baud rate (e.g., `+BAUD:9600`)
- `+STATUS` - Show current configuration and board info

### Example Commands
```
+BAUD:9600     # Set UART to 9600 baud
+BAUD:115200   # Set UART to 115200 baud
+STATUS        # Show current status
```

## Supported Baud Rates

Common baud rates supported:
- 300, 600, 1200, 2400, 4800
- 9600, 19200, 38400, 57600, 115200
- 230400, 460800, 921600
- Up to 3,000,000 baud

## Building from Source

```bash
mkdir build && cd build
cmake ..
cmake --build .
```

The resulting `uart_debug_bridge.uf2` file can be copied to the Pico in BOOTSEL mode.

## Technical Details

- **Architecture**: Dual-core processing
  - Core 0: USB to UART direction + command processing
  - Core 1: UART to USB direction
- **Buffering**: 1KB ring buffers for each direction
- **Interrupts**: UART RX interrupt for immediate data capture
- **USB**: CDC (Communication Device Class) for universal compatibility

## Use Cases

- **Debugging embedded devices** via their UART interfaces
- **Serial port expansion** for computers without physical serial ports
- **Protocol analysis** and logging of UART communications
- **Firmware development** and testing
- **IoT device debugging** and configuration

## Troubleshooting

### No USB Device Detected
- Ensure the Pico is properly connected
- Try a different USB cable
- Check that the firmware was flashed correctly

### No UART Data
- Verify wiring connections (TX/RX may be swapped)
- Check ground connection
- Confirm baud rate settings match target device
- Use `+STATUS` command to verify configuration

### Data Loss or Corruption
- Try reducing baud rate
- Check for electrical interference
- Ensure stable power supply
- Verify target device timing specifications