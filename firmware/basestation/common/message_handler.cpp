#include "message_handler.h"
#include "active_configuration.h"
#include "basestation-config.pb.h"
#include "events.h"
#include <pb_decode.h>
#include <pb_encode.h>
#include <stdio.h>
#include <string>

#define DEBUG_MESSAGE_HANDLER 1
#define CLICK_TIME_THRESHOLD 300

// Global callback for sending CAN messages and RF messages
static void (*g_send_can_message_callback)(const uint8_t *data, uint32_t len) = nullptr;
static void (*g_send_rf_message_callback)(const uint8_t *data, uint32_t len) = nullptr;

void register_send_can_message_callback(void (*callback)(const uint8_t *data, uint32_t len)) {
    g_send_can_message_callback = callback;
#ifdef DEBUG_MESSAGE_HANDLER
    printf("CAN message callback registered\n");
#endif
}

void register_send_rf_message_callback(void (*callback)(const uint8_t *data, uint32_t len)) {
    g_send_rf_message_callback = callback;
#ifdef DEBUG_MESSAGE_HANDLER
    printf("RF message callback registered\n");
#endif
}

// Helper function to find CanboConfig by node_id
CanboConfig *find_canbo_config_by_node_id(uint8_t node_id) {
    for (uint8_t i = 0; i < g_active_configuration.config.canbo_configs_count; i++) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Canbo config %d: Node ID %d\n", i, g_active_configuration.config.canbo_configs[i].node_id);
#endif
        if (g_active_configuration.config.canbo_configs[i].node_id == node_id) {
            return &g_active_configuration.config.canbo_configs[i];
        }
    }
    return nullptr;
}

RFReedSensorConfig *find_rf_reed_sensor_config_by_node_id(uint8_t node_id) {
    for (uint8_t i = 0; i < g_active_configuration.config.rf_reed_configs_count; i++) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  RF Reed sensor config %d: Node ID %d\n", i, g_active_configuration.config.rf_reed_configs[i].node_id);
#endif
        if (g_active_configuration.config.rf_reed_configs[i].node_id == node_id) {
            return &g_active_configuration.config.rf_reed_configs[i];
        }
    }
    return nullptr;
}

RFReedState *find_rf_reed_state_by_node_id(uint8_t node_id) {
    for (uint8_t i = 0; i < g_active_configuration.state.reeds_count; i++) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  RF Reed state %d: Node ID %d\n", i, g_active_configuration.state.reeds[i].node_id);
#endif
        if (g_active_configuration.state.reeds[i].node_id == node_id) {
            return &g_active_configuration.state.reeds[i];
        }
    }
    return nullptr;
}

// Helper function to find momentary input by connector_id
CanboConfig_ThreePinInput *find_momentary_input_by_connector_id(CanboConfig *canbo_config, uint8_t connector_id) {
    // TODO: should the search be the same in this case?
    for (uint8_t i = 0; i < canbo_config->three_pin_inputs_count; i++) {
        if (canbo_config->three_pin_inputs[i].connector_id == connector_id) {
            return &canbo_config->three_pin_inputs[i];
        }
    }
    return nullptr;
}

// Helper function to find toggle input by connector_id
CanboConfig_ThreePinInput *find_toggle_input_by_connector_id(CanboConfig *canbo_config, uint8_t connector_id) {
    for (uint8_t i = 0; i < canbo_config->three_pin_inputs_count; i++) {
        if (canbo_config->three_pin_inputs[i].connector_id == connector_id) {
            return &canbo_config->three_pin_inputs[i];
        }
    }
    return nullptr;
}

// Helper function to find or create button state
ButtonState *find_or_create_button_state(uint8_t node_id) {
    ButtonState *button_state = nullptr;

    // Find existing button state
    for (uint8_t i = 0; i < g_active_configuration.state.buttons_count; i++) {
        if (g_active_configuration.state.buttons[i].node_id == node_id) {
            button_state = &g_active_configuration.state.buttons[i];
            break;
        }
    }

    // Create new button state if not found
    if (!button_state && g_active_configuration.state.buttons_count < 10) {
        button_state = &g_active_configuration.state.buttons[g_active_configuration.state.buttons_count++];
        button_state->node_id = node_id;
        button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
        button_state->last_modified_time = get_time_in_ms();
    }

    return button_state;
}

// Helper function to find light state by light_id
LightState *find_light_state_by_id(uint8_t light_id) {
    for (uint8_t j = 0; j < g_active_configuration.state.lights_count; j++) {
        if (g_active_configuration.state.lights[j].id == light_id) {
            return &g_active_configuration.state.lights[j];
        }
    }
    return nullptr;
}

// Helper function to find or create provisioning state by node_id
ProvisioningState *find_or_create_provisioning_state_by_node_id(uint8_t node_id) {
    ProvisioningState *provisioning_state = nullptr;

    // Find existing provisioning state
    for (uint8_t i = 0; i < g_active_configuration.state.provisioned_devices_count; i++) {
        if (g_active_configuration.state.provisioned_devices[i].node_id == node_id) {
            provisioning_state = &g_active_configuration.state.provisioned_devices[i];
            break;
        }
    }

    // Create new provisioning state if not found
    if (!provisioning_state && g_active_configuration.state.provisioned_devices_count < 10) {
        provisioning_state = &g_active_configuration.state.provisioned_devices[g_active_configuration.state.provisioned_devices_count++];
        provisioning_state->node_id = node_id;
        provisioning_state->is_provisioned = false;
        provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
    }

    return provisioning_state;
}

LightConfig *find_light_config_by_id(uint8_t light_id) {
    for (uint8_t i = 0; i < g_active_configuration.config.lights_count; i++) {
        if (g_active_configuration.config.lights[i].id == light_id) {
            return &g_active_configuration.config.lights[i];
        }
    }
    return nullptr;
}

void apply_light_state_change(LightState *light_state, float target_value, uint32_t dim_speed_msec, uint32_t activate_delay_msec) {
    uint32_t now = get_time_in_ms();
    light_state->target_value = target_value;
    light_state->dim_speed_msec = dim_speed_msec;
    light_state->last_modified_time = now + activate_delay_msec;
    light_state->active_after_time = now + activate_delay_msec + 1; // + 1 to always trigger a state change

    // printf("apply_light_state_change: light_state->id: %d, light_state->target_value: %f, light_state->dim_speed_msec: %d, activate_delay_msec: %d\n", light_state->id, light_state->target_value, light_state->dim_speed_msec, activate_delay_msec);
}

// Helper function to find zero to ten volt config by node id
LightConfig_FixtureConfig_ZeroToTenVoltConfig *find_canbo_zero_to_ten_volt_config_by_node(uint8_t node_id, float *light_min_brightness, float *light_max_brightness) {
    for (uint8_t i = 0; i < g_active_configuration.config.lights_count; i++) {
        for (uint8_t j = 0; j < g_active_configuration.config.lights[i].fixtures_count; j++) {
            if (g_active_configuration.config.lights[i].fixtures[j].type == LightConfig_FixtureConfig_FixtureType_ZERO_TO_TEN_VOLT) {
                *light_min_brightness = g_active_configuration.config.lights[i].fixtures[j].min_brightness;
                *light_max_brightness = g_active_configuration.config.lights[i].fixtures[j].max_brightness;
                return &g_active_configuration.config.lights[i].fixtures[j].config.zero_to_ten_volt;
            }
        }
    }

    return nullptr;
}

// Helper function to apply light actions
void apply_light_actions(CanboConfig_ThreePinInput_Action *actions, uint8_t action_count, bool stop_at_current = false) {
    for (uint8_t i = 0; i < action_count; i++) {
        CanboConfig_ThreePinInput_Action *action = &actions[i];
#ifdef DEBUG_MESSAGE_HANDLER
        printf("    Action %d: Light ID %d, Target Brightness %.2f, Dim Speed %d ms\n",
               i, action->light_id, action->target_brightness, action->dim_speed_msec);
#endif

        LightState *light_state = find_light_state_by_id(action->light_id);
        // TODO: Digest the action into zero to ten volt // DMX // RF // ON/OFF
        if (light_state) {
            if (stop_at_current) {
                // Stop dimming at current brightness
                apply_light_state_change(light_state, light_state->brightness, action->dim_speed_msec, 0);
            } else {
                // Set to target brightness
                apply_light_state_change(light_state, action->target_brightness, action->dim_speed_msec, 0);
            }

#ifdef DEBUG_MESSAGE_HANDLER
            printf("    [Dimming] Current light state: Brightness %.2f, Target Value %.2f, Dim Speed %d ms\n",
                   light_state->brightness, light_state->target_value, light_state->dim_speed_msec);
#endif
        }
    }
}

// Helper function to convert MomentaryButtonCommand_State to ButtonState_State
ButtonState_State convert_momentary_state_to_button_state(MomentaryButtonCommand_State momentary_state) {
    switch (momentary_state) {
    case MomentaryButtonCommand_State_Pressed:
        return ButtonState_State_BUTTON_STATE_UP_PRESSED;
    case MomentaryButtonCommand_State_Released:
        return ButtonState_State_BUTTON_STATE_RELEASED;
    default:
        return ButtonState_State_BUTTON_STATE_RELEASED;
    }
}

// Set target_brightness to on_brightness if it's off_brightness, and vice versa
void alternate_target_brightness(CanboConfig_ThreePinInput_Action *actions, uint8_t action_count) {
    for (uint8_t i = 0; i < action_count; i++) {
        CanboConfig_ThreePinInput_Action *action = &actions[i];
        if (action->target_brightness == action->on_brightness) {
            action->target_brightness = action->off_brightness;
        } else {
            action->target_brightness = action->on_brightness;
        }
    }
}

// Helper function to handle momentary button press
void handle_momentary_button_press(ButtonState *button_state, CanboConfig_ThreePinInput *three_pin_input,
                                   MomentaryButtonCommand_State button_state_enum, bool is_up) {
    button_state->current_state = convert_momentary_state_to_button_state(button_state_enum);
    button_state->last_modified_time = get_time_in_ms();

    // Start dimming action
    apply_light_actions(three_pin_input->config.momentary.up_hold, three_pin_input->config.momentary.up_hold_count);
}

// Helper function to handle momentary button release
void handle_momentary_button_release(ButtonState *button_state, CanboConfig_ThreePinInput *three_pin_input) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Released button pressed\n");
#endif

    uint32_t now = get_time_in_ms();
    uint32_t time_since_last_action = now - button_state->last_modified_time;

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Time since last button action: %d ms\n", time_since_last_action);
#endif

    // Store the current button state before we change it
    ButtonState_State previous_state = button_state->current_state;

    // If the button was pressed for less than threshold, it's a click
    if (time_since_last_action < CLICK_TIME_THRESHOLD) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Released button pressed [click]\n");
#endif

        button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
        button_state->last_modified_time = get_time_in_ms();

        if (previous_state == ButtonState_State_BUTTON_STATE_UP_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  up click\n");
#endif
            apply_light_actions(three_pin_input->config.momentary.up_click, three_pin_input->config.momentary.up_click_count);
            alternate_target_brightness(three_pin_input->config.momentary.up_click, three_pin_input->config.momentary.up_click_count);
        }
    } else {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Released button pressed [dimming]\n");
#endif
        // Stop the dimming action
        button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
        button_state->last_modified_time = get_time_in_ms();

        if (previous_state == ButtonState_State_BUTTON_STATE_UP_PRESSED) {
            apply_light_actions(three_pin_input->config.momentary.up_hold, three_pin_input->config.momentary.up_hold_count, true);
            alternate_target_brightness(three_pin_input->config.momentary.up_hold, three_pin_input->config.momentary.up_hold_count);
        }
    }
}

// Helper function to convert ToggleButtonCommand_State to ButtonState_State
ButtonState_State convert_toggle_state_to_button_state(ToggleButtonCommand_State toggle_state) {
    switch (toggle_state) {
    case ToggleButtonCommand_State_Up:
        return ButtonState_State_BUTTON_STATE_UP_PRESSED;
    case ToggleButtonCommand_State_Down:
        return ButtonState_State_BUTTON_STATE_DOWN_PRESSED;
    case ToggleButtonCommand_State_Released:
        return ButtonState_State_BUTTON_STATE_RELEASED;
    default:
        return ButtonState_State_BUTTON_STATE_RELEASED;
    }
}

// Helper function to handle toggle button press (up or down)
void handle_toggle_button_press(ButtonState *button_state, CanboConfig_ThreePinInput *three_pin_input,
                                ToggleButtonCommand_State button_state_enum, bool is_up) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("  %s button pressed\n", is_up ? "Up" : "Down");
#endif

    button_state->current_state = convert_toggle_state_to_button_state(button_state_enum);
    button_state->last_modified_time = get_time_in_ms();

    // Start dimming action
    if (is_up) {
        apply_light_actions(three_pin_input->config.toggle.up_hold, three_pin_input->config.toggle.up_hold_count);
    } else {
        apply_light_actions(three_pin_input->config.toggle.down_hold, three_pin_input->config.toggle.down_hold_count);
    }
}

// Helper function to handle toggle button release
void handle_toggle_button_release(ButtonState *button_state, CanboConfig_ThreePinInput *three_pin_input) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Released button pressed\n");
#endif

    uint32_t now = get_time_in_ms();
    uint32_t time_since_last_action = now - button_state->last_modified_time;

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Time since last button action: %d ms\n", time_since_last_action);
#endif

    // Store the current button state before we change it
    ButtonState_State previous_state = button_state->current_state;

    // If the button was pressed for less than threshold, it's a click
    if (time_since_last_action < CLICK_TIME_THRESHOLD) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Released button pressed [click]\n");
#endif

        button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
        button_state->last_modified_time = get_time_in_ms();

        if (previous_state == ButtonState_State_BUTTON_STATE_UP_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  up click\n");
#endif
            apply_light_actions(three_pin_input->config.toggle.up_click, three_pin_input->config.toggle.up_click_count);
        } else if (previous_state == ButtonState_State_BUTTON_STATE_DOWN_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  Down button released -> down click\n");
#endif
            apply_light_actions(three_pin_input->config.toggle.down_click, three_pin_input->config.toggle.down_click_count);
        }
    } else {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Released button pressed [dimming]\n");
#endif
        // Stop the dimming action
        button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
        button_state->last_modified_time = get_time_in_ms();

        if (previous_state == ButtonState_State_BUTTON_STATE_UP_PRESSED) {
            apply_light_actions(three_pin_input->config.toggle.up_hold, three_pin_input->config.toggle.up_hold_count, true);
        } else if (previous_state == ButtonState_State_BUTTON_STATE_DOWN_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  Down button released -> down click\n");
#endif
            apply_light_actions(three_pin_input->config.toggle.down_hold, three_pin_input->config.toggle.down_hold_count, true);
        }
    }
}

// Helper function to handle ping command
void handle_ping_command(const uint8_t *data, uint32_t len) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("Received ping command\n");
#endif

    PingCommand command = PingCommand_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, PingCommand_fields, &command);

    if (!status) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("Failed to decode ping command: %s\n", PB_GET_ERROR(&stream));
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  QR Code: %s\n", command.qr_code);
    printf("  Is Provisioned: %s\n", command.is_provisioned ? "Yes" : "No");
#endif

    if (command.is_provisioned && strncmp(command.version, g_active_configuration.config.version, sizeof(command.version)) == 0) {
        printf("  Provisioned and version matches\n");
        return;
    }

    printf("  Version: %s\n", command.version);
    printf("  Active Version: %s\n", g_active_configuration.config.version);

    // find the node id from the qr code
    uint8_t node_id = 0;
    for (size_t i = 0; i < g_active_configuration.config.node_qr_mappings_count; i++) {
        const BasestationConfig_NodeQRMapping &mapping = g_active_configuration.config.node_qr_mappings[i];
        if (strncmp(mapping.qr_code, command.qr_code, sizeof(mapping.qr_code)) == 0) {
            node_id = mapping.node_id;
            break;
        }
    }
    if (node_id == 0) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No node ID found for QR code: %s\n", command.qr_code);
#endif
        return;
    }

    // find the provisioning state for this node id
    ProvisioningState *provisioning_state = find_or_create_provisioning_state_by_node_id(node_id);
    provisioning_state->last_seen_time = get_time_in_ms();

    // find canbo config for this node id
    CanboConfig *canbo_config = find_canbo_config_by_node_id(node_id);
    if (!canbo_config) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No Canbo config found for node ID %d\n", node_id);
#endif
        provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NO_CANBO_CONFIG;
        provisioning_state->is_provisioned = false;
        return;
    }

    // create a canbo provisioning command
    CanboProvisioningCommand provisioning_command = CanboProvisioningCommand_init_zero;
    provisioning_command.node_id = node_id;
    strncpy(provisioning_command.version, g_active_configuration.config.version, sizeof(provisioning_command.version));
    provisioning_command.version[sizeof(provisioning_command.version) - 1] = '\0';
    strncpy(provisioning_command.qr_code, command.qr_code, sizeof(provisioning_command.qr_code));
    provisioning_command.qr_code[sizeof(provisioning_command.qr_code) - 1] = '\0';

    provisioning_command.three_pin_inputs_count = 0;
    // Add toggle button inputs
    for (uint8_t i = 0; i < canbo_config->three_pin_inputs_count; i++) {
        provisioning_command.three_pin_inputs[provisioning_command.three_pin_inputs_count].connector_id = canbo_config->three_pin_inputs[i].connector_id;
        if (canbo_config->three_pin_inputs[i].type == CanboConfig_ThreePinInput_ConnectorType_TOGGLE) {
            provisioning_command.three_pin_inputs[provisioning_command.three_pin_inputs_count].connector_type = CanboProvisioningCommand_ThreePinInput_ConnectorType_TOGGLE;
        } else if (canbo_config->three_pin_inputs[i].type == CanboConfig_ThreePinInput_ConnectorType_MOMENTARY) {
            provisioning_command.three_pin_inputs[provisioning_command.three_pin_inputs_count].connector_type = CanboProvisioningCommand_ThreePinInput_ConnectorType_MOMENTARY;
        }
        provisioning_command.three_pin_inputs_count++;
    }

    provisioning_command.two_pin_inputs_count = 0;
    // Add PIR inputs
    for (uint8_t i = 0; i < canbo_config->two_pin_inputs_count; i++) {
        provisioning_command.two_pin_inputs[provisioning_command.two_pin_inputs_count].connector_id = canbo_config->two_pin_inputs[i].connector_id;
        if (canbo_config->two_pin_inputs[i].type == CanboConfig_TwoPinInput_ConnectorType_PIR) {
            provisioning_command.two_pin_inputs[provisioning_command.two_pin_inputs_count].connector_type = CanboProvisioningCommand_TwoPinInput_ConnectorType_PIR;
        } else if (canbo_config->two_pin_inputs[i].type == CanboConfig_TwoPinInput_ConnectorType_MOMENTARY) {
            provisioning_command.two_pin_inputs[provisioning_command.two_pin_inputs_count].connector_type = CanboProvisioningCommand_TwoPinInput_ConnectorType_MOMENTARY;
        } else if (canbo_config->two_pin_inputs[i].type == CanboConfig_TwoPinInput_ConnectorType_DOOR_SENSOR) {
            provisioning_command.two_pin_inputs[provisioning_command.two_pin_inputs_count].connector_type = CanboProvisioningCommand_TwoPinInput_ConnectorType_DOOR_SENSOR;
        }
        provisioning_command.two_pin_inputs_count++;
    }

    // Add 0-10V dimming connection
    LightConfig_FixtureConfig_ZeroToTenVoltConfig *zero_to_ten_volt_config =
        find_canbo_zero_to_ten_volt_config_by_node(node_id, &provisioning_command.zero_to_ten_volt_config.min_brightness, &provisioning_command.zero_to_ten_volt_config.max_brightness);

    if (!zero_to_ten_volt_config) {
        provisioning_command.has_zero_to_ten_volt_config = false;
    } else {
        provisioning_command.has_zero_to_ten_volt_config = true;

        if (zero_to_ten_volt_config->type == LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SOURCING) {
            provisioning_command.zero_to_ten_volt_config.type = CanboProvisioningCommand_ZeroToTenVoltConfig_Type_SOURCING;
        } else if (zero_to_ten_volt_config->type == LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SINKING) {
            provisioning_command.zero_to_ten_volt_config.type = CanboProvisioningCommand_ZeroToTenVoltConfig_Type_SINKING;
        }
        provisioning_command.zero_to_ten_volt_config.use_relay = zero_to_ten_volt_config->use_relay;
        provisioning_command.zero_to_ten_volt_config.relay_connector_id = zero_to_ten_volt_config->out_connector_id;

        provisioning_command.outputs_count++;
        provisioning_command.outputs[zero_to_ten_volt_config->out_connector_id].connector_id = zero_to_ten_volt_config->out_connector_id;
    }
    // Setup kleverness connector
    provisioning_command.has_kleverness_connector = false;

    // Send the provisioning command
    int ret = send_provisioning_command(node_id, &provisioning_command);

    if (ret == 0) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Provisioning command sent successfully to node %d\n", node_id);
#endif
        provisioning_state->is_provisioned = true;
        provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
    } else {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Failed to send provisioning command to node %d\n", node_id);
#endif
        provisioning_state->is_provisioned = false;
        provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_COULD_NOT_SEND_PROVISIONING_COMMAND;
    }
}

// Helper function to handle momentary button command
void handle_momentary_button_command(const uint8_t *data, uint32_t len, uint8_t node_id) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("Received momentary button command\n");
#endif

    MomentaryButtonCommand command = MomentaryButtonCommand_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, MomentaryButtonCommand_fields, &command);

    if (!status) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("Failed to decode momentary button command: %s\n", PB_GET_ERROR(&stream));
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Connector ID: %d\n", command.connector_id);
    printf("  State: %d\n", command.state);
#endif

    // Find the Canbo device configuration for this node ID
    CanboConfig *canbo_config = find_canbo_config_by_node_id(node_id);

    if (!canbo_config) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No Canbo config found for node ID %d (count: %d)\n", node_id, g_active_configuration.config.canbo_configs_count);
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Found Canbo config for node ID %d\n", node_id);
#endif

    // Find the momentary input configuration for this connector ID
    CanboConfig_ThreePinInput *three_pin_input = find_momentary_input_by_connector_id(canbo_config, command.connector_id);

    if (!three_pin_input) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No momentary input found for connector ID %d\n", command.connector_id);
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Found momentary input for connector ID %d\n", command.connector_id);
#endif

    // Find or create button state for this node and connector
    ButtonState *button_state = find_or_create_button_state(node_id);

    if (command.state == MomentaryButtonCommand_State_Pressed) {
        handle_momentary_button_press(button_state, three_pin_input, command.state, true);
    } else if (command.state == MomentaryButtonCommand_State_Released) {
        handle_momentary_button_release(button_state, three_pin_input);
    }
}

// Helper function to handle toggle button command
void handle_toggle_button_command(const uint8_t *data, uint32_t len, uint8_t node_id) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("Received toggle button command\n");
#endif

    ToggleButtonCommand command = ToggleButtonCommand_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, ToggleButtonCommand_fields, &command);

    if (!status) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("Failed to decode toggle button command: %s\n", PB_GET_ERROR(&stream));
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Connector ID: %d\n", command.connector_id);
    printf("  State: %d\n", command.state);
#endif

    // Find the Canbo device configuration for this node ID
    CanboConfig *canbo_config = find_canbo_config_by_node_id(node_id);

    if (!canbo_config) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No Canbo config found for node ID %d (count: %d)\n", node_id, g_active_configuration.config.canbo_configs_count);
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Found Canbo config for node ID %d\n", node_id);
#endif

    // Find the toggle input configuration for this connector ID
    CanboConfig_ThreePinInput *three_pin_input = find_toggle_input_by_connector_id(canbo_config, command.connector_id);

    if (!three_pin_input) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No toggle input found for connector ID %d\n", command.connector_id);
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Found toggle input for connector ID %d\n", command.connector_id);
#endif

    // Find or create button state for this node and connector
    ButtonState *button_state = find_or_create_button_state(node_id);

    if (command.state == ToggleButtonCommand_State_Up) {
        handle_toggle_button_press(button_state, three_pin_input, command.state, true);
    } else if (command.state == ToggleButtonCommand_State_Down) {
        handle_toggle_button_press(button_state, three_pin_input, command.state, false);
    } else if (command.state == ToggleButtonCommand_State_Released) {
        handle_toggle_button_release(button_state, three_pin_input);
    }
}

// Helper function to handle door sensor command
void handle_rf_door_sensor_command(const uint8_t *data, uint32_t len, uint8_t node_id) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("Received door sensor command\n");
#endif

    RFDoorSensorCommand command = RFDoorSensorCommand_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, RFDoorSensorCommand_fields, &command);

    if (!status) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("Failed to decode door sensor command: %s\n", PB_GET_ERROR(&stream));
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Node ID: %d\n", command.node_id);
    printf("  State: %s\n", command.state == RFDoorSensorCommand_State_Closed ? "Closed" : "Opened");
#endif

    // Find the RF Reed state for this node ID
    RFReedState *rf_reed_state = find_rf_reed_state_by_node_id(node_id);

    if (!rf_reed_state) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No RF Reed state found for node ID %d\n", node_id);
#endif
        return;
    }

    RFReedSensorConfig *rf_reed_sensor_config = find_rf_reed_sensor_config_by_node_id(node_id);
    if (!rf_reed_sensor_config) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No RF Reed sensor config found for node ID %d\n", node_id);
#endif
        return;
    }

    if (command.state == RFDoorSensorCommand_State_Closed) {
        for (uint8_t i = 0; i < rf_reed_sensor_config->door_close_count; i++) {
            RFReedSensorConfig_Action *action = &rf_reed_sensor_config->door_close[i];
            LightState *light_state = find_light_state_by_id(action->light_id);
            if (light_state) {
                apply_light_state_change(light_state, action->target_brightness, action->dim_speed_msec, action->activate_delay_msec);
            }
        }
    } else if (command.state == RFDoorSensorCommand_State_Opened) {
        for (uint8_t i = 0; i < rf_reed_sensor_config->door_open_count; i++) {
            RFReedSensorConfig_Action *action = &rf_reed_sensor_config->door_open[i];
            LightState *light_state = find_light_state_by_id(action->light_id);
            if (light_state) {
                apply_light_state_change(light_state, action->target_brightness, action->dim_speed_msec, action->activate_delay_msec);
            }
        }
    }
}

// Function to send provisioning command to a CAN device
int send_provisioning_command(uint8_t node_id, const CanboProvisioningCommand *command) {
    // Encode the provisioning command
    uint8_t data[256] = {0};
    data[0] = static_cast<uint8_t>(MessageType::MESSAGE_CANBO_PROVISIONING);

    pb_ostream_t stream = pb_ostream_from_buffer(data + 1, sizeof(data) - 1);
    bool status = pb_encode(&stream, CanboProvisioningCommand_fields, command);

    if (!status) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("Failed to encode provisioning command for node %d\n", node_id);
#endif
        return -1;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("Sending provisioning command to node %d (size: %zu bytes)\n", node_id, stream.bytes_written + 1);
#endif

    // Send the command via the registered callback
    if (g_send_can_message_callback != nullptr) {
        g_send_can_message_callback(data, stream.bytes_written + 1);
        return 0; // Success
    } else {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("No CAN message callback registered, cannot send provisioning command\n");
#endif
        return -1; // No callback registered
    }
}

uint32_t get_time_in_ms() {
#ifdef __EMSCRIPTEN__
    return emscripten_get_now();
#else
    return time_us_32() / 1000;
#endif
}

void handle_command(const uint8_t *data, uint32_t len, uint8_t node_id) {
    MessageType command_type = static_cast<MessageType>(data[0]);

    switch (command_type) {
    case MessageType::MESSAGE_PING:
        handle_ping_command(data, len);
        break;

    case MessageType::MESSAGE_TOGGLE_BUTTON:
        handle_toggle_button_command(data, len, node_id);
        break;

    case MessageType::MESSAGE_MOMENTARY_BUTTON:
        handle_momentary_button_command(data, len, node_id);
        break;

    case MessageType::MESSAGE_RF_DOOR_SENSOR:
        handle_rf_door_sensor_command(data, len, node_id);
        break;

    default:
#ifdef DEBUG_MESSAGE_HANDLER
        printf("Unknown command type: %d\n", command_type);
#endif
        break;
    }
}

#ifdef __EMSCRIPTEN__
void handle_command_emscripten(std::string data, uint8_t node_id) {
    const char *data_chars = data.c_str();
    handle_command(reinterpret_cast<const uint8_t *>(data_chars), data.size(), node_id);
}

using namespace emscripten;
EMSCRIPTEN_BINDINGS(message_handler) {
    function("handleCommand", &handle_command_emscripten);
}
#endif
