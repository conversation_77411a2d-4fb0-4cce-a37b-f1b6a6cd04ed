#include "active_configuration.h"
#include "basestation-config.pb.h"
#include <pb_decode.h>
#include <pb_encode.h>
#include <string>
#include <string.h>
#include <algorithm>

#ifdef __EMSCRIPTEN__
#include <emscripten/bind.h>
#include <emscripten/emscripten.h>
#include <emscripten/val.h>
#endif

#include "message_handler.h"

ActiveConfiguration g_active_configuration = ActiveConfiguration_init_zero;

ActiveConfiguration *get_active_configuration() {
    return &g_active_configuration;
}

bool set_active_configuration_from_protobuf(const pb_byte_t *data, size_t size) {
    ActiveConfiguration config = ActiveConfiguration_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(data, size);

    bool status = pb_decode(&stream, ActiveConfiguration_fields, &config);

    if (!status) {
        printf("Failed to decode active configuration: %s\n", PB_GET_ERROR(&stream));
        return false;
    }

    g_active_configuration = config;
    return true;
}

bool set_configuration_from_update_message_protobuf(const pb_byte_t *data, size_t size, const char* expected_qr_code)
{
    BasestationUpdateMessage message = BasestationUpdateMessage_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(data, size);

    bool status = pb_decode(&stream, BasestationUpdateMessage_fields, &message);

    if (!status) {
        printf("[ ACTIVE CONFIGURATION ] Failed to decode basestation configuration: %s\n", PB_GET_ERROR(&stream));
        return false;
    }

    // Check if the QR code is valid by comparing with the expected QR code
    if (expected_qr_code == nullptr || strlen(expected_qr_code) == 0) {
        printf("[ ACTIVE CONFIGURATION ] No expected QR code provided, rejecting configuration update\n");
        return false;
    }
    
    // Check if the QR code from the message matches the expected QR code
    if (strcmp(message.qr_code, expected_qr_code) != 0) {
        printf("[ ACTIVE CONFIGURATION ] QR code mismatch: received '%s', expected '%s'\n", message.qr_code, expected_qr_code);
        return false;
    }
    
    g_active_configuration.config = message.config;
    g_active_configuration.has_config = true;


    // Update light states, preserving existing ones that match the config
    uint8_t old_lights_count = g_active_configuration.state.lights_count;
    g_active_configuration.state.lights_count = g_active_configuration.config.lights_count;

    for (uint8_t i = 0; i < g_active_configuration.state.lights_count; i++) {
        LightState *light_state = &g_active_configuration.state.lights[i];
        LightConfig *light_config = &g_active_configuration.config.lights[i];

        // Check if this light already exists in current state
        bool found = false;
        for (uint8_t j = 0; j < old_lights_count; j++) {
            if (g_active_configuration.state.lights[j].id == light_config->id) {
                // Light exists, just update the dim speed which may have changed
                g_active_configuration.state.lights[j].dim_speed_msec = light_config->dim_speed_msec;
                if (i != j) {
                    // Copy to new position if needed
                    *light_state = g_active_configuration.state.lights[j];
                }
                found = true;
                break;
            }
        }

        // Initialize new lights
        if (!found) {
            light_state->id = light_config->id;
            light_state->brightness = 0;
            light_state->target_value = 0;
            light_state->dim_speed_msec = light_config->dim_speed_msec;
            light_state->last_modified_time = get_time_in_ms();
        }
    }

    // Preserve button states - keep existing ones, don't reset
    // Button states are managed dynamically and should persist across config updates

    // Update provisioning states, preserving existing ones for configured devices
    // We need to preserve provisioning states for devices that are in the new config
    // but also clean up any provisioning states for devices that are no longer configured
    
    // First, collect all node IDs from the new configuration
    uint8_t configured_node_ids[50]; // Temporary array to store configured node IDs
    uint8_t configured_node_count = 0;
    
    // Add CAN node IDs from canbo configs
    for (uint8_t i = 0; i < g_active_configuration.config.canbo_configs_count; i++) {
        configured_node_ids[configured_node_count++] = g_active_configuration.config.canbo_configs[i].node_id;
    }
    
    // Add RF node IDs from rf reed configs
    for (uint8_t i = 0; i < g_active_configuration.config.rf_reed_configs_count; i++) {
        configured_node_ids[configured_node_count++] = g_active_configuration.config.rf_reed_configs[i].node_id;
    }
    
    // Add RF node IDs from light fixtures
    for (uint8_t i = 0; i < g_active_configuration.config.lights_count; i++) {
        for (uint8_t j = 0; j < g_active_configuration.config.lights[i].fixtures_count; j++) {
            if (g_active_configuration.config.lights[i].fixtures[j].type == LightConfig_FixtureConfig_FixtureType_RF) {
                configured_node_ids[configured_node_count++] = g_active_configuration.config.lights[i].fixtures[j].config.rf.node_id;
            }
        }
    }
    
    // Preserve existing provisioning states for configured devices
    uint8_t old_provisioned_count = g_active_configuration.state.provisioned_devices_count;
    uint8_t new_provisioned_count = 0;
    
    // First pass: preserve existing states for configured devices
    for (uint8_t i = 0; i < old_provisioned_count; i++) {
        bool is_configured = false;
        for (uint8_t j = 0; j < configured_node_count; j++) {
            if (g_active_configuration.state.provisioned_devices[i].node_id == configured_node_ids[j]) {
                is_configured = true;
                break;
            }
        }
        
        if (is_configured) {
            // Move to new position if needed
            if (new_provisioned_count != i) {
                g_active_configuration.state.provisioned_devices[new_provisioned_count] = g_active_configuration.state.provisioned_devices[i];
            }
            new_provisioned_count++;
        }
    }
    
    // Update the count
    g_active_configuration.state.provisioned_devices_count = new_provisioned_count;
    
    // Second pass: initialize provisioning states for new devices that don't have existing states
    for (uint8_t i = 0; i < configured_node_count; i++) {
        bool has_existing_state = false;
        for (uint8_t j = 0; j < g_active_configuration.state.provisioned_devices_count; j++) {
            if (g_active_configuration.state.provisioned_devices[j].node_id == configured_node_ids[i]) {
                has_existing_state = true;
                break;
            }
        }
        
        if (!has_existing_state) {
            // Create new provisioning state for this device
            ProvisioningState *new_state = &g_active_configuration.state.provisioned_devices[g_active_configuration.state.provisioned_devices_count];
            new_state->node_id = configured_node_ids[i];
            new_state->is_provisioned = false;
            new_state->last_seen_time = 0;
            new_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
            g_active_configuration.state.provisioned_devices_count++;
        }
    }

    // Update RF reed states, preserving existing ones that match the config
    uint8_t old_reeds_count = g_active_configuration.state.reeds_count;
    g_active_configuration.state.reeds_count = g_active_configuration.config.rf_reed_configs_count;

    for (uint8_t i = 0; i < g_active_configuration.state.reeds_count; i++) {
        RFReedState *reed_state = &g_active_configuration.state.reeds[i];
        RFReedSensorConfig *reed_config = &g_active_configuration.config.rf_reed_configs[i];

        // Check if this reed already exists in current state
        bool found = false;
        for (uint8_t j = 0; j < old_reeds_count; j++) {
            if (g_active_configuration.state.reeds[j].node_id == reed_config->node_id) {
                // Reed exists, preserve its state
                if (i != j) {
                    // Copy to new position if needed
                    *reed_state = g_active_configuration.state.reeds[j];
                }
                found = true;
                break;
            }
        }

        // Initialize new reed states
        if (!found) {
            reed_state->node_id = reed_config->node_id;
            reed_state->sensor_status = RFReedState_Status_UNKNOWN;
            reed_state->last_modified_time = get_time_in_ms();
            reed_state->battery_voltage = 0.0;
        }
    }

    return true;
}

bool set_active_configuration_from_protobuf_emscripten(std::string data) {
    const char *data_chars = data.c_str();
    return set_active_configuration_from_protobuf(reinterpret_cast<const uint8_t *>(data_chars), data.size());
}

void set_debug_configuration() {
    g_active_configuration = ActiveConfiguration_init_zero;

    static LightConfig_FixtureConfig_DMXConfig_LightParams wledParams = {0.0f, 1.0f, 1.5f, 0.0f, 1.0f, 1.5f};

    g_active_configuration.config = BasestationConfig_init_zero;

    g_active_configuration.config.id = 1;
    strncpy(g_active_configuration.config.version, "000000", sizeof(g_active_configuration.config.version) - 1);
    g_active_configuration.config.version[sizeof(g_active_configuration.config.version) - 1] = '\0';
    g_active_configuration.has_config = true;

    uint32_t now = get_time_in_ms();

    LightConfig *lightConfig = &g_active_configuration.config.lights[0];
    lightConfig->id = 1;
    lightConfig->dim_speed_msec = 1000;

    LightConfig_FixtureConfig *wledStripRight = &lightConfig->fixtures[0];
    wledStripRight->min_brightness = 0;
    wledStripRight->max_brightness = 50;
    wledStripRight->which_config = LightConfig_FixtureConfig_dmx_tag;
    wledStripRight->config.dmx.type = LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_TUNABLE_WHITE;
    wledStripRight->config.dmx.channels_count = 1;
    wledStripRight->config.dmx.channels[0] = 4;
    wledStripRight->config.dmx.params = wledParams;
    wledStripRight->config.dmx.has_params = true;

    LightConfig_FixtureConfig *rfLight = &lightConfig->fixtures[1];
    rfLight->which_config = LightConfig_FixtureConfig_rf_tag;
    rfLight->config.rf.node_id = 51;
    rfLight->min_brightness = 0;
    rfLight->max_brightness = 100;

    lightConfig->fixtures_count = 2;

    g_active_configuration.config.lights_count = 1;

    LightState *lightState = &g_active_configuration.state.lights[0];
    lightState->id = 1;
    lightState->brightness = 0;
    lightState->target_value = 0;
    lightState->dim_speed_msec = 1000;
    lightState->last_modified_time = now;

    g_active_configuration.state.lights_count = 1;

    // qr code mapping
    // BasestationConfig_NodeQRMapping *canboDevice = &g_active_configuration.config.node_qr_mappings[0];
    // strncpy(canboDevice->qr_code, "132145", sizeof(canboDevice->qr_code) - 1);
    // canboDevice->qr_code[sizeof(canboDevice->qr_code) - 1] = '\0';
    // canboDevice->type = BasestationConfig_NodeQRMapping_DeviceType_CAN;
    // canboDevice->node_id = 50;

    BasestationConfig_NodeQRMapping *rfReedDevice = &g_active_configuration.config.node_qr_mappings[0];
    strncpy(rfReedDevice->qr_code, "KW3223005845", sizeof(rfReedDevice->qr_code) - 1);
    rfReedDevice->qr_code[sizeof(rfReedDevice->qr_code) - 1] = '\0';
    rfReedDevice->type = BasestationConfig_NodeQRMapping_DeviceType_RF;
    rfReedDevice->node_id = 41;

    g_active_configuration.config.node_qr_mappings_count = 1;

    // canbo setup
    // CanboConfig *canboConfig = &g_active_configuration.config.canbo_configs[0];
    // canboConfig->node_id = 1;
    // canboConfig->two_pin_inputs_count = 0;
    // canboConfig->three_pin_inputs[0].connector_id = 1;
    // canboConfig->three_pin_inputs[0].type = CanboConfig_ThreePinInput_ConnectorType_TOGGLE;
    // canboConfig->three_pin_inputs[0].config.toggle.up_click[0].dim_speed_msec = 1000;
    // canboConfig->three_pin_inputs[0].config.toggle.up_click[0].light_id = 1;
    // canboConfig->three_pin_inputs[0].config.toggle.up_click[0].target_brightness = 60.0;
    // canboConfig->three_pin_inputs[0].config.toggle.up_click_count = 1;
    // canboConfig->three_pin_inputs[0].config.toggle.down_click[0].dim_speed_msec = 1000;
    // canboConfig->three_pin_inputs[0].config.toggle.down_click[0].light_id = 1;
    // canboConfig->three_pin_inputs[0].config.toggle.down_click[0].target_brightness = 0.0;
    // canboConfig->three_pin_inputs[0].config.toggle.down_click_count = 1;
    // canboConfig->three_pin_inputs[0].config.toggle.up_hold[0].dim_speed_msec = 4000;
    // canboConfig->three_pin_inputs[0].config.toggle.up_hold[0].light_id = 1;
    // canboConfig->three_pin_inputs[0].config.toggle.up_hold[0].target_brightness = 100.0;
    // canboConfig->three_pin_inputs[0].config.toggle.up_hold_count = 1;
    // canboConfig->three_pin_inputs[0].config.toggle.down_hold[0].dim_speed_msec = 4000;
    // canboConfig->three_pin_inputs[0].config.toggle.down_hold[0].light_id = 1;
    // canboConfig->three_pin_inputs[0].config.toggle.down_hold[0].target_brightness = 0.0;
    // canboConfig->three_pin_inputs[0].config.toggle.down_hold_count = 1;
    // canboConfig->three_pin_inputs_count = 1;

    g_active_configuration.config.canbo_configs_count = 0;

    // rf reed config
    RFReedSensorConfig *rfReedConfig = &g_active_configuration.config.rf_reed_configs[0];
    rfReedConfig->node_id = 41;
    rfReedConfig->door_open[0].light_id = 1;
    rfReedConfig->door_open[0].target_brightness = 100.0;
    rfReedConfig->door_open[0].dim_speed_msec = 2000;
    rfReedConfig->door_open[0].activate_delay_msec = 0;
    
    rfReedConfig->door_close[0].light_id = 1;
    rfReedConfig->door_close[0].target_brightness = 0.0;
    rfReedConfig->door_close[0].dim_speed_msec = 2000;
    rfReedConfig->door_close[0].activate_delay_msec = 5000;

    rfReedConfig->door_close_count = 1;
    rfReedConfig->door_open_count = 1;

    g_active_configuration.config.rf_reed_configs_count = 1;

    // setup rf
    g_active_configuration.config.rf_config.channel = 15;
    g_active_configuration.config.rf_config.network = 30;
    g_active_configuration.config.has_rf_config = true;

    // provisioning (remove this if you want to test provisioning)
    ProvisioningState *provisioning_state = get_provisioned_device_state_from_node_id(41);
    provisioning_state->is_provisioned = true;
    provisioning_state->last_seen_time = get_time_in_ms();
    provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
}

ProvisioningState *get_provisioned_device_state_from_node_id(uint8_t ucNodeId) {
    for (uint8_t i = 0; i < g_active_configuration.state.provisioned_devices_count; i++) {
        if (g_active_configuration.state.provisioned_devices[i].node_id == ucNodeId) {
            return &g_active_configuration.state.provisioned_devices[i];
        }
    }
    // create a new provisioning state
    ProvisioningState *provisioning_state = &g_active_configuration.state.provisioned_devices[g_active_configuration.state.provisioned_devices_count];
    provisioning_state->node_id = ucNodeId;
    provisioning_state->is_provisioned = false;
    provisioning_state->last_seen_time = 0;
    provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
    g_active_configuration.state.provisioned_devices_count++;
    return provisioning_state;
}

bool has_provisioned_device_state_for_node_id(uint8_t ucNodeId) {
    for (uint8_t i = 0; i < g_active_configuration.state.provisioned_devices_count; i++) {
        if (g_active_configuration.state.provisioned_devices[i].node_id == ucNodeId) {
            return true;
        }
    }
    return false;
}

CanboConfig *get_canbo_config_from_node_id(uint8_t ucNodeId) {
    for (uint8_t i = 0; i < g_active_configuration.config.canbo_configs_count; i++) {
        if (g_active_configuration.config.canbo_configs[i].node_id == ucNodeId) {
            return &g_active_configuration.config.canbo_configs[i];
        }
    }
    return nullptr;
}

LightConfig *get_light_config_from_id(uint8_t light_id) {
    for (uint8_t i = 0; i < g_active_configuration.config.lights_count; i++) {
        if (g_active_configuration.config.lights[i].id == light_id) {
            return &g_active_configuration.config.lights[i];
        }
    }
    return nullptr;
}

LightState * get_light_state_from_id(uint8_t light_id) {
    for (uint8_t i = 0; i < g_active_configuration.state.lights_count; i++) {
        if (g_active_configuration.state.lights[i].id == light_id) {
            return &g_active_configuration.state.lights[i];
        }
    }
    return nullptr;
}

RFReedState * get_rf_reed_state_from_node_id(uint8_t ucNodeId) {
    for (uint8_t i = 0; i < g_active_configuration.state.reeds_count; i++) {
        if (g_active_configuration.state.reeds[i].node_id == ucNodeId) {
            return &g_active_configuration.state.reeds[i];
        }
    }
    // create a new rf reed state
    RFReedState *rf_reed_state = &g_active_configuration.state.reeds[g_active_configuration.state.reeds_count];
    rf_reed_state->node_id = ucNodeId;
    rf_reed_state->sensor_status = RFReedState_Status_UNKNOWN;
    rf_reed_state->last_modified_time = get_time_in_ms();
    rf_reed_state->battery_voltage = 0.0;
    g_active_configuration.state.reeds_count++;
    return rf_reed_state;
}

ButtonState * get_button_state_from_node_id(uint8_t ucNodeId) {
    for (uint8_t i = 0; i < g_active_configuration.state.buttons_count; i++) {
        if (g_active_configuration.state.buttons[i].node_id == ucNodeId) {
            return &g_active_configuration.state.buttons[i];
        }
    }
    // create a new button state
    ButtonState *button_state = &g_active_configuration.state.buttons[g_active_configuration.state.buttons_count];
    button_state->node_id = ucNodeId;
    button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
    button_state->last_modified_time = get_time_in_ms();
    g_active_configuration.state.buttons_count++;
    return button_state;
}

void pretty_print_active_configuration() {
    printf("\n=== Active Configuration ===\n");

    // Print Basestation Config
    printf("\nBasestation Config:\n");
    // printf("  Name: %s\n", g_active_configuration.config.name);
    printf("  Version: %s\n", g_active_configuration.config.version);
    // printf("  Room ID: %s\n", g_active_configuration.config.room_id);
    printf("  Node ID: %u\n", g_active_configuration.config.id);

    // Print RF Config if present
    if (g_active_configuration.config.has_rf_config) {
        printf("\n  RF Config:\n");
        printf("    Channel: %u\n", g_active_configuration.config.rf_config.channel);
        printf("    Network: %u\n", g_active_configuration.config.rf_config.network);
        printf("    Basestation ID: %u\n", g_active_configuration.config.id);
    }

    // Print Lights
    printf("\n  Lights (%u):\n", g_active_configuration.config.lights_count);
    for (size_t i = 0; i < g_active_configuration.config.lights_count; i++) {
        const LightConfig &light = g_active_configuration.config.lights[i];
        printf("    Light %zu: ID %u\n", i, light.id);
        printf("      Dim Speed: %u msec\n", light.dim_speed_msec);
        printf("      Fixtures (%u):\n", light.fixtures_count);
        

        for (size_t j = 0; j < light.fixtures_count; j++) {
            const LightConfig_FixtureConfig &fixture = light.fixtures[j];
            printf("        Fixture %zu:\n", j);
            // printf("          UUID: %s\n", fixture.uuid);
            printf("          Brightness Range: %.02f-%.02f\n", fixture.min_brightness, fixture.max_brightness);

            // Print fixture specific config
            if (fixture.type == LightConfig_FixtureConfig_FixtureType_DMX) {
                printf("          Type: %d\n", fixture.config.dmx.type);
                printf("          DMX Channels (%u):\n", fixture.config.dmx.channels_count);
                for (size_t k = 0; k < fixture.config.dmx.channels_count; k++) {
                    printf("            Channel %zu: %u\n", k, fixture.config.dmx.channels[k]);
                }
                if (fixture.config.dmx.has_rgb) {
                    printf("          RGB: R=%u G=%u B=%u\n",
                           fixture.config.dmx.rgb.red,
                           fixture.config.dmx.rgb.green,
                           fixture.config.dmx.rgb.blue);
                }
            } else if (fixture.type == LightConfig_FixtureConfig_FixtureType_ZERO_TO_TEN_VOLT) {
                if (fixture.config.zero_to_ten_volt.type == LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SOURCING) {
                    printf("          Type: Sourcing\n");
                } else if (fixture.config.zero_to_ten_volt.type == LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SINKING) {
                    printf("          Type: Sinking\n");
                } else {
                    printf("          Type: Disabled\n");
                }
                printf("          Node ID: %d\n", fixture.config.zero_to_ten_volt.node_id);
                printf("          Use Relay: %d\n", fixture.config.zero_to_ten_volt.use_relay);
                printf("          Out Connector ID: %u\n", fixture.config.zero_to_ten_volt.out_connector_id);
            } else if (fixture.type == LightConfig_FixtureConfig_FixtureType_ON_OFF_LIGHT) {
                printf("          Type: ON/OFF\n");
            }
        }
    }

    // Print Canbo Configs
    printf("\n  Canbo Configs (%u):\n", g_active_configuration.config.canbo_configs_count);
    for (size_t i = 0; i < g_active_configuration.config.canbo_configs_count; i++) {
        const CanboConfig &canbo = g_active_configuration.config.canbo_configs[i];
        printf("    Canbo %zu: Node ID %u\n", i, canbo.node_id);
        printf("      Toggle Inputs: %u\n", canbo.three_pin_inputs_count);
        printf("      PIR Inputs: %u\n", canbo.two_pin_inputs_count);
        for (size_t j = 0; j < canbo.three_pin_inputs_count; j++) {
            const CanboConfig_ThreePinInput &three_pin_input = canbo.three_pin_inputs[j];
            printf("        Three Pin Input %zu: Connector ID %u\n", j, three_pin_input.connector_id);
            printf("          Type: %d\n", three_pin_input.type);
            printf("          Up Click Count: %u\n", three_pin_input.config.toggle.up_click_count);
            printf("          Down Click Count: %u\n", three_pin_input.config.toggle.down_click_count);
            printf("          Up Hold Count: %u\n", three_pin_input.config.toggle.up_hold_count);
            printf("          Down Hold Count: %u\n", three_pin_input.config.toggle.down_hold_count);
        }
        for (size_t j = 0; j < canbo.two_pin_inputs_count; j++) {
            const CanboConfig_TwoPinInput &two_pin_input = canbo.two_pin_inputs[j];
            printf("        Two Pin Input %zu: Connector ID %u\n", j, two_pin_input.connector_id);
            printf("          Type: %d\n", two_pin_input.type);
        }
        // for(size_t j = 0; j < canbo.){}
    }

    // Print RF Reed Configs
    printf("\n  RF Reed Configs (%u):\n", g_active_configuration.config.rf_reed_configs_count);
    for (size_t i = 0; i < g_active_configuration.config.rf_reed_configs_count; i++) {
        const RFReedSensorConfig &reed = g_active_configuration.config.rf_reed_configs[i];
        printf("    Reed %zu: Node ID %u\n", i, reed.node_id);
        printf("      Door Close Actions (%u):\n", reed.door_close_count);
        for (size_t j = 0; j < reed.door_close_count; j++) {
            const RFReedSensorConfig_Action &action = reed.door_close[j];
            printf("        Action %zu:\n", j);
            printf("          Light ID: %u\n", action.light_id);
            printf("          Target Brightness: %.2f\n", action.target_brightness);
            printf("          Dim Speed: %u msec\n", action.dim_speed_msec);
            printf("          Activate Delay: %u msec\n", action.activate_delay_msec);
        }
        printf("      Door Open Actions (%u):\n", reed.door_open_count);
        for (size_t j = 0; j < reed.door_open_count; j++) {
            const RFReedSensorConfig_Action &action = reed.door_open[j];
            printf("        Action %zu:\n", j);
            printf("          Light ID: %u\n", action.light_id);
            printf("          Target Brightness: %.2f\n", action.target_brightness);
            printf("          Dim Speed: %u msec\n", action.dim_speed_msec);
            printf("          Activate Delay: %u msec\n", action.activate_delay_msec);
        }
    }

    // Print Node QR Mappings
    printf("\n  Node QR Mappings (%u):\n", g_active_configuration.config.node_qr_mappings_count);
    for (size_t i = 0; i < g_active_configuration.config.node_qr_mappings_count; i++) {
        const BasestationConfig_NodeQRMapping &mapping = g_active_configuration.config.node_qr_mappings[i];
        printf("    Mapping %zu:\n", i);
        printf("      QR Code: %s\n", mapping.qr_code);
        printf("      Node ID: %u\n", mapping.node_id);
        printf("      Type: %d\n", mapping.type);
    }

    // Print Light States
    printf("\nLight States (%u):\n", g_active_configuration.state.lights_count);
    for (size_t i = 0; i < g_active_configuration.state.lights_count; i++) {
        const LightState &state = g_active_configuration.state.lights[i];
        printf("  Light %zu:\n", i);
        printf("    ID: %u\n", state.id);
        printf("    Brightness: %.2f\n", state.brightness);
        printf("    Target Brightness: %.2f\n", state.target_value);
        printf("    Dim Speed: %lu msec\n", state.dim_speed_msec);
        printf("    Last Modified: %llu\n", (unsigned long long)state.last_modified_time);
        printf("    Active After: %llu\n", (unsigned long long)state.active_after_time);
    }

    // Print Button States
    printf("\nButton States (%u):\n", g_active_configuration.state.buttons_count);
    for (size_t i = 0; i < g_active_configuration.state.buttons_count; i++) {
        const ButtonState& state = g_active_configuration.state.buttons[i];
        printf("  Button %zu:\n", i);
        printf("    Node ID: %u\n", state.node_id);
        printf("    Current State: %d\n", state.current_state);
        printf("    Last Modified: %llu\n", (unsigned long long)state.last_modified_time);
    }

    // Print Device Provisioning Status
    printf("\nDevice Provisioning Status (%u):\n", g_active_configuration.state.provisioned_devices_count);
    for (size_t i = 0; i < g_active_configuration.state.provisioned_devices_count; i++) {
        const ProvisioningState &state = g_active_configuration.state.provisioned_devices[i];
        printf("  Device %zu:\n", i);
        printf("    Node ID: %u\n", state.node_id);
        printf("    Is Provisioned: %s\n", state.is_provisioned ? "true" : "false");
        printf("    Error Code: %d\n", state.error_code);
        printf("    Last Seen: %llu\n", (unsigned long long)state.last_seen_time);
    }

    // Print RF Reed States
    printf("\nRF Reed States (%u):\n", g_active_configuration.state.reeds_count);
    for (size_t i = 0; i < g_active_configuration.state.reeds_count; i++) {
        const RFReedState &state = g_active_configuration.state.reeds[i];
        printf("  Reed %zu:\n", i);
        printf("    Node ID: %u\n", state.node_id);
        printf("    Sensor Status: %d\n", state.sensor_status);
        printf("    Last Modified: %llu\n", (unsigned long long)state.last_modified_time);
        printf("    Battery Voltage: %.2f\n", state.battery_voltage);
    }

    printf("\n=== End Configuration ===\n");
}

void update_light_states(uint32_t current_time_ms)
{
    for (size_t i = 0; i < g_active_configuration.state.lights_count; i++)
    {
        LightState *lightState = &g_active_configuration.state.lights[i];
        
        if (lightState->active_after_time > current_time_ms) {
            // not active yet
            lightState->last_modified_time = current_time_ms;
            continue;
        }
       
        float delta = (100.0 / lightState->dim_speed_msec) * (current_time_ms - lightState->last_modified_time);
        
        // Calculate new brightness based on target and current values
        float newBrightness = lightState->brightness;
        if (lightState->target_value < lightState->brightness)
        {
            newBrightness = std::max(lightState->target_value, lightState->brightness - delta);
        }
        else if (lightState->target_value > lightState->brightness)
        {
            newBrightness = std::min(lightState->target_value, lightState->brightness + delta);
        }
        
        if (newBrightness != lightState->brightness)
        {
            lightState->brightness = newBrightness;
            lightState->last_modified_time = current_time_ms;
            lightState->is_transitioning = true;
        } else if (lightState->is_transitioning) {
            lightState->is_transitioning = false;
            lightState->last_modified_time = current_time_ms;
        }
    }
}

#ifdef __EMSCRIPTEN__

// Make buffer size large enough to support bigger room configurations
uint8_t configuration_protobuf_buffer[16384];
size_t configuration_protobuf_buffer_length;

emscripten::val get_active_configuration_emscripten() {
    pb_ostream_t stream = pb_ostream_from_buffer(configuration_protobuf_buffer, sizeof(configuration_protobuf_buffer));
    bool status = pb_encode(&stream, ActiveConfiguration_fields, &g_active_configuration);
    configuration_protobuf_buffer_length = stream.bytes_written;
    if (!status) {
        printf("Failed to encode active configuration: %s\n", PB_GET_ERROR(&stream));
        return emscripten::val(false);
    }

    return emscripten::val(emscripten::typed_memory_view(configuration_protobuf_buffer_length, configuration_protobuf_buffer));
}

void process_all_lights_emscripten() {
    uint32_t now = get_time_in_ms();
    update_light_states(now);
}

EMSCRIPTEN_BINDINGS(active_configuration) {
    emscripten::function("setActiveConfigurationFromProtobuf", &set_active_configuration_from_protobuf_emscripten);
    emscripten::function("getActiveConfiguration", &get_active_configuration_emscripten);
    emscripten::function("setDebugConfiguration", &set_debug_configuration);
    emscripten::function("prettyPrintActiveConfiguration", &pretty_print_active_configuration);
    emscripten::function("processAllLights", &process_all_lights_emscripten);
}
#endif
