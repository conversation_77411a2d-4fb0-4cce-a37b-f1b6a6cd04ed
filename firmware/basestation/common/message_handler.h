#ifndef MESSAGE_HANDLER_H
#define MESSAGE_HANDLER_H

#include <stdint.h>
#ifdef __EMSCRIPTEN__
#include <emscripten/emscripten.h>
#include <emscripten/bind.h>
#else
#define EMSCRIPTEN_KEEPALIVE
#include "pico/stdlib.h"
#endif

#include "commands.pb.h"

#ifdef __cplusplus
extern "C" {
#endif

void handle_command(const uint8_t *data, uint32_t len, uint8_t node_id);

// Function to send provisioning command to a CAN device
int send_provisioning_command(uint8_t node_id, const CanboProvisioningCommand* command);

void register_send_can_message_callback(void (*callback)(const uint8_t *data, uint32_t len));
void register_send_rf_message_callback(void (*callback)(const uint8_t *data, uint32_t len));

uint32_t get_time_in_ms();

#ifdef __cplusplus
}
#endif

#endif // MESSAGE_HANDLER_H
