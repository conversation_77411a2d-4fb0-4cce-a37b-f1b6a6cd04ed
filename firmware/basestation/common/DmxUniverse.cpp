#include "DmxUniverse.h"
#include <cmath>
#include <algorithm>
#include "active_configuration.h"
#include "message_handler.h"

#ifdef __EMSCRIPTEN__
#include <emscripten/emscripten.h>
#include <emscripten/bind.h>
#include <emscripten/val.h>
#else
    #include "pico/stdlib.h"
#endif

DmxUniverse::DmxUniverse()
{
    std::fill(_universe, _universe + sizeof(_universe), 0);
}

uint8_t *DmxUniverse::getUniverse()
{
    return _universe;
}

float DmxUniverse::adjustBrightness(float curr, float target, float delta)
{
    if (target < curr)
    {
        curr = std::max(target, curr - delta);
    }
    else if (target > curr)
    {
        curr = std::min(target, curr + delta);
    }
    return curr;
}

void DmxUniverse::buildDmxDataFrame()
{

    for (size_t i = 0; i < g_active_configuration.state.lights_count; i++)
    {
        LightState *lightState = &g_active_configuration.state.lights[i];
        LightConfig *lightConfig = &g_active_configuration.config.lights[i];


        for (size_t j = 0; j < lightConfig->fixtures_count; j++)
        {

            LightConfig_FixtureConfig *fixtureConfig = &lightConfig->fixtures[j];
            if (fixtureConfig->which_config != LightConfig_FixtureConfig_dmx_tag)
            {
                continue;
            }
            
            // only handle dmx
            LightConfig_FixtureConfig_DMXConfig *dmxConfig = &fixtureConfig->config.dmx;
            float scaledBrightness = fixtureConfig->min_brightness +
                                     (lightState->brightness / 100.0f) * (fixtureConfig->max_brightness - fixtureConfig->min_brightness);

            // Calculate DMX brightness with gamma correction
            float dmxBrightness = std::pow(scaledBrightness / 100.0f, 1.5f) * 255.0f;
            LightConfig_FixtureConfig_DMXConfig_LightParams dParams = LightConfig_FixtureConfig_DMXConfig_LightParams_init_default;
            if (fixtureConfig->config.dmx.has_params)
            {
                dParams.gamma1 = fixtureConfig->config.dmx.params.gamma1;
                dParams.gamma2 = fixtureConfig->config.dmx.params.gamma2;
                dParams.min1 = fixtureConfig->config.dmx.params.min1;
                dParams.min2 = fixtureConfig->config.dmx.params.min2;
                dParams.max1 = fixtureConfig->config.dmx.params.max1;
                dParams.max2 = fixtureConfig->config.dmx.params.max2;
            }

            for (size_t k = 0; k < dmxConfig->channels_count; k++)
            {
                uint8_t d = dmxConfig->channels[k] + 1;
                // TODO: apply fixture-specific parameters
                switch (dmxConfig->type)
                {
                case LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_D4:
                    _universe[d - 1] = static_cast<uint8_t>(dmxBrightness);
                    _universe[d] = static_cast<uint8_t>(dmxBrightness / 4.0f);
                    _universe[d + 1] = _universe[d + 2] = 0;
                    break;
                case LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_TUNABLE_WHITE:
                    _universe[d - 1] = static_cast<uint8_t>(
                        (dParams.min1 +
                         std::pow(scaledBrightness / 100.0f, dParams.gamma1) *
                             (dParams.max1 - dParams.min1)) *
                        255.0f);
                    _universe[d] = static_cast<uint8_t>(
                        (dParams.min2 +
                         std::pow(scaledBrightness / 100.0f, dParams.gamma2) *
                             (dParams.max2 - dParams.min2)) *
                        255.0f);
                    // write out d-1 and d values to console
                    break;
                case LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_ELV:
                case LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_DF_12:
                    _universe[d - 1] = static_cast<uint8_t>((scaledBrightness / 100.0f) * 255.0f);
                    break;
                case LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_EST:
                    _universe[d - 1] = static_cast<uint8_t>(
                        (dParams.min1 +
                         (scaledBrightness / 100.0f) * (dParams.max1 - dParams.min1)) *
                        255.0f);
                    break;
                case LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_RGB_STRIP:
                    if (fixtureConfig->config.dmx.has_rgb)
                    {
                        float brightnessScale = scaledBrightness / 100.0f;
                        uint8_t blueVal = static_cast<uint8_t>(fixtureConfig->config.dmx.rgb.blue * brightnessScale);
                        uint8_t redVal = static_cast<uint8_t>(fixtureConfig->config.dmx.rgb.red * brightnessScale);
                        uint8_t greenVal = static_cast<uint8_t>(fixtureConfig->config.dmx.rgb.green * brightnessScale);

                        // Apply brightness scaling to RGB values
                        _universe[d - 1] = redVal;
                        _universe[d] = greenVal;
                        _universe[d + 1] = blueVal;
                    }
                    else
                    {
                        uint8_t whiteValue = static_cast<uint8_t>((scaledBrightness / 100.0f) * 255.0f);
                        _universe[d - 1] = whiteValue;
                        _universe[d] = whiteValue;
                        _universe[d + 1] = whiteValue;
                    }
                    break;
                default:
                    break;
                }
            }
        }
    }

    //     for (auto& light : _lights) {
    //         float delta = light.dimSpeedMsec * (now - light.lastModifiedTime);
    //         float newBrightness = adjustBrightness(
    //             light.brightness,
    //             light.targetBrightness,
    //             delta
    //         );

    //         if (newBrightness != light.brightness) {
    //             light.brightness = newBrightness;
    //             light.lastModifiedTime = now;
    //         }

    //         for (const auto& fixture : light.fixtures) {
    //             // Calculate the scaled brightness based on the fixture's min and max brightness
    //             float scaledBrightness = fixture.minBrightness +
    //                 (light.brightness / 100.0f) * (fixture.maxBrightness - fixture.minBrightness);

    //             // Calculate DMX brightness with gamma correction
    //             float dmxBrightness = powf(scaledBrightness / 100.0f, 1.5f) * 255.0f;
    //             DefaultParams dParams = getDefaultParams(fixture.type);

    //             // Apply fixture-specific parameters if they exist
    //             if (fixture.params) {
    //                 dParams.min1 = fixture.params->min1 != 0 ? fixture.params->min1 : dParams.min1;
    //                 dParams.max1 = fixture.params->max1 != 0 ? fixture.params->max1 : dParams.max1;
    //                 dParams.gamma1 = fixture.params->gamma1 != 0 ? fixture.params->gamma1 : dParams.gamma1;
    //                 dParams.min2 = fixture.params->min2 != 0 ? fixture.params->min2 : dParams.min2;
    //                 dParams.max2 = fixture.params->max2 != 0 ? fixture.params->max2 : dParams.max2;
    //                 dParams.gamma2 = fixture.params->gamma2 != 0 ? fixture.params->gamma2 : dParams.gamma2;
    //             }

    //             for (uint8_t d : fixture.channel) {
    //                 d = d + 1; // first byte i always 0
    //                 switch (fixture.type) {
    //                     case FixtureType::D4:
    //                         _universe[d - 1] = static_cast<uint8_t>(dmxBrightness);
    //                         _universe[d] = static_cast<uint8_t>(dmxBrightness / 4.0f);
    //                         _universe[d + 1] = _universe[d + 2] = 0;
    //                         break;
    //                     case FixtureType::WDLED8:
    //                         _universe[d - 1] = static_cast<uint8_t>(
    //                             (dParams.min1 +
    //                              powf(scaledBrightness / 100.0f, dParams.gamma1) *
    //                              (dParams.max1 - dParams.min1)) *
    //                             255.0f
    //                         );
    //                         _universe[d] = static_cast<uint8_t>(
    //                             (dParams.min2 +
    //                              powf(scaledBrightness / 100.0f, dParams.gamma2) *
    //                              (dParams.max2 - dParams.min2)) *
    //                             255.0f
    //                         );
    //                         break;
    //                     case FixtureType::ELV:
    //                     case FixtureType::DF_12:
    //                         _universe[d - 1] = static_cast<uint8_t>((scaledBrightness / 100.0f) * 255.0f);
    //                         break;
    //                     case FixtureType::EST:
    //                         _universe[d - 1] = static_cast<uint8_t>(
    //                             (dParams.min1 +
    //                              (scaledBrightness / 100.0f) * (dParams.max1 - dParams.min1)) *
    //                             255.0f
    //                         );
    //                         break;
    //                     case FixtureType::RGB_STRIP:
    //                         if (fixture.rgb.red != 0 || fixture.rgb.green != 0 || fixture.rgb.blue != 0) {
    //                             float brightnessScale = scaledBrightness / 100.0f;
    //                             uint8_t blueVal = static_cast<uint8_t>(fixture.rgb.blue * brightnessScale);
    //                             uint8_t redVal = static_cast<uint8_t>(fixture.rgb.red * brightnessScale);
    //                             uint8_t greenVal = static_cast<uint8_t>(fixture.rgb.green * brightnessScale);

    //                             // Apply brightness scaling to RGB values
    //                             _universe[d -1] = redVal;
    //                             _universe[d] = greenVal;
    //                             _universe[d + 1] = blueVal;
    // #ifdef DEBUG_DMX
    //                             Serial.printf("DMX Channel %d - Red: %d, Green: %d, Blue: %d, Brightness: %f\n",
    //                                 d, redVal, greenVal, blueVal, scaledBrightness);
    // #endif
    //                         } else {
    //                             uint8_t whiteValue = static_cast<uint8_t>((scaledBrightness / 100.0f) * 255.0f);
    //                             _universe[d - 1] = whiteValue;
    //                             _universe[d] = whiteValue;
    //                             _universe[d + 1] = whiteValue;
    //                         }
    //                         break;
    //                     case FixtureType::ANALOG:
    //                         // Don't do anything - handled in process_analog
    //                         break;
    //                 }
    //             }
    //         }
    //     }
}

DmxUniverse::~DmxUniverse()
{
    std::fill(_universe, _universe + sizeof(_universe), 0);
}

#ifdef __EMSCRIPTEN__

emscripten::val get_dmx_universe_emscripten()
{
    DmxUniverse dmxUniverse;
    dmxUniverse.buildDmxDataFrame();
    uint8_t *universe = dmxUniverse.getUniverse();

    return emscripten::val(emscripten::typed_memory_view(513, universe));
}

EMSCRIPTEN_BINDINGS(dmx_universe)
{
    emscripten::function("getDmxUniverse", &get_dmx_universe_emscripten);
}

#endif