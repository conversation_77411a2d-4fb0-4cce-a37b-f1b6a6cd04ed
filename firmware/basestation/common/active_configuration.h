#ifndef ACTIVE_CONFIGURATION_H
#define ACTIVE_CONFIGURATION_H

#include <basestation-config.pb.h>

#include <pb_decode.h>
#include <pb_encode.h>

#ifdef __EMSCRIPTEN__
#include <emscripten/emscripten.h>
#else
#define EMSCRIPTEN_KEEPALIVE
#endif

#ifdef __cplusplus
extern "C" {
#endif

// Default parameters for different fixture types
struct DefaultParams {
    float min1;
    float max1;
    float gamma1;
    float min2;
    float max2;
    float gamma2;
};

extern ActiveConfiguration g_active_configuration;

// void reset_inputs();
void reset_lights();
// void reset_fans();
void reset_active_configuration();
void pretty_print_active_configuration();

// Update light states based on time and transitions
void update_light_states(uint32_t current_time_ms);

// bool active_config_is_at_least_one_light_on(const InputConfig* input);
// bool active_config_has_at_least_one_fan_not_at(const InputConfig *input, uint16_t level);
// bool active_config_is_at_least_one_shades_open(const InputConfig *input);
void set_debug_configuration();

ProvisioningState * get_provisioned_device_state_from_node_id(uint8_t ucNodeId);
bool has_provisioned_device_state_for_node_id(uint8_t ucNodeId);
LightConfig * get_light_config_from_id(uint8_t light_id);
CanboConfig * get_canbo_config_from_node_id(uint8_t ucNodeId);
LightState * get_light_state_from_id(uint8_t light_id);
RFReedState * get_rf_reed_state_from_node_id(uint8_t ucNodeId);
ButtonState * get_button_state_from_node_id(uint8_t ucNodeId);

ActiveConfiguration* get_active_configuration();
bool set_active_configuration_from_protobuf(const pb_byte_t* data, size_t size);
bool set_configuration_from_update_message_protobuf(const pb_byte_t *data, size_t size, const char* expected_qr_code);

#ifdef __cplusplus
}
#endif

#endif // ACTIVE_CONFIGURATION_H