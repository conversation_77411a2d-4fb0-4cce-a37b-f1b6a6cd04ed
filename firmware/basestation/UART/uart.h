#ifndef __UART_H__
#define __UART_H__

// C standard libraries
#include <stdio.h>

// pico sdk libraries
#include "pico/stdlib.h"
#include "hardware/uart.h"
#include "hardware/gpio.h"
#include "hardware/dma.h"

// user libraries

#ifdef __cplusplus
extern "C" {
#endif

// local definitions/enums
#define UART_DIS_IRQ_IMPLEMENTATION ( uint8_t )0xFF

typedef struct{
    union{
        uint32_t Word;
        struct{
            bool en:1;
            bool trigger_en:1;
            bool busy:1;
            uint8_t channel:8;
            uint8_t dreq_num:8;
            uint8_t irq_num:8;
        };
    };
    uint8_t * buf_add;
    uint32_t buf_len;
    uint16_t dma_index;
    uint16_t user_index;
    void * irq_cb_func;
} DMAParams_t;

typedef struct {
    uint32_t baudrate;
    uint8_t data_bits;
    uint8_t stop_bits;
    uint8_t parity;
    uart_inst_t *uart_inst;
    uint8_t tx_pin;
    uint8_t rx_pin;
    bool hw_ctrl;
    bool fifo_en;
    uint8_t irq_num;
    void * irq_cb_func;
    DMAParams_t dma_rx;
    DMAParams_t dma_tx;
} UARTParams_t;

// function prototyping
uint8_t ucUARTInit( UARTParams_t * pxParams );
uint8_t ucUARTDMAInit( DMAParams_t * pxParams, uart_inst_t * pxInstance, bool xIsToPeripheral );

#ifdef __cplusplus
}
#endif

#endif