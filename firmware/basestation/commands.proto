// Compile me into c with
// python lib/nanopb-0.4.9.1-macosx-x86/generator/nanopb_generator.py basestation-config.proto

syntax = "proto3";

import "nanopb.proto";

// incoming commands

message MomentaryButtonCommand {
  uint32 connector_id = 1;
  State state = 2;

  enum State {
    Pressed = 0;
    Released = 1;
  }
}

message ToggleButtonCommand {
  uint32 connector_id = 1;
  State state = 2;

  enum State {
    Up = 0;
    Released = 1;
    Down = 2;
  }
}

message MotionDetectedCommand {
  uint32 connector_id = 1;
  State state = 2;

  enum State {
    Detected = 0;
    Stopped = 1;
  }
}

message RFDoorSensorCommand {
  uint32 node_id = 1;
  State state = 2;

  enum State {
    Opened = 0;
    Closed = 1;
  }
}

message DoorSensorCommand {
  uint32 connector_id = 1;
  State state = 2;

  enum State {
    Opened = 0;
    Closed = 1;
  }
}

message KnobCommand {
  float percent_turn = 1;
}

message ThermostatCommand {
  float degree_celcius = 1;
}

message PingCommand {
  string qr_code = 1 [(nanopb).max_length = 12];
  bool is_provisioned = 2;
  string version = 3 [(nanopb).max_length = 8];
}

// outgoing commands

message CanboProvisioningCommand {
  uint32 node_id = 1;
  string version = 2 [(nanopb).max_length = 8];
  string qr_code = 3 [(nanopb).max_length = 12];

  repeated ThreePinInput three_pin_inputs = 4 [(nanopb).max_count = 4];
  repeated TwoPinInput two_pin_inputs = 5 [(nanopb).max_count = 2];
  ADCInput adc_inputs = 6;

  repeated Output outputs = 7 [(nanopb).max_count = 4];
  ZeroToTenVoltConfig zero_to_ten_volt_config = 8;
  KlevernessConnector kleverness_connector = 9;

  message ThreePinInput {
    uint32 connector_id = 1;
    ConnectorType connector_type = 2;

    enum ConnectorType {
      TOGGLE = 0;
      MOMENTARY = 1;
    }
  }

  message TwoPinInput {
    uint32 connector_id = 1;
    ConnectorType connector_type = 2;

    enum ConnectorType {
      PIR = 0;
      MOMENTARY = 1;
      DOOR_SENSOR = 2;
    }
  }

  message ADCInput {
    oneof range {
      OffsetScalingFactorRange offset_scaling = 1;
      MinMaxRange min_max = 2;
    }
    ConnectorType connector_type = 3;

    enum ConnectorType {
      KNOB = 0;
      THERMOSTAT = 1;
    }

    message OffsetScalingFactorRange {
      float offset = 1;
      float scaling_factor = 2;
    }

    message MinMaxRange {
      float min = 1;
      float max = 2;
    }
  }

  message ZeroToTenVoltConfig {
    Type type = 1;
    bool use_relay = 2;
    uint32 relay_connector_id = 3 [(nanopb).int_size = IS_8];
    float min_brightness = 4;
    float max_brightness = 5;

    enum Type {
      SOURCING = 0;
      SINKING = 1;
    }
  }

  message Output {
    uint32 connector_id = 1;
    ConnectorType connector_type = 2;

    enum ConnectorType {
      Relay = 0;
    }
  }

  message KlevernessConnector {
    bool is_enabled = 1;
    bool button1_enabled = 2;
    bool button2_enabled = 3;
    bool button3_enabled = 4;
    bool low_light_led_enabled = 5;
    bool high_light_led_enabled = 6;
  }
}

message StartDimmingCommand {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];
  uint32 brightness = 2 [(nanopb).int_size = IS_8];
  uint32 dim_speed_msec = 3;
}

message StopDimmingCommand {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];
}

message DimmingStateCommand {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];
  uint32 brightness = 2 [(nanopb).int_size = IS_8];
}

message ToggleRelayCommand {
  uint32 connector_id = 1;
  State state = 2;

  enum State {
    On = 0;
    Off = 1;
  }
}
