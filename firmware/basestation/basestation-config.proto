// Compile me into c with
// python lib/nanopb-0.4.9.1-macosx-x86/generator/nanopb_generator.py basestation-config.proto

syntax = "proto3";

import "nanopb.proto";

/**
* Basestation Main Schema (config + state + metadata)
*/

message ActiveConfiguration {
  BasestationConfig config = 1;
  BasestationState state = 2;
}

/**
* Basestation Update Message
*/

message BasestationUpdateMessage {
  string qr_code = 1 [(nanopb).max_length = 12];
  BasestationConfig config = 2;
}

/**
* Basestation Configuration
*/

message BasestationConfig {
  // Unique identifier for this basestation on the network
  uint32 id = 1 [(nanopb).int_size = IS_8];
  // Version number incremented on each config change
  string version = 2 [(nanopb).max_length = 8];
  // Configuration for radio-frequency devices
  RFConfig rf_config = 3;
  // Configurations for the inputs
  repeated CanboConfig canbo_configs = 4 [(nanopb).max_count = 20];
  repeated RFReedSensorConfig rf_reed_configs = 5 [(nanopb).max_count = 10];
  // Configurations for the lights
  repeated LightConfig lights = 6 [(nanopb).max_count = 20];
  // Maps CAN and RF devices to their QR code
  repeated NodeQRMapping node_qr_mappings = 7 [(nanopb).max_count = 20];

  WifiConfig wifi_config = 8;

  message RFConfig {
    uint32 channel = 1 [(nanopb).int_size = IS_8];
    uint32 network = 2 [(nanopb).int_size = IS_16];
  }

  message WifiConfig {
    string ssid = 1 [(nanopb).max_length = 32];
    string password = 2 [(nanopb).max_length = 32];
  }

  message NodeQRMapping {
    // The QR code string value for this device
    string qr_code = 1 [(nanopb).max_length = 12];
    // Either the CAN or the RF node ID
    uint32 node_id = 2 [(nanopb).int_size = IS_8];
    DeviceType type = 3;

    enum DeviceType {
      CAN = 0;
      RF = 1;
    }
  }
}

message RFReedSensorConfig {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];

  repeated Action door_close = 2 [(nanopb).max_count = 5];
  repeated Action door_open = 3 [(nanopb).max_count = 5];

  message Action {
    uint32 dim_speed_msec = 1 [(nanopb).int_size = IS_32];
    uint32 light_id = 2 [(nanopb).int_size = IS_8];
    float target_brightness = 3;
    uint32 activate_delay_msec = 4 [(nanopb).int_size = IS_32];
  }
}

message CanboConfig {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];

  repeated ThreePinInput three_pin_inputs = 2 [(nanopb).max_count = 4];
  repeated TwoPinInput two_pin_inputs = 3 [(nanopb).max_count = 2];

  message ThreePinInput {
    uint32 connector_id = 1 [(nanopb).int_size = IS_8];
    ConnectorType type = 2;

    enum ConnectorType {
      TOGGLE = 0;
      MOMENTARY = 1;
    }

    oneof config {
      ToggleConfig toggle = 3;
      MomentaryConfig momentary = 4;
    }

    message ToggleConfig {
      repeated Action up_click = 1 [(nanopb).max_count = 5];
      repeated Action up_hold = 2 [(nanopb).max_count = 5];
      repeated Action down_click = 3 [(nanopb).max_count = 5];
      repeated Action down_hold = 4 [(nanopb).max_count = 5];
    }

    message MomentaryConfig {
      repeated Action up_click = 1 [(nanopb).max_count = 5];
      repeated Action up_hold = 2 [(nanopb).max_count = 5];
    }

    message Action {
      uint32 dim_speed_msec = 1 [(nanopb).int_size = IS_32];
      uint32 light_id = 2 [(nanopb).int_size = IS_8];
      float target_brightness = 3;
      float on_brightness = 4;
      float off_brightness = 5;
    }
  }

  message TwoPinInput {
    uint32 connector_id = 1 [(nanopb).int_size = IS_8];
    ConnectorType type = 2;

    enum ConnectorType {
      PIR = 0;
      MOMENTARY = 1;
      DOOR_SENSOR = 2;
    }

    oneof config {
      PIRConfig pir = 3;
      MomentaryConfig momentary = 4;
      DoorSensorConfig door_sensor = 5;
    }

    message PIRConfig {
      repeated Action on_activate = 1 [(nanopb).max_count = 5];
      repeated Action on_deactivate = 2 [(nanopb).max_count = 5];
    }

    message MomentaryConfig {
      repeated Action up_click = 1 [(nanopb).max_count = 5];
      repeated Action up_hold = 2 [(nanopb).max_count = 5];
    }

    message DoorSensorConfig {
      repeated Action on_open = 1 [(nanopb).max_count = 5];
      repeated Action on_close = 2 [(nanopb).max_count = 5];
    }

    message Action {
      uint32 dim_speed_msec = 1 [(nanopb).int_size = IS_32];
      uint32 light_id = 2 [(nanopb).int_size = IS_8];
      uint32 delay_in_msec = 3 [(nanopb).int_size = IS_32];
      float target_brightness = 4;
      float on_brightness = 5;
      float off_brightness = 6;
    }
  }
}

message LightConfig {
  uint32 id = 1 [(nanopb).int_size = IS_8];
  // How fast the light dims in milliseconds
  uint32 dim_speed_msec = 2 [(nanopb).int_size = IS_32];
  // Configurations for the fixtures
  repeated FixtureConfig fixtures = 3 [(nanopb).max_count = 5];

  message FixtureConfig {
    // Min and max brightness of the fixture
    float min_brightness = 1;
    float max_brightness = 2;

    // Configuration for the fixture
    FixtureType type = 3;

    enum FixtureType {
      DMX = 0;
      RF = 1;
      ZERO_TO_TEN_VOLT = 2;
      ON_OFF_LIGHT = 3;
    }

    oneof config {
      DMXConfig dmx = 4;
      RFConfig rf = 5;
      ZeroToTenVoltConfig zero_to_ten_volt = 6;
      OnOffLightConfig on_off_light = 7;
      // Analog0To10VConfig analog_0_10 = 7;
    }

    message DMXConfig {
      LightParams params = 1;
      RGBConfig rgb = 2;
      repeated uint32 channels = 3 [(nanopb).max_count = 10, (nanopb).int_size = IS_8];
      FixtureTypeConfig type = 4;

      enum FixtureTypeConfig {
        D4 = 0;
        TUNABLE_WHITE = 1;
        ELV = 2;
        DF_12 = 3;
        EST = 4;
        RGB_STRIP = 5;
      }

      message LightParams {
        float min1 = 1;
        float max1 = 2;
        float gamma1 = 3;
        float min2 = 4;
        float max2 = 5;
        float gamma2 = 6;
      }

      message RGBConfig {
        uint32 red = 1 [(nanopb).int_size = IS_8];
        uint32 green = 2 [(nanopb).int_size = IS_8];
        uint32 blue = 3 [(nanopb).int_size = IS_8];
      }
    }

    message RFConfig {
      Type type = 1;
      uint32 node_id = 2 [(nanopb).int_size = IS_8];

      enum Type {
        DIMMER = 0;
        SWITCH = 1;
      }
    }

    message ZeroToTenVoltConfig {
      Type type = 1;
      uint32 node_id = 2 [(nanopb).int_size = IS_8];
      bool use_relay = 3;
      uint32 out_connector_id = 4 [(nanopb).int_size = IS_8];

      enum Type {
        SOURCING = 0;
        SINKING = 1;
      }
    }

    message OnOffLightConfig {
      uint32 node_id = 1 [(nanopb).int_size = IS_8];
      uint32 out_connector_id = 2 [(nanopb).int_size = IS_8];
    }

    // message Analog0To10VConfig {
    //   repeated uint32 channels = 1 [(nanopb).max_count = 10, (nanopb).int_size = IS_8];
    // }
  }
}


/**
* Basestation State
*/

message BasestationState {
  repeated LightState lights = 1 [(nanopb).max_count = 10];
  repeated ButtonState buttons = 2 [(nanopb).max_count = 10];
  repeated ProvisioningState provisioned_devices = 3 [(nanopb).max_count = 10];
  repeated RFReedState reeds = 4 [(nanopb).max_count = 10];
}

message RFReedState {
    uint32 node_id = 1 [(nanopb).int_size = IS_8];
    Status sensor_status = 2;
    uint64 last_modified_time = 3;
    float battery_voltage = 4;

    enum Status {
      UNKNOWN = 0;
      OPEN = 1;
      CLOSED = 2;
    }
}

message ProvisioningState {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];
  bool is_provisioned = 2;
  ProvisioningErrorCode error_code = 3;
  uint64 last_seen_time = 4;

  enum ProvisioningErrorCode {
    NONE = 0;
    NOT_FOUND = 1;
    NO_CANBO_CONFIG = 2;
    COULD_NOT_SEND_PROVISIONING_COMMAND = 3;
    NO_REED_CONFIG = 4;
  }
}

message LightState {
  uint32 id = 1 [(nanopb).int_size = IS_8];
  float brightness = 2;
  float target_value = 3;
  uint32 dim_speed_msec = 4 [(nanopb).int_size = IS_32];
  uint64 last_modified_time = 5;
  uint64 active_after_time = 6;
  bool is_transitioning = 7;
}

message ButtonState {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];
  State current_state = 2;
  uint64 last_modified_time = 3;

  enum State {
    BUTTON_STATE_RELEASED = 0;
    BUTTON_STATE_UP_PRESSED = 1;
    BUTTON_STATE_DOWN_PRESSED = 2;
  }
}