{"cmake.options.statusBarVisibility": "visible", "cmake.options.advanced": {"build": {"statusBarVisibility": "hidden"}, "launch": {"statusBarVisibility": "hidden"}, "debug": {"statusBarVisibility": "hidden"}}, "cmake.configureOnEdit": true, "cmake.automaticReconfigure": true, "cmake.configureOnOpen": true, "cmake.generator": "Ninja", "cmake.cmakePath": "${userHome}/.pico-sdk/cmake/v3.31.5/bin/cmake", "C_Cpp.debugShortcut": false, "terminal.integrated.env.windows": {"PICO_SDK_PATH": "${env:USERPROFILE}/.pico-sdk/sdk/2.1.1", "PICO_TOOLCHAIN_PATH": "${env:USERPROFILE}/.pico-sdk/toolchain/14_2_Rel1", "Path": "${env:USERPROFILE}/.pico-sdk/toolchain/14_2_Rel1/bin;${env:USERPROFILE}/.pico-sdk/picotool/2.1.1/picotool;${env:USERPROFILE}/.pico-sdk/cmake/v3.31.5/bin;${env:USERPROFILE}/.pico-sdk/ninja/v1.12.1;${env:PATH}"}, "terminal.integrated.env.osx": {"PICO_SDK_PATH": "${env:HOME}/.pico-sdk/sdk/2.1.1", "PICO_TOOLCHAIN_PATH": "${env:HOME}/.pico-sdk/toolchain/14_2_Rel1", "PATH": "${env:HOME}/.pico-sdk/toolchain/14_2_Rel1/bin:${env:HOME}/.pico-sdk/picotool/2.1.1/picotool:${env:HOME}/.pico-sdk/cmake/v3.31.5/bin:${env:HOME}/.pico-sdk/ninja/v1.12.1:${env:PATH}"}, "terminal.integrated.env.linux": {"PICO_SDK_PATH": "${env:HOME}/.pico-sdk/sdk/2.1.1", "PICO_TOOLCHAIN_PATH": "${env:HOME}/.pico-sdk/toolchain/14_2_Rel1", "PATH": "${env:HOME}/.pico-sdk/toolchain/14_2_Rel1/bin:${env:HOME}/.pico-sdk/picotool/2.1.1/picotool:${env:HOME}/.pico-sdk/cmake/v3.31.5/bin:${env:HOME}/.pico-sdk/ninja/v1.12.1:${env:PATH}"}, "raspberry-pi-pico.cmakeAutoConfigure": false, "raspberry-pi-pico.useCmakeTools": true, "raspberry-pi-pico.cmakePath": "${HOME}/.pico-sdk/cmake/v3.31.5/bin/cmake", "raspberry-pi-pico.ninjaPath": "${HOME}/.pico-sdk/ninja/v1.12.1/ninja", "files.associations": {"array": "cpp", "atomic": "cpp", "bit": "cpp", "*.tcc": "cpp", "cctype": "cpp", "charconv": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "format": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "span": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "text_encoding": "cpp", "cfenv": "cpp", "cinttypes": "cpp", "typeinfo": "cpp", "variant": "cpp", "cyw43_arch.h": "c", "time.h": "c", "wizchip_conf.h": "c", "timer.h": "c", "flash.h": "c", "active-configuration.h": "c", "basestation-config.pb.h": "c", "events.h": "c", "bitset": "cpp", "condition_variable": "cpp", "regex": "cpp", "future": "cpp", "mutex": "cpp", "semaphore": "cpp", "stop_token": "cpp", "thread": "cpp", "sync.h": "c", "async_context_freertos.h": "c", "dmxinput.h": "c", "can_task.h": "c", "uart_task.h": "c", "uart.h": "c", "spi.h": "c", "rf4463spi.h": "c", "types.h": "c", "pb_encode.h": "c", "commands.pb.h": "c", "event_task.h": "c", "main.h": "c", "stddef.h": "c", "active_configuration.h": "c", "io_task.h": "c", "flash_storage.h": "c", "multicore.h": "c", "string.h": "c", "unique_id.h": "c"}, "idf.pythonInstallPath": "/Library/Frameworks/Python.framework/Versions/3.13/bin/python3"}