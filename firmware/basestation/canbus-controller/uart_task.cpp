#include "uart_task.h"

// ============================================================================
// UART TASK IMPLEMENTATION
// ============================================================================
// 
// This module implements UART command processing for the CANbus controller.
// It provides a command-line interface for device configuration and debugging.
//
// Supported Commands:
// - "QR|<qr_code>"     : Sets the QR code for device identification
// - "reprovision"      : Resets device provisioning while preserving QR code
// - "debug"            : Sets a predefined debug configuration for testing
//
// Thread Safety:
// - All configuration operations use thread-safe functions with mutex protection
// - Flash storage operations use synchronous versions to ensure completion
// - Memory operations are protected against concurrent access
//
// Flash Storage:
// - All persistent operations use _sync() versions to ensure completion
// - Operations include proper error handling and verification
// - Data integrity is verified through read-back operations
//
// Error Handling:
// - All operations include comprehensive error checking
// - Failed operations are reported with descriptive messages
// - Critical failures cause early return to prevent inconsistent state
// ============================================================================

// Local Instances/Variables
UARTParams_t uart_params[ NUM_UART_CHANNELS ] = UART_PARAMS_DEFAULT();

// TODO: Reduce these buffers and variables to a structure
    volatile size_t usUARTBufferInSize = 0;
    volatile uint8_t pcUARTInTemp[ UART_MAX_BUFFER_IN_LENGTH ];
    char pcUARTInBuffer[ UART_MAX_BUFFER_IN_LENGTH ];
    char pcUARTOutBuffer[ UART_MAX_BUFFER_OUT_LENGTH ];
// END OF TODO
TaskHandle_t xUART0RxTaskHandle;

// ISRs / callbacks
void vUART0RxCallback(){
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;

    // Read all available characters from UART
    while( uart_is_readable( uart_params[ 0 ].uart_inst ) ){
        pcUARTInTemp[ usUARTBufferInSize++ ] = uart_getc( uart_params[ 0 ].uart_inst );

        // Check for line termination (\r\n)
        if( pcUARTInTemp[ usUARTBufferInSize - 2 ] == '\r' && pcUARTInTemp[ usUARTBufferInSize - 1 ] ==  '\n' ){
            // Copy complete line to processing buffer (excluding \r\n)
            memcpy( pcUARTInBuffer, ( void * )pcUARTInTemp, usUARTBufferInSize -2 );

            // Notify UART task that a complete command is ready for processing
            vTaskNotifyGiveFromISR( xUART0RxTaskHandle, &xHigherPriorityTaskWoken );
            portYIELD_FROM_ISR( xHigherPriorityTaskWoken );
        }
    }
}

// Helper function to fill debug config
/**
 * @brief Fills a CanboProvisioningCommand struct with one of 5 debug configurations.
 *
 * Config 1: (General default)
 *   - Node ID: 0x0F, Version: 11118735
 *   - 4x three-pin toggle, 2x two-pin momentary
 *   - ADC: KNOB, min/max 0.0-1.0
 *   - Zero-to-ten volt: SOURCING, relay enabled, full brightness range
 *   - Kleverness connector: disabled
 *
 * Config 2: (More two-pin, thermostat ADC, kleverness partial)
 *   - Node ID: 0x10, Version: 22229999
 *   - 2x three-pin toggle, 4x two-pin momentary
 *   - ADC: THERMOSTAT, offset/scaling 15.0/20.0 (for 5-25°C range)
 *   - Zero-to-ten volt: SINKING, relay disabled, brightness 0.1-0.9
 *   - Kleverness: enabled, button1+low LED only
 *
 * Config 3: (Minimal IO, all kleverness features)
 *   - Node ID: 0x11, Version: 33334444
 *   - 1x three-pin toggle, 1x two-pin momentary
 *   - ADC: none
 *   - Zero-to-ten volt: none
 *   - Kleverness: enabled, all buttons, high LED only
 *
 * Config 4: (ADC only, partial zero-to-ten volt)
 *   - Node ID: 0x12, Version: 44445555
 *   - No three/two-pin inputs
 *   - ADC: KNOB, min/max 0.2-0.8
 *   - Zero-to-ten volt: SOURCING, relay disabled, brightness 0.2-0.8
 *   - Kleverness: disabled
 *
 * Config 5: (Mixed IO, thermostat ADC, all relay/LED features)
 *   - Node ID: 0x13, Version: 55556666
 *   - 3x three-pin toggle, 3x two-pin momentary
 *   - ADC: THERMOSTAT, offset/scaling 10.0/20.0 (for 0-20°C range)
 *   - Zero-to-ten volt: SINKING, relay enabled, full brightness
 *   - Kleverness: enabled, button2+both LEDs
 *
 * The QR code is fixed for all configs.
 *
 * @param config Pointer to struct to fill
 * @param config_num Which config to use (1-5)
 */
static void fill_debug_config(CanboProvisioningCommand* config, int config_num) {
    *config = CanboProvisioningCommand_init_zero;
    // Fixed QR code for all debug configs
    strcpy(config->qr_code, "SC1ABC000001");
    switch (config_num) {
        case 1:
            config->node_id = 0x0F;
            strcpy(config->version, "11118735");
            config->three_pin_inputs_count = 4;
            for (int i = 0; i < 4; ++i) {
                config->three_pin_inputs[i].connector_id = i + 1;
                config->three_pin_inputs[i].connector_type = CanboProvisioningCommand_ThreePinInput_ConnectorType_TOGGLE;
            }
            config->two_pin_inputs_count = 2;
            for (int i = 0; i < 2; ++i) {
                config->two_pin_inputs[i].connector_id = i + 1;
                config->two_pin_inputs[i].connector_type = CanboProvisioningCommand_TwoPinInput_ConnectorType_MOMENTARY;
            }
            config->has_adc_inputs = true;
            config->adc_inputs.connector_type = CanboProvisioningCommand_ADCInput_ConnectorType_KNOB;
            config->adc_inputs.which_range = CanboProvisioningCommand_ADCInput_MinMaxRange_min_tag;
            config->adc_inputs.range.min_max.min = 0.0f;
            config->adc_inputs.range.min_max.max = 1.0f;
            config->has_zero_to_ten_volt_config = true;
            config->zero_to_ten_volt_config.type = CanboProvisioningCommand_ZeroToTenVoltConfig_Type_SOURCING;
            config->zero_to_ten_volt_config.use_relay = true;
            config->zero_to_ten_volt_config.relay_connector_id = 1;
            config->zero_to_ten_volt_config.min_brightness = 0.0f;
            config->zero_to_ten_volt_config.max_brightness = 1.0f;
            config->has_kleverness_connector = false;
            config->kleverness_connector.is_enabled = false;
            break;
        case 2:
            config->node_id = 0x10;
            strcpy(config->version, "22229999");
            config->three_pin_inputs_count = 2;
            for (int i = 0; i < 2; ++i) {
                config->three_pin_inputs[i].connector_id = i + 1;
                config->three_pin_inputs[i].connector_type = CanboProvisioningCommand_ThreePinInput_ConnectorType_TOGGLE;
            }
            config->two_pin_inputs_count = 2;
            for (int i = 0; i < 2; ++i) {
                config->two_pin_inputs[i].connector_id = i + 1;
                config->two_pin_inputs[i].connector_type = CanboProvisioningCommand_TwoPinInput_ConnectorType_MOMENTARY;
            }
            config->has_adc_inputs = true;
            config->adc_inputs.connector_type = CanboProvisioningCommand_ADCInput_ConnectorType_THERMOSTAT;
            config->adc_inputs.which_range = CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange_offset_tag;
            config->adc_inputs.range.offset_scaling.offset = 15.0f;
            config->adc_inputs.range.offset_scaling.scaling_factor = 20.0f;
            config->has_zero_to_ten_volt_config = true;
            config->zero_to_ten_volt_config.type = CanboProvisioningCommand_ZeroToTenVoltConfig_Type_SINKING;
            config->zero_to_ten_volt_config.use_relay = false;
            config->zero_to_ten_volt_config.relay_connector_id = 2;
            config->zero_to_ten_volt_config.min_brightness = 0.1f;
            config->zero_to_ten_volt_config.max_brightness = 0.9f;
            config->has_kleverness_connector = true;
            config->kleverness_connector.is_enabled = true;
            config->kleverness_connector.button1_enabled = true;
            config->kleverness_connector.button2_enabled = false;
            config->kleverness_connector.button3_enabled = false;
            config->kleverness_connector.low_light_led_enabled = true;
            config->kleverness_connector.high_light_led_enabled = false;
            break;
        case 3:
            config->node_id = 0x11;
            strcpy(config->version, "33334444");
            config->three_pin_inputs_count = 1;
            config->three_pin_inputs[0].connector_id = 1;
            config->three_pin_inputs[0].connector_type = CanboProvisioningCommand_ThreePinInput_ConnectorType_TOGGLE;
            config->two_pin_inputs_count = 1;
            config->two_pin_inputs[0].connector_id = 1;
            config->two_pin_inputs[0].connector_type = CanboProvisioningCommand_TwoPinInput_ConnectorType_MOMENTARY;
            config->has_adc_inputs = false;
            config->has_zero_to_ten_volt_config = false;
            config->has_kleverness_connector = true;
            config->kleverness_connector.is_enabled = true;
            config->kleverness_connector.button1_enabled = true;
            config->kleverness_connector.button2_enabled = true;
            config->kleverness_connector.button3_enabled = true;
            config->kleverness_connector.low_light_led_enabled = false;
            config->kleverness_connector.high_light_led_enabled = true;
            break;
        case 4:
            config->node_id = 0x12;
            strcpy(config->version, "44445555");
            config->three_pin_inputs_count = 0;
            config->two_pin_inputs_count = 0;
            config->has_adc_inputs = true;
            config->adc_inputs.connector_type = CanboProvisioningCommand_ADCInput_ConnectorType_KNOB;
            config->adc_inputs.which_range = CanboProvisioningCommand_ADCInput_MinMaxRange_min_tag;
            config->adc_inputs.range.min_max.min = 0.2f;
            config->adc_inputs.range.min_max.max = 0.8f;
            config->has_zero_to_ten_volt_config = true;
            config->zero_to_ten_volt_config.type = CanboProvisioningCommand_ZeroToTenVoltConfig_Type_SOURCING;
            config->zero_to_ten_volt_config.use_relay = false;
            config->zero_to_ten_volt_config.relay_connector_id = 3;
            config->zero_to_ten_volt_config.min_brightness = 0.2f;
            config->zero_to_ten_volt_config.max_brightness = 0.8f;
            config->has_kleverness_connector = false;
            break;
        case 5:
        default:
            config->node_id = 0x13;
            strcpy(config->version, "55556666");
            config->three_pin_inputs_count = 3;
            for (int i = 0; i < 3; ++i) {
                config->three_pin_inputs[i].connector_id = i + 1;
                config->three_pin_inputs[i].connector_type = CanboProvisioningCommand_ThreePinInput_ConnectorType_TOGGLE;
            }
            config->two_pin_inputs_count = 2;
            for (int i = 0; i < 2; ++i) {
                config->two_pin_inputs[i].connector_id = i + 1;
                config->two_pin_inputs[i].connector_type = CanboProvisioningCommand_TwoPinInput_ConnectorType_MOMENTARY;
            }
            config->has_adc_inputs = true;
            config->adc_inputs.connector_type = CanboProvisioningCommand_ADCInput_ConnectorType_THERMOSTAT;
            config->adc_inputs.which_range = CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange_offset_tag;
            config->adc_inputs.range.offset_scaling.offset = 10.0f;
            config->adc_inputs.range.offset_scaling.scaling_factor = 20.0f;
            config->has_zero_to_ten_volt_config = true;
            config->zero_to_ten_volt_config.type = CanboProvisioningCommand_ZeroToTenVoltConfig_Type_SINKING;
            config->zero_to_ten_volt_config.use_relay = true;
            config->zero_to_ten_volt_config.relay_connector_id = 4;
            config->zero_to_ten_volt_config.min_brightness = 0.0f;
            config->zero_to_ten_volt_config.max_brightness = 1.0f;
            config->has_kleverness_connector = true;
            config->kleverness_connector.is_enabled = true;
            config->kleverness_connector.button1_enabled = false;
            config->kleverness_connector.button2_enabled = true;
            config->kleverness_connector.button3_enabled = false;
            config->kleverness_connector.low_light_led_enabled = true;
            config->kleverness_connector.high_light_led_enabled = true;
            break;
    }
}

// Tasks
void vUART0RxTask( void * pvParameters ){
    static uint32_t ulUART0RxEvent = 0;

    uart_params[ 0 ].irq_cb_func = (void *)&vUART0RxCallback;
    uart_params[ 0 ].irq_num = UART0_IRQ;
    uart_params[ 0 ].dma_rx.en = false;
    uart_params[ 0 ].dma_tx.en = false;

    ucUARTInit( &uart_params[ 0 ] );

    printf( "[START] UART task created\r\n");

    for(;;){
        ulUART0RxEvent = ulTaskNotifyTake( pdTRUE, portMAX_DELAY );
        if( ulUART0RxEvent > 0 ){
            #ifdef DEBUG_UART_BASIC
            printf( "[UART] Size: %d\r\n", usUARTBufferInSize );
            printf( "[UART] Fetched: %s\r\n", pcUARTInBuffer );
            printf( "[UART] Set: %s\r\n", pcUARTInBuffer );
            #endif

            if (strncmp(pcUARTInBuffer, "QR|", 3) == 0) {
                // ============================================================================
                // QR CODE SETTING COMMAND HANDLER
                // ============================================================================
                // Command format: "QR|<qr_code_string>"
                // Purpose: Sets the QR code for device identification and provisioning
                // 
                // Process:
                // 1. Parse QR code from command (up to 12 characters after "QR|")
                // 2. Validate QR code length (must be 1-12 characters)
                // 3. Update active configuration in memory (thread-safe)
                // 4. Persist configuration to flash storage (synchronous operation)
                // 5. Verify flash write operation completed successfully
                
                // Extract QR code from command - copy up to 12 characters after "QR|"
                char qr_buf[sizeof(active_configuration.qr_code)] = "";
                size_t len = strnlen(pcUARTInBuffer + 3, sizeof(active_configuration.qr_code));
                strncpy(qr_buf, pcUARTInBuffer + 3, len);
                qr_buf[len] = '\0';  // Ensure NULL termination

                if (len > 0) {
                    #ifdef DEBUG_UART_QR
                    printf("> Setting QR code to: \"%s\"\r\n", qr_buf);
                    #endif

                    // Step 1: Update the configuration in memory (thread-safe operation)
                    if (!set_active_config_qr_code(qr_buf)) {
                        printf("> Error: Failed to set QR code in memory\r\n");
                    } else {
                        // Step 2: Get the complete configuration for flash storage
                        CanboProvisioningCommand config;
                        if (get_active_configuration(&config)) {
                            // Step 3: Persist to flash storage with synchronous operation
                            // Using sync version to ensure write completion before proceeding
                            bool success = flash_storage_set_provisioning_command_sync(&config);
                            
                            if (!success) {
                                printf("> Error setting QR code (flash operation failed)\r\n");
                            } else {
                                #ifdef DEBUG_UART_QR
                                printf("> QR successfully set to: \"%s\"\r\n", qr_buf);
                                #endif
                            }
                        } else {
                            printf("> Error: Failed to get configuration for flash storage\r\n");
                        }
                    }
                }
                else {
                    printf("> Invalid QR string (must be 1-12 chars)\r\n");
                }
            } else if (strncmp(pcUARTInBuffer, "reprovision", 11) == 0) {
                // ============================================================================
                // REPROVISIONING COMMAND HANDLER
                // ============================================================================
                // Command: "reprovision"
                // Purpose: Resets device provisioning while preserving QR code
                // 
                // Process:
                // 1. Save current QR code before resetting
                // 2. Reset configuration structure in memory (thread-safe)
                // 3. Reset flash storage (synchronous operation)
                // 4. Restore QR code to memory (thread-safe)
                // 5. Write updated configuration to flash (synchronous operation)
                // 6. Verify flash write by reading back configuration
                // 7. Set provisioned status to false (synchronous operation)
                // 8. Display final QR code for user verification
                
                #ifdef DEBUG_UART_REPROVISION
                printf("> Undoing provision...\r\n");
                #endif

                #ifdef DEBUG_UART_REPROVISION_DEEP
                printf("> About to get current QR code\r\n");
                #endif

                // Step 1: Save the current QR code before resetting
                // This preserves the QR code identity while clearing other provisioning data
                char qr_read[256] = "";
                if (!get_active_config_qr_code(qr_read, sizeof(qr_read))) {
                    printf("> Error: Failed to get current QR code\r\n");
                    return;
                }
                #ifdef DEBUG_UART_REPROVISION_DEEP
                printf("> Successfully got current QR code\r\n");
                printf("> Prior QR code: \"%s\"\r\n", qr_read);
                printf("> About to reset configuration in memory\r\n");
                #endif

                // Step 2: Reset the configuration structure safely in memory
                // This clears all provisioning data except the QR code we just saved
                CanboProvisioningCommand empty_config = CanboProvisioningCommand_init_zero;
                // Ensure all fields are valid for proto constraints
                empty_config.three_pin_inputs_count = 0;
                empty_config.two_pin_inputs_count = 0;
                empty_config.outputs_count = 0;
                empty_config.has_adc_inputs = false;
                empty_config.has_zero_to_ten_volt_config = false;
                empty_config.has_kleverness_connector = false;
                // Set QR code to preserved value
                strncpy(empty_config.qr_code, qr_read, sizeof(empty_config.qr_code) - 1);
                empty_config.qr_code[sizeof(empty_config.qr_code) - 1] = '\0';
                if (!set_active_configuration(&empty_config)) {
                    printf("> Error: Failed to reset configuration in memory\r\n");
                    return;
                }
                #ifdef DEBUG_UART_REPROVISION_DEEP
                printf("> Successfully reset configuration in memory\r\n");
                printf("> About to call flash_reset_sync()\r\n");
                #endif

                // Step 3: Reset flash storage with synchronous operation
                // This ensures the flash reset completes before proceeding
                bool reset_success = flash_reset_sync();
                #ifdef DEBUG_UART_REPROVISION_DEEP
                printf("> flash_reset_sync() returned: %s\r\n", reset_success ? "true" : "false");
                #endif
                if (!reset_success) {
                    #ifdef DEBUG_UART_REPROVISION
                    printf("> Flash reset failed - continuing anyway\r\n");
                    #endif
                } else {
                    #ifdef DEBUG_UART_REPROVISION
                    printf("> Flash storage reset complete\r\n");
                    #endif
                }

                // Step 4: Restore QR code to memory safely
                // This ensures the device retains its QR code identity
                if (!set_active_config_qr_code(qr_read)) {
                    printf("> Error: Failed to restore QR code to memory\r\n");
                    return;
                }
                #ifdef DEBUG_UART_REPROVISION_DEEP
                printf("> Successfully restored QR code to memory\r\n");
                printf("> Writing QR code to flash storage\r\n");
                printf("> About to get active configuration\r\n");
                #endif

                // Step 5: Write the updated configuration to flash storage
                // Using synchronous operation to ensure write completion
                CanboProvisioningCommand config;
                if (get_active_configuration(&config)) {
                    #ifdef DEBUG_UART_REPROVISION_DEEP
                    printf("> Successfully got active configuration\r\n");
                    printf("> About to call flash_storage_set_provisioning_command_sync()\r\n");
                    #endif
                    bool write_success = flash_storage_set_provisioning_command_sync(&config);
                    #ifdef DEBUG_UART_REPROVISION_DEEP
                    printf("> flash_storage_set_provisioning_command_sync() returned: %s\r\n", write_success ? "true" : "false");
                    #endif
                    if (!write_success) {
                        #ifdef DEBUG_UART_REPROVISION
                        printf("> Warning: Failed to write configuration to flash\r\n");
                        #endif
                    } else {
                        #ifdef DEBUG_UART_REPROVISION
                        printf("> Configuration written to flash\r\n");
                        #endif
                    }
                } else {
                    printf("> Error: Failed to get active configuration\r\n");
                }

                // Step 6: Read back configuration to verify flash write
                // This ensures data integrity and confirms the operation completed
                #ifdef DEBUG_UART_REPROVISION_DEEP
                printf("> About to read back configuration\r\n");
                #endif
                CanboProvisioningCommand config_read;
                bool read_success = flash_storage_get_provisioning_command(&config_read);
                #ifdef DEBUG_UART_REPROVISION_DEEP
                printf("> flash_storage_get_provisioning_command() returned: %s\r\n", read_success ? "true" : "false");
                #endif
                if (!read_success) {
                    #ifdef DEBUG_UART_REPROVISION
                    printf("> Warning: Failed to read back configuration\r\n");
                    #endif
                } else {
                    #ifdef DEBUG_UART_REPROVISION_DEEP
                    printf("> Successfully read back configuration\r\n");
                    printf("> About to set active configuration from read data\r\n");
                    #endif
                    // Update memory with verified flash data
                    set_active_configuration(&config_read);
                    #ifdef DEBUG_UART_REPROVISION_DEEP
                    printf("> Successfully set active configuration from read data\r\n");
                    printf("> About to get QR code for display\r\n");
                    #endif
                }

                // Step 7: Get the QR code for display and verification
                char current_qr[256];
                if (get_active_config_qr_code(current_qr, sizeof(current_qr))) {
                    #ifdef DEBUG_UART_REPROVISION_DEEP
                    printf("> Successfully got QR code for display\r\n");
                    printf("> Post QR code: \"%s\"\r\n", current_qr);
                    printf("> About to set provisioned status\r\n");
                    #endif
                } else {
                    printf("> Error: Failed to get QR code for display\r\n");
                }

                // Step 8: Mark device as not provisioned with synchronous operation
                // This ensures the provisioned status is properly set in flash
                bool provision_success = flash_storage_set_provisioned_sync(false);
                #ifdef DEBUG_UART_REPROVISION_DEEP
                printf("> flash_storage_set_provisioned_sync() returned: %s\r\n", provision_success ? "true" : "false");
                #endif
                if (!provision_success) {
                    #ifdef DEBUG_UART_REPROVISION
                    printf("> Warning: Failed to set provisioned status\r\n");
                    #endif
                }
                
                #ifdef DEBUG_UART_REPROVISION
                printf("> Provision successfully erased!\r\n");
                #endif
            } else if (strncmp(pcUARTInBuffer, "debug", 5) == 0) {
                // ============================================================================
                // DEBUG CONFIGURATION COMMAND HANDLER (MULTIPLE PROFILES)
                // ============================================================================
                // Command: "debug [n]"
                // Purpose: Sets one of several predefined debug configurations for testing
                //
                // Process:
                // 1. Parse optional config number (1-5)
                // 2. Fill config struct with selected profile
                // 3. Set active configuration in memory (thread-safe)
                // 4. Persist configuration to flash storage (synchronous operation)
                // 5. Set provisioned status to true (synchronous operation)
                // 6. Verify configuration was applied successfully

                int config_num = 1;
                if (strlen(pcUARTInBuffer) > 6) {
                    config_num = atoi(pcUARTInBuffer + 6);
                    if (config_num < 1 || config_num > 5) config_num = 1;
                }

                CanboProvisioningCommand config;
                fill_debug_config(&config, config_num);

                // Step 1: Persist debug configuration to flash storage (synchronous operation)
                bool flash_success = flash_storage_set_provisioning_command_sync(&config);
                if (!flash_success) {
                    printf("> Error: Failed to write debug configuration to flash\r\n");
                    return;
                }

                // Step 1.5: Read-back verification
                CanboProvisioningCommand verify;
                bool ok = flash_storage_get_provisioning_command(&verify);
                printf("> Read-back after write: %s\r\n", ok ? "OK" : "FAIL");

                // Step 2: Set provisioned status to true (synchronous operation)
                bool provision_success = flash_storage_set_provisioned_sync(true);
                if (!provision_success) {
                    printf("> Error: Failed to set provisioned status\r\n");
                    return;
                }
                // Step 3: Update active configuration in memory (thread-safe operation)
                if (!set_active_configuration(&config)) {
                    printf("> Error: Failed to set active configuration in memory\r\n");
                    return;
                }

                printf("> Debug Configuration %d Set!\r\n", config_num);
#ifdef DEBUG_CONFIGURATION_SET
                printf("> Node ID: 0x%02X\r\n", config.node_id);
                printf("> QR Code: %s\r\n", config.qr_code);
                printf("> Version: %s\r\n", config.version);
                printf("> Three-pin inputs: %d\r\n", config.three_pin_inputs_count);
                printf("> Two-pin inputs: %d\r\n", config.two_pin_inputs_count);
                printf("> ADC Present: %s\r\n", config.has_adc_inputs ? "Yes" : "No");
                if ( config.has_adc_inputs ){
                                    printf("> ADC Type: %s\r\n", config.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_KNOB ? "KNOB" : "THERMOSTAT");
                if (config.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_KNOB) {
                    printf("> ADC Min=%.2f, Max=%.2f\r\n",
                            config.adc_inputs.range.min_max.min, 
                            config.adc_inputs.range.min_max.max);
                } else if (config.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_THERMOSTAT) {
                    printf("> ADC Offset=%.2f, Scaling=%.2f\r\n",
                            config.adc_inputs.range.offset_scaling.offset, 
                            config.adc_inputs.range.offset_scaling.scaling_factor);
                }
                }
                printf("> Kleverness connector: %s\r\n", config.has_kleverness_connector ? "Enabled" : "Disabled");
                if (config.has_kleverness_connector) {
                    printf("> Kleverness Button1: %s\r\n", config.kleverness_connector.button1_enabled ? "Enabled" : "Disabled");
                    printf("> Kleverness Button2: %s\r\n", config.kleverness_connector.button2_enabled ? "Enabled" : "Disabled");
                    printf("> Kleverness Button3: %s\r\n", config.kleverness_connector.button3_enabled ? "Enabled" : "Disabled");
                    printf("> Kleverness Low Light LED: %s\r\n", config.kleverness_connector.low_light_led_enabled ? "Enabled" : "Disabled");
                    printf("> Kleverness High Light LED: %s\r\n", config.kleverness_connector.high_light_led_enabled ? "Enabled" : "Disabled");
                }
                printf("> Zero-to-ten volt config: %d\r\n", config.has_zero_to_ten_volt_config);
                printf("> Zero-to-ten volt config type: %d\r\n", config.zero_to_ten_volt_config.type);
                printf("> Zero-to-ten volt config use relay: %d\r\n", config.zero_to_ten_volt_config.use_relay);
                printf("> Zero-to-ten volt config relay connector ID: %d\r\n", config.zero_to_ten_volt_config.relay_connector_id);
                printf("> Zero-to-ten volt config min brightness: %f\r\n", config.zero_to_ten_volt_config.min_brightness);
                printf("> Zero-to-ten volt config max brightness: %f\r\n", config.zero_to_ten_volt_config.max_brightness);
#endif
            } else if (strncmp(pcUARTInBuffer, "dimmax", 6) == 0) {
                // ============================================================================
                // DIM TO MAX BRIGHTNESS COMMAND HANDLER
                // ============================================================================
                // Command format: "dimmax <time_ms>"
                // Purpose: Dims to maximum brightness over specified time
                // 
                // Process:
                // 1. Parse time parameter from command
                // 2. Set dimming target to 1.0 (100% brightness)
                // 3. Set dimming time to user-specified value
                // 4. Execute dimming transition
                
                uint32_t dim_time_ms = 1000; // Default 1 second
                
                // Parse time parameter if provided
                if (strlen(pcUARTInBuffer) > 7) {
                    char *time_str = pcUARTInBuffer + 7;
                    dim_time_ms = atoi(time_str);
                    // if (dim_time_ms == 0) {
                    //     dim_time_ms = 1000; // Default if parsing fails
                    // }
                }
                
                printf("> Dimming to max brightness over %d ms\r\n", dim_time_ms);
                vSetPWMDimVariables(1.0f, dim_time_ms);
                
            } else if (strncmp(pcUARTInBuffer, "dimmin", 6) == 0) {
                // ============================================================================
                // DIM TO MIN BRIGHTNESS COMMAND HANDLER
                // ============================================================================
                // Command format: "dimmin <time_ms>"
                // Purpose: Dims to minimum brightness over specified time
                // 
                // Process:
                // 1. Parse time parameter from command
                // 2. Set dimming target to 0.0 (0% brightness)
                // 3. Set dimming time to user-specified value
                // 4. Execute dimming transition
                
                uint32_t dim_time_ms = 1000; // Default 1 second
                
                // Parse time parameter if provided
                if (strlen(pcUARTInBuffer) > 7) {
                    char *time_str = pcUARTInBuffer + 7;
                    dim_time_ms = atoi(time_str);
                    // if (dim_time_ms == 0) {
                    //     dim_time_ms = 1000; // Default if parsing fails
                    // }
                }
                
                printf("> Dimming to min brightness over %d ms\r\n", dim_time_ms);
                vSetPWMDimVariables(0.0f, dim_time_ms);
                
            } else if (strncmp(pcUARTInBuffer, "stopdim", 7) == 0) {
                // ============================================================================
                // STOP DIMMING COMMAND HANDLER
                // ============================================================================
                // Command: "stopdim"
                // Purpose: Stops the current dimming transition and keeps current brightness
                // 
                // Process:
                // 1. Call stop dimming function
                // 2. Display current brightness level
                
                printf("> Stopping dimming transition\r\n");
                vStopPWMDimming();
                
                float current_level = fGetCurrentPWMDimLevel();
                printf("> Current brightness level: %.1f%%\r\n", current_level * 100.0f);
                
            } else if (strncmp(pcUARTInBuffer, "dimrelay", 8) == 0) {
                // ============================================================================
                // DIM RELAY COMMAND HANDLER
                // ============================================================================
                // Command: "dimrelay"
                // Purpose: Toggles the use of the relay for dimming
                // 
                // Process:
                // 1. Change the configuration between using the relay and not using the relay
                // 2. Display the new configuration
                
                printf("> Toggling relay use for dimming\r\n");
                
                CanboProvisioningCommand config = CanboProvisioningCommand_init_zero;
                if (get_active_configuration(&config)) {
                    config.zero_to_ten_volt_config.use_relay = !config.zero_to_ten_volt_config.use_relay;
                    if (!set_active_configuration(&config)) {
                        printf("> Error: Failed to set active configuration in memory\r\n");
                        return;
                    }
                    printf("> Relay use for dimming toggled\r\n");
                } else {
                    printf("> Error: Failed to get active configuration\r\n");
                }
                
            } else {
                printf("> Unknown command: \"%s\"\r\n", pcUARTInBuffer);
            }

            usUARTBufferInSize = 0;
            memset( ( void * )pcUARTInTemp, 0, UART_MAX_BUFFER_IN_LENGTH );
            memset( ( void * )pcUARTInBuffer, 0, UART_MAX_BUFFER_IN_LENGTH );
        }
    }
}
