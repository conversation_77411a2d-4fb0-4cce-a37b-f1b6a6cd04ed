#ifndef __UART_TASK_H__
#define __UART_TASK_H__

// C standard libraries
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

// pico sdk libraries
#include "FreeRTOS.h"
#include "task.h"

#include "hardware/dma.h"
#include "flash_storage.h"
#include "commands.pb.h"

// user libraries
#include "types.h"
#include "pinout.h"
#include "uart.h"
#include "can_task.h"
#include "io_task.h"
#include "defs.h"

// local definitions/enums
#define UART_MAX_BUFFER_OUT_LENGTH      ( unsigned int )64
#define UART_MAX_BUFFER_IN_LENGTH       ( unsigned int )64

extern TaskHandle_t xUART0RxTaskHandle;
extern TaskHandle_t xUART1RxTaskHandle;

// function prototyping

/**
 * @brief UART0 Receive Interrupt Service Routine
 * 
 * This ISR handles incoming UART data and buffers it until a complete line
 * is received (terminated by \r\n). When a complete line is detected, it
 * notifies the UART task to process the command.
 * 
 * Thread Safety: This ISR is called from interrupt context and uses
 * FreeRTOS ISR-safe functions for task notification.
 */
void vUART0RxCallback();

/**
 * @brief UART0 Receive Task
 * 
 * This task processes UART commands received from the interrupt service routine.
 * It provides a command-line interface for device configuration and debugging.
 * 
 * Supported Commands:
 * - "QR|<qr_code>"     : Sets the QR code for device identification
 * - "reprovision"      : Resets device provisioning while preserving QR code  
 * - "debug"            : Sets a predefined debug configuration for testing
 * 
 * Thread Safety: This task uses thread-safe functions for all configuration
 * operations and synchronous flash storage operations to ensure data integrity.
 * 
 * @param pvParameters Task parameters (unused)
 */
void vUART0RxTask( void * pvParameters );

#ifdef __cplusplus
}
#endif

#endif