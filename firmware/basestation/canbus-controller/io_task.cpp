#include "io_task.h"

// C standard libraries
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>

// Global Instances/Variables
TaskHandle_t xInputPollingTaskHandle;
TaskHandle_t xInputKnobPollingTaskHandle;
TaskHandle_t xPWMDimmerTaskHandle;

// Local Instances/Variables
IO3PinParams_t threeInput[ NUM_INPUT_3_PIN ] = INPUT_3_PIN_DEFAULT_PARAMS_PREV();
IO2PinParams_t twoInput[ NUM_INPUT_2_PIN ] = INPUT_2_PIN_DEFAULT_PARAMS_PREV();
IO2PinParams_t twoOut[ NUM_OUTPUT_2_PIN ] = OUTPUT_2_PIN_DEFAULT_PARAMS_PREV();
KnobParams_t knob_param = KNOB_DEFAULT_PARAMS_PREV();
PWMParams_t pwm_param = PWM_DEFAULT_PARAMS_PREV();

static float fDimTarget = 0.0f;
static uint32_t uiDimTimeMs = 0;
static float fCurrentDimLevel = 0.0f;  // Track current dimming level for stop functionality

void vInputPollingTask( void * pvParameters ){
    CanboProvisioningCommand last_configuration = CanboProvisioningCommand_init_zero;
    CanboProvisioningCommand current_configuration = CanboProvisioningCommand_init_zero;
    bool add_3_pin[sizeof(last_configuration.three_pin_inputs) / sizeof(*last_configuration.three_pin_inputs)];
    bool remove_3_pin[sizeof(last_configuration.three_pin_inputs) / sizeof(*last_configuration.three_pin_inputs)];
    bool add_2_pin[sizeof(last_configuration.two_pin_inputs) / sizeof(*last_configuration.two_pin_inputs)];
    bool remove_2_pin[sizeof(last_configuration.two_pin_inputs) / sizeof(*last_configuration.two_pin_inputs)];
    // Fetch data from flash on how the device was configured
    vTaskDelay( pdMS_TO_TICKS( 250 ) );
    //
    printf( "[START] Input polling task started\r\n" );
    for( ;; ) {
        // Direct read - no semaphore needed
        get_active_configuration(&current_configuration);
        // reconfigure 3 pin inputs
        if ( current_configuration.three_pin_inputs_count != last_configuration.three_pin_inputs_count
                || !same_three_pin_input_configs(current_configuration.three_pin_inputs, last_configuration.three_pin_inputs, sizeof(current_configuration.three_pin_inputs)) ) {

            #ifdef DEBUG_IO
            printf("[IO] 3 Pin Inputs Configuration Changed\r\n");
            #endif

            memset(add_3_pin, true, sizeof(current_configuration.three_pin_inputs) * sizeof(bool) / sizeof(*current_configuration.three_pin_inputs));
            memset(remove_3_pin, true, sizeof(last_configuration.three_pin_inputs) * sizeof(bool) / sizeof(*last_configuration.three_pin_inputs));

            for ( size_t i = 0; i < sizeof(current_configuration.three_pin_inputs) / sizeof(*current_configuration.three_pin_inputs); ++i ) {
                CanboProvisioningCommand_ThreePinInput active_in = current_configuration.three_pin_inputs[i];
                for ( size_t j = 0; j < sizeof(last_configuration.three_pin_inputs) / sizeof(*last_configuration.three_pin_inputs); ++j ) {
                    CanboProvisioningCommand_ThreePinInput last_in = last_configuration.three_pin_inputs[j];

                    if (active_in.connector_id == last_in.connector_id) {
                        add_3_pin[i] = false;
                        remove_3_pin[j] = false;
                        break;
                    }
                }
            }

            for ( size_t i = 0; i < sizeof(current_configuration.three_pin_inputs) / sizeof(*current_configuration.three_pin_inputs); ++i ) {
                if ( add_3_pin[i] && current_configuration.three_pin_inputs[i].connector_id != 0 ) {
                    #ifdef DEBUG_IO
                    printf("[IO] Adding 3 Pin Input with Connector ID: %d\r\n", current_configuration.three_pin_inputs[i].connector_id);
                    #endif

                    vInitIO(threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_pin, true);
                    vInitIO(threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_pin, true);
                    threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state = true;
                    threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state = true;
                    threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state = 1;
                }
                if ( remove_3_pin[i] && last_configuration.three_pin_inputs[i].connector_id != 0 ) {
                    #ifdef DEBUG_IO
                    printf("[IO] Removing 3 Pin Input with Connector ID: %d\r\n", last_configuration.three_pin_inputs[i].connector_id);
                    #endif

                    vDeInitIO(threeInput[last_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_pin);
                    vDeInitIO(threeInput[last_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_pin);
                }
            }

            last_configuration.three_pin_inputs_count = current_configuration.three_pin_inputs_count;
            memcpy(last_configuration.three_pin_inputs, current_configuration.three_pin_inputs, sizeof(current_configuration.three_pin_inputs));
        }
        // reconfigure 2 pin inputs
        if ( current_configuration.two_pin_inputs_count != last_configuration.two_pin_inputs_count
                || !same_two_pin_input_configs(current_configuration.two_pin_inputs, last_configuration.two_pin_inputs, sizeof(current_configuration.two_pin_inputs)) ) {

            #ifdef DEBUG_IO
            printf("[IO] 2 Pin Inputs Configuration Changed\r\n");
            #endif

            memset(add_2_pin, true, sizeof(current_configuration.two_pin_inputs) * sizeof(bool) / sizeof(*current_configuration.two_pin_inputs));
            memset(remove_2_pin, true, sizeof(last_configuration.two_pin_inputs) * sizeof(bool) / sizeof(*last_configuration.two_pin_inputs));

            for ( size_t i = 0; i < sizeof(current_configuration.two_pin_inputs) / sizeof(*current_configuration.two_pin_inputs); ++i ) {
                CanboProvisioningCommand_TwoPinInput active_in = current_configuration.two_pin_inputs[i];
                for ( size_t j = 0; j < sizeof(last_configuration.two_pin_inputs) / sizeof(*last_configuration.two_pin_inputs); ++j ) {
                    CanboProvisioningCommand_TwoPinInput last_in = last_configuration.two_pin_inputs[j];

                    if (active_in.connector_id == last_in.connector_id) {
                        add_2_pin[i] = false;
                        remove_2_pin[j] = false;
                        break;
                    }
                }
            }

            for ( size_t i = 0; i < sizeof(current_configuration.two_pin_inputs) / sizeof(*current_configuration.two_pin_inputs); ++i ) {
                if ( add_2_pin[i] && current_configuration.two_pin_inputs[i].connector_id != 0 ) {
                    #ifdef DEBUG_IO
                    printf("[IO] Adding 2 Pin Input with Connector ID: %d\r\n", current_configuration.two_pin_inputs[i].connector_id);
                    #endif

                    vInitIO(twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_pin, true);
                    twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state = false;
                }
                if ( remove_2_pin[i] && last_configuration.two_pin_inputs[i].connector_id != 0 ) {
                    #ifdef DEBUG_IO
                    printf("[IO] Removing 2 Pin Input with Connector ID: %d\r\n", current_configuration.two_pin_inputs[i].connector_id);
                    #endif

                    vDeInitIO(twoInput[last_configuration.two_pin_inputs[i].connector_id - 1].gpio_pin);
                }
            }

            last_configuration.two_pin_inputs_count = current_configuration.two_pin_inputs_count;
            memcpy(last_configuration.two_pin_inputs, current_configuration.two_pin_inputs, sizeof(current_configuration.two_pin_inputs));
        }
        // poll 3 pin inputs
        for( size_t i = 0; i < current_configuration.three_pin_inputs_count; ++i ) {
            uint8_t last_pos = threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state;
            uint8_t last_gpio0_state = threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state;
            uint8_t last_gpio1_state = threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state;

            // "Up GPIO"
            threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state = \
                        !gpio_get( threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_pin );
            // "Down GPIO"
            threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state = \
                        !gpio_get( threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_pin );

            // "Up State"
            if ( threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state
                        && !threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state ) {
                threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state = 0;
            }
            // "Down State"
            else if ( !threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state
                        && threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state ) {
                threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state = 2;
            }
            // "Released State"
            else{
                threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state = 1;
            }

            // Handle state changes based on connector type
            if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state != last_pos) {
                switch (current_configuration.three_pin_inputs[i].connector_type) {
                    case CanboProvisioningCommand_ThreePinInput_ConnectorType_TOGGLE:
                        // on rising "up" edge
                        if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state == 0 && last_pos != 0) {
                            #ifdef DEBUG_IO_TOGGLE
                            printf("[IO] TOGGLE UP\r\n");
                            #endif
                            send_toggle_button_state((current_configuration.three_pin_inputs[i].connector_id), ToggleButtonCommand_State_Up);
                        }
                        // on rising "down" edge
                        else if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state == 2 && last_pos != 2) {
                            #ifdef DEBUG_IO_TOGGLE
                            printf("[IO] TOGGLE DOWN\r\n");
                            #endif
                            send_toggle_button_state((current_configuration.three_pin_inputs[i].connector_id), ToggleButtonCommand_State_Down);
                        }
                        // on falling edges
                        else if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state != last_pos) {
                            #ifdef DEBUG_IO_TOGGLE
                            printf("[IO] TOGGLE RELEASE\r\n");
                            #endif
                            send_toggle_button_state((current_configuration.three_pin_inputs[i].connector_id), ToggleButtonCommand_State_Released);
                        }
                        break;

                    case CanboProvisioningCommand_ThreePinInput_ConnectorType_MOMENTARY:
                        // Handle "up" button
                        if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state != last_gpio0_state) {
                            if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state) {
                                #ifdef DEBUG_IO_3_MOMENTARY
                                printf("[IO] UP MOMENTARY PRESSED\r\n");
                                #endif
                                send_momentary_button_state(current_configuration.three_pin_inputs[i].connector_id,
                                    MomentaryButtonCommand_State_Pressed);
                            } else {
                                #ifdef DEBUG_IO_3_MOMENTARY
                                printf("[IO] UP MOMENTARY RELEASED\r\n");
                                #endif
                                send_momentary_button_state(current_configuration.three_pin_inputs[i].connector_id,
                                    MomentaryButtonCommand_State_Released);
                            }
                        }

                        // Handle "down" button
                        if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state != last_gpio1_state) {
                            if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state) {
                                #ifdef DEBUG_IO_3_MOMENTARY
                                printf("[IO] DOWN MOMENTARY PRESSED\r\n");
                                #endif
                                send_momentary_button_state(current_configuration.three_pin_inputs[i].connector_id,
                                    MomentaryButtonCommand_State_Pressed);
                            } else {
                                #ifdef DEBUG_IO_3_MOMENTARY
                                printf("[IO] DOWN MOMENTARY RELEASED\r\n");
                                #endif
                                send_momentary_button_state(current_configuration.three_pin_inputs[i].connector_id,
                                    MomentaryButtonCommand_State_Released);
                            }
                        }
                        break;

                    default:
                        printf("[Error] Unknown three-pin connector type: %d\r\n", current_configuration.three_pin_inputs[i].connector_type);
                        break;
                }
            }
        }
        // poll 2 pin inputs
        for( size_t i = 0; i < current_configuration.two_pin_inputs_count; ++i ) {
            uint8_t last_pos = twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state;

            twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state = \
                        !gpio_get( twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_pin );

            // Handle state changes based on connector type
            if ( twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state != last_pos ) {
                switch (current_configuration.two_pin_inputs[i].connector_type) {
                    case CanboProvisioningCommand_TwoPinInput_ConnectorType_PIR:
                        // Rising edge - motion detected
                        if ( twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state && !last_pos ) {
                            #ifdef DEBUG_IO_PIR
                            printf("[IO] MOTION DETECTED\r\n");
                            #endif
                            send_motion_detected_state((current_configuration.two_pin_inputs[i].connector_id), MotionDetectedCommand_State_Detected);
                        }
                        // Falling edge - motion stopped
                        else if ( !twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state && last_pos ) {
                            #ifdef DEBUG_IO_PIR
                            printf("[IO] MOTION STOPPED\r\n");
                            #endif
                            send_motion_detected_state((current_configuration.two_pin_inputs[i].connector_id), MotionDetectedCommand_State_Stopped);
                        }
                        break;

                    case CanboProvisioningCommand_TwoPinInput_ConnectorType_MOMENTARY:
                        // Rising edge - button pressed
                        if ( twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state && !last_pos ) {
                            #ifdef DEBUG_IO_2_MOMENTARY
                            printf("[IO] MOMENTARY PRESSED\r\n");
                            #endif
                            send_momentary_button_state((current_configuration.two_pin_inputs[i].connector_id), MomentaryButtonCommand_State_Pressed);
                        }
                        // Falling edge - button released
                        else if ( !twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state && last_pos ) {
                            #ifdef DEBUG_IO_2_MOMENTARY
                            printf("[IO] MOMENTARY RELEASED\r\n");
                            #endif
                            send_momentary_button_state((current_configuration.two_pin_inputs[i].connector_id), MomentaryButtonCommand_State_Released);
                        }
                        break;

                    case CanboProvisioningCommand_TwoPinInput_ConnectorType_DOOR_SENSOR:
                        // Rising edge - door opened
                        if ( twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state && !last_pos ) {
                            #ifdef DEBUG_IO_DOOR_SENSOR
                            printf("[IO] DOOR OPENED\r\n");
                            #endif
                            send_door_sensor_state((current_configuration.two_pin_inputs[i].connector_id), DoorSensorCommand_State_Opened);
                        }
                        // Falling edge - door closed
                        else if ( !twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state && last_pos ) {
                            #ifdef DEBUG_IO_DOOR_SENSOR
                            printf("[IO] DOOR CLOSED\r\n");
                            #endif
                            send_door_sensor_state((current_configuration.two_pin_inputs[i].connector_id), DoorSensorCommand_State_Closed);
                        }
                        break;

                    default:
                        printf("[ERROR] Unknown two-pin connector type: %d\r\n", current_configuration.two_pin_inputs[i].connector_type);
                        break;
                }
            }
        }

        vTaskDelay( pdMS_TO_TICKS( 10 ) );
    }
}

void vInputKnobPollingTask( void * pvParameters ){
    // ============================================================================
    // ADC INPUT POLLING TASK
    // ============================================================================
    // Purpose: Polls ADC inputs (knobs/thermostats) and sends CAN commands based on configuration
    //
    // Variables:
    // - current_configuration: Current device configuration
    // - last_configuration: Previous configuration for change detection
    // - adc_initialized: Flag indicating if ADC has been initialized
    // - last_adc_value: Previous ADC value for change detection
    // - last_sent_value: Last value that was actually sent via CAN
    // - movement_detected: Flag indicating if knob is currently being turned
    // - movement_start_time: Timestamp when movement was first detected
    // - stable_count: Counter for stable readings at final position
    // - iir_filtered_value: IIR filtered value for stable state averaging
    // - iir_alpha: IIR filter coefficient (0.0-1.0, higher = more responsive)
    // - fConversionFactor: ADC conversion factor (3.3V / 4095)
    // - adc_pin: Current ADC pin being used
    // - adc_channel: Current ADC channel being used
    
    CanboProvisioningCommand current_configuration = CanboProvisioningCommand_init_zero;
    CanboProvisioningCommand last_configuration = CanboProvisioningCommand_init_zero;
    bool adc_initialized = false;
    float last_adc_value = 0.0f;
    float last_sent_value = 0.0f;
    float last_stable_sent_value = 0.0f;  // Track last value sent during stable state
    float last_filtered_value = 0.0f;      // Track last filtered value for change detection
    float last_normalized_value = 0.0f;    // Track last normalized value for threshold detection
    bool movement_detected = false;
    uint32_t movement_start_time = 0;
    uint32_t stable_count = 0;
    bool value_latched = false;  // Track if we've latched to filtered value
    uint32_t latch_start_time = 0;  // Track when we started waiting for latch
    float iir_filtered_value = 0.0f;
    bool iir_initialized = false;  // Track if IIR filter has been initialized
    const float iir_alpha = 0.4f; // IIR filter coefficient (40% new value, 60% previous) - more responsive
    const float fConversionFactor = ( 3.3f / ( ( 1 << 12 ) - 1 ) );

    // Thresholds for different phases
    const float MOVEMENT_THRESHOLD = 0.015f;      // 1.5% - more sensitive for movement detection
    const uint32_t STABLE_TIMEOUT_MS = 75;       // 75ms of stable readings before sending final value
    const uint32_t MOVEMENT_TIMEOUT_MS = 10;     // 10ms between movement updates
    const uint32_t LATCH_TIMEOUT_MS = 1000;      // 1000ms (1 second) before latching to filtered value

    vTaskDelay( pdMS_TO_TICKS( 250 ) );

    printf( "[START] ADC polling task started\r\n" );

    for( ;; ){
        // Get current configuration to check for ADC settings
        get_active_configuration(&current_configuration);
        
        // Check if ADC is configured
        if ( !current_configuration.has_adc_inputs ) {
#ifdef DEBUG_ADC_POLLING_DEEP
            printf("[ADC] No ADC inputs configured\r\n");
#endif

            if ( adc_initialized ) {
                // Deinitialize ADC if it was previously initialized
                vDeInitIO(knob_param.pin);
                adc_initialized = false;
                last_adc_value = 0.0f;
                last_sent_value = 0.0f;
                last_stable_sent_value = 0.0f;
                last_filtered_value = 0.0f;
                movement_detected = false;
                stable_count = 0;
                value_latched = false;
                latch_start_time = 0;
                iir_filtered_value = 0.0f;
                movement_start_time = 0;

#ifdef DEBUG_ADC_POLLING_DEEP
                printf("[ADC] ADC deinitialized\r\n");
#endif
            }

            vTaskDelay( pdMS_TO_TICKS( 100 ) );
            continue;
        }

        // Check if ADC configuration or range type has changed
        bool config_changed = false;
        if ( !adc_initialized ||
             !same_adc_input_config(&current_configuration.adc_inputs, &last_configuration.adc_inputs) ) {
            config_changed = true;
        }

        // Initialize ADC if not already done or if configuration changed
        if ( !adc_initialized || config_changed ) {
            if ( adc_initialized ) {
                // Deinitialize previous ADC setup
                vDeInitIO(knob_param.pin);
                adc_initialized = false;
            }
            // Reset ALL state variables for new configuration or range type
            last_adc_value = 0.0f;
            last_sent_value = 0.0f;
            last_stable_sent_value = 0.0f;
            last_filtered_value = 0.0f;
            last_normalized_value = 0.0f;
            movement_detected = false;
            stable_count = 0;
            value_latched = false;
            latch_start_time = 0;
            iir_filtered_value = 0.0f;
            iir_initialized = false;
            movement_start_time = 0;

            // Initialize ADC with default knob parameters
            vInitADC( knob_param.pin, knob_param.channel );
            adc_initialized = true;
#ifdef DEBUG_ADC_POLLING_DEEP
            printf("[ADC] ADC initialized on pin %d, channel %d\r\n", knob_param.pin, knob_param.channel);
            printf("[ADC] ADC Type: %s\r\n", current_configuration.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_KNOB ? "KNOB" : "THERMOSTAT");
            if (current_configuration.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_KNOB) {
                printf("[ADC] ADC Min=%.2f, Max=%.2f\r\n",
                        current_configuration.adc_inputs.range.min_max.min, 
                        current_configuration.adc_inputs.range.min_max.max);
            } else if (current_configuration.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_THERMOSTAT) {
                printf("[ADC] ADC Offset=%.2f, Scaling=%.2f\r\n",
                        current_configuration.adc_inputs.range.offset_scaling.offset, 
                        current_configuration.adc_inputs.range.offset_scaling.scaling_factor);
            }
#endif
        }

        // --- Always use min/max formula for ADC scaling ---
        float min = 0.0f;
        float max = 1.0f;
        // Use connector_type to determine which range type to use
        if (current_configuration.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_KNOB) {
            // KNOB uses min/max range
            min = current_configuration.adc_inputs.range.min_max.min;
            max = current_configuration.adc_inputs.range.min_max.max;
        } else if (current_configuration.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_THERMOSTAT) {
            // THERMOSTAT uses offset/scaling range - convert to min/max
            min = current_configuration.adc_inputs.range.offset_scaling.offset - 0.5f * current_configuration.adc_inputs.range.offset_scaling.scaling_factor;
            max = current_configuration.adc_inputs.range.offset_scaling.offset + 0.5f * current_configuration.adc_inputs.range.offset_scaling.scaling_factor;
        }
        // Read ADC value
        uint16_t usRead = adc_read();
        float fConversion = usRead * fConversionFactor;
        float normalized = fConversion / 3.3f;
        float processed_value = min + normalized * (max - min);
        
        // Update IIR filter on raw voltage values (before scaling/offset)
        if (!iir_initialized) {
            iir_filtered_value = fConversion;
            iir_initialized = true;
        } else {
            iir_filtered_value = (iir_alpha * fConversion) + ((1.0f - iir_alpha) * iir_filtered_value);
        }
        float filtered_normalized = iir_filtered_value / 3.3f;
        float filtered_processed_value = min + filtered_normalized * (max - min);

        // Get current time for timing-based logic
        uint32_t current_time = time_us_32() / 1000;
        if (movement_start_time == 0) {
            movement_start_time = current_time;
        }

        float filtered_sent_value_change = fabs(filtered_processed_value - last_sent_value);

        // Determine if we should send a command based on movement state
        bool should_send = false;
        bool just_latched = false;

        // Apply thresholds to normalized values (0-1 range) before scaling
        float normalized_change = fabs(normalized - last_normalized_value);
        float filtered_normalized_change = fabs(filtered_normalized - last_normalized_value);
        bool is_movement = normalized_change > MOVEMENT_THRESHOLD;

        // --- Movement/Stable/Latch Logic ---
        if (movement_detected && !is_movement) {
            uint32_t time_since_movement = current_time - movement_start_time;
            if (time_since_movement >= STABLE_TIMEOUT_MS) {
                movement_detected = false;
                stable_count = 0;
                movement_start_time = current_time;
#ifdef DEBUG_ADC_POLLING_DEEP
                printf("[ADC] Transitioning to stable mode after %d ms of stable filtered values\r\n", time_since_movement);
#endif
            }
        }

        if (is_movement && !movement_detected) {
            movement_detected = true;
            movement_start_time = current_time;
            stable_count = 0;
            value_latched = false;
            should_send = true;
#ifdef DEBUG_ADC_POLLING_DEEP
            printf("[ADC] Movement started: %.3f (raw)\r\n", processed_value);
#endif
        } else if (is_movement && movement_detected && value_latched) {
            // If we're already in movement mode but value was latched, reset latching
            value_latched = false;
            stable_count = 0;
            movement_start_time = current_time;
            should_send = true;
#ifdef DEBUG_ADC_POLLING_DEEP
            printf("[ADC] Movement resumed after latching: %.3f (raw)\r\n", processed_value);
#endif
        } else if (movement_detected && is_movement) {
            uint32_t time_since_movement = current_time - movement_start_time;
            if (time_since_movement >= MOVEMENT_TIMEOUT_MS) {
                should_send = true;
                movement_start_time = current_time;
#ifdef DEBUG_ADC_POLLING_DEEP
                printf("[ADC] Movement update: %.3f (raw)\r\n", processed_value);
#endif
            }
        } else if (!movement_detected && !is_movement) {
            if (!value_latched) {
                uint32_t time_since_movement = current_time - movement_start_time;
                if (time_since_movement >= LATCH_TIMEOUT_MS) {
                    value_latched = true;
                    latch_start_time = current_time;
                    just_latched = true;
#ifdef DEBUG_ADC_POLLING_DEEP
                    printf("[ADC] Value latched to filtered after %d ms of stability\r\n", time_since_movement);
#endif
                }
            }
            stable_count++;
            // Always send the filtered value ONCE when latching
            if (just_latched) {
                should_send = true;
#ifdef DEBUG_ADC_POLLING_DEEP
                printf("[ADC] Final resting value: %.3f (filtered processed, transitioning to stable)\r\n", filtered_processed_value);
#endif
            } else if (value_latched && filtered_sent_value_change > (MOVEMENT_THRESHOLD * (max - min))) {
                should_send = true;
#ifdef DEBUG_ADC_POLLING_DEEP
                printf("[ADC] Stable value changed: %.3f (filtered processed, change=%.4f)\r\n", filtered_processed_value, filtered_sent_value_change);
#endif
            }
#ifdef DEBUG_ADC_POLLING_DEEP
            if (value_latched && !should_send) {
                printf("[ADC] Latched but no send: filtered_change=%.4f, sent_change=%.4f, threshold=%.4f\r\n", 
                       filtered_normalized_change, filtered_sent_value_change, MOVEMENT_THRESHOLD * (max - min));
                printf("[ADC] Values: filtered=%.3f, last_sent=%.3f, last_stable=%.3f\r\n", 
                       filtered_processed_value, last_sent_value, last_stable_sent_value);
            }
#endif
        } else if (!movement_detected && !is_movement && filtered_sent_value_change > (MOVEMENT_THRESHOLD * (max - min))) {
                should_send = true;
#ifdef DEBUG_ADC_POLLING_DEEP
                printf("[ADC] Significant change (no movement): %.3f (filtered processed, change=%.4f)\r\n", filtered_processed_value, filtered_sent_value_change);
#endif
            }

        // Send command if needed
        if (should_send) {
            float value_to_send;
            // Always send filtered value if latched, otherwise send raw
            if (value_latched) {
                value_to_send = filtered_processed_value;
            } else {
                value_to_send = processed_value;
            }
            switch (current_configuration.adc_inputs.connector_type) {
                case CanboProvisioningCommand_ADCInput_ConnectorType_KNOB:
                    send_knob_value(value_to_send);
#ifdef DEBUG_ADC_POLLING_KNOB
                    printf("[ADC] Knob value: %.3f (raw: %.3f, filtered: %.3f)\r\n", value_to_send, processed_value, filtered_processed_value);
#endif
                    break;
                case CanboProvisioningCommand_ADCInput_ConnectorType_THERMOSTAT:
                    send_thermostat_value(value_to_send);
#ifdef DEBUG_ADC_POLLING_THERMOSTAT
                    printf("[ADC] Thermostat value: %.1f°C (raw: %.3f, filtered: %.3f)\r\n", value_to_send, processed_value, filtered_processed_value);
#endif
                    break;
                default:
#ifdef DEBUG_ADC_POLLING_DEEP
                    printf("[ADC] Unknown ADC connector type: %d\r\n", current_configuration.adc_inputs.connector_type);
#endif
                    break;
            }
            if (value_latched) {
                last_stable_sent_value = value_to_send;
            }
            last_sent_value = value_to_send;
#ifdef DEBUG_ADC_POLLING_DEEP
            printf("[ADC] Sent value: %.3f, stored as last_sent_value\r\n", value_to_send);
#endif
        }

        // Update tracking variables
        last_adc_value = processed_value;
        last_filtered_value = iir_filtered_value;
        last_normalized_value = normalized;
        last_configuration = current_configuration;

        vTaskDelay( pdMS_TO_TICKS( 1 ) );
    }
}

void vPWMDimmerTask( void * pvParameters ){
    // ============================================================================
    // PWM DIMMER TASK
    // ============================================================================
    // Purpose: Controls PWM output for dimming applications with time-based transitions
    //
    // Variables:
    // - fCurrentDimLevel: Current dimming level (0.0 to 1.0) - global for stop functionality
    // - fDimTarget: Target dimming level to reach
    // - uiDimTimeMs: Time in milliseconds to complete the dimming transition
    // - dim_start_time: Timestamp when dimming transition started
    // - dim_start_level: Dimming level when transition started
    // - dim_in_progress: Flag indicating if a dimming transition is active
    // - relay_initialized: Flag indicating if relay has been initialized
    // - relay_connector_id: The connector ID for the relay (from zero_to_ten_volt_config)
    // - min_brightness: Minimum brightness level (0.0 to 1.0) from config
    // - max_brightness: Maximum brightness level (0.0 to 1.0) from config
    
    CanboProvisioningCommand current_configuration = CanboProvisioningCommand_init_zero;
    uint32_t dim_start_time = 0;
    float dim_start_level = 0.0f;
    bool dim_in_progress = false;
    bool relay_initialized = false;
    uint32_t relay_connector_id = 0;
    bool last_relay_state = false;
    float min_brightness = 0.0f;
    float max_brightness = 1.0f;

    vTaskDelay( pdMS_TO_TICKS( 250 ) );

    vPWMSetup();

    printf( "[START] PWM dimmer task started\r\n" );

    for( ;; ){
        // Get current configuration to check for relay settings
        get_active_configuration(&current_configuration);
        
        if ( !current_configuration.has_zero_to_ten_volt_config ) {
            #ifdef DEBUG_PWM_DIMMING_DEEP
            printf("[PWM] No zero to ten volt config found\r\n");
            #endif

            if ( relay_initialized ) {
                gpio_put(twoOut[relay_connector_id - 1].gpio_pin, false);
                twoOut[relay_connector_id - 1].gpio_state = false;
                last_relay_state = false;
                vDeInitIO(twoOut[relay_connector_id - 1].gpio_pin);
                relay_initialized = false;
                relay_connector_id = 0;
            }

            // Stop dimming
            pwm_param.duty_cycle = 0;
            pwm_set_chan_level( pwm_param.slice, pwm_param.channel, pwm_param.duty_cycle );

            vTaskDelay( pdMS_TO_TICKS( 100 ) );
            continue;
        }

        // Get min and max brightness constraints
        min_brightness = current_configuration.zero_to_ten_volt_config.min_brightness;
        max_brightness = current_configuration.zero_to_ten_volt_config.max_brightness;

        // Clamp brightness constraints to valid range
        min_brightness = (min_brightness < 0.0f) ? 0.0f : min_brightness;
        min_brightness = (min_brightness > 1.0f) ? 1.0f : min_brightness;
        max_brightness = (max_brightness < 0.0f) ? 0.0f : max_brightness;
        max_brightness = (max_brightness > 1.0f) ? 1.0f : max_brightness;
        
        // Ensure min <= max
        if (min_brightness > max_brightness) {
            min_brightness = 0.0f;
            max_brightness = 1.0f;
        }

        // Check relay configuration and handle relay control
        bool relay_enabled = current_configuration.zero_to_ten_volt_config.use_relay;
        uint32_t new_relay_connector_id = current_configuration.zero_to_ten_volt_config.relay_connector_id;
        
        // If relay is disabled or out of bounds in config, disable relay control
        if (relay_initialized && (!relay_enabled || new_relay_connector_id <= 0 || new_relay_connector_id > NUM_OUTPUT_2_PIN)) {
            // Turn off relay if it was previously on
            if (last_relay_state) {
                gpio_put(twoOut[relay_connector_id - 1].gpio_pin, false);
                twoOut[relay_connector_id - 1].gpio_state = false;
                last_relay_state = false;

                #ifdef DEBUG_PWM_RELAY
                printf("[PWM] Relay %d turned OFF (relay disabled in config)\r\n", relay_connector_id);
                #endif
            }

            // Reset relay state
            vDeInitIO(twoOut[relay_connector_id - 1].gpio_pin);
            relay_initialized = false;
            relay_connector_id = 0;
        }
        // Initialize relay if not already done and relay is configured
        else if (!relay_initialized && relay_enabled && new_relay_connector_id > 0 && new_relay_connector_id <= NUM_OUTPUT_2_PIN) {
            
            // Initialize the relay pin as output
            if ( current_configuration.outputs[new_relay_connector_id - 1].connector_type == CanboProvisioningCommand_Output_ConnectorType_Relay ) {
                vInitIO(twoOut[new_relay_connector_id - 1].gpio_pin, false); // false = output
                gpio_put(twoOut[new_relay_connector_id - 1].gpio_pin, false); // Start with relay off
                twoOut[new_relay_connector_id - 1].gpio_state = false; // Start with relay off
                relay_initialized = true;
                relay_connector_id = new_relay_connector_id;
                
                #ifdef DEBUG_PWM_RELAY
                printf("[PWM] Relay initialized on connector ID: %d\r\n", new_relay_connector_id);
                #endif
            } else {
                #ifdef DEBUG_PWM_RELAY
                printf("[PWM] Invalid output of type: %d\r\n", current_configuration.outputs[new_relay_connector_id - 1].connector_type);
                #endif
            }
        }

        // Get current time in milliseconds
        uint32_t current_time = time_us_32() / 1000;
        
        // Check if we need to start a new dimming transition
        if (fCurrentDimLevel != fDimTarget && !dim_in_progress) {
            // Apply min/max constraints to the target value
            float constrained_target = fDimTarget;
            constrained_target = (constrained_target < min_brightness) ? min_brightness : constrained_target;
            constrained_target = (constrained_target > max_brightness) ? max_brightness : constrained_target;
            
            // Only start transition if the constrained target is different from current level
            if (fCurrentDimLevel != constrained_target) {
                // Start a new dimming transition
                dim_start_time = current_time;
                dim_start_level = fCurrentDimLevel;
                dim_in_progress = true;
                fDimTarget = constrained_target; // Update target to constrained value

                #ifdef DEBUG_PWM_DIMMING
                printf("[PWM] Starting dim transition: %.4f -> %.4f over %d ms\r\n",
                       dim_start_level, fDimTarget, uiDimTimeMs);
                #endif
            }
        }

        // Calculate dimming progress if transition is in progress
        if (dim_in_progress && uiDimTimeMs > 0) {
            // Check if target has been changed (indicating a stop command)
            if (fDimTarget == fCurrentDimLevel) {
                // Target matches current level - transition is complete
                dim_in_progress = false;

                #ifdef DEBUG_PWM_DIMMING
                printf("[PWM] Dim transition stopped at current level: %.4f\r\n", fCurrentDimLevel);
                #endif
            } else {
                // Calculate elapsed time since transition started
                uint32_t elapsed_time = current_time - dim_start_time;

                // Calculate progress (0.0 to 1.0)
                float progress = (float)elapsed_time / (float)uiDimTimeMs;

                // Clamp progress to 1.0
                progress = (progress > 1.0f) ? 1.0f : progress;
                
                // Calculate new dimming level using linear interpolation
                fCurrentDimLevel = dim_start_level + (fDimTarget - dim_start_level) * progress;

                // Apply min/max constraints during transition
                fCurrentDimLevel = (fCurrentDimLevel < min_brightness) ? min_brightness : fCurrentDimLevel;
                fCurrentDimLevel = (fCurrentDimLevel > max_brightness) ? max_brightness : fCurrentDimLevel;
                
                // Check if transition is complete
                if (progress >= 1.0f) {
                    fCurrentDimLevel = fDimTarget;  // Ensure exact target value
                    dim_in_progress = false;
                    #ifdef DEBUG_PWM_DIMMING
                    printf("[PWM] Dim transition complete: %.4f\r\n", fCurrentDimLevel);
                    #endif
                }
            }
        }
        // If no dimming time specified, use immediate transition
        else if (dim_in_progress && uiDimTimeMs == 0) {
            fCurrentDimLevel = fDimTarget;

            // Apply min/max constraints for immediate transition
            fCurrentDimLevel = (fCurrentDimLevel < min_brightness) ? min_brightness : fCurrentDimLevel;
            fCurrentDimLevel = (fCurrentDimLevel > max_brightness) ? max_brightness : fCurrentDimLevel;
            
            dim_in_progress = false;

            #ifdef DEBUG_PWM_DIMMING
            printf("[PWM] Immediate dim transition: %.4f\r\n", fCurrentDimLevel);
            #endif
        }

        // Clamp dimming level to valid range
        fCurrentDimLevel = (fCurrentDimLevel < 0.0f) ? 0.0f : fCurrentDimLevel;
        fCurrentDimLevel = (fCurrentDimLevel > 1.0f) ? 1.0f : fCurrentDimLevel;
        
        // Apply min/max brightness constraints
        fCurrentDimLevel = (fCurrentDimLevel < min_brightness) ? min_brightness : fCurrentDimLevel;
        fCurrentDimLevel = (fCurrentDimLevel > max_brightness) ? max_brightness : fCurrentDimLevel;
        
        // Control relay based on current dim level (only when relay is enabled)
        if (relay_enabled && relay_initialized && relay_connector_id > 0 && relay_connector_id <= NUM_OUTPUT_2_PIN) {
            bool new_relay_state = (fCurrentDimLevel > 0.0f);

            // Only update relay if state has changed
            if (new_relay_state != last_relay_state) {
                twoOut[relay_connector_id - 1].gpio_state = new_relay_state;
                gpio_put(twoOut[relay_connector_id - 1].gpio_pin, new_relay_state);
                last_relay_state = new_relay_state;

                #ifdef DEBUG_PWM_RELAY
                printf("[PWM] Relay %d %s (dim level: %.4f)\r\n",
                       relay_connector_id,
                       new_relay_state ? "ON" : "OFF",
                       fCurrentDimLevel);
                #endif
            }
        }
        // Update PWM duty cycle
        pwm_param.duty_cycle = (uint16_t)(pwm_param.config.top * fCurrentDimLevel);
        pwm_set_chan_level( pwm_param.slice, pwm_param.channel, pwm_param.duty_cycle );

        vTaskDelay( pdMS_TO_TICKS( 1 ) );
    }
}

static void vInitIO(uint32_t gpio, bool input) {
    gpio_init( gpio );
    gpio_set_dir( gpio, (input) ? GPIO_IN : GPIO_OUT );

    // Commented out due to v4 having the pull up resistors on the board
    if( input == true ){
        gpio_pull_up( gpio );
    }
    #ifdef DEBUG_IO
    printf("[IO] Pin Number: %d initialized as GPIO\r\n", gpio);
    #endif
}

static void vInitADC(uint32_t gpio, uint32_t channel) {
    adc_init();
    adc_gpio_init( gpio );
    adc_select_input( channel );
    #ifdef DEBUG_IO
    printf("[IO] Pin Number: %d initialized as ADC\r\n", gpio);
    #endif
}

static void vDeInitIO(uint32_t gpio) {
    gpio_deinit( gpio );
    gpio_disable_pulls( gpio );
    #ifdef DEBUG_IO
    printf("[IO] Pin Number: %d de-initialized as GPIO\r\n", gpio);
    #endif
}

void vPWMSetup(){
    gpio_set_function( pwm_param.pin, GPIO_FUNC_PWM );
    pwm_param.slice = pwm_gpio_to_slice_num( pwm_param.pin );
    pwm_param.channel = pwm_gpio_to_channel( pwm_param.pin );

    #ifdef DEBUG_PWM_DIMMING_SETUP
    printf( "[PWM] Slice: %d\r\n", pwm_param.slice );
    printf( "[PWM] Channel: %d\r\n", pwm_param.channel );
    #endif

    pwm_param.config = pwm_get_default_config(); // has clkdiv value of 1 for max resolution

    uint16_t wrap_value = (SYS_CLOCK_150MHZ / pwm_param.frequency) - 1;
    pwm_config_set_wrap( &pwm_param.config, wrap_value ); // wrap = 149999 for 1kHz

    pwm_init( pwm_param.slice, &pwm_param.config, false );

    // Continuous mode - reloads the counter automatically
    pwm_set_enabled( pwm_param.slice, true );
}

uint16_t usADCtoPWM( float fValue ){
    return (uint16_t)(fValue * ((1 << 12) - 1) / 3.3f);
}

static bool same_two_pin_input_configs( CanboProvisioningCommand_TwoPinInput *active, CanboProvisioningCommand_TwoPinInput *last, size_t len ) {
    return memcmp(active, last, len) == 0;
}

static bool same_three_pin_input_configs( CanboProvisioningCommand_ThreePinInput *active, CanboProvisioningCommand_ThreePinInput *last, size_t len ) {
    return memcmp(active, last, len) == 0;
}

static bool same_adc_input_config( CanboProvisioningCommand_ADCInput *active, CanboProvisioningCommand_ADCInput *last ) {
    if (active->connector_type != last->connector_type) {
        return false;
    }
    
    // Compare range values based on connector_type
    if (active->connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_KNOB) {
        return (active->range.min_max.min == last->range.min_max.min) &&
               (active->range.min_max.max == last->range.min_max.max);
    } else if (active->connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_THERMOSTAT) {
        return (active->range.offset_scaling.offset == last->range.offset_scaling.offset) &&
               (active->range.offset_scaling.scaling_factor == last->range.offset_scaling.scaling_factor);
    }
    
    return true;
}

// ============================================================================
// PWM DIMMING CONTROL FUNCTIONS
// ============================================================================

void vSetPWMDimVariables(float target, uint32_t time_ms) {
    vSetPWMDimTime( time_ms );
    vSetPWMDimTarget( target );
}

void vSetPWMDimTarget(float target) {
    // Clamp target to valid range
    target = (target < 0.0f) ? 0.0f : target;
    target = (target > 1.0f) ? 1.0f : target;

    fDimTarget = target;

    #ifdef DEBUG_PWM_DIMMING
    printf("[PWM] Dim target set to: %.4f\r\n", fDimTarget);
    #endif
}

void vSetPWMDimTime(uint32_t time_ms) {
    uiDimTimeMs = time_ms;

    #ifdef DEBUG_PWM_DIMMING
    printf("[PWM] Dim time set to: %d ms\r\n", uiDimTimeMs);
    #endif
}

float fGetPWMDimTarget(void) {
    return fDimTarget;
}

uint32_t uiGetPWMDimTime(void) {
    return uiDimTimeMs;
}

void vStopPWMDimming(void) {
    // Set the target to the current level, effectively stopping the dimming
    fDimTarget = fCurrentDimLevel;

    #ifdef DEBUG_PWM_DIMMING
    printf("[PWM] Dimming stopped at current level: %.4f\r\n", fCurrentDimLevel);
    #endif
}

float fGetCurrentPWMDimLevel(void) {
    return fCurrentDimLevel;
}
