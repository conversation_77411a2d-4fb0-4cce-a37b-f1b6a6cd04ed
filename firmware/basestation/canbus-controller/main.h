#ifndef __MAIN_H__
#define __MAIN_H__

#ifdef __cplusplus
extern "C" {
#endif

// C standard libraries
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// pico sdk libraries
#include "pico/stdlib.h"

// #include "pico/unique_id.h"
// #include "hardware/flash.h"

#include "FreeRTOS.h"
#include "task.h"

// user libraries
#include "pinout.h"
#include "defs.h"

#include "peripherals/irq_handler.h"
#include "flash_storage.h"

#include "uart_task.h"
#include "can_task.h"
#include "io_task.h"
#include "touch_task.h"
#include "test.h"

// global definitions

// local definitions/enums

// function prototyping
// void vApplicationIdleHook( void );
// void vApplicationStackOverflowHook( TaskHandle_t xTask, char *pcTaskName );

#ifdef __cplusplus
}
#endif

#endif