#include "touch_task.h"

TaskHandle_t xTouchTaskHandle;
TouchpadParams_t touch_params = TOUCHPAD_DEFAULT_PARAMS_PREV();

void vTouchTask( void * pvParameters ){
    uint32_t ulTouchEvent = 0;
    uint8_t ucCounter = 0;
    uint8_t ucLastEvent = 0;
    uint8_t ucCurrentEvent = 0;
    uint8_t ucRead = 0;

    vTaskDelay( pdMS_TO_TICKS( 250 ) );

    // Set irq data
        touch_params.irq_num = IO_IRQ_BANK0;
        touch_params.irq_cb_func = &gpio_irq_handler;
    // Start LEDs GPIOs
        // for( ucCounter = 0; ucCounter < NUM_LEDS_PAD; ucCounter+=2 ){
        //     gpio_init( touch_params.LED[ ucCounter ].pin );
        //     gpio_set_dir( touch_params.LED[ ucCounter ].pin, GPIO_IN );
        //     gpio_put( touch_params.LED[ ucCounter ].pin, 1 );
        //     printf( "LED: %d | Pin: %d\r\n", ucCounter, touch_params.LED[ ucCounter ].pin );
        // }
        for( ucCounter = 0; ucCounter < NUM_LEDS_PAD; ucCounter++ ){
            gpio_init( touch_params.LED[ ucCounter ].pin );
            // gpio_set_dir( touch_params.LED[ ucCounter ].pin, GPIO_OUT );
            // gpio_put( touch_params.LED[ ucCounter ].pin, 1 );

            printf( "LED: %d | Pin: %d\r\n", ucCounter, touch_params.LED[ ucCounter ].pin );
        }

        for( ;; ){
            // vTaskDelay( pdMS_TO_TICKS( 100 ) );

            printf( "LED ON - on\r\n" );
            gpio_set_dir( GPIO43_PIN, GPIO_OUT );
            gpio_set_dir( GPIO44_PIN, GPIO_OUT );
            gpio_set_dir( GPIO46_PIN, GPIO_OUT );
            gpio_put( GPIO43_PIN, 0 );
            gpio_put( GPIO44_PIN, 0 );
            gpio_put( GPIO46_PIN, 0 );
            vTaskDelay( pdMS_TO_TICKS( 1000 ) );

            printf( "LED ON - off\r\n" );
            gpio_put( GPIO43_PIN, 1 );
            gpio_put( GPIO44_PIN, 1 );
            gpio_put( GPIO46_PIN, 1 );
            gpio_set_dir( GPIO43_PIN, GPIO_IN );
            gpio_set_dir( GPIO44_PIN, GPIO_IN );
            gpio_set_dir( GPIO46_PIN, GPIO_IN );
            vTaskDelay( pdMS_TO_TICKS( 10 ) );

            printf( "LED OFF - on\r\n" );
            gpio_set_dir( GPIO42_PIN, GPIO_OUT );
            gpio_set_dir( GPIO45_PIN, GPIO_OUT );
            gpio_set_dir( GPIO47_PIN, GPIO_OUT );
            gpio_put( GPIO42_PIN, 0 );
            gpio_put( GPIO45_PIN, 0 );
            gpio_put( GPIO47_PIN, 0 );

            vTaskDelay( pdMS_TO_TICKS( 1000 ) );

            printf( "LED OFF - off\r\n" );
            gpio_put( GPIO42_PIN, 1 );
            gpio_put( GPIO45_PIN, 1 );
            gpio_put( GPIO47_PIN, 1 );
            gpio_set_dir( GPIO42_PIN, GPIO_IN );
            gpio_set_dir( GPIO45_PIN, GPIO_IN );
            gpio_set_dir( GPIO47_PIN, GPIO_IN );
            vTaskDelay( pdMS_TO_TICKS( 1000 ) );
        }

        // for( ;; ){
        //     for( ucCounter = 0; ucCounter < NUM_LEDS_PAD; ucCounter++ ){
        //         gpio_init( touch_params.LED[ ucCounter ].pin );
        //         gpio_set_dir( touch_params.LED[ ucCounter ].pin, GPIO_OUT );
        //         gpio_put( touch_params.LED[ ucCounter ].pin, 1 );
        //     }
        //     vTaskDelay( pdMS_TO_TICKS( 1000 ) );
        //     for( ucCounter = 0; ucCounter < NUM_LEDS_PAD; ucCounter++ ){
        //         gpio_init( touch_params.LED[ ucCounter ].pin );
        //         gpio_set_dir( touch_params.LED[ ucCounter ].pin, GPIO_OUT );
        //         gpio_put( touch_params.LED[ ucCounter ].pin, 0 );
        //     }
        //     vTaskDelay( pdMS_TO_TICKS( 1000 ) );
        // }
    // Start additional IOs for CAP129x
        gpio_init( touch_params.reset_pin );
        gpio_init( touch_params.irq_pin );

        gpio_set_dir( touch_params.reset_pin, GPIO_OUT );
        gpio_set_dir( touch_params.irq_pin, GPIO_IN );

        gpio_put( touch_params.reset_pin, 0 );
        // Start I2C bus and pass along the i2c params
        printf( "> I2C Init\r\n" );
        ucI2CInit( &touch_params.i2c_config );
        printf( "> CAP129x Init\r\n" );
        xCAP129xInit( &touch_params.i2c_config, touch_params.reset_pin );
    // Start IRQ
        printf( "> IRQ setup\r\n" );
        gpio_init( touch_params.irq_pin );
        gpio_set_dir( touch_params.irq_pin, GPIO_IN );
        // Pass the taskhandle so it can do vTaskNotifyGiveFromISR
        vIRQSetPinAwareness( touch_params.irq_pin, xTouchTaskHandle );
        // this function will allocate the callback into a shared type so it's safe to use
        gpio_set_irq_enabled_with_callback( touch_params.irq_pin, GPIO_IRQ_EDGE_FALL, true, touch_params.irq_cb_func );
        irq_set_enabled( touch_params.irq_num, true );
    // Start CAP129x and configure channels
        vCAP129xSetup( &touch_params.channels );
        vCAP129xSetSensitivity( CAP129x_LOW_SENSITIVITY );
        vCAP129xEnableChannels( &touch_params.channels );
        vCAP129xConfigureChannels();
    //
    printf( ">>> Touch Task Init\r\n" );
    for( ;; ){
        ulTouchEvent = ulTaskNotifyTake( pdTRUE, portMAX_DELAY);
        if( ulTouchEvent > 0 ){
            printf( "Touch Event\r\n" );
            ucRead = 0;
            ucRead = ucCAP129xRead( CAP129x_REG_MAIN_CONTROL );
            printf( "CAP Event: 0x%02X\r\n", ucRead );

            vCAP129xWrite( CAP129x_REG_MAIN_CONTROL, 0xCC );

            ucCurrentEvent = 0;
            ucCurrentEvent = ucCAP129xRead( CAP129x_REG_SENSOR_INPUT_STATUS );
            printf( "Channel event: 0x%02X\r\n", ucCurrentEvent );

            ucLastEvent = ucCurrentEvent;
        }
    }
}