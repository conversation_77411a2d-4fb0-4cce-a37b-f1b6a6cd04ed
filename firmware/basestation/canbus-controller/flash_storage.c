#include "pico/stdlib.h"
#include "hardware/flash.h"
#include "hardware/sync.h"
#include <string.h>
#include <stddef.h>

#include "flash_storage.h"

// Flash task handle and queue
static TaskHandle_t xFlashTaskHandle = NULL;
static QueueHandle_t xFlashQueue = NULL;
static bool flash_task_running = false;

// This single static buffer is used by the flash task for all operations
static uint8_t flash_write_buffer[FLASH_SECTOR_SIZE] __attribute__((section(".data")));

// ------------------------------------------------------------------------------------------------------------------
// Read the status reg until B<PERSON>Y clears
#define FLASH_CMD_READ_STATUS 0x05

static void wait_for_flash_ready(void) {
    uint8_t cmd = FLASH_CMD_READ_STATUS;
    uint8_t stat = 0;
    do {
        // Correct call: &cmd is your 1‑byte TX buffer,
        // &stat is your 1‑byte RX buffer,
        // 1 == number of bytes
        flash_do_cmd(&cmd, &stat, 1);

        // Let the FreeRTOS tick fire so other tasks (and delays) can progress
        taskYIELD();
    } while (stat & 0x01);  // bit 0 == BUSY
}


// In RAM: only erase/program, no RTOS calls, no cache flushes
static void __not_in_flash_func(erase_sector)(uint32_t base) {
    uint32_t ints = save_and_disable_interrupts();
    flash_range_erase(base, FLASH_SECTOR_SIZE);
    restore_interrupts(ints);
}
static void __not_in_flash_func(program_sector)(uint32_t base, const uint8_t *buf) {
    uint32_t ints = save_and_disable_interrupts();
    flash_range_program(base, buf, FLASH_SECTOR_SIZE);
    restore_interrupts(ints);
}

// Helper: write a single chunk within one sector with proper interrupt handling
static void write_sector_chunk(uint32_t sector_base,
                               uint32_t chunk_off,
                               const uint8_t *data,
                               size_t len)
{
    // Read entire sector into our static buffer
    memcpy(flash_write_buffer, (const void*)(XIP_BASE + sector_base), FLASH_SECTOR_SIZE);
    // Patch just our bytes
    memcpy(flash_write_buffer + chunk_off, data, len);

    // Erase sector
    erase_sector(sector_base);
    
    wait_for_flash_ready();

    // Program sector
    program_sector(sector_base, flash_write_buffer);

    wait_for_flash_ready();
}

// Helper: stream‑write a blob across sector boundaries
static void stream_write_blob(uint32_t abs_offset,
                              const uint8_t *blob,
                              size_t blob_size)
{
    size_t offset = 0;
    while (offset < blob_size) {
        uint32_t addr       = abs_offset + offset;
        uint32_t sector     = addr & ~(FLASH_SECTOR_SIZE - 1);
        uint32_t off        = addr &  (FLASH_SECTOR_SIZE - 1);
        size_t   can_write  = FLASH_SECTOR_SIZE - off;
        size_t   wr_len     = (can_write > (blob_size - offset))
                                ? (blob_size - offset) : can_write;
        write_sector_chunk(sector, off, blob + offset, wr_len);
        offset += wr_len;
    }
}

// ------------------------------------------------------------------------------------------------------------------
// Flash task that handles all flash operations
static void flash_task(void *pvParameters) {
    flash_operation_request_t request;
    
    // Mark that the flash task is now running
    flash_task_running = true;

    printf( "[START] Flash task started\r\n" );
    
    for (;;) {
        // Wait for a flash operation request
        if (xQueueReceive(xFlashQueue, &request, portMAX_DELAY) == pdTRUE) {
            bool result = false;

            #ifdef DEBUG_FLASH_STORAGE_ACCESS
            printf("[FLASH] Flash task received request: %d\r\n", request.op_type);
            #endif
            
            switch (request.op_type) {
                case FLASH_OP_SET_PROVISIONED: {
                    #ifdef DEBUG_FLASH_STORAGE_ACCESS
                    printf("[FLASH] Setting provisioned flag to %d\r\n", request.data.set_provisioned.value);
                    #endif

                    // Read current sector, modify provisioned flag, write back
                    stream_write_blob(FLASH_PROVISIONED_ABS_OFFSET,
                                      (const uint8_t*)&request.data.set_provisioned.value,
                                      FLASH_PROVISIONED_SIZE);

                    #ifdef DEBUG_FLASH_STORAGE_ACCESS
                    printf("[FLASH] Provisioned flag set\r\n");
                    #endif

                    result = true;
                    break;
                }
                
                case FLASH_OP_SET_PROVISIONING_CMD: {
                    #ifdef DEBUG_FLASH_STORAGE_ACCESS
                    printf("[FLASH] Setting provisioning command\r\n");
                    #endif

                    // Write the provisioning command to flash
                    stream_write_blob(FLASH_PROV_CMD_ABS_OFFSET,
                                      (const uint8_t*)&request.data.set_provisioning_cmd.cmd,
                                      FLASH_PROV_CMD_SIZE);

                    #ifdef DEBUG_FLASH_STORAGE_ACCESS
                    printf("[FLASH] Provisioning command set\r\n");
                    #endif

                    result = true;
                    break;
                }
                
                case FLASH_OP_RESET: {
                    #ifdef DEBUG_FLASH_STORAGE_ACCESS
                    printf("[FLASH] Resetting flash storage\r\n");
                    #endif

                    // Re-initialize with default values
                    memset(flash_write_buffer, 0, FLASH_SECTOR_SIZE);
                    flash_write_buffer[FLASH_INITIALIZED_OFFSET] = 1;
                    flash_write_buffer[FLASH_PROVISIONED_OFFSET] = 0;
                    
                    // Erase sector
                    erase_sector(FLASH_STORAGE_BASE_OFFSET);

                    wait_for_flash_ready();
                    
                    // Program sector
                    program_sector(FLASH_STORAGE_BASE_OFFSET, flash_write_buffer);

                    wait_for_flash_ready();

                    #ifdef DEBUG_FLASH_STORAGE_ACCESS
                    printf("[FLASH] Flash storage reset\r\n");
                    #endif

                    result = true;
                    break;
                }
                
                case FLASH_OP_READ_PROVISIONED: {
                    #ifdef DEBUG_FLASH_STORAGE_ACCESS
                    printf("[FLASH] Reading provisioned flag\r\n");
                    #endif

                    // Read provisioned flag directly from flash
                    uint8_t v = *(const uint8_t*)(XIP_BASE + FLASH_PROVISIONED_ABS_OFFSET);

                    if ( request.result_ptr != NULL ) {
                        *(bool*)(request.result_ptr) = (v != 0);
                    }

                    #ifdef DEBUG_FLASH_STORAGE_ACCESS
                    printf("[FLASH] Provisioned flag read: %d\r\n", v);
                    #endif

                    result = true;
                    break;
                }
                
                case FLASH_OP_READ_PROVISIONING_CMD: {
                    #ifdef DEBUG_FLASH_STORAGE_ACCESS
                    printf("[FLASH] Reading provisioning command\r\n");
                    #endif

                    // Read provisioning command directly from flash
                    memcpy(request.data.read_provisioning_cmd.cmd_ptr,
                           (const void*)(XIP_BASE + FLASH_PROV_CMD_ABS_OFFSET),
                           FLASH_PROV_CMD_SIZE);

                    #ifdef DEBUG_FLASH_STORAGE_ACCESS
                    printf("[FLASH] Provisioning command read\r\n");
                    #endif

                    result = true;
                    break;
                }
            }
            
            // For write operations, set the result if a pointer was provided
            if (request.op_type != FLASH_OP_READ_PROVISIONED && request.result_ptr != NULL) {
                *(bool*)request.result_ptr = result;
            }

            if (request.completion_semaphore != NULL) {
                xSemaphoreGive(request.completion_semaphore);
            }
            
            // Small delay to prevent overwhelming the system and give other tasks a chance
            vTaskDelay(pdMS_TO_TICKS(1));
        }
    }
}

// ------------------------------------------------------------------------------------------------------------------
// Public API

void flash_setup(void) {
    xFlashQueue = xQueueCreate(FLASH_QUEUE_LENGTH, sizeof(flash_operation_request_t));
    if (xFlashQueue == NULL) {
        // Handle queue creation failure
        return;
    }

    const uint8_t initialized = *(const uint8_t*)(XIP_BASE + FLASH_INITIALIZED_ABS_OFFSET);

    if (initialized != 1) {
        // This runs before the scheduler, so no locks are needed.
        memset(flash_write_buffer, 0, FLASH_SECTOR_SIZE);
        flash_write_buffer[FLASH_INITIALIZED_OFFSET] = 1;
        flash_write_buffer[FLASH_PROVISIONED_OFFSET] = 0;

        erase_sector(FLASH_STORAGE_BASE_OFFSET);
        program_sector(FLASH_STORAGE_BASE_OFFSET, flash_write_buffer);
    }

    BaseType_t result = xTaskCreate(flash_task,
                                   "FlashTask",
                                   FLASH_TASK_STACK_SIZE,
                                   NULL,
                                   FLASH_TASK_PRIORITY,
                                   &xFlashTaskHandle);
    
    
    vTaskCoreAffinitySet( xFlashTaskHandle, 1 );

    if (result != pdPASS) {
        // Handle task creation failure
        return;
    }
}

// Synchronous reset operation that waits for completion
bool flash_reset_sync(void) {
    if (xFlashQueue == NULL) return false;

    SemaphoreHandle_t sem = xSemaphoreCreateBinary();
    if (sem == NULL) return false;

    flash_operation_request_t request = {
        .op_type = FLASH_OP_RESET,
        .result_ptr = NULL,
        .completion_semaphore = sem
    };

    if (xQueueSend(xFlashQueue, &request, pdMS_TO_TICKS(500)) != pdTRUE) {
        vSemaphoreDelete(sem);
        return false;
    }

    // Wait for the reset operation to complete
    bool success = (xSemaphoreTake(sem, pdMS_TO_TICKS(FLASH_OPERATION_TIMEOUT_MS)) == pdTRUE);
    vSemaphoreDelete(sem);

    return success;
}

bool flash_storage_is_provisioned(void) {
    if (!flash_task_running || xFlashQueue == NULL) {
        uint8_t v = *(const uint8_t*)(XIP_BASE + FLASH_PROVISIONED_ABS_OFFSET);
        return (v != 0);
    }

    bool result = false;
    SemaphoreHandle_t sem = xSemaphoreCreateBinary();
    if (sem == NULL) return false;

    flash_operation_request_t request = {
        .op_type = FLASH_OP_READ_PROVISIONED,
        .result_ptr = &result, // Pointer to our local result variable
        .completion_semaphore = sem
    };

    if (xQueueSend(xFlashQueue, &request, pdMS_TO_TICKS(FLASH_OPERATION_TIMEOUT_MS)) != pdTRUE) {
        vSemaphoreDelete(sem);
        return false;
    }

    // Block and wait for the flash_task to give the semaphore
    xSemaphoreTake(sem, portMAX_DELAY);
    vSemaphoreDelete(sem);

    return result;
}

// Synchronous version that waits for completion
bool flash_storage_set_provisioned_sync(bool value) {
    if (xFlashQueue == NULL) return false;

    SemaphoreHandle_t sem = xSemaphoreCreateBinary();
    if (sem == NULL) return false;

    flash_operation_request_t request = {
        .op_type = FLASH_OP_SET_PROVISIONED,
        .data.set_provisioned.value = value,
        .result_ptr = NULL,
        .completion_semaphore = sem
    };

    if (xQueueSend(xFlashQueue, &request, pdMS_TO_TICKS(500)) != pdTRUE) {
        vSemaphoreDelete(sem);
        return false;
    }

    // Wait for the write operation to complete
    bool success = (xSemaphoreTake(sem, pdMS_TO_TICKS(FLASH_OPERATION_TIMEOUT_MS)) == pdTRUE);
    vSemaphoreDelete(sem);

    return success;
}

// Synchronous version that waits for completion
bool flash_storage_set_provisioning_command_sync(const CanboProvisioningCommand *cmd) {
    if (xFlashQueue == NULL || cmd == NULL) return false;

    SemaphoreHandle_t sem = xSemaphoreCreateBinary();
    if (sem == NULL) return false;

    flash_operation_request_t request = {
        .op_type = FLASH_OP_SET_PROVISIONING_CMD,
        .data.set_provisioning_cmd.cmd = *cmd,
        .result_ptr = NULL,
        .completion_semaphore = sem
    };

    if (xQueueSend(xFlashQueue, &request, pdMS_TO_TICKS(500)) != pdTRUE) {
        vSemaphoreDelete(sem);
        return false;
    }

    // Wait for the write operation to complete
    bool success = (xSemaphoreTake(sem, pdMS_TO_TICKS(FLASH_OPERATION_TIMEOUT_MS)) == pdTRUE);
    vSemaphoreDelete(sem);

    return success;
}

bool flash_storage_get_provisioning_command(CanboProvisioningCommand *cmd) {
    if (cmd == NULL) return false;
    
    if (!flash_task_running || xFlashQueue == NULL) {
        memcpy(cmd, (const void*)(XIP_BASE + FLASH_PROV_CMD_ABS_OFFSET), FLASH_PROV_CMD_SIZE);
        return true;
    }

    SemaphoreHandle_t sem = xSemaphoreCreateBinary();
    if (sem == NULL) return false;

    flash_operation_request_t request = {
        .op_type = FLASH_OP_READ_PROVISIONING_CMD,
        .data.read_provisioning_cmd.cmd_ptr = cmd,
        .result_ptr = NULL,
        .completion_semaphore = sem
    };

    if (xQueueSend(xFlashQueue, &request, pdMS_TO_TICKS(FLASH_OPERATION_TIMEOUT_MS)) != pdTRUE) {
        vSemaphoreDelete(sem);
        return false;
    }

    // Block and wait, preventing a dangling pointer and guaranteeing the memcpy is complete
    bool success = (xSemaphoreTake(sem, pdMS_TO_TICKS(FLASH_OPERATION_TIMEOUT_MS)) == pdTRUE);
    vSemaphoreDelete(sem);

    return success;
}
