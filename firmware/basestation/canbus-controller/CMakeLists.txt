# CMake minimum required version
cmake_minimum_required(VERSION 3.12)

set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# If you enable this make sure you first flash ../picoboot3 on your pico
set(PICO_USE_CANBO_OTA ON)

set(SOURCES
    main.cpp
    flash_storage.c
    #### hardware/peripheral tasks
    can_task.cpp
    uart_task.cpp
    io_task.cpp
    touch_task.c
    test.c
    #### hardwre handlers
    peripherals/irq_handler.c
    peripherals/i2c.c
    cap129x/CAP129x.c
    #### business logic tasks
)

# Add executable. Default name is the project name, version 0.1
add_executable(canbus-controller ${SOURCES})

# Add RP2350 definition for our target
target_compile_definitions(canbus-controller PRIVATE PICO_RP2350)

# Pull in FreeRTOS Kernel
set(FREERTOS_KERNEL_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../lib/FreeRTOS-Kernel)
include(FREERTOS_KERNEL_import.cmake)

pico_set_program_name(canbus-controller "canbus-controller")
pico_set_program_version(canbus-controller "0.1")

# Modify the below lines to enable/disable output over UART/USB
pico_enable_stdio_uart(canbus-controller 1)
pico_enable_stdio_usb(canbus-controller 1)

# Add the standard include files to the build
target_include_directories(canbus-controller PRIVATE
    ${CMAKE_CURRENT_LIST_DIR}/../can
    ${CMAKE_CURRENT_LIST_DIR}/../lib/FreeRTOS-Kernel/include
    ${CMAKE_CURRENT_LIST_DIR}/../common
    ${CMAKE_CURRENT_LIST_DIR}/../
    ${CMAKE_CURRENT_LIST_DIR}/
    ${CMAKE_CURRENT_BINARY_DIR}
)

# Add any user requested libraries
target_link_libraries(canbus-controller
    # pico sdk libraries
    pico_stdlib
    hardware_uart
    hardware_i2c
    hardware_gpio
    hardware_irq
    hardware_pio
    hardware_adc
    hardware_pwm
    # FreeRTOS SMP support libraries
    pico_async_context_freertos
    FreeRTOS-Kernel-Heap3
    # User defined libraries
    commands-proto
    can
    common
    uart
)

pico_add_extra_outputs(canbus-controller)

# Linker
if (PICO_USE_CANBO_OTA)
    # this removes the default boot2 loader and offsets the start of the flash memory
    set_property(TARGET canbus-controller APPEND_STRING PROPERTY LINK_FLAGS "-Wl,--print-memory-usage")
    pico_set_linker_script(canbus-controller ${CMAKE_CURRENT_LIST_DIR}/memmap_default_rp2350.ld)
endif()
