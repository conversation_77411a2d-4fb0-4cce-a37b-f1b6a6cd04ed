#include "test.h"

TaskHandle_t xIOTestTaskHandle;
TaskHandle_t xADCTestTaskHandle;
TaskHandle_t xPWMTestTaskHandle;

static IO3PinParams_t threeInput[ NUM_INPUT_3_PIN ] = INPUT_3_PIN_DEFAULT_PARAMS();
static IO2PinParams_t twoInput[ NUM_INPUT_2_PIN ] = INPUT_2_PIN_DEFAULT_PARAMS();
static IO2PinParams_t twoOut[ NUM_OUTPUT_PIN ] = OUTPUT_2_PIN_DEFAULT_PARAMS();
static KnobParams_t knob_param = KNOB_DEFAULT_PARAMS();
static PWMParams_t pwm_param = PWM_DEFAULT_PARAMS();

void vIOTestTask( void * pvParameters ){
    uint8_t last[ NUM_INPUT_3_PIN * 2 ];
    uint8_t actual[ NUM_INPUT_3_PIN * 2 ];
    uint8_t last2[ NUM_INPUT_2_PIN ];
    uint8_t actual2[ NUM_INPUT_2_PIN ];

    memset( last, 0, sizeof( last ) );
    memset( actual, 0, sizeof( actual ) );
    memset( last2, 0, sizeof( last2 ) );
    memset( actual2, 0, sizeof( actual2 ) );

    // Initialize input
        for( size_t i = 0; i < NUM_INPUT_3_PIN; ++i ){
            gpio_init( threeInput[i].gpio0.gpio_pin );
            gpio_set_dir( threeInput[i].gpio0.gpio_pin, GPIO_IN );
            gpio_pull_up( threeInput[i].gpio0.gpio_pin );
            gpio_init( threeInput[i].gpio1.gpio_pin );
            gpio_set_dir( threeInput[i].gpio1.gpio_pin, GPIO_IN );
            gpio_pull_up( threeInput[i].gpio1.gpio_pin );
        }
        for( size_t i = 0; i < NUM_INPUT_2_PIN; ++i ){
            gpio_init( (uint32_t) twoInput[i].gpio_pin );
            gpio_set_dir( (uint32_t) twoInput[i].gpio_pin, GPIO_IN );
            gpio_pull_up( (uint32_t) twoInput[i].gpio_pin );
        }
    // Initialize output
        for( size_t i = 0; i < NUM_OUTPUT_PIN; ++i ){
            gpio_init( (uint32_t) twoOut[i].gpio_pin );
            gpio_set_dir( (uint32_t) twoOut[i].gpio_pin, GPIO_OUT );
            gpio_put( (uint32_t) twoOut[i].gpio_pin, 0 );
        }
    // Start the task
    printf( "[START] Test task started\r\n" );

    gpio_put( twoOut[ 0 ].gpio_pin, 1 );
    gpio_put( twoOut[ 1 ].gpio_pin, 1 );
    gpio_put( twoOut[ 2 ].gpio_pin, 1 );
    gpio_put( twoOut[ 3 ].gpio_pin, 1 );

    for( ;; ){
        // for( size_t i = 0; i < NUM_INPUT_3_PIN; ++i ){
        //     actual[ i * 2 ] = gpio_get( threeInput[i].gpio0.gpio_pin );
        //     actual[ i * 2 + 1 ] = gpio_get( threeInput[i].gpio1.gpio_pin );

        //     if( last[ i * 2 ] != actual[ i * 2 ] ){
        //         printf( "3 Pin Input changed %d: %d\r\n", i, gpio_get( threeInput[i].gpio0.gpio_pin ) );
        //         last[ i * 2 ] = actual[ i * 2 ];
        //     }

        //     if( last[ i * 2 + 1 ] != actual[ i * 2 + 1 ] ){
        //         printf( "3 Pin Input changed %d: %d\r\n", i, gpio_get( threeInput[i].gpio1.gpio_pin ) );
        //         last[ i * 2 + 1 ] = actual[ i * 2 + 1 ];
        //     }
        // }
        // for( size_t i = 0; i < NUM_INPUT_2_PIN; ++i ){
        //     actual2[ i ] = gpio_get( (uint32_t) twoInput[i].gpio_pin );

        //     if( last2[ i ] != actual2[ i ] ){
        //         printf( "2 Pin Input changed %d: %d\r\n", i, gpio_get( (uint32_t) twoInput[i].gpio_pin ) );
        //         last2[ i ] = actual2[ i ];

        //         gpio_put( twoOut[ i * 2 ].gpio_pin, actual2[ i ] );
        //         gpio_put( twoOut[ i * 2 + 1 ].gpio_pin, actual2[ i ] );
        //     }
        // }
        vTaskDelay( pdMS_TO_TICKS( 100 ) );
    }
}

void vADCTestTask( void * pvParameters ){
    const float fConversionFactor = ( 3.3f / ( 1 << 12 ) );
    float fConversion = 0.0;

    // Set up ADC
    adc_init();
    adc_gpio_init( knob_param.pin );
    adc_select_input( knob_param.channel );
    //
    printf( "Knob polling task\r\n" );
    for( ;; ){
        // TODO: Poll in a constant rate so if a spin is aggresive
        uint16_t usRead = adc_read();
        fConversion = usRead * fConversionFactor;
        printf( "ADC read: %.2f\r\n", fConversion );
        vTaskDelay( pdMS_TO_TICKS( 100 ) );
    }
}

void vPWMTestTask( void * pvParameters ){
    bool xIsUpwards = true;
    uint16_t usStep = 0;
    // Initialize PWM
    gpio_set_function( pwm_param.pin, GPIO_FUNC_PWM );
    pwm_param.slice = pwm_gpio_to_slice_num( pwm_param.pin );
    pwm_param.channel = pwm_gpio_to_channel( pwm_param.pin );

    #ifdef DEBUG_PWM_DIMMING_SETUP
    printf( "[PWM] Slice: %d\r\n", pwm_param.slice );
    printf( "[PWM] Channel: %d\r\n", pwm_param.channel );
    #endif

    pwm_param.config = pwm_get_default_config(); // has clkdiv value of 1 for max resolution

    uint16_t wrap_value = (SYS_CLOCK_150MHZ / pwm_param.frequency) - 1;
    pwm_config_set_wrap( &pwm_param.config, wrap_value ); // wrap = 149999 for 1kHz

    pwm_init( pwm_param.slice, &pwm_param.config, false );

    // Continuous mode - reloads the counter automatically
    pwm_set_enabled( pwm_param.slice, true );
    // Start the task
    printf( "[START] PWM test task started\r\n" );
    for( ;; ){
        if( xIsUpwards == true ){
            usStep += 1;
            if( usStep > 90 ){
                xIsUpwards = false;
            }
        }
        else{
            usStep -= 1;
            if( usStep == 0 ){
                xIsUpwards = true;
            }
        }

        pwm_param.duty_cycle = pwm_param.config.top * usStep / 100;
        pwm_set_chan_level( pwm_param.slice, pwm_param.channel, pwm_param.duty_cycle );
        vTaskDelay( pdMS_TO_TICKS( 10 ) );
    }
}