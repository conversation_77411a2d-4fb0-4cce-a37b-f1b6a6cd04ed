
#ifndef __PINOUT_H__
#define __PINOUT_H__

#include "types.h"

#define PIO0    0
#define PIO1    1
#define PIO2    2

// In next version:
// On Board LED -> GPIO32
// Soft Reset -> GPIO6
#define ON_BOARD_LED_PIN_PREV   GPIO2_PIN
#define ON_BOARD_LED_PIN        GPIO32_PIN
#define RESET_BUTTON_PIN        GPIO6_PIN

#define NUM_OUTPUT_PIN          4
#define NUM_INPUT_PIN           10
#define NUM_INPUT_3_PIN         4
#define NUM_INPUT_2_PIN         2
#define NUM_OUTPUT_2_PIN        4
#define NUM_LEDS_PAD            6
#define NUM_CAN_CHANNELS        1
#define NUM_UART_CHANNELS       1

#define SYS_CLOCK_150MHZ 150000000UL

/*
    #define ON_BOARD_LED_PIN    GPIO25_PIN
    #define CAN_PARAMS_DEFAULT() { \
        { \
            .baudrate = 0, \
            .sys_clock = 0, \
            .irq_num = PIO0_IRQ_0, \
            .irq_cb_func = NULL, \
            .rx_pin = GPIO17_PIN, \
            .tx_pin = GPIO18_PIN, \
            .pio_num = PIO0, \
            .rx_cb_func = NULL, \
        }, \
    };
*/

/*
#define ON_BOARD_LED_PIN    GPIO25_PIN
#define CAN_PARAMS_DEFAULT() { \
    { \
        .baudrate = 0, \
        .sys_clock = 0, \
        .irq_num = PIO0_IRQ_0, \
        .irq_cb_func = NULL, \
        .rx_pin = GPIO5_PIN, \
        .tx_pin = GPIO4_PIN, \
        .pio_num = PIO0, \
        .rx_cb_func = NULL, \
    }, \
};
 */

// Previous version:
// Rx -> GPIO5
// Tx -> GPIO4
#define CAN_PARAMS_DEFAULT_PREV() { \
    { \
        .en = 0, \
        .baudrate = 0, \
        .sys_clock = SYS_CLOCK_150MHZ, \
        .irq_num = PIO0_IRQ_0, \
        .irq_cb_func = NULL, \
        .tx_pin = GPIO4_PIN, \
        .rx_pin = GPIO5_PIN, \
        .pio_num = PIO0, \
        .rx_cb_func = NULL, \
    }, \
};
// Next version:
// Rx -> GPIO3
// Tx -> GPIO2
#define CAN_PARAMS_DEFAULT() { \
    { \
        .en = 0, \
        .baudrate = 0, \
        .sys_clock = SYS_CLOCK_150MHZ, \
        .irq_num = PIO0_IRQ_0, \
        .irq_cb_func = NULL, \
        .tx_pin = GPIO2_PIN, \
        .rx_pin = GPIO3_PIN, \
        .pio_num = PIO0, \
        .rx_cb_func = NULL, \
    }, \
}

// Previous version:
// Debugger Tx -> GPIO0, Rx -> GPIO1
#define UART_PARAMS_DEFAULT_PREV() { \
    {   /* Debugger */ \
        .baudrate = 115200, \
        .uart_inst = uart0, \
        .tx_pin = GPIO0_PIN, \
        .rx_pin = GPIO1_PIN, \
        .hw_ctrl = false, \
        .fifo_en = false, \
        .irq_num = 0, \
        .irq_cb_func = NULL, \
        .dma_rx = { 0 }, \
        .dma_tx = { 0 }, \
    }, \
}
// Next version:
// Debugger Tx -> GPIO0, Rx -> GPIO1 (unchanged)
#define UART_PARAMS_DEFAULT() { \
    {   /* Debugger */ \
        .baudrate = 115200, \
        .uart_inst = uart0, \
        .tx_pin = GPIO0_PIN, \
        .rx_pin = GPIO1_PIN, \
        .hw_ctrl = false, \
        .fifo_en = false, \
        .irq_num = 0, \
        .irq_cb_func = NULL, \
        .dma_rx = { 0 }, \
        .dma_tx = { 0 }, \
    }, \
}

// Previous version:
#define INPUT_DEFAULT_PARAMS_PREV(){ \
    { /* IN 1 CONN 1 */ \
        .mode = 0, \
        .pin = GPIO20_PIN \
    }, \
    { /* IN 2 CONN 1 */ \
        .mode = 0, \
        .pin = GPIO21_PIN \
    }, \
    { /* IN 3 CONN 2 */ \
        .mode = 0, \
        .pin = GPIO22_PIN \
    }, \
    { /* IN 4 CONN 2 */ \
        .mode = 0, \
        .pin = GPIO23_PIN \
    }, \
    { /* IN 5 CONN 3 */ \
        .mode = 0, \
        .pin = GPIO10_PIN \
    }, \
    { /* IN 6 CONN 3 */ \
        .mode = 0, \
        .pin = GPIO11_PIN \
    }, \
    { /* IN 7 CONN 4 */ \
        .mode = 0, \
        .pin = GPIO8_PIN \
    }, \
    { /* IN 8 CONN 4 */ \
        .mode = 0, \
        .pin = GPIO9_PIN \
    }, \
    { /* IN 9 CONN 5 -- Dry Contact */ \
        .mode = 0, \
        .pin = GPIO6_PIN \
    }, \
    { /* IN 10 CONN 6 -- Dry Contact */ \
        .mode = 0, \
        .pin = GPIO7_PIN \
    } \
}
// Next version:
#define INPUT_DEFAULT_PARAMS(){ \
    { /* IN 1 CONN 1 */ \
        .mode = 0, \
        .pin = GPIO30_PIN \
    }, \
    { /* IN 2 CONN 1 */ \
        .mode = 0, \
        .pin = GPIO31_PIN \
    }, \
    { /* IN 3 CONN 2 */ \
        .mode = 0, \
        .pin = GPIO28_PIN \
    }, \
    { /* IN 4 CONN 2 */ \
        .mode = 0, \
        .pin = GPIO29_PIN \
    }, \
    { /* IN 5 CONN 3 */ \
        .mode = 0, \
        .pin = GPIO12_PIN \
    }, \
    { /* IN 6 CONN 3 */ \
        .mode = 0, \
        .pin = GPIO13_PIN \
    }, \
    { /* IN 7 CONN 4 */ \
        .mode = 0, \
        .pin = GPIO14_PIN \
    }, \
    { /* IN 8 CONN 4 */ \
        .mode = 0, \
        .pin = GPIO15_PIN \
    }, \
    { /* IN 9 CONN 5 -- Dry Contact */ \
        .mode = 0, \
        .pin = GPIO16_PIN \
    }, \
    { /* IN 10 CONN 6 -- Dry Contact */ \
        .mode = 0, \
        .pin = GPIO17_PIN \
    } \
}

// Previous version:
// MJ1 -> GPIO20, GPIO21
// MJ2 -> GPIO22, GPIO23
// MJ3 -> GPIO10, GPIO11
// MJ4 -> GPIO8, GPIO9
#define INPUT_3_PIN_DEFAULT_PARAMS_PREV(){ \
    { /* CONN 1 - MJ */ \
        .gpio0 = { \
            .gpio_pin = GPIO20_PIN, \
            .gpio_state = false, \
        }, \
        .gpio1 = { \
            .gpio_pin = GPIO21_PIN, \
            .gpio_state = false, \
        }, \
        .gpio_state = false, \
    }, \
    { /* CONN 2 - MJ */ \
        .gpio0 = { \
            .gpio_pin = GPIO22_PIN, \
            .gpio_state = false, \
        }, \
        .gpio1 = { \
            .gpio_pin = GPIO23_PIN, \
            .gpio_state = false, \
        }, \
        .gpio_state = false, \
    }, \
    { /* CONN 3 - MJ */ \
        .gpio0 = { \
            .gpio_pin = GPIO10_PIN, \
            .gpio_state = false, \
        }, \
        .gpio1 = { \
            .gpio_pin = GPIO11_PIN, \
            .gpio_state = false, \
        }, \
        .gpio_state = false, \
    }, \
    { /* CONN 4 - MJ */ \
        .gpio0 = { \
            .gpio_pin = GPIO8_PIN, \
            .gpio_state = false, \
        }, \
        .gpio1 = { \
            .gpio_pin = GPIO9_PIN, \
            .gpio_state = false, \
        }, \
    }, \
}
// Next version:
// MJ1 -> GPIO30, GPIO31
// MJ2 -> GPIO28, GPIO29
// MJ3 -> GPIO12, GPIO13
// MJ4 -> GPIO14, GPIO15
#define INPUT_3_PIN_DEFAULT_PARAMS(){ \
    { /* CONN 1 - MJ */ \
        .gpio0 = { \
            .gpio_pin = GPIO30_PIN, \
            .gpio_state = false, \
        }, \
        .gpio1 = { \
            .gpio_pin = GPIO31_PIN, \
            .gpio_state = false, \
        }, \
        .gpio_state = false, \
    }, \
    { /* CONN 2 - MJ */ \
        .gpio0 = { \
            .gpio_pin = GPIO28_PIN, \
            .gpio_state = false, \
        }, \
        .gpio1 = { \
            .gpio_pin = GPIO29_PIN, \
            .gpio_state = false, \
        }, \
        .gpio_state = false, \
    }, \
    { /* CONN 3 - MJ */ \
        .gpio0 = { \
            .gpio_pin = GPIO12_PIN, \
            .gpio_state = false, \
        }, \
        .gpio1 = { \
            .gpio_pin = GPIO13_PIN, \
            .gpio_state = false, \
        }, \
        .gpio_state = false, \
    }, \
    { /* CONN 4 - MJ */ \
        .gpio0 = { \
            .gpio_pin = GPIO14_PIN, \
            .gpio_state = false, \
        }, \
        .gpio1 = { \
            .gpio_pin = GPIO15_PIN, \
            .gpio_state = false, \
        }, \
        .gpio_state = false, \
    }, \
}

// Previous version:
// Door1 -> GPIO6
// Door2 -> GPIO7
#define INPUT_2_PIN_DEFAULT_PARAMS_PREV(){ \
    { /* CONN 5 -- Dry Contact */ \
        .gpio_pin = GPIO16_PIN, \
        .gpio_state = false, \
    }, \
    { /* CONN 6 -- Dry Contact */ \
        .gpio_pin = GPIO17_PIN, \
        .gpio_state = false, \
    }, \
}
// Next version:
// Door1 -> GPIO16
// Door2 -> GPIO17
#define INPUT_2_PIN_DEFAULT_PARAMS(){ \
    { /* CONN 5 -- Dry Contact */ \
        .gpio_pin = GPIO16_PIN, \
        .gpio_state = false, \
    }, \
    { /* CONN 6 -- Dry Contact */ \
        .gpio_pin = GPIO17_PIN, \
        .gpio_state = false, \
    }, \
}

// Previous version:
// Relay1 -> GPIO19
// Relay2 -> GPIO20
#define OUTPUT_2_PIN_DEFAULT_PARAMS_PREV(){ \
    { /* CONN 1 -- Relay */ \
        .gpio_pin = GPIO19_PIN, \
        .gpio_state = false, \
    }, \
    { /* CONN 2 -- Relay */ \
        .gpio_pin = GPIO20_PIN, \
        .gpio_state = false, \
    }, \
}
// Next version:
// Relay1 -> GPIO19
// Relay2 -> GPIO20
// Relay3 -> GPIO23
// Relay4 -> GPIO22
#define OUTPUT_2_PIN_DEFAULT_PARAMS(){ \
    { /* CONN 1 -- Relay */ \
        .gpio_pin = GPIO19_PIN, \
        .gpio_state = false, \
    }, \
    { /* CONN 2 -- Relay */ \
        .gpio_pin = GPIO20_PIN, \
        .gpio_state = false, \
    }, \
    { /* CONN 3 -- Relay */ \
        .gpio_pin = GPIO23_PIN, \
        .gpio_state = false, \
    }, \
    { /* CONN 4 -- Relay */ \
        .gpio_pin = GPIO22_PIN, \
        .gpio_state = false, \
    }, \
}

// Previous version:
// Potentiometer -> GPIO27
#define KNOB_DEFAULT_PARAMS_PREV() {\
    .channel = 1,\
    .pin = GPIO27_PIN,\
}
// Next version:
// Potentiometer -> GPIO41
#define KNOB_DEFAULT_PARAMS() {\
    .channel = 1,\
    .pin = GPIO41_PIN, \
}

// Previous version:
// Reset Pin -> GPIO14
// IRQ Pin -> GPIO15
// ON LED1 -> GPIO16
// OFF LED1 -> GPIO17
// ON LED2 -> GPIO28
// OFF LED2 -> GPIO29
// ON LED3 -> GPIO18
// OFF LED3 -> GPIO26
// I2C SDA -> GPIO12
// I2C SCL -> GPIO13
#define TOUCHPAD_DEFAULT_PARAMS_PREV() {\
    .reset_pin = GPIO14_PIN, \
    .irq_pin = GPIO15_PIN, \
    .irq_num = 255, \
    .irq_cb_func = NULL, \
    .LED = { \
        { /* ON LED PAD 1 */ \
            .pin = GPIO16_PIN, \
            .mode = 0, \
        }, \
        { /* OFF LED PAD 1 */ \
            .pin = GPIO17_PIN, \
            .mode = 0, \
        },\
        { /* ON LED PAD 2 */ \
            .pin = GPIO28_PIN, \
            .mode = 0, \
        }, \
        { /* OFF LED PAD 2 */ \
            .pin = GPIO29_PIN, \
            .mode = 0, \
        }, \
        { /* ON LED PAD 3 */ \
            .pin = GPIO18_PIN, \
            .mode = 0, \
        }, \
        { /* OFF LED PAD 3 */ \
            .pin = GPIO26_PIN, \
            .mode = 0, \
        }, \
    }, \
    .i2c_config = { \
        .i2c_inst = i2c0, \
        .baudrate = I2C_BUS_CLK_SPEED_100KHZ, \
        .sda_pin = GPIO12_PIN, \
        .scl_pin = GPIO13_PIN, \
    }, \
}
// Next version:
// Reset Pin -> GPIO11
// IRQ Pin -> GPIO10
// ON LED1 -> GPIO43
// OFF LED1 -> GPIO42
// ON LED2 -> GPIO44
// OFF LED2 -> GPIO45
// ON LED3 -> GPIO46
// OFF LED3 -> GPIO47
// I2C SDA -> GPIO26
// I2C SCL -> GPIO27
#define TOUCHPAD_DEFAULT_PARAMS() {\
    .reset_pin = GPIO11_PIN, \
    .irq_pin = GPIO10_PIN, \
    .irq_num = 255, \
    .irq_cb_func = NULL, \
    .LED = { \
        { /* ON LED PAD 1 */ \
            .pin = GPIO43_PIN, \
            .mode = 0, \
        }, \
        { /* OFF LED PAD 1 */ \
            .pin = GPIO42_PIN, \
            .mode = 0, \
        },\
        { /* ON LED PAD 2 */ \
            .pin = GPIO44_PIN, \
            .mode = 0, \
        }, \
        { /* OFF LED PAD 2 */ \
            .pin = GPIO45_PIN, \
            .mode = 0, \
        }, \
        { /* ON LED PAD 3 */ \
            .pin = GPIO46_PIN, \
            .mode = 0, \
        }, \
        { /* OFF LED PAD 3 */ \
            .pin = GPIO47_PIN, \
            .mode = 0, \
        }, \
    }, \
    .i2c_config = { \
        .i2c_inst = i2c1, \
        .baudrate = I2C_BUS_CLK_SPEED_100KHZ, \
        .sda_pin = GPIO26_PIN, \
        .scl_pin = GPIO27_PIN, \
    }, \
}

// Previous version:
// Source/Sink -> GPIO25
#define PWM_DEFAULT_PARAMS_PREV(){ \
    .pin = GPIO25_PIN,\
    .sys_clock = SYS_CLOCK_150MHZ, \
    .frequency = 1000, \
    .config = { 0 }, \
}
// Next version:
// Source/Sink -> GPIO21
#define PWM_DEFAULT_PARAMS(){ \
    .pin = GPIO21_PIN, \
    .sys_clock = SYS_CLOCK_150MHZ, \
    .frequency = 1000, \
    .config = { 0 }, \
}

#endif