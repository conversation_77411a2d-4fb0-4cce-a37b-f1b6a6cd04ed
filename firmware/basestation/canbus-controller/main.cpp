#include "main.h"

TaskHandle_t xBlinkingLEDTaskHandle;
void vBlinkingLEDTask( void * pvParameters ){

    gpio_init( ON_BOARD_LED_PIN_PREV );
    gpio_set_dir( ON_BOARD_LED_PIN_PREV, GPIO_OUT );

    printf("[START] Blinking LED task created\r\n");

    for( ;; ){
        gpio_put( ON_BOARD_LED_PIN_PREV, 1 );
        vTaskDelay( pdMS_TO_TICKS( 250 ) );
        gpio_put( ON_BOARD_LED_PIN_PREV, 0 );
        vTaskDelay( pdMS_TO_TICKS( 250 ) );
    }
}

int main(){
    stdio_init_all();
    stdio_usb_init();

    sleep_ms( 1000 );
    printf("\n[START] Canbus controller booting...\r\n");

    // Initialize the active_configuration mutex
    active_config_mutex = xSemaphoreCreateMutex();
    if (active_config_mutex == NULL) {
        printf("[ERROR] Failed to create active_config_mutex\r\n");
    }

    flash_setup();

    // Read configuration from flash storage safely
    CanboProvisioningCommand config = CanboProvisioningCommand_init_zero;
    if (flash_storage_get_provisioning_command(&config)) {
        // Set the configuration safely using the mutex-protected function
        if (!set_active_configuration(&config)) {
            printf("[ERROR] Failed to set active configuration\r\n");
        } else {
            printf( ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\r\n" );
            #ifdef DEBUG_SETUP
            printf("[SETUP] ID: %d\r\n", config.node_id);
            printf("[SETUP] Version: %s\r\n", config.version);
            printf("[SETUP] QR Code: %s\r\n", config.qr_code);
            printf("[SETUP] 3 Pin In Count: %d\r\n", config.three_pin_inputs_count);
            for (size_t i = 0; i < config.three_pin_inputs_count; ++i) {
                printf("[SETUP] 3 Pin In #%u: Conn #%u,%u\r\n", i, config.three_pin_inputs[i].connector_id, config.three_pin_inputs[i].connector_type);
            }
            printf("[SETUP] 2 Pin In Count: %d\r\n", config.two_pin_inputs_count);
            for (size_t i = 0; i < config.two_pin_inputs_count; ++i) {
                printf("[SETUP] 2 Pin In #%u: Conn #%u,%u\r\n", i, config.two_pin_inputs[i].connector_id, config.two_pin_inputs[i].connector_type);
            }
            printf("[SETUP] ADC Present: %s\r\n", config.has_adc_inputs ? "Yes" : "No");
            if ( config.has_adc_inputs ){
                printf("[SETUP] ADC Type: %s\r\n", config.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_KNOB ? "KNOB" : "THERMOSTAT");
                if (config.adc_inputs.which_range == CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange_offset_tag) {
                    printf("[SETUP] ADC Offset=%.2f, Scaling=%.2f\r\n",
                            config.adc_inputs.range.offset_scaling.offset, 
                            config.adc_inputs.range.offset_scaling.scaling_factor);
                } else if (config.adc_inputs.which_range == CanboProvisioningCommand_ADCInput_MinMaxRange_min_tag) {
                    printf("[SETUP] ADC Min=%.2f, Max=%.2f\r\n",
                            config.adc_inputs.range.min_max.min, 
                            config.adc_inputs.range.min_max.max);
                }
            }
            printf("[SETUP] Out Count: %d\r\n", config.outputs_count);
            for(size_t i = 0; i < config.outputs_count; ++i){
                printf("[SETUP] Out #%u: Conn #%u,%u\r\n", i, config.outputs[i].connector_id, config.outputs[i].connector_type);
            }
            if( config.has_zero_to_ten_volt_config ){
                printf("[SETUP] Type: %s\r\n", config.zero_to_ten_volt_config.type == CanboProvisioningCommand_ZeroToTenVoltConfig_Type_SOURCING ? "Sourcing" : "Sinking" );
                printf("[SETUP] Min Brightness: %.2f\r\n", config.zero_to_ten_volt_config.min_brightness);
                printf("[SETUP] Max Brightness: %.2f\r\n", config.zero_to_ten_volt_config.max_brightness);

                printf("[SETUP] Use Relay?: %d\r\n", config.zero_to_ten_volt_config.use_relay);
                printf("[SETUP] Relay Connector ID: %d\r\n", config.zero_to_ten_volt_config.relay_connector_id);
            }
            printf("[SETUP] Kleverness Connector?: %d\r\n", config.has_kleverness_connector);
            if (config.has_kleverness_connector) {
                printf("[SETUP] Kleverness Button1: %s\r\n", config.kleverness_connector.button1_enabled ? "Enabled" : "Disabled");
                printf("[SETUP] Kleverness Button2: %s\r\n", config.kleverness_connector.button2_enabled ? "Enabled" : "Disabled");
                printf("[SETUP] Kleverness Button3: %s\r\n", config.kleverness_connector.button3_enabled ? "Enabled" : "Disabled");
                printf("[SETUP] Kleverness Low Light LED: %s\r\n", config.kleverness_connector.low_light_led_enabled ? "Enabled" : "Disabled");
                printf("[SETUP] Kleverness High Light LED: %s\r\n", config.kleverness_connector.high_light_led_enabled ? "Enabled" : "Disabled");
            }
            printf( "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\r\n\r\n" );
            #else
            printf("[SETUP] Device has a QR code of: %s\r\n", config.qr_code);
            printf("[SETUP] Device has a version of: %s\r\n", config.version);
            printf( "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\r\n\r\n" );
            #endif
        }
    } else {
        printf("[ERROR] Failed to read configuration from flash\r\n");
    }

    vIRQInitData();

    xTaskCreate( vBlinkingLEDTask, "BlinkingLEDTask", 256, NULL, 1, &xBlinkingLEDTaskHandle );
    
    xTaskCreate( vCANTxTask, "CAN0TxTask", 4096, NULL, 1, &xCAN0TxTaskHandle );
    
    xTaskCreate( vInputPollingTask, "InputPollingTask", 512, NULL, 1, &xInputPollingTaskHandle );
    xTaskCreate( vInputKnobPollingTask, "InputKnobPollingTask", 512, NULL, 1, &xInputKnobPollingTaskHandle );
    xTaskCreate( vPWMDimmerTask, "PWMDimmerTask", 512, NULL, 1, &xPWMDimmerTaskHandle );

    xTaskCreate( vUART0RxTask, "UARTRxTask", 4096, NULL, 1, &xUART0RxTaskHandle );

    vTaskCoreAffinitySet( xBlinkingLEDTaskHandle, 1 );
    
    vTaskCoreAffinitySet( xCAN0TxTaskHandle, 1 );
    
    vTaskCoreAffinitySet( xInputPollingTaskHandle, 1 );
    vTaskCoreAffinitySet( xInputKnobPollingTaskHandle, 1 );
    vTaskCoreAffinitySet( xPWMDimmerTaskHandle, 1 );

    vTaskCoreAffinitySet( xUART0RxTaskHandle, 2 );

    // Uncomment these tasks to test the hardware if previous tasks are commented out
    // if not then the device will not work as expected

    // xTaskCreate( vIOTestTask, "IOTestTask", 256, NULL, 1, &xIOTestTaskHandle );
    // xTaskCreate( vADCTestTask, "ADCTestTask", 256, NULL, 1, &xADCTestTaskHandle );
    // xTaskCreate( vPWMTestTask, "PWMTestTask", 256, NULL, 1, &xPWMTestTaskHandle );
    // xTaskCreate( vTouchTask, "TouchpadTask", 1024, NULL, 1, &xTouchTaskHandle );

    // vTaskCoreAffinitySet( xIOTestTaskHandle, 1 );
    // vTaskCoreAffinitySet( xADCTestTaskHandle, 1 );
    // vTaskCoreAffinitySet( xPWMTestTaskHandle, 1 );
    // vTaskCoreAffinitySet( xTouchTaskHandle, 1 );

    vTaskStartScheduler();

    // Cleanup
    return 0;
}

//
void vApplicationStackOverflowHook( TaskHandle_t xTask, char *pcTaskName ){
    /* Optional: log, blink LED, or reset */
    printf("Stack overflow in task: %s\n", pcTaskName);
    taskDISABLE_INTERRUPTS();
    for( ;; ); // Halt
}
