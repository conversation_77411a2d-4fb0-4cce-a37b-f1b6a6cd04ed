#ifndef __DEFS_H__
#define __DEFS_H__

// ============================================================================
// DEBUG DEFINITIONS
// ============================================================================

// CAN Debug
#define DEBUG_CAN_PACKET_RX
// #define DEBUG_CAN_PACKET_TX
#define DEBUG_PROVISIONING

// UART Debug
#define DEBUG_UART_BASIC
#define DEBUG_UART_QR
#define DEBUG_UART_REPROVISION
// #define DEBUG_UART_REPROVISION_DEEP
#define DEBUG_CONFIGURATION_SET

// IO Debug
// #define DEBUG_IO
#define DEBUG_IO_TOGGLE
#define DEBUG_IO_3_MOMENTARY
#define DEBUG_IO_2_MOMENTARY
#define DEBUG_IO_PIR
#define DEBUG_IO_DOOR_SENSOR
#define DEBUG_PWM_DIMMING
#define DEBUG_PWM_RELAY
// #define DEBUG_ADC_POLLING_DEEP
#define DEBUG_ADC_POLLING_KNOB
#define DEBUG_ADC_POLLING_THERMOSTAT
// #define DEBUG_PWM_DIMMING_SETUP

// Flash Storage Debug
// #define DEBUG_FLASH_STORAGE_ACCESS

// Setup Debug
#define DEBUG_SETUP

// ============================================================================
// END DEBUG DEFINITIONS
// ============================================================================

#endif