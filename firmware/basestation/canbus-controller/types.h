#ifndef __TYPES_H__
#define __TYPES_H__

#include "hardware/pio.h"
#include "hardware/uart.h"
#include "hardware/pwm.h"

#include "can2040.h"
#include "peripherals/i2c.h"
#include "cap129x/CAP129x.h"

#include "commands.pb.h"

enum{
    CHANNEL_0 = 0,
    CHANNEL_1,
    CHANNEL_2,
    CHANNEL_LIST
};

typedef enum {
    GPIO0_PIN = 0,
    <PERSON>IO1_PIN,
    GPIO2_PIN,
    GPIO3_PIN,
    GPIO4_PIN,
    GP<PERSON>5_PIN,
    GPIO6_PIN,
    GPIO7_PIN,
    GP<PERSON>8_PIN,
    GP<PERSON>9_PIN,
    GPIO10_PIN,
    GPIO11_PIN,
    GP<PERSON><PERSON>_PIN,
    <PERSON><PERSON>13_PIN,
    GP<PERSON>14_PIN,
    GPIO15_PIN,
    GPIO16_PIN,
    GP<PERSON>17_PIN,
    GP<PERSON>18_PIN,
    GP<PERSON>19_PIN,
    GPIO20_PIN,
    GPIO21_PIN,
    GPIO22_PIN,
    GP<PERSON><PERSON>_PIN,
    <PERSON><PERSON><PERSON>_<PERSON>IN,
    <PERSON><PERSON><PERSON>_PIN,
    <PERSON><PERSON><PERSON>_<PERSON>IN,
    <PERSON><PERSON><PERSON>_<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON>IN,
    <PERSON><PERSON><PERSON>_<PERSON>IN,
    <PERSON><PERSON><PERSON>_<PERSON>IN,
    <PERSON><PERSON><PERSON>_<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>_PIN,
    GP<PERSON>33_PIN,
    GP<PERSON><PERSON>_PIN,
    GPIO35_PIN,
    GPIO36_PIN,
    GPIO37_PIN,
    GPIO38_PIN,
    GPIO39_PIN,
    GPIO40_PIN,
    GPIO41_PIN,
    GPIO42_PIN,
    GPIO43_PIN,
    GPIO44_PIN,
    GPIO45_PIN,
    GPIO46_PIN,
    GPIO47_PIN,
    GPIO_PIN
} GPIO_Pinout;

// enum{
//     GPIO_INPUT_MODE_TOGGLE = 0,
//     GPIO_INPUT_MODE_MOMENTARY,
//     GPIO_OUTPUT_MODE
// };

// enum{
//     INPUT_STATE_PRESS = 0,
//     INPUT_STATE_RELEASE
// };

typedef struct {
    bool en;
    uint32_t baudrate;
    uint32_t sys_clock;
    uint8_t irq_num;
    void * irq_cb_func;
    uint32_t tx_pin;
    uint32_t rx_pin;
    uint8_t pio_num;
    can2040_rx_cb rx_cb_func;
} CANParams_t;

typedef struct{
    uint32_t pin;
    uint8_t mode;
    uint8_t state;
} InputParams_t;

// typedef struct{
//     uint32_t gpio0;
//     uint32_t gpio1;
//     uint8_t gpio_state;
//     bool gpio0_state;
//     bool gpio1_state;
// } IO3PinParams_t;

typedef struct{
    uint32_t gpio_pin;
    bool gpio_state;
} IO2PinParams_t;

typedef struct {
    IO2PinParams_t gpio0;
    IO2PinParams_t gpio1;
    uint8_t gpio_state;
} IO3PinParams_t;

typedef struct{
    uint32_t channel;
    uint32_t pin;
} KnobParams_t;

typedef struct{
    uint32_t reset_pin;
    uint32_t irq_pin;
    uint8_t irq_num;
    void * irq_cb_func;
    InputParams_t LED[ 6 ];
    I2CParams_t i2c_config;
    PadChannels_t channels;
} TouchpadParams_t;

typedef struct{
    uint32_t pin;
    uint32_t sys_clock;
    uint32_t frequency;
    uint32_t slice;
    uint16_t duty_cycle;
    uint8_t channel;
    uint8_t irq_num;
    void * irq_cb_func;
    pwm_config config;
} PWMParams_t;

#endif // TYPES_H