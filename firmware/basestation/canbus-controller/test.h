#ifndef __TEST_H__
#define __TEST_H__

// C standard libraries
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// pico sdk libraries
#include "FreeRTOS.h"
#include "task.h"

#include "pico/stdlib.h"
#include "pico/unique_id.h"

#include "hardware/gpio.h"
#include "hardware/adc.h"
#include "hardware/pwm.h"

// user libraries
#include "pinout.h"

// global definitions
extern TaskHandle_t xIOTestTaskHandle;
extern TaskHandle_t xADCTestTaskHandle;
extern TaskHandle_t xPWMTestTaskHandle;

// function prototyping
void vIOTestTask( void * pvParameters );
void vADCTestTask( void * pvParameters );
void vPWMTestTask( void * pvParameters );

#endif