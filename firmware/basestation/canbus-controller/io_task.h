#ifndef __IO_TASK_H__
#define __IO_TASK_H__

#ifdef __cplusplus
extern "C" {
#endif

// C standard libraries
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>

// pico sdk libraries
#include "FreeRTOS.h"
#include "task.h"

#include "pico/stdlib.h"
#include "pico/unique_id.h"

#include "hardware/gpio.h"
#include "hardware/adc.h"
#include "hardware/pwm.h"

// user libraries
#include "pinout.h"
#include "can_task.h"
#include "defs.h"

// global definitions
extern TaskHandle_t xInputPollingTaskHandle;
extern TaskHandle_t xInputKnobPollingTaskHandle;
extern TaskHandle_t xPWMDimmerTaskHandle;
extern TaskHandle_t xIOTestTaskHandle;

// local definitions/enums

// function prototyping
void vIOTestTask( void * pvParameters );

void vInputPollingTask( void * pvParameters );
void vInputKnobPollingTask( void * pvParameters );

void vPWMDimmerTask( void * pvParameters );

static void vInitIO(uint32_t gpio, bool input);
static void vInitADC(uint32_t gpio, uint32_t channel);
static void vDeInitIO(uint32_t gpio);

void vPWMSetup();
uint16_t usADCtoPWM( float fValue );

// ============================================================================
// PWM DIMMING CONTROL FUNCTIONS
// ============================================================================

/**
 * @brief Sets the PWM dimming target and transition time
 *
 * @param target Dimming target level (0.0 to 1.0, where 0.0 = off, 1.0 = full brightness)
 * @param time_ms Time in milliseconds to complete the dimming transition (0 = immediate)
 */
void vSetPWMDimVariables(float target, uint32_t time_ms);

/**
 * @brief Sets the PWM dimming target level
 *
 * @param target Dimming target level (0.0 to 1.0, where 0.0 = off, 1.0 = full brightness)
 */
void vSetPWMDimTarget(float target);

/**
 * @brief Sets the PWM dimming transition time
 *
 * @param time_ms Time in milliseconds to complete the dimming transition (0 = immediate)
 */
void vSetPWMDimTime(uint32_t time_ms);

/**
 * @brief Gets the current PWM dimming target level
 *
 * @return Current dimming target level (0.0 to 1.0)
 */
float fGetPWMDimTarget(void);

/**
 * @brief Gets the current PWM dimming transition time
 *
 * @return Current dimming transition time in milliseconds
 */
uint32_t uiGetPWMDimTime(void);

/**
 * @brief Stops the current dimming transition and keeps the current brightness level
 *
 * This function immediately stops any ongoing dimming transition and sets the target
 * to the current brightness level, effectively freezing the dimming at its current state.
 */
void vStopPWMDimming(void);

/**
 * @brief Gets the current PWM dimming level
 *
 * @return Current dimming level (0.0 to 1.0)
 */
float fGetCurrentPWMDimLevel(void);

static bool same_two_pin_input_configs( CanboProvisioningCommand_TwoPinInput *active, CanboProvisioningCommand_TwoPinInput *last, size_t len );
static bool same_three_pin_input_configs( CanboProvisioningCommand_ThreePinInput *active, CanboProvisioningCommand_ThreePinInput *last, size_t len );
static bool same_adc_input_config( CanboProvisioningCommand_ADCInput *active, CanboProvisioningCommand_ADCInput *last );

#ifdef __cplusplus
}
#endif

#endif