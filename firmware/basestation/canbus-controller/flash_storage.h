#ifndef FLASH_STORAGE_H
#define FLASH_STORAGE_H

#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>

#include "FreeRTOS.h"
#include "queue.h"
#include "task.h"
#include "pico/multicore.h"
#include "semphr.h"

#include "commands.pb.h"
#include "defs.h"

#ifdef __cplusplus
extern "C" {
#endif

/* base of our 4KB reserved sector */
#define FLASH_STORAGE_BASE_OFFSET    (1536 * 1024)
#define FLASH_STORAGE_TOTAL_SIZE     FLASH_SECTOR_SIZE

/*  helper to align `x` _up_ to the next multiple of `a` */
#define ALIGN_UP(x, a)   ( (((x) + ((a) - 1)) & ~((a) - 1)) )

/* 1) Initialized flag: 1 byte, no special alignment */
#define FLASH_INITIALIZED_OFFSET     0
#define FLASH_INITIALIZED_SIZE       1

/* 2) Provisioned flag: immediately after initialized */
#define FLASH_PROVISIONED_OFFSET     (FLASH_INITIALIZED_OFFSET  + FLASH_INITIALIZED_SIZE)
#define FLASH_PROVISIONED_SIZE       1

/* 5) Provisioning command blob: 4‑byte aligned */
#define FLASH_PROV_CMD_ALIGN     4
#define FLASH_PROV_CMD_SIZE      sizeof(CanboProvisioningCommand)
#define FLASH_PROV_CMD_OFFSET    \
    ALIGN_UP(FLASH_PROVISIONED_OFFSET + FLASH_PROVISIONED_SIZE, FLASH_PROV_CMD_ALIGN)
#define FLASH_PROV_CMD_ABS_OFFSET \
    (FLASH_STORAGE_BASE_OFFSET + FLASH_PROV_CMD_OFFSET)

/* now absolute addresses */
#define FLASH_INITIALIZED_ABS_OFFSET   (FLASH_STORAGE_BASE_OFFSET + FLASH_INITIALIZED_OFFSET)
#define FLASH_PROVISIONED_ABS_OFFSET   (FLASH_STORAGE_BASE_OFFSET + FLASH_PROVISIONED_OFFSET)
#define FLASH_ACTIVE_CONFIG_ABS_OFFSET (FLASH_STORAGE_BASE_OFFSET + FLASH_ACTIVE_CONFIG_OFFSET)

/* Flash task configuration */
#define FLASH_TASK_PRIORITY           (configMAX_PRIORITIES - 2)
#define FLASH_TASK_STACK_SIZE         2048
#define FLASH_QUEUE_LENGTH            10
#define FLASH_OPERATION_TIMEOUT_MS    1000

/* Debug flag to enable flash storage access debug messages */

/* Flash operation types */
typedef enum {
    FLASH_OP_SET_PROVISIONED,
    FLASH_OP_SET_PROVISIONING_CMD,
    FLASH_OP_RESET,
    FLASH_OP_READ_PROVISIONED,
    FLASH_OP_READ_PROVISIONING_CMD
} flash_operation_type_t;

/* Flash operation request structure */
typedef struct {
    flash_operation_type_t op_type;
    xSemaphoreHandle completion_semaphore;
    union {
        struct {
            bool value;
        } set_provisioned;
        struct {
            CanboProvisioningCommand cmd;
        } set_provisioning_cmd;
        struct {
            CanboProvisioningCommand *cmd_ptr;
        } read_provisioning_cmd;
    } data;
    bool *result_ptr;  // For operations that return a boolean result
} flash_operation_request_t;

/**
 * @brief Initializes the flash storage and starts the flash task.
 * This function should be called once at startup to ensure the flash storage is initialized.
 * This function must be called BEFORE vTaskStartScheduler().
 */
void flash_setup(void);

/**
 * @brief Erases the reserved flash storage sector for user data and waits for completion.
 * This function is thread-safe and multicore-safe.
 * @return true on success, false on timeout or error.
 */
bool flash_reset_sync(void);

/**
 * @brief Returns whether a device is provisioned (i.e., whether the device ID was set).
 * This function is thread-safe and multicore-safe.
 * @returns true if the device is provisioned, false otherwise or on error.
 */
bool flash_storage_is_provisioned(void);

/**
 * @brief Sets the provisioned status of the device and waits for completion.
 * This function is thread-safe and multicore-safe.
 * @param value true if the device is provisioned, false otherwise.
 * @return true on success, false on timeout or error.
 */
bool flash_storage_set_provisioned_sync(bool value);

/**
 * @brief Writes the entire CanboProvisioningCommand struct into flash and waits for completion.
 * This function is thread-safe and multicore-safe.
 * @param cmd  Pointer to the populated struct to store.
 * @return     true on success, false on timeout or error.
 */
bool flash_storage_set_provisioning_command_sync(const CanboProvisioningCommand *cmd);

/**
 * @brief Reads back the CanboProvisioningCommand from flash.
 * This function is thread-safe and multicore-safe.
 * @param cmd  Pointer to your struct to fill in.
 * @return     true on success, false on timeout or error.
 */
bool flash_storage_get_provisioning_command(CanboProvisioningCommand *cmd);

#ifdef __cplusplus
}
#endif

#endif // FLASH_STORAGE_H
