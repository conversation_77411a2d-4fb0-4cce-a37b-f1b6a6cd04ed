CompileFlags:
    Add: [
            # Target architecture
            --target=arm-none-eabi,
            -march=armv6-m,
            -mthumb,
            -mfpu=fpv4-sp-d16,
            -mfloat-abi=hard,
            # clang warning flags, these flags will be added to the clang command line when clangd analyze the code.
            -Wunused-variable,
            -Winfinite-recursion,
            -Warray-bounds,
            -Wreturn-stack-address,
            -Werror=implicit-function-declaration,
            -Wshadow,
            -Wshadow-field-in-constructor-modified,
            -Wshadow-ivar,
            -Wuninitialized,
            -Wunused-label,
            -Wunused-lambda-capture,

            # You can also add header include paths here, each prefixed with '-I', one path per line, like this:
            # -I/usr/include
            
            # ARM toolchain includes (for standard C library)
            -I/Users/<USER>/.pico-sdk/toolchain/14_2_Rel1/arm-none-eabi/include,
            -I/Users/<USER>/.pico-sdk/toolchain/14_2_Rel1/lib/gcc/arm-none-eabi/14.2.1/include,
            -I/Users/<USER>/.pico-sdk/toolchain/14_2_Rel1/lib/gcc/arm-none-eabi/14.2.1/include-fixed,
            -I/Users/<USER>/.pico-sdk/toolchain/14_2_Rel1/arm-none-eabi/include/c++/14.2.1,
            -I/Users/<USER>/.pico-sdk/toolchain/14_2_Rel1/arm-none-eabi/include/c++/14.2.1/arm-none-eabi,
            -I/Users/<USER>/.pico-sdk/toolchain/14_2_Rel1/arm-none-eabi/include/c++/14.2.1/backward,
            -I/Users/<USER>/.pico-sdk/toolchain/14_2_Rel1/arm-none-eabi/include/newlib-nano,
            -I/Users/<USER>/.pico-sdk/toolchain/14_2_Rel1/arm-none-eabi/include/machine,
            -I/Users/<USER>/.pico-sdk/toolchain/14_2_Rel1/arm-none-eabi/include/sys,
            
            # FreeRTOS include paths
            -I./lib/FreeRTOS-Kernel/include,
            -I./lib/FreeRTOS-Kernel/portable/GCC/ARM_CM0,
            -I./lib/FreeRTOS-Kernel/portable/MemMang,
            
            # Pico SDK include paths
            -I$/Users/<USER>/.pico-sdk/src/common/pico_stdlib_headers/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_stdlib/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_unique_id/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_multicore/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_platform/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_time/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_sync/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_cyw43_arch/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_cyw43_driver/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_flash/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_stdio/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_stdio_uart/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_stdio_usb/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_lwip/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_malloc/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_rand/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_runtime/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_printf/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_double/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_float/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_mem_ops/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_bootrom/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_platform_panic/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_platform_compiler/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_platform_sections/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_async_context/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_time_adapter/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_int64_ops/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_sha256/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_i2c_slave/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_btstack/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_stdio_rtt/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_stdio_semihosting/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_aon_timer/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2_common/pico_fix/rp2040_usb_device_enumeration/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2040/pico_platform/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2040/hardware_regs/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2040/hardware_structs/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2040/boot_stage2/include,
            -I$/Users/<USER>/.pico-sdk/src/rp2350/pico_platform/include,
            -I$/Users/<USER>/.pico-sdk/src/common/pico_util/include,
            -I$/Users/<USER>/.pico-sdk/src/common/pico_time/include,
            -I$/Users/<USER>/.pico-sdk/src/common/pico_sync/include,
            -I$/Users/<USER>/.pico-sdk/src/common/pico_binary_info/include,
            -I$/Users/<USER>/.pico-sdk/src/common/pico_base_headers/include,
            -I$/Users/<USER>/.pico-sdk/src/common/pico_bit_ops_headers/include,
            -I$/Users/<USER>/.pico-sdk/src/common/pico_divider_headers/include,
            -I$/Users/<USER>/.pico-sdk/src/common/pico_usb_reset_interface_headers/include,
            -I$/Users/<USER>/.pico-sdk/src/common/boot_picoboot_headers/include,
            -I$/Users/<USER>/.pico-sdk/src/common/boot_picobin_headers/include,
            -I$/Users/<USER>/.pico-sdk/src/common/hardware_claim/include,
            -I$/Users/<USER>/.pico-sdk/src/host/pico_platform/include,
            -I$/Users/<USER>/.pico-sdk/src/host/pico_runtime/include,
            -I$/Users/<USER>/.pico-sdk/src/host/pico_stdio/include,
            -I$/Users/<USER>/.pico-sdk/src/host/pico_multicore/include,
            -I$/Users/<USER>/.pico-sdk/src/host/pico_time_adapter/include,
        ]

# for more options, type ctrl + space to bring up the auto-completion list