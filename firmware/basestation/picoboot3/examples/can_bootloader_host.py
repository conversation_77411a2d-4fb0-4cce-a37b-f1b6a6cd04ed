#!/usr/bin/env python3
"""
Example host program to interact with picoboot3 CAN bootloader.
This demonstrates how to update firmware over CAN bus.
"""

import can
import struct
import time
import sys
import os

class PicoBoot3CAN:
    def __init__(self, interface='socketcan', channel='can0', bitrate=125000):
        self.base_id = 0x700  # Must match PICOBOOT3_CAN_BASE_ID
        self.response_id = 0x701  # base_id + 1
        
        # Initialize CAN bus
        self.bus = can.interface.Bus(channel=channel, bustype=interface, bitrate=bitrate)
        
        # Command codes
        self.CMD_READY_BUSY = 0x01
        self.CMD_VERSION = 0x02
        self.CMD_READ = 0x10
        self.CMD_PROGRAM = 0x20
        self.CMD_ERASE = 0x30
        self.CMD_GO_TO_APPCODE = 0x40
        self.CMD_FLASH_SIZE = 0x50
        self.CMD_ACTIVATE = 0xA5
        
    def send_variable_length(self, data):
        """Send variable length data using CAN fragmentation"""
        # This is a simplified version - real implementation would use can_varlen protocol
        MAX_FRAG_SIZE = 5  # Based on CAN_VARLEN_MAX_FRAG_PAYLOAD
        
        # Send fragments
        offset = 0
        frag_num = 0
        total_frags = (len(data) + MAX_FRAG_SIZE - 1) // MAX_FRAG_SIZE
        
        while offset < len(data):
            frag_data = data[offset:offset + MAX_FRAG_SIZE]
            
            # Create fragment header (simplified)
            msg_data = bytes([frag_num, total_frags]) + frag_data
            
            msg = can.Message(
                arbitration_id=self.base_id,
                data=msg_data[:8],  # CAN max is 8 bytes
                is_extended_id=False
            )
            self.bus.send(msg)
            
            offset += MAX_FRAG_SIZE
            frag_num += 1
            time.sleep(0.001)  # Small delay between fragments
    
    def receive_response(self, timeout=1.0):
        """Receive response from bootloader"""
        msg = self.bus.recv(timeout=timeout)
        if msg and msg.arbitration_id == self.response_id:
            return msg.data
        return None
    
    def activate(self):
        """Activate CAN bootloader interface"""
        print("Activating CAN bootloader...")
        msg = can.Message(
            arbitration_id=self.base_id,
            data=bytes([self.CMD_ACTIVATE]),
            is_extended_id=False
        )
        self.bus.send(msg)
        
        # Wait for activation response
        response = self.receive_response()
        if response and len(response) >= 4:
            # Check for activation response: {0x70, 0x62, 0x74, 0x33}
            if response[:4] == b'\x70\x62\x74\x33':
                print("Bootloader activated successfully!")
                return True
        
        print("Failed to activate bootloader")
        return False
    
    def check_ready(self):
        """Check if bootloader is ready"""
        msg = can.Message(
            arbitration_id=self.base_id,
            data=bytes([self.CMD_READY_BUSY]),
            is_extended_id=False
        )
        self.bus.send(msg)
        
        response = self.receive_response()
        if response and len(response) >= 1:
            return response[0] == 1
        return False
    
    def get_version(self):
        """Get bootloader version"""
        msg = can.Message(
            arbitration_id=self.base_id,
            data=bytes([self.CMD_VERSION]),
            is_extended_id=False
        )
        self.bus.send(msg)
        
        response = self.receive_response()
        if response and len(response) >= 3:
            return f"{response[0]}.{response[1]}.{response[2]}"
        return None
    
    def get_flash_size(self):
        """Get flash size in bytes"""
        msg = can.Message(
            arbitration_id=self.base_id,
            data=bytes([self.CMD_FLASH_SIZE]),
            is_extended_id=False
        )
        self.bus.send(msg)
        
        response = self.receive_response()
        if response and len(response) >= 4:
            return struct.unpack('<I', response[:4])[0]
        return None
    
    def erase_sector(self, sector):
        """Erase a flash sector"""
        print(f"Erasing sector {sector}...")
        data = struct.pack('<BH', self.CMD_ERASE, sector)
        self.send_variable_length(data)
        
        # Wait for erase to complete
        while not self.check_ready():
            time.sleep(0.1)
        print("Erase complete")
    
    def program_flash(self, address, data):
        """Program flash memory"""
        print(f"Programming {len(data)} bytes at 0x{address:08X}...")
        
        # Build program command
        cmd_data = struct.pack('<BIH', self.CMD_PROGRAM, address, len(data))
        cmd_data += data
        
        self.send_variable_length(cmd_data)
        
        # Wait for programming to complete
        while not self.check_ready():
            time.sleep(0.1)
        print("Programming complete")
    
    def go_to_app(self):
        """Jump to application code"""
        print("Jumping to application...")
        msg = can.Message(
            arbitration_id=self.base_id,
            data=bytes([self.CMD_GO_TO_APPCODE]),
            is_extended_id=False
        )
        self.bus.send(msg)
    
    def update_firmware(self, firmware_file):
        """Update firmware from file"""
        # Read firmware file
        with open(firmware_file, 'rb') as f:
            firmware_data = f.read()
        
        print(f"Firmware size: {len(firmware_data)} bytes")
        
        # Check bootloader is ready
        if not self.check_ready():
            print("Bootloader not ready")
            return False
        
        # Get flash info
        flash_size = self.get_flash_size()
        print(f"Flash size: {flash_size} bytes")
        
        # Calculate required sectors (4KB sectors)
        SECTOR_SIZE = 4096
        APP_OFFSET = 32 * 1024  # PICOBOOT3_APPCODE_OFFSET
        
        start_sector = APP_OFFSET // SECTOR_SIZE
        num_sectors = (len(firmware_data) + SECTOR_SIZE - 1) // SECTOR_SIZE
        
        # Erase sectors
        for i in range(num_sectors):
            self.erase_sector(start_sector + i)
        
        # Program firmware in pages
        PAGE_SIZE = 256
        offset = 0
        
        while offset < len(firmware_data):
            chunk = firmware_data[offset:offset + PAGE_SIZE]
            if len(chunk) < PAGE_SIZE:
                # Pad to page size
                chunk += b'\xFF' * (PAGE_SIZE - len(chunk))
            
            self.program_flash(APP_OFFSET + offset, chunk)
            offset += PAGE_SIZE
            
            # Progress
            progress = (offset * 100) // len(firmware_data)
            print(f"Progress: {progress}%")
        
        print("Firmware update complete!")
        return True


def main():
    if len(sys.argv) < 2:
        print("Usage: python3 can_bootloader_host.py <firmware.bin>")
        print("Example: python3 can_bootloader_host.py app.bin")
        return
    
    firmware_file = sys.argv[1]
    if not os.path.exists(firmware_file):
        print(f"Error: Firmware file '{firmware_file}' not found")
        return
    
    # Create bootloader interface
    # Note: Adjust interface and channel for your system
    # For Linux with SocketCAN: interface='socketcan', channel='can0'
    # For Windows with PCAN: interface='pcan', channel='PCAN_USBBUS1'
    bootloader = PicoBoot3CAN(interface='socketcan', channel='can0')
    
    try:
        # Activate bootloader
        if not bootloader.activate():
            return
        
        # Get version
        version = bootloader.get_version()
        if version:
            print(f"Bootloader version: {version}")
        
        # Update firmware
        if bootloader.update_firmware(firmware_file):
            # Jump to application
            bootloader.go_to_app()
            print("Device should now be running the new firmware")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        bootloader.bus.shutdown()


if __name__ == "__main__":
    main()