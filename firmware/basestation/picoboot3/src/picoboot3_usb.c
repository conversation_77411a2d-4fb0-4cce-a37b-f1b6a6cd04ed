/*
 * USB CDC interface for picoboot3
 */

#include "picoboot3.h"
#include <stdio.h>
#include <string.h>
#include "pico/stdlib.h"
#include "pico/stdio_usb.h"
#include "hardware/flash.h"
#include "hardware/watchdog.h"
#include "tusb.h"

#if PICOBOOT3_USB_ENABLED

#define USB_BUFFER_SIZE 4096
#define USB_TIMEOUT_MS 1000

// USB command codes (matching CAN commands)
#define USB_CMD_ACTIVATE     0xA5
#define USB_CMD_VERSION      0x02
#define USB_CMD_READ         0x10
#define USB_CMD_PROGRAM      0x20
#define USB_CMD_ERASE        0x30
#define USB_CMD_GO_TO_APP    0x40
#define USB_CMD_FLASH_SIZE   0x50
#define USB_CMD_NODE_ID      0x60  // Get node ID

// Response codes
#define USB_RESP_ACK         0x06
#define USB_RESP_NACK        0x15
#define USB_RESP_DATA        0x80

static uint8_t usb_rx_buffer[USB_BUFFER_SIZE];
static uint32_t usb_rx_count = 0;
static uint32_t usb_last_rx_time = 0;
static bool usb_activated = false;

// External variables from picoboot3.c
extern uint8_t ready;
extern const uint8_t activation_response[];
extern const uint8_t version[];
#if PICOBOOT3_CAN_ENABLED
extern uint32_t can_node_id;
#else
static uint32_t can_node_id = 0;  // Default when CAN is disabled
#endif

// Send response over USB
static void usb_send_response(uint8_t cmd, const uint8_t *data, uint32_t len) {
    // Send command byte
    putchar_raw(cmd);
    
    // Send length (4 bytes, big endian)
    putchar_raw((len >> 24) & 0xFF);
    putchar_raw((len >> 16) & 0xFF);
    putchar_raw((len >> 8) & 0xFF);
    putchar_raw(len & 0xFF);
    
    // Send data
    for (uint32_t i = 0; i < len; i++) {
        putchar_raw(data[i]);
    }
    
    stdio_flush();
}

// Process USB commands
static void usb_process_command(const uint8_t *buffer, uint32_t len) {
    if (len < 5) return;  // Need at least CMD + LENGTH(4)
    
    uint8_t cmd = buffer[0];
    uint32_t data_len = (buffer[1] << 24) | (buffer[2] << 16) | (buffer[3] << 8) | buffer[4];
    const uint8_t *data = &buffer[5];  // Data starts after CMD + LENGTH
    
    switch (cmd) {
        case USB_CMD_ACTIVATE: {
            if (data_len >= 4) {
                // Check activation key
                if (memcmp(data, activation_response, 4) == 0) {
                    usb_activated = true;
                    usb_send_response(USB_RESP_ACK, activation_response, 4);
                } else {
                    uint8_t nack = 0;
                    usb_send_response(USB_RESP_NACK, &nack, 1);
                }
            }
            break;
        }
        
        case USB_CMD_VERSION: {
            if (usb_activated) {
                usb_send_response(USB_RESP_DATA, version, 3);
            }
            break;
        }
        
        case USB_CMD_NODE_ID: {
            if (usb_activated) {
                uint8_t node_id_data[4];
                memcpy(node_id_data, &can_node_id, 4);
                usb_send_response(USB_RESP_DATA, node_id_data, 4);
            }
            break;
        }
        
        case USB_CMD_FLASH_SIZE: {
            if (usb_activated) {
                uint32_t flash_size = PICO_FLASH_SIZE_BYTES;
                uint8_t size_data[4];
                size_data[0] = (flash_size >> 24) & 0xFF;
                size_data[1] = (flash_size >> 16) & 0xFF;
                size_data[2] = (flash_size >> 8) & 0xFF;
                size_data[3] = flash_size & 0xFF;
                usb_send_response(USB_RESP_DATA, size_data, 4);
            }
            break;
        }
        
        case USB_CMD_READ: {
            if (usb_activated && data_len >= 6) {
                uint32_t addr = (data[0] << 24) | (data[1] << 16) | 
                               (data[2] << 8) | data[3];
                uint16_t read_len = (data[4] << 8) | data[5];
                
                if (read_len > 4096) read_len = 4096;
                
                uint8_t *flash_addr = (uint8_t *)(XIP_BASE + addr);
                usb_send_response(USB_RESP_DATA, flash_addr, read_len);
            }
            break;
        }
        
        case USB_CMD_ERASE: {
            if (usb_activated && data_len >= 2 && ready) {
                uint16_t sector = (data[0] << 8) | data[1];
                
                // Validate sector
                if (sector * FLASH_SECTOR_SIZE < PICO_FLASH_SIZE_BYTES) {
                    ready = 0;  // Set busy
                    
                    uint32_t ints = save_and_disable_interrupts();
                    flash_range_erase(sector * FLASH_SECTOR_SIZE, FLASH_SECTOR_SIZE);
                    restore_interrupts(ints);
                    
                    ready = 1;  // Set ready
                    uint8_t ack = 1;
                    usb_send_response(USB_RESP_ACK, &ack, 1);
                } else {
                    uint8_t nack = 0;
                    usb_send_response(USB_RESP_NACK, &nack, 1);
                }
            }
            break;
        }
        
        case USB_CMD_PROGRAM: {
            if (usb_activated && data_len >= 6 && ready) {
                uint32_t addr = (data[0] << 24) | (data[1] << 16) | 
                               (data[2] << 8) | data[3];
                uint16_t prog_len = (data[4] << 8) | data[5];
                
                if (prog_len <= FLASH_PAGE_SIZE && data_len >= 6 + prog_len) {
                    ready = 0;  // Set busy
                    
                    // Align data to page boundary
                    uint8_t page_buffer[FLASH_PAGE_SIZE];
                    memset(page_buffer, 0xFF, FLASH_PAGE_SIZE);
                    memcpy(page_buffer, &data[6], prog_len);
                    
                    uint32_t ints = save_and_disable_interrupts();
                    flash_range_program(addr, page_buffer, FLASH_PAGE_SIZE);
                    restore_interrupts(ints);
                    
                    ready = 1;  // Set ready
                    uint8_t ack = 1;
                    usb_send_response(USB_RESP_ACK, &ack, 1);
                } else {
                    uint8_t nack = 0;
                    usb_send_response(USB_RESP_NACK, &nack, 1);
                }
            }
            break;
        }
        
        case USB_CMD_GO_TO_APP: {
            if (usb_activated) {
                uint8_t ack = 1;
                usb_send_response(USB_RESP_ACK, &ack, 1);
                stdio_flush();
                sleep_ms(100);
                
                // Deinit everything and jump to app
                picoboot3_usb_deinit();
                picoboot3_can_deinit();
                picoboot3_go_to_appcode();
            }
            break;
        }
    }
}

void picoboot3_usb_init(void) {
    // USB is already initialized by stdio_init_all() in main
    // Just reset our state
    usb_rx_count = 0;
    usb_activated = false;
    usb_last_rx_time = to_ms_since_boot(get_absolute_time());
}

void picoboot3_usb_deinit(void) {
    // Nothing special to do for USB CDC deinit
    usb_activated = false;
}

void picoboot3_usb_task(void) {
    // Check for USB data
    int c = getchar_timeout_us(0);
    if (c != PICO_ERROR_TIMEOUT) {
        // Reset timeout on any received byte
        usb_last_rx_time = to_ms_since_boot(get_absolute_time());
        
        // Add to buffer
        if (usb_rx_count < USB_BUFFER_SIZE) {
            usb_rx_buffer[usb_rx_count++] = (uint8_t)c;
        }
        
        // Check if we have a complete command
        if (usb_rx_count >= 5) {
            // Commands are: CMD(1) + LENGTH(4) + DATA(LENGTH)
            uint32_t expected_len = (usb_rx_buffer[1] << 24) | 
                                   (usb_rx_buffer[2] << 16) |
                                   (usb_rx_buffer[3] << 8) | 
                                   usb_rx_buffer[4];
            
            if (usb_rx_count >= 5 + expected_len) {
                // Process command  - pass just the command and data part
                usb_process_command(usb_rx_buffer, 5 + expected_len);
                
                // Reset buffer
                usb_rx_count = 0;
            }
        }
    }
    
    // Check for timeout
    uint32_t now = to_ms_since_boot(get_absolute_time());
    if (usb_rx_count > 0 && (now - usb_last_rx_time) > USB_TIMEOUT_MS) {
        // Timeout - reset buffer
        usb_rx_count = 0;
    }
}

#else

// Stub implementations when USB is disabled
void picoboot3_usb_init(void) {}
void picoboot3_usb_deinit(void) {}
void picoboot3_usb_task(void) {}

#endif // PICOBOOT3_USB_ENABLED