/*
 * Copyright (c) 2024 Indoor Corgi
 *
 * SPDX-License-Identifier: MIT
 */

#include <stdio.h>

#include "pico/stdlib.h"
#include "picoboot3.h"

int main() {
  gpio_init(PICO_DEFAULT_LED_PIN);
  gpio_set_dir(PICO_DEFAULT_LED_PIN, GPIO_OUT);

  picoboot3_bootsel_init();
  sleep_ms(PICOBOOT3_BOOTSEL3_READ_DELAY_MS);
  if (!picoboot3_bootsel_is_bootloader()) {
    // Blink LED 10 times
    for (int i = 0; i < 10; i++) {
      gpio_put(PICO_DEFAULT_LED_PIN, 1);
      sleep_ms(100);
      gpio_put(PICO_DEFAULT_LED_PIN, 0);
      sleep_ms(100);
    }
    picoboot3_bootsel_deinit();
    picoboot3_go_to_appcode();
  }

  gpio_put(PICO_DEFAULT_LED_PIN, 1);

  // Initialize stdio for USB CDC
  stdio_init_all();
  
  // Give USB time to enumerate
  sleep_ms(1000);
  
  picoboot3_debug_uart_init();
  // picoboot3_uart_init();
  // picoboot3_i2c_init();
  // picoboot3_spi_init();
  picoboot3_can_init();  // Enable CAN interface
  picoboot3_usb_init();  // Enable USB interface

  while (1) {
    picoboot3_reserved_command_handler();
    picoboot3_uart_timeout_handler();
    picoboot3_usb_task();  // Process USB commands
  }
}
