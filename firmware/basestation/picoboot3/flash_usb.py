#!/usr/bin/env python3
"""
Flash firmware to Pico via USB using picoboot3 bootloader
"""

import serial
import serial.tools.list_ports
import struct
import sys
import time
import argparse
from pathlib import Path

class Picoboot3USB:
    # Command codes
    USB_CMD_ACTIVATE = 0xA5
    USB_CMD_VERSION = 0x02
    USB_CMD_READ = 0x10
    USB_CMD_PROGRAM = 0x20
    USB_CMD_ERASE = 0x30
    USB_CMD_GO_TO_APP = 0x40
    USB_CMD_FLASH_SIZE = 0x50
    USB_CMD_NODE_ID = 0x60
    
    # Response codes
    USB_RESP_ACK = 0x06
    USB_RESP_NACK = 0x15
    USB_RESP_DATA = 0x80
    
    # Activation key
    ACTIVATION_KEY = b'\x70\x62\x74\x33'  # "pbt3"
    
    def __init__(self, port=None):
        self.port = port
        self.ser = None
        self.activated = False
        
    def find_pico(self):
        """Find Pico in bootloader mode"""
        ports = serial.tools.list_ports.comports()
        for port in ports:
            # Look for Pico USB device
            if "Pico" in port.description or "2e8a:000a" in port.hwid:
                return port.device
        return None
        
    def connect(self):
        """Connect to bootloader"""
        if not self.port:
            self.port = self.find_pico()
            if not self.port:
                raise Exception("No Pico bootloader found. Please ensure the device is in bootloader mode.")
                
        self.ser = serial.Serial(self.port, 115200, timeout=2)
        time.sleep(0.5)  # Allow USB to stabilize
        self.ser.reset_input_buffer()
        print(f"Connected to {self.port}")
        
    def disconnect(self):
        """Disconnect from bootloader"""
        if self.ser:
            self.ser.close()
            
    def send_command(self, cmd, data=b''):
        """Send command to bootloader"""
        packet = struct.pack('B', cmd) + struct.pack('>I', len(data)) + data
        self.ser.write(packet)
        
    def read_response(self):
        """Read response from bootloader"""
        # Read response header (5 bytes: cmd + length)
        header = self.ser.read(5)
        if len(header) != 5:
            raise Exception("Timeout reading response")
            
        resp_cmd = header[0]
        resp_len = struct.unpack('>I', header[1:5])[0]
        
        # Read response data
        data = self.ser.read(resp_len) if resp_len > 0 else b''
        if len(data) != resp_len:
            raise Exception("Timeout reading response data")
            
        return resp_cmd, data
        
    def activate(self):
        """Activate bootloader interface"""
        print("Activating bootloader...")
        self.send_command(self.USB_CMD_ACTIVATE, self.ACTIVATION_KEY)
        
        resp_cmd, resp_data = self.read_response()
        if resp_cmd == self.USB_RESP_ACK and resp_data == self.ACTIVATION_KEY:
            self.activated = True
            print("Bootloader activated")
        else:
            raise Exception("Failed to activate bootloader")
            
    def get_version(self):
        """Get bootloader version"""
        if not self.activated:
            raise Exception("Bootloader not activated")
            
        self.send_command(self.USB_CMD_VERSION)
        resp_cmd, resp_data = self.read_response()
        
        if resp_cmd == self.USB_RESP_DATA and len(resp_data) == 3:
            return f"{resp_data[0]}.{resp_data[1]}.{resp_data[2]}"
        else:
            raise Exception("Failed to get version")
            
    def get_node_id(self):
        """Get CAN node ID"""
        if not self.activated:
            raise Exception("Bootloader not activated")
            
        self.send_command(self.USB_CMD_NODE_ID)
        resp_cmd, resp_data = self.read_response()
        
        if resp_cmd == self.USB_RESP_DATA and len(resp_data) == 4:
            return struct.unpack('<I', resp_data)[0]
        else:
            raise Exception("Failed to get node ID")
            
    def get_flash_size(self):
        """Get flash size"""
        if not self.activated:
            raise Exception("Bootloader not activated")
            
        self.send_command(self.USB_CMD_FLASH_SIZE)
        resp_cmd, resp_data = self.read_response()
        
        if resp_cmd == self.USB_RESP_DATA and len(resp_data) == 4:
            return struct.unpack('>I', resp_data)[0]
        else:
            raise Exception("Failed to get flash size")
            
    def erase_sector(self, sector):
        """Erase a flash sector"""
        if not self.activated:
            raise Exception("Bootloader not activated")
            
        self.send_command(self.USB_CMD_ERASE, struct.pack('>H', sector))
        resp_cmd, resp_data = self.read_response()
        
        if resp_cmd != self.USB_RESP_ACK:
            raise Exception(f"Failed to erase sector {sector}")
            
    def program_page(self, address, data):
        """Program a flash page"""
        if not self.activated:
            raise Exception("Bootloader not activated")
            
        # Pad data to page size (256 bytes)
        if len(data) < 256:
            data = data + b'\xFF' * (256 - len(data))
        elif len(data) > 256:
            data = data[:256]
            
        cmd_data = struct.pack('>IH', address, len(data)) + data
        self.send_command(self.USB_CMD_PROGRAM, cmd_data)
        
        resp_cmd, resp_data = self.read_response()
        if resp_cmd != self.USB_RESP_ACK:
            raise Exception(f"Failed to program at 0x{address:08X}")
            
    def read_flash(self, address, length):
        """Read flash memory"""
        if not self.activated:
            raise Exception("Bootloader not activated")
            
        self.send_command(self.USB_CMD_READ, struct.pack('>IH', address, length))
        resp_cmd, resp_data = self.read_response()
        
        if resp_cmd == self.USB_RESP_DATA:
            return resp_data
        else:
            raise Exception(f"Failed to read flash at 0x{address:08X}")
            
    def go_to_app(self):
        """Exit bootloader and start application"""
        if not self.activated:
            raise Exception("Bootloader not activated")
            
        print("Starting application...")
        self.send_command(self.USB_CMD_GO_TO_APP)
        
        try:
            resp_cmd, resp_data = self.read_response()
            if resp_cmd == self.USB_RESP_ACK:
                print("Application started")
        except:
            # Connection may close when jumping to app
            pass
            
    def flash_firmware(self, firmware_path, offset=0x8000):
        """Flash firmware file"""
        # Read firmware
        with open(firmware_path, 'rb') as f:
            firmware_data = f.read()
            
        firmware_size = len(firmware_data)
        print(f"Firmware size: {firmware_size} bytes")
        
        # Calculate sectors to erase
        sector_size = 4096
        start_sector = offset // sector_size
        num_sectors = (firmware_size + sector_size - 1) // sector_size
        
        # Erase sectors
        print(f"Erasing {num_sectors} sectors...")
        for i in range(num_sectors):
            sector = start_sector + i
            self.erase_sector(sector)
            progress = (i + 1) * 100 // num_sectors
            print(f"\rErasing: {progress}%", end='', flush=True)
        print()
        
        # Program pages
        page_size = 256
        num_pages = (firmware_size + page_size - 1) // page_size
        print(f"Programming {num_pages} pages...")
        
        for i in range(num_pages):
            page_offset = i * page_size
            page_addr = offset + page_offset
            page_data = firmware_data[page_offset:page_offset + page_size]
            
            self.program_page(page_addr, page_data)
            
            progress = (i + 1) * 100 // num_pages
            print(f"\rProgramming: {progress}%", end='', flush=True)
        print()
        
        # Verify
        print("Verifying...")
        for i in range(0, firmware_size, 4096):
            chunk_size = min(4096, firmware_size - i)
            read_data = self.read_flash(offset + i, chunk_size)
            expected_data = firmware_data[i:i + chunk_size]
            
            if read_data != expected_data:
                raise Exception(f"Verification failed at offset 0x{i:08X}")
                
            progress = (i + chunk_size) * 100 // firmware_size
            print(f"\rVerifying: {progress}%", end='', flush=True)
        print()
        
        print("Flash complete!")

def main():
    parser = argparse.ArgumentParser(description='Flash Pico via USB bootloader')
    parser.add_argument('firmware', nargs='?', help='Firmware file to flash')
    parser.add_argument('-p', '--port', help='Serial port')
    parser.add_argument('-i', '--info', action='store_true', help='Show bootloader info only')
    parser.add_argument('-o', '--offset', type=lambda x: int(x, 0), default=0x8000,
                        help='Flash offset (default: 0x8000)')
    parser.add_argument('-r', '--run', action='store_true', help='Run application after flashing')
    
    args = parser.parse_args()
    
    # Create flasher
    flasher = Picoboot3USB(args.port)
    
    try:
        # Connect and activate
        flasher.connect()
        flasher.activate()
        
        # Get info
        version = flasher.get_version()
        node_id = flasher.get_node_id()
        flash_size = flasher.get_flash_size()
        
        print(f"Bootloader version: {version}")
        print(f"CAN node ID: {node_id}")
        print(f"Flash size: {flash_size // 1024}KB")
        
        if args.info:
            # Just show info
            return
            
        if not args.firmware:
            print("\nNo firmware file specified. Use -i for info only.")
            return
            
        # Check firmware file
        if not Path(args.firmware).exists():
            print(f"Error: Firmware file '{args.firmware}' not found")
            sys.exit(1)
            
        # Flash firmware
        flasher.flash_firmware(args.firmware, args.offset)
        
        # Run if requested
        if args.run:
            flasher.go_to_app()
            
    except KeyboardInterrupt:
        print("\nInterrupted")
    except Exception as e:
        print(f"\nError: {e}")
        sys.exit(1)
    finally:
        flasher.disconnect()

if __name__ == "__main__":
    main()