#ifndef __DMX_TASK_H__
#define __DMX_TASK_H__

// C standard libraries
#include <stdio.h>
#include <string.h>

// pico sdk libraries
#include "FreeRTOS.h"
#include "task.h"
#include "queue.h"

#include "hardware/gpio.h"
#include "hardware/irq.h"

// user libraries
#include "types.h"
#include "pinout.h"

#ifdef __cplusplus
// C++ includes
#include "SC16IS750/SC16IS750.h"
#include "DmxUniverse.h"
#endif

#define DMX_REFRESH_RATE 40 // Hz ( makes for 25 ms send times \
                             as sending the physical packet takes 22.682ms assuming perfect transmit)

#ifdef __cplusplus
extern "C" {
#endif

// global definitions
extern TaskHandle_t xDmxTaskHandle;

// function prototyping
void send_break();
void vDmxTask( void * pvParameters );

#ifdef __cplusplus
}
#endif

#endif