#ifndef SD_TASK_H
#define SD_TASK_H

#include "FreeRTOS.h"
#include "task.h"
#include "queue.h"

#include "sd_card/tf_card.h"
#include "sd_card/fatfs/ff.h"
#include "pinout.h"

// SD write queue configuration
#define SD_WRITE_QUEUE_LENGTH   10
#define SD_SECTOR_SIZE          512
#define SD_MAX_FILENAME_LENGTH  32

// SD write operation structure
typedef struct {
    char filename[SD_MAX_FILENAME_LENGTH];
    uint8_t data[SD_SECTOR_SIZE];
    uint32_t sector_number;
    uint32_t data_length;
    bool is_write_request;
} sd_write_request_t;

// Task handle for SD task
extern TaskHandle_t xSDTaskHandle;

#ifdef __cplusplus
extern "C" {
#endif

// Queue handle for SD write operations
extern QueueHandle_t xSDWriteQueue;

// Function declarations
void sd_task(void *pvParameters);
bool sd_card_init(void);
FRESULT sd_write_sector(const char* filename, const uint8_t* data, uint32_t sector_number, uint32_t data_length);
bool sd_send_write_request(const char* filename, const uint8_t* data, uint32_t sector_number, uint32_t data_length);
void sd_test_write(void);

#ifdef __cplusplus
}
#endif

#endif // SD_TASK_H