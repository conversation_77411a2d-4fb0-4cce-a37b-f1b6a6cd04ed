#ifndef __EVENT_TASK_H__
#define __EVENT_TASK_H__

// Include rf_task.h outside extern "C" since it contains C++ headers
#include "rf_task.h"

#ifdef __cplusplus
    extern "C"{
#endif

// C standard libraries
#include <stdio.h>

// pico sdk libraries
#include "FreeRTOS.h"
#include "task.h"
#include "queue.h"

// protobuf libraries
#include "active_configuration.h"
#include "message_handler.h"
#include "events.h"

// user libraries
#include "types.h"
#include "flash_storage.h"

#include "can_task.h"
#include "uart_task.h"

// global definitions
typedef enum {
    EVENT_ID_CAN_RX = 0,
    EVENT_ID_CAN_TX,
    EVENT_ID_RF_RX,
    EVENT_ID_RF_TX,
    EVENT_ID_UART_RX,
    EVENT_ID_UART_TX,
    EVENT_ID_SERIAL_RX,
    EVENT_ID_SERIAL_TX,
    EVENT_ID_LIST
} EventType_t;

typedef enum{
    ACTION_ID_UPDATE = 1,
    ACTION_ID_REBOOT,
} ActionType_t;
typedef struct{
    uint8_t id;
    uint8_t type;
    uint32_t len;
    pb_byte_t data[ INTERNAL_BUFFER_MAX_LENGTH ];
} Event_t;

extern TaskHandle_t xInEventTaskHandle;
extern TaskHandle_t xOutEventTaskHandle;

extern QueueHandle_t xInEventQueueHandle;

// local definitions/enums
#define EVENT_QUEUE_LENGTH 5 // Max number of items in each individual queue
#define EVENT_ITEM_SIZE sizeof( Event_t ) // Size of each item

// function prototyping
void vInEventTask( void * pvParameters );
bool xInEventQueueSend( uint8_t ucEventId, void * pvEventBuffer );
void vInEventDigest( Event_t * pxEvent );

#ifdef __cplusplus
    }
#endif

#endif