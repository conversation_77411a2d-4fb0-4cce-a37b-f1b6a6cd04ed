#include "task_cleanup.h"
#include <stdio.h>
#include <string.h>

#define MAX_QUEUES_PER_TASK 10
#define MAX_MUTEXES_PER_TASK 10

bool vTaskResourcesInit(TaskResources_t* pResources, TaskHandle_t taskHandle) {
    if (pResources == NULL) {
        return false;
    }
    
    // Clear the structure
    memset(pResources, 0, sizeof(TaskResources_t));
    
    // Allocate memory for resource arrays
    pResources->queue_handles = pvPortMalloc(MAX_QUEUES_PER_TASK * sizeof(QueueHandle_t));
    if (pResources->queue_handles == NULL) {
        printf("ERROR: Failed to allocate memory for queue handles\r\n");
        return false;
    }
    
    pResources->mutex_handles = pvPortMalloc(MAX_MUTEXES_PER_TASK * sizeof(SemaphoreHandle_t));
    if (pResources->mutex_handles == NULL) {
        vPortFree(pResources->queue_handles);
        pResources->queue_handles = NULL;
        printf("[ TASK_CLEANUP ] ERROR: Failed to allocate memory for mutex handles\r\n");
        return false;
    }
    
    pResources->task_handle = taskHandle;
    pResources->queue_count = 0;
    pResources->mutex_count = 0;
    pResources->is_initialized = true;
    
    printf("[ TASK_CLEANUP ] Task resources initialized for task handle: %p\r\n", (void*)taskHandle);
    return true;
}

bool vTaskResourcesAddQueue(TaskResources_t* pResources, QueueHandle_t queueHandle) {
    if (pResources == NULL || !pResources->is_initialized || queueHandle == NULL) {
        printf("[ TASK_CLEANUP ] ERROR: Invalid parameters for adding queue resource\r\n");
        return false;
    }
    
    if (pResources->queue_count >= MAX_QUEUES_PER_TASK) {
        printf("[ TASK_CLEANUP ] ERROR: Maximum number of queues per task exceeded\r\n");
        return false;
    }
    
    pResources->queue_handles[pResources->queue_count] = queueHandle;
    pResources->queue_count++;
    
    printf("[ TASK_CLEANUP ] Added queue handle to task resources: %p (count: %d)\r\n", 
           (void*)queueHandle, pResources->queue_count);
    return true;
}

bool vTaskResourcesAddMutex(TaskResources_t* pResources, SemaphoreHandle_t mutexHandle) {
    if (pResources == NULL || !pResources->is_initialized || mutexHandle == NULL) {
        printf("[ TASK_CLEANUP ] ERROR: Invalid parameters for adding mutex resource\r\n");
        return false;
    }
    
    if (pResources->mutex_count >= MAX_MUTEXES_PER_TASK) {
        printf("[ TASK_CLEANUP ] ERROR: Maximum number of mutexes per task exceeded\r\n");
        return false;
    }
    
    pResources->mutex_handles[pResources->mutex_count] = mutexHandle;
    pResources->mutex_count++;
    
    printf("[ TASK_CLEANUP ] Added mutex handle to task resources: %p (count: %d)\r\n", 
           (void*)mutexHandle, pResources->mutex_count);
    return true;
}

void vTaskCleanupResources(TaskResources_t* pResources) {
    if (pResources == NULL || !pResources->is_initialized) {
        printf("[ TASK_CLEANUP ] WARNING: Attempting to cleanup uninitialized task resources\r\n");
        return;
    }
    
    printf("[ TASK_CLEANUP ] Cleaning up task resources for task handle: %p\r\n", (void*)pResources->task_handle);
    
    // Clean up queues
    for (uint8_t i = 0; i < pResources->queue_count; i++) {
        if (pResources->queue_handles[i] != NULL) {
            printf("[ TASK_CLEANUP ] Deleting queue handle: %p\r\n", (void*)pResources->queue_handles[i]);
            vQueueDelete(pResources->queue_handles[i]);
            pResources->queue_handles[i] = NULL;
        }
    }
    
    // Clean up mutexes
    for (uint8_t i = 0; i < pResources->mutex_count; i++) {
        if (pResources->mutex_handles[i] != NULL) {
            printf("[ TASK_CLEANUP ] Deleting mutex handle: %p\r\n", (void*)pResources->mutex_handles[i]);
            vSemaphoreDelete(pResources->mutex_handles[i]);
            pResources->mutex_handles[i] = NULL;
        }
    }
    
    // Free allocated memory
    if (pResources->queue_handles != NULL) {
        vPortFree(pResources->queue_handles);
        pResources->queue_handles = NULL;
    }
    
    if (pResources->mutex_handles != NULL) {
        vPortFree(pResources->mutex_handles);
        pResources->mutex_handles = NULL;
    }
    
    // Reset counters
    pResources->queue_count = 0;
    pResources->mutex_count = 0;
    pResources->is_initialized = false;
    
    printf("[ TASK_CLEANUP ] Task resource cleanup completed\r\n");
}

void vTaskCleanupAndDelete(TaskResources_t* pResources) {
    if (pResources == NULL) {
        printf("[ TASK_CLEANUP ] ERROR: NULL task resources pointer, deleting task without cleanup\r\n");
        vTaskDelete(NULL);
        return;
    }
    
    // Clean up all resources first
    vTaskCleanupResources(pResources);
    
    // Delete the task
    printf("[ TASK_CLEANUP ] Deleting task: %p\r\n", (void*)pResources->task_handle);
    vTaskDelete(pResources->task_handle);
}