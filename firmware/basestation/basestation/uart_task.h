#ifndef __UART_TASK_H__
#define __UART_TASK_H__

// Include event_task.h outside extern "C" since it contains C++ headers
#include "event_task.h"

#ifdef __cplusplus
    extern "C" {
#endif

// C standard libraries
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>

// pico sdk libraries
#include "FreeRTOS.h"
#include "task.h"

#include "hardware/dma.h"

// user libraries
#include "types.h"
#include "pinout.h"
#include "uart.h"

// local definitions/enums
#define UART_MAX_BUFFER_OUT_LENGTH      ( unsigned int )64
#define UART_MAX_BUFFER_IN_LENGTH       ( unsigned int )64

#define UART_MAX_DMA_BUFFER_IN_LENGTH   INTERNAL_BUFFER_MAX_LENGTH
#define UART_MAX_DMA_BUFFER_OUT_LENGTH  ( unsigned int )256

#define ENABLE_DEBUG_UART_ESP_RX

typedef struct{
    uint16_t len;
    uint8_t payload[ UART_MAX_BUFFER_IN_LENGTH ];
} DebugMessage_t;

typedef struct{
    uint16_t len;
    uint8_t payload[ UART_MAX_DMA_BUFFER_IN_LENGTH ];
} UARTMessage_t;

// global definitions
extern TaskHandle_t xUART0RxTaskHandle;
extern TaskHandle_t xUART1RxTaskHandle;

// function prototyping
void vUART0RxTask( void * pvParameters );
void vUART1RxTask( void * pvParameters );

void vUARTDMASend( uint8_t ucInstance, uint8_t * pcOutBuffer, uint16_t usLength );

#ifdef __cplusplus
    }
#endif

#endif