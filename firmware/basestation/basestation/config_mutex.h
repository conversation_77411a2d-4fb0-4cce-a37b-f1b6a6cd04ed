#ifndef __CONFIG_MUTEX_H__
#define __CONFIG_MUTEX_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "FreeRTOS.h"
#include "semphr.h"
#include <stdbool.h>

// Initialize the configuration mutex
bool vConfigMutexInit(void);

// Lock the configuration mutex with timeout
// Returns true if lock acquired, false on timeout
bool xConfigLock(TickType_t xTimeout);

// Unlock the configuration mutex
void vConfigUnlock(void);

// Lock with default timeout (1 second)
#define CONFIG_LOCK() xConfigLock(pdMS_TO_TICKS(1000))

// Lock with infinite wait
#define CONFIG_LOCK_WAIT() xConfigLock(portMAX_DELAY)

#ifdef __cplusplus
}
#endif

#endif // __CONFIG_MUTEX_H__