#include "rf_task.h"
#include "active_configuration.h"
#include "basestation-config.pb.h"
#include "events.h"
#include "rf4463/RFPacketdefs.h"
#include "rf4463/rf4463prodefs.h"
#include <cstdint>
#include <cstdio>
#include <cstring>
#include "task_cleanup.h"

RFParams_t rf_params = RF_PARAMS_DEFAULT();
TaskHandle_t xRfRxTaskHandle;
TaskHandle_t xRFRxQueueTaskHandle;
SemaphoreHandle_t xRfDeviceOperationMutex = NULL;

RFMessage_t xRxMessage;
QueueHandle_t xRFRxQueue;

// Use atomic variable for thread-safe access
static volatile bool xRfProcessMessages = true;
static SemaphoreHandle_t xRfProcessMessagesMutex = NULL;

// Thread-safe setter for xRfProcessMessages
static void vSetRfProcessMessages(bool bValue) {
    if (xRfProcessMessagesMutex != NULL) {
        xSemaphoreTake(xRfProcessMessagesMutex, portMAX_DELAY);
    }
    xRfProcessMessages = bValue;
    if (xRfProcessMessagesMutex != NULL) {
        xSemaphoreGive(xRfProcessMessagesMutex);
    }
}

// Thread-safe getter for xRfProcessMessages
static bool bGetRfProcessMessages(void) {
    bool bValue;
    if (xRfProcessMessagesMutex != NULL) {
        xSemaphoreTake(xRfProcessMessagesMutex, portMAX_DELAY);
    }
    bValue = xRfProcessMessages;
    if (xRfProcessMessagesMutex != NULL) {
        xSemaphoreGive(xRfProcessMessagesMutex);
    }
    return bValue;
}

void vRfUpdateChannelAndNetworkFromConfig() {    
    if (g_active_configuration.has_config && g_active_configuration.config.has_rf_config) {
        rf_params.rf_config.rx_channel = g_active_configuration.config.rf_config.channel;
        rf_params.rf_config.tx_channel = g_active_configuration.config.rf_config.channel;
        rf_params.rf_config.syncword = g_active_configuration.config.rf_config.network;
    } else {
        printf("[ RF4463 ] No RF config found, using default channel and network\r\n");
        printf("[ RF4463 ] has_config: %d\r\n", g_active_configuration.has_config);
        printf("[ RF4463 ] has_rf_config: %d\r\n", g_active_configuration.config.has_rf_config);
        uint8_t ucChannel = 15;
        uint8_t ucNetwork = 15; 
        // Test channel and network
        rf_params.rf_config.rx_channel = ucChannel;
        rf_params.rf_config.tx_channel = ucChannel;
        rf_params.rf_config.syncword = ucNetwork;
    }
    vRfSetChannelAndNetwork(rf_params.rf_config.rx_channel, rf_params.rf_config.syncword);
}

void vRfRxTask( void * pvParameters ){
    printf( "RF Rx Task\r\n" );
    TaskResources_t taskResources;

    static uint32_t ulRfEvent = 0;

    // Initialize resource tracking
    if (!vTaskResourcesInit(&taskResources, xRfRxTaskHandle)) {
        printf("ERROR: Failed to initialize task resources for RfRxTask\r\n");
        vTaskDelete(NULL);
        return;
    }

    rf_params.irq_num = IO_IRQ_BANK0;
    rf_params.irq_cb_func = (gpio_irq_callback_t *) &gpio_irq_handler;

    printf("[ RF4463 ] Initializing RF\r\n");

    gpio_init( rf_params.spi_config.cs_pin );
    gpio_set_dir( rf_params.spi_config.cs_pin, GPIO_OUT );
    gpio_put( rf_params.spi_config.cs_pin, 1 );

    printf("[ RF4463 ] Initializing SPI\r\n");

    // Setup SPI bus and create binary semaphore (MUTEX)
    if( !xSPIInit( &rf_params.spi_config, true ) ){
        printf("[ RF4463 ] SPI initialization failed\r\n");
        vTaskCleanupAndDelete(&taskResources);
        return;
    }
    
    // Copy the spi instance and cs data to a structure in the driver files
    vRF4463SPISetConfig( &rf_params.spi_config );
    // Reset and configure rf4463
    if( ucRfInit() == 1 ){
        printf("[ RF4463 ] RF initialization failed\r\n");
        vTaskCleanupAndDelete(&taskResources);
        return;
    }

    // Start IRQ
    gpio_init( rf_params.nIRQ_pin );
    gpio_set_dir( rf_params.nIRQ_pin, GPIO_IN );
    gpio_pull_up( rf_params.nIRQ_pin );
    // Pass the taskhandle so it can do vTaskNotifyGiveFromISR
    vIRQSetPinAwareness( rf_params.nIRQ_pin, xRfRxTaskHandle );
    // this function will allocate the callback into a shared type so it's safe to use
    gpio_set_irq_enabled_with_callback( rf_params.nIRQ_pin, GPIO_IRQ_EDGE_FALL, true, (gpio_irq_callback_t)rf_params.irq_cb_func );
    irq_set_enabled( rf_params.irq_num, true );

    register_send_rf_message_callback(send_rf_message);

    vRfUpdateChannelAndNetworkFromConfig();

    // create queue for rf messages
    xRFRxQueue = xQueueCreate( RF_RX_QUEUE_LENGTH, RF_RX_MSG_ITEM_SIZE );
    if (xRFRxQueue == NULL) {
        printf("[ RF4463 ] Failed to create RF RX queue!\r\n");
        vTaskCleanupAndDelete(&taskResources);
        return;
    }
    vTaskResourcesAddQueue(&taskResources, xRFRxQueue);
    
    xTaskCreate( vRFRxQueueTask, "RFRxQueueTask", 4096, NULL, 1, &xRFRxQueueTaskHandle );
    vTaskCoreAffinitySet( xRFRxQueueTaskHandle, 1 );

    // Create mutex for device operations (adoption/removal)
    xRfDeviceOperationMutex = xSemaphoreCreateMutex();
    if (xRfDeviceOperationMutex == NULL) {
        printf("[ RF4463 ] Failed to create device operation mutex!\r\n");
        vTaskCleanupAndDelete(&taskResources);
        return;
    }
    vTaskResourcesAddMutex(&taskResources, xRfDeviceOperationMutex);
    
    // Create mutex for process messages flag
    xRfProcessMessagesMutex = xSemaphoreCreateMutex();
    if (xRfProcessMessagesMutex == NULL) {
        printf("[ RF4463 ] Failed to create process messages mutex!\r\n");
        vTaskCleanupAndDelete(&taskResources);
        return;
    }
    vTaskResourcesAddMutex(&taskResources, xRfProcessMessagesMutex);

    for( ;; ){
        ulRfEvent = ulTaskNotifyTake( pdTRUE, portMAX_DELAY );
        if( ulRfEvent > 0 ){
            xRxMessage.rssi = xRF4463GetRSSI();
            vRfRxMessage();

            if (xRxMessage.len > 0 && xRxMessage.len < RF4463_MAX_BUFFER_LENGTH) {
            // forward to queue
            xQueueSend( xRFRxQueue, &xRxMessage, portMAX_DELAY );
            }

            // Always call this function to restore reception functionality
            vRfRxRestore();
            // Reset buffer
            memset( &xRxMessage, 0, sizeof( RFMessage_t ) );
        }
    }
}

/**
 * Helper function to send a message and wait for a response with retry logic
 * @param pcBuffer Message buffer to send
 * @param ucLength Length of the message buffer
 * @param ucDestination Destination node ID
 * @param ucTable Message table
 * @param ucCommand Message command
 * @param ulTimeoutMs Timeout in milliseconds
 * @param pResponse Pointer to store the response message (can be NULL if not needed)
 * @param ucMaxRetries Maximum number of retries (default 3 if 0 is passed)
 * @return true if response was received, false if all retries failed
 */
bool vRfSendAndWaitForResponse(
    uint8_t * pcBuffer, 
    uint8_t ucLength, 
    uint8_t ucDestination, 
    uint8_t ucTable, 
    uint8_t ucCommand, 
    uint32_t ulTimeoutMs,
    RFMessage_t * pResponse,
    uint8_t ucMaxRetries,
    uint8_t ucIdleNetwork,
    uint8_t ucIdleChannel
) {    
    // Disable normal message processing
    vSetRfProcessMessages(false);
    
    uint8_t ucRetryCount = 0;
    bool bFound = false;
    
    while (!bFound && ucRetryCount <= ucMaxRetries) {
        if (ucRetryCount > 0) {
            printf("\n\n[ RF4463 ] Retry %d/%d...\r\n", ucRetryCount, ucMaxRetries);
        }
        
        // Prepare and send the message
        uint8_t pcRF4463TxBuffer[RF4463_MAX_BUFFER_LENGTH];
        uint8_t ucMsgLength = vRFPrepareKlevernessMessage(pcBuffer, ucLength, ucTable, ucCommand, ucDestination, pcRF4463TxBuffer);
        vRfTxMessage(pcRF4463TxBuffer, RF_MESSAGE_HEADER_SIZE + ucMsgLength);
        
        // Wait for response with timeout
        TickType_t xStartTime = xTaskGetTickCount();
        RFMessage_t xRfRxMessage;
        uint8_t ucCounter = 0;
        
        while (!bFound) {
            if ((xTaskGetTickCount() - xStartTime) > pdMS_TO_TICKS(ulTimeoutMs)) {
                printf("[ RF4463 ] Response timeout\r\n");
                break;
            }
            
            while (xQueueReceive(xRFRxQueue, &xRfRxMessage, QUEUE_TICKS_DELAY) != errQUEUE_EMPTY) {                
                #if defined(DEBUG_RF_PACKET_RX)
                printf("[ RF4463 ] Rx - rssi: %d | size: %02d | pkt: ", xRfRxMessage.rssi, xRfRxMessage.len);
                if (xRfRxMessage.len > 0 && xRfRxMessage.len < RF4463_MAX_BUFFER_LENGTH) {
                    for (ucCounter = 0; ucCounter < xRfRxMessage.len; ucCounter++) {
                        printf("%02X ", xRfRxMessage.message[ucCounter]);
                    }
                }
                printf("\r\n");
                #endif
                
                if (xRfRxMessage.message[RF_MESSAGE_SOURCE] == ucDestination && 
                    xRfRxMessage.message[RF_MESSAGE_TABLE] == ucTable && 
                    xRfRxMessage.message[RF_MESSAGE_COMMAND] == ucCommand) {
                    printf("[ RF4463 ] Response received successfully\r\n");
                    bFound = true;
                    
                    // Copy the response if pointer is provided
                    if (pResponse != NULL) {
                        memcpy(pResponse, &xRfRxMessage, sizeof(RFMessage_t));
                    }
                    break;
                }
            }
            
            if (!bFound) {
                vTaskDelay(QUEUE_TICKS_DELAY);
            }
        }
        
        if (!bFound) {
            ucRetryCount++;
            if (ucRetryCount <= ucMaxRetries) {
                // Calculate exponential backoff delay: 1, 2, 4, 8, 16... seconds
                uint32_t ulDelaySeconds = 1 << (ucRetryCount - 1); // 2^(ucRetryCount-1)

                // save the current channel and network
                uint8_t ucSavedChannel = rf_params.rf_config.rx_channel;
                uint16_t usSavedNetwork = rf_params.rf_config.syncword;
                
                // switch to idle network and channel
                vRfSetChannelAndNetwork(ucIdleChannel, ucIdleNetwork);
                
                printf("[ RF4463 ] Retrying in %d seconds...\r\n", ulDelaySeconds);    
                
                vSetRfProcessMessages(true);
                vTaskDelay(pdMS_TO_TICKS(ulDelaySeconds * 1000));
                vSetRfProcessMessages(false);

                // restore channel and network
                vRfSetChannelAndNetwork(ucSavedChannel, usSavedNetwork);
            }
        }
    }
    
    printf("\r\n");
    
    if (!bFound) {
        printf("[ RF4463 ] All %d retries failed\r\n", ucMaxRetries);
    }
    
    // Re-enable normal message processing
    vSetRfProcessMessages(true);
    
    return bFound;
}

void vRfSetChannelAndNetwork(uint8_t ucChannel, uint16_t usNetwork) {
    printf("[ RF4463 ] Switching to channel and network: %d | %d\r\n", ucChannel, usNetwork);
    rf_params.rf_config.rx_channel = ucChannel;
    rf_params.rf_config.tx_channel = ucChannel;
    rf_params.rf_config.syncword = usNetwork;
    vRF4463SetChannels(ucChannel);
    vRF4463SetNetwork(usNetwork);
    vRfRxRestore();
}

bool vRFAdoptDevice(const char * pcQrCode, uint8_t ucNodeId, DeviceOperationMode eOpMode) {
    // Acquire mutex to prevent parallel device operations
    if (xRfDeviceOperationMutex == NULL) {
        printf("[ RF4463 ] Device operation mutex not initialized!\r\n");
        return false;
    }
    
    if (xSemaphoreTake(xRfDeviceOperationMutex, pdMS_TO_TICKS(30000)) != pdTRUE) { // 30 second timeout
        printf("[ RF4463 ] Failed to acquire device operation mutex - operation in progress\r\n");
        printf("[ RF4463 ] Another device adoption or removal operation is currently running\r\n");
        vTaskDelay(pdMS_TO_TICKS(100)); // Small delay to prevent rapid retries
        return false;
    }
    
    printf("[ RF4463 ] Device operation mutex acquired for adoption\r\n");

    // save current channel and network
    uint8_t ucChannel = rf_params.rf_config.rx_channel;
    uint16_t usNetwork =  rf_params.rf_config.syncword;

    TickType_t xStartTime = xTaskGetTickCount();
    printf("[ RF4463 ] Adopting device\r\n");
    printf("[ RF4463 ] QR Code: %s\r\n", pcQrCode);
    printf("[ RF4463 ] Node ID: %d\r\n", ucNodeId);
    printf("[ RF4463 ] Channel: %d\r\n", ucChannel);
    printf("[ RF4463 ] Network: %d\r\n", usNetwork);
    bool bAdopted = false;

    uint8_t pcTempTxBuffer[RF4463_MAX_BUFFER_LENGTH];
    size_t ucBufferCounter = 0;
        // Build the adoption message
    for (ucBufferCounter = 0; ucBufferCounter < std::strlen(pcQrCode); ucBufferCounter++) {
        pcTempTxBuffer[ucBufferCounter] = pcQrCode[ucBufferCounter];
    }
    pcTempTxBuffer[ucBufferCounter++] = ucNodeId;
    pcTempTxBuffer[ucBufferCounter++] = ucChannel;
    pcTempTxBuffer[ucBufferCounter++] = (0xFF00 & usNetwork) >> 8;
    pcTempTxBuffer[ucBufferCounter++] = (0x00FF & usNetwork);
    pcTempTxBuffer[ucBufferCounter++] = rf_params.rf_config.tx_power;
    pcTempTxBuffer[ucBufferCounter++] = rf_params.rf_config.baudrate;
    pcTempTxBuffer[ucBufferCounter++] = eOpMode;

    printf("[ RF4463 ] Discovering...\r\n");

    vRfSetChannelAndNetwork(ADOPTION_DEFAULT_CHANNEL, ADOPTION_DEFAULT_NETWORK);
    
    // Use the helper function to send and wait for response
    RFMessage_t xFindByQrResponse;
    bool bFound = vRfSendAndWaitForResponse(
        pcTempTxBuffer,
        ucBufferCounter,
        DEFAULT_DESTINATION,
        TABLE_RF_SETUP,
        RF_SETUP_FIND_BY_QR,
        1000, // 5 second timeout
        &xFindByQrResponse,
        3, // 3 retries
        usNetwork,
        ucChannel
    );

    vRfSetChannelAndNetwork(ucChannel, usNetwork);

    if (bFound) {
        printf("[ RF4463 ] Discovery successful. Adopting...\r\n");

        // now register the device. The response holds the pairing code, that we need to pad with 4x 0xFF
        ucBufferCounter = 0;
        for (ucBufferCounter = 0; ucBufferCounter < 12; ucBufferCounter++) {
            pcTempTxBuffer[ucBufferCounter] = xFindByQrResponse.message[RF_MESSAGE_CONTENT + ucBufferCounter];
        }

        // TODO: this is a checksum of the basestation QR code CRC32
        pcTempTxBuffer[ ucBufferCounter++ ] = 0xFF;
        pcTempTxBuffer[ ucBufferCounter++ ] = 0xFF;
        pcTempTxBuffer[ ucBufferCounter++ ] = 0xFF;
        pcTempTxBuffer[ ucBufferCounter++ ] = 0xFF;

        RFMessage_t xRegisterResponse;
        bool bRegisterSuccess = vRfSendAndWaitForResponse(
            pcTempTxBuffer,
            ucBufferCounter,
            ucNodeId,
            TABLE_RF_SETUP,
            RF_SETUP_REGISTER,
            1000, // 5 second timeout
            &xRegisterResponse,
            3, // 3 retries
            usNetwork,
            ucChannel
        );

        if (bRegisterSuccess) {
            printf("[ RF4463 ] Adoption successful\r\n");
            bAdopted = true;
        } else {
            printf("[ RF4463 ] Adoption timeout\r\n");
        }
    } else {
        printf("[ RF4463 ] Discovery timeout\r\n");
    }
    
    // Calculate and print execution time
    TickType_t xEndTime = xTaskGetTickCount();
    uint32_t ulExecutionTimeMs = pdTICKS_TO_MS(xEndTime - xStartTime);
    printf("[ RF4463 ] Adoption process completed in %u ms\r\n", ulExecutionTimeMs);

    printf("[ RF4463 ] Device operation mutex released after adoption\r\n");
    xSemaphoreGive(xRfDeviceOperationMutex); // Release mutex
    return bAdopted;
}

bool vRFRemoveDevice(const char * pcQrCode, uint8_t ucNodeId, uint16_t usNetwork, uint8_t ucChannel) {
    // Acquire mutex to prevent parallel device operations
    if (xRfDeviceOperationMutex == NULL) {
        printf("[ RF4463 ] Device operation mutex not initialized!\r\n");
        return false;
    }
    
    if (xSemaphoreTake(xRfDeviceOperationMutex, pdMS_TO_TICKS(30000)) != pdTRUE) { // 30 second timeout
        printf("[ RF4463 ] Failed to acquire device operation mutex - operation in progress\r\n");
        printf("[ RF4463 ] Another device adoption or removal operation is currently running\r\n");
        vTaskDelay(pdMS_TO_TICKS(100)); // Small delay to prevent rapid retries
        return false;
    }
        
    TickType_t xStartTime = xTaskGetTickCount();
    printf("[ RF4463 ] Removing device\r\n");
    printf("[ RF4463 ] QR Code: %s\r\n", pcQrCode);
    printf("[ RF4463 ] Node ID: %d\r\n", ucNodeId);
    printf("[ RF4463 ] Channel: %d\r\n", ucChannel);
    printf("[ RF4463 ] Network: %d\r\n", usNetwork);

    // save the current channel and network
    uint8_t ucSavedChannel = rf_params.rf_config.rx_channel;
    uint16_t usSavedNetwork = rf_params.rf_config.syncword;

    // switch to the channel and network of the device to remove
    vRfSetChannelAndNetwork(ucChannel, usNetwork);
    
    bool bRemoved = false;
    
    // Build the removal message with QR code
    uint8_t pcTempTxBuffer[RF4463_MAX_BUFFER_LENGTH];
    size_t ucBufferCounter = 0;
    
    // Copy QR code to buffer
    for (ucBufferCounter = 0; ucBufferCounter < std::strlen(pcQrCode) && ucBufferCounter < DEVICE_QR_FIELD_LENGTH; ucBufferCounter++) {
        pcTempTxBuffer[ucBufferCounter] = pcQrCode[ucBufferCounter];
    }
    
    // Ensure we have a full QR code length
    while (ucBufferCounter < DEVICE_QR_FIELD_LENGTH) {
        pcTempTxBuffer[ucBufferCounter++] = 0;
    }
    
    printf("[ RF4463 ] Sending removal command...\r\n");
    
    // Send removal command to broadcast address
    // The ESP32 reference sends to RF_NODE_BROADCAST with RF_SETUP_REMOVE command
    RFMessage_t xRemoveResponse;
    bool bRemoveSuccess = vRfSendAndWaitForResponse(
        pcTempTxBuffer,
        DEVICE_QR_FIELD_LENGTH,
        RF_NODE_BROADCAST,  // Broadcast to all devices
        TABLE_RF_SETUP,
        RF_SETUP_REMOVE,
        1000, // 1 second timeout
        &xRemoveResponse,
        3, // 3 retries
        usNetwork,
        ucChannel
    );
    
    if (bRemoveSuccess) {
        printf("[ RF4463 ] Device removal acknowledged\r\n");
        bRemoved = true;
        
        // Update provisioning state if we have one
        ProvisioningState *pProvisioningState = get_provisioned_device_state_from_node_id(ucNodeId);
        if (pProvisioningState) {
            pProvisioningState->is_provisioned = false;
            pProvisioningState->last_seen_time = get_time_in_ms();
            pProvisioningState->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
        }
    } else {
        printf("[ RF4463 ] Device removal timeout or no response\r\n");
    }

    // restore channel and network
    vRfSetChannelAndNetwork(ucSavedChannel, usSavedNetwork);
    
    // Calculate and print execution time
    TickType_t xEndTime = xTaskGetTickCount();
    uint32_t ulExecutionTimeMs = pdTICKS_TO_MS(xEndTime - xStartTime);
    printf("[ RF4463 ] Removal process completed in %u ms\r\n", ulExecutionTimeMs);
    
    printf("[ RF4463 ] Device operation mutex released after removal\r\n");
    xSemaphoreGive(xRfDeviceOperationMutex); // Release mutex
    return bRemoved;
}

// tasks
void vRFRxQueueTask( void * pvParameters ){
    RFMessage_t xRfRxMessage;
    uint8_t ucCounter = 0;

    printf( "[ RF4463 ] RF Rx Queue Task\r\n" );

    for( ;; ){
        // Check for provisioning updates periodically
        static uint32_t ulLastNodeMappingsCount = 0;
        static TickType_t xLastProvisioningCheck = 0;
        TickType_t xCurrentTime = xTaskGetTickCount();
        
        // Lock configuration to check for provisioning updates
        bool bShouldCheckProvisioning = (g_active_configuration.has_config && 
                                        ulLastNodeMappingsCount != g_active_configuration.config.node_qr_mappings_count) ||
                                        ((xCurrentTime - xLastProvisioningCheck) > pdMS_TO_TICKS(10000));
        
        if (bShouldCheckProvisioning) {
            if (g_active_configuration.has_config && 
                g_active_configuration.config.has_rf_config && g_active_configuration.config.node_qr_mappings_count > 0) {
                bool bMadeChanges = false;
                
                for (uint8_t i = 0; i < g_active_configuration.config.node_qr_mappings_count; i++) {
                    ProvisioningState *pProvisioningState = get_provisioned_device_state_from_node_id(g_active_configuration.config.node_qr_mappings[i].node_id);
                    if (g_active_configuration.config.node_qr_mappings[i].type == BasestationConfig_NodeQRMapping_DeviceType_RF && !pProvisioningState->is_provisioned) {
                        // check if node id is an rf reed device
                        if (g_active_configuration.config.rf_reed_configs_count > 0) {
                            for (uint8_t j = 0; j < g_active_configuration.config.rf_reed_configs_count; j++) {
                                if (g_active_configuration.config.rf_reed_configs[j].node_id == g_active_configuration.config.node_qr_mappings[i].node_id) {
                                    bool bAdopted = vRFAdoptDevice(g_active_configuration.config.node_qr_mappings[i].qr_code, g_active_configuration.config.node_qr_mappings[i].node_id, DEVICE_OP_MODE_SENSOR_PRIMARY);;
                                    if (bAdopted) {
                                        pProvisioningState->is_provisioned = true;
                                        pProvisioningState->last_seen_time = get_time_in_ms();
                                        pProvisioningState->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
                                        bMadeChanges = true;
                                    } else {
                                        pProvisioningState->is_provisioned = false;
                                        pProvisioningState->last_seen_time = get_time_in_ms();
                                        pProvisioningState->error_code = ProvisioningState_ProvisioningErrorCode_NOT_FOUND;
                                    }
                                    break;
                                }
                            }
                        } else {
                            pProvisioningState->is_provisioned = false;
                            pProvisioningState->last_seen_time = get_time_in_ms();
                            pProvisioningState->error_code = ProvisioningState_ProvisioningErrorCode_NO_REED_CONFIG;
                        }
                    }
                }

                // Update tracking variables
                xLastProvisioningCheck = xCurrentTime;
                ulLastNodeMappingsCount = g_active_configuration.config.node_qr_mappings_count;
                
                // Only save to flash if we made provisioning changes
                // if (bMadeChanges) {
                //     flash_storage_set_configuration(&g_active_configuration);
                // }
            }
        }

        while( bGetRfProcessMessages() && xQueueReceive( xRFRxQueue, &xRfRxMessage, QUEUE_TICKS_DELAY ) != errQUEUE_EMPTY ){
            // handle the message
            #if defined( DEBUG_RF_PACKET_RX )
            printf( "[ RF4463 ] Rx - rssi: %d | size: %02d | pkt: ", xRfRxMessage.rssi, xRfRxMessage.len );
            if( xRfRxMessage.len > 0 && xRfRxMessage.len < RF4463_MAX_BUFFER_LENGTH ){
                for( ucCounter = 0; ucCounter < xRfRxMessage.len; ucCounter++ ){
                    printf( "%02X ", xRfRxMessage.message[ ucCounter ] );
                }
            }
            printf( "\r\n" );
            #endif
            uint8_t ucNodeId = xRfRxMessage.message[ RF_MESSAGE_SOURCE ];

            ProvisioningState *pProvisioningState = get_provisioned_device_state_from_node_id(ucNodeId);
            pProvisioningState->last_seen_time = get_time_in_ms();

            printf( "[ RF4463 ] Node ID: %d\r\n", ucNodeId );

            switch( xRfRxMessage.message[ RF_MESSAGE_TABLE ] ){
                case TABLE_RF_TEST: {
                    printf( "    RF TEST\r\n" );
                    break;
                }
                case TABLE_RF_REED: {
                    float fBatteryVoltage = (double)xRfRxMessage.message[RF_MESSAGE_CONTENT] / 10.0f;
                    printf( "    Battery Voltage: %d.%d\r\n", ( xRfRxMessage.message[ RF_MESSAGE_CONTENT ] / 10 ), ( xRfRxMessage.message[ RF_MESSAGE_CONTENT ] % 10 ) );

                    RFReedState *pRFReedState = get_rf_reed_state_from_node_id(ucNodeId);
                    pRFReedState->last_modified_time = get_time_in_ms();
                    pRFReedState->battery_voltage = fBatteryVoltage;
                    switch( xRfRxMessage.message[ RF_MESSAGE_COMMAND ] ){
                        case RF_REED_SWITCH_CLOSE_RETRANSMIT:
                            if (pRFReedState->sensor_status == RFReedState_Status_CLOSED) {
                                break;
                            }
                        case RF_REED_SWITCH_CLOSE: {
                            pRFReedState->sensor_status = RFReedState_Status_CLOSED;
                            
                            RFDoorSensorCommand rf_door_sensor_command = RFDoorSensorCommand_init_zero;
                            rf_door_sensor_command.node_id = ucNodeId;
                            rf_door_sensor_command.state = RFDoorSensorCommand_State_Closed;

                            uint8_t buffer[128];
                            buffer[0] = static_cast<uint8_t>(MessageType::MESSAGE_RF_DOOR_SENSOR);
                            pb_ostream_t stream = pb_ostream_from_buffer(buffer + 1, sizeof(buffer) - 1);
                            if (pb_encode(&stream, RFDoorSensorCommand_fields, &rf_door_sensor_command)) {
                                handle_command(buffer, stream.bytes_written + 1, ucNodeId);
                            }
                            break;
                        }
                        case RF_REED_SWITCH_OPEN_RETRANSMIT: {
                            if (pRFReedState->sensor_status == RFReedState_Status_OPEN) {
                                break;
                            }
                        }
                        case RF_REED_SWITCH_OPEN: {
                            pRFReedState->sensor_status = RFReedState_Status_OPEN;
                            
                            RFDoorSensorCommand rf_door_sensor_command = RFDoorSensorCommand_init_zero;
                            rf_door_sensor_command.node_id = ucNodeId;
                            rf_door_sensor_command.state = RFDoorSensorCommand_State_Opened;

                            uint8_t buffer[128];
                            buffer[0] = static_cast<uint8_t>(MessageType::MESSAGE_RF_DOOR_SENSOR);
                            pb_ostream_t stream = pb_ostream_from_buffer(buffer + 1, sizeof(buffer) - 1);
                            if (pb_encode(&stream, RFDoorSensorCommand_fields, &rf_door_sensor_command)) {
                                handle_command(buffer, stream.bytes_written + 1, ucNodeId);
                            }
                            break;
                        }
                        default: {
                            break;
                        }
                    }
                    break;
                }
                case TABLE_RF_DIMMER: {
                    break;
                }
                case TABLE_RF_SETUP: {
                    
                    switch( xRfRxMessage.message[ RF_MESSAGE_COMMAND ] ){
                        case RF_SETUP_HARD_RESET_REQUEST:
                            if (pProvisioningState->is_provisioned) {
                                pProvisioningState->is_provisioned = false;
                                pProvisioningState->last_seen_time = get_time_in_ms();
                                pProvisioningState->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
                            }
                            break;
                        default:
                            break;
                    }
                    break;
                }
                default: {
                    printf( "RF UNKNOWN: %d | COMMAND: %d\r\n", xRfRxMessage.message[ RF_MESSAGE_TABLE ], xRfRxMessage.message[ RF_MESSAGE_COMMAND ] );
                    break;
                }
            }
        }
    }
}

uint8_t ucRfInit(){
    uint8_t ucCounter = 0;

    gpio_init( rf_params.nSDN_pin );
    gpio_set_dir( rf_params.nSDN_pin, GPIO_OUT );
    gpio_put( rf_params.nSDN_pin, 0 );

    if( ucRF4463Init( rf_params.nSDN_pin, &rf_params.rf_config ) ){
        return 1;
    }
    /*********************************************************************
     * Check if the device is present since there's no analysis for
     * buffer responses for each command
    *********************************************************************/
    for( ucCounter = 0; ucCounter < 15; ucCounter++ ){
        if( ucRF4463GetPartInfo( RF4463_PART_INFO_VALUE ) == 0 ){
            break;
        }
    }
    if( ucCounter == 15 ){
        printf( "[ RF4463 ] failed to init rf\r\n" );
        return 1;
    }

    return 0;
}

void vRfRxRestore(){
    vRfRxRestoreNoDelay();
    vTaskDelay(pdMS_TO_TICKS(200));
}

void vRfRxRestoreNoDelay(){
    vRF4463ResetFIFO();
    vRF4463ClearInterrupts();
    vRF4463RxInit();
}

void vRfRxMessage(){
    xRxMessage.len = ucRF4463RxReadFIFO( xRxMessage.message );
    vRF4463ResetFIFO();
}

uint8_t vRFPrepareKlevernessMessage( const uint8_t * pcBuffer, uint8_t ucLength, uint8_t ucTable, uint8_t ucCommand, uint8_t ucDestination, uint8_t* pcOutBuffer ){
    uint8_t ucCounter = 0;
    uint8_t ucBasestationId = 1;
    if (g_active_configuration.has_config && g_active_configuration.config.id != 0) {
        ucBasestationId = g_active_configuration.config.id;
    }
        
    pcOutBuffer[ RF_MESSAGE_SIZE ]             = RF_MESSAGE_HEADER_SIZE + ucLength;
    pcOutBuffer[ RF_MESSAGE_DESTINATION ]      = ucDestination;
    pcOutBuffer[ RF_MESSAGE_SOURCE ]           = ucBasestationId;
    pcOutBuffer[ RF_MESSAGE_TABLE ]            = ucTable;
    pcOutBuffer[ RF_MESSAGE_COMMAND ]          = ucCommand;
/*********************************************************************
    * Insert additional information to packet
*********************************************************************/
    if( ucLength > 0 ){
        for( ucCounter = 0; ucCounter < ucLength; ucCounter++ ){
            pcOutBuffer[ RF_MESSAGE_HEADER_SIZE + ucCounter ] = pcBuffer[ ucCounter ];
        }
    }

    return ucCounter;
}

void vRfTxMessage( uint8_t * pcOutMessage, uint8_t ucLength ){
    uint8_t ucCounter = 0;

    if( !pcOutMessage || ucLength == 0 || ucLength > RF4463_MAX_BUFFER_LENGTH ){
        return;
    }

    #if defined( DEBUG_RF_PACKET_TX )
        printf( "[ RF ] Tx Length: %d\r\n", ucLength );
        printf( "[ RF ] Tx Packet Hex: " );
        for( ucCounter = 0; ucCounter < ucLength; ucCounter++ ){
            printf( "%02X ", pcOutMessage[ ucCounter ] );
        }
        printf( "\r\n" );
    #endif

    vRF4463TxData( pcOutMessage, ucLength );
}

static void send_rf_message(const uint8_t *data, uint32_t len) {
    vRfTxMessage((uint8_t*)data, len);
}