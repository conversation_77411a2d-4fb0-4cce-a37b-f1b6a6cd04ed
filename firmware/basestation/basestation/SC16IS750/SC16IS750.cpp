/*
 Sandbox Electronics SC16IS750 Driver – UPDATED for Raspberry Pi Pico SDK
 -------------------------------------------------------------
 * Converted from Arduino to Raspberry Pi Pico SDK
 * Uses Pico SDK SPI and I2C hardware interfaces
 * Removed Arduino Stream inheritance and dependencies
 * Updated timing functions to use Pico SDK equivalents
*/

//#define SC16IS750_DEBUG_PRINT
#include "SC16IS750.h"

SC16IS750::SC16IS750(SPIParams_t* spi)
{
    spi_params = spi;
    peek_flag = 0;
}

void SC16IS750::begin(uint32_t baud)
{
    // Initialize SPI
    gpio_set_function(spi_params->cs_pin, GPIO_FUNC_SIO);
    gpio_set_dir(spi_params->cs_pin, GPIO_OUT);
    gpio_put(spi_params->cs_pin, 1);

    // Initialize SPI at 4MHz
    xSPIInit(spi_params, true);

    ResetDevice();
    FIFOEnable(1);
    SetBaudrate(baud);
    SetLine(8, 0, 2); // <PERSON><PERSON> wants 8 data, 0 parity, 2 stop bits
}

int SC16IS750::available(void)
{
    return FIFOAvailableData();
}

int SC16IS750::read(void)
{
    if (peek_flag == 0) {
        return ReadByte();
    } else {
        peek_flag = 0;
        return peek_buf;
    }
}

size_t SC16IS750::write(uint8_t val)
{
    WriteByte(val);
    return 1;  // one byte successfully queued
}

void SC16IS750::pinMode(uint8_t pin, uint8_t i_o)
{
    GPIOSetPinMode(pin, i_o);
}

void SC16IS750::digitalWrite(uint8_t pin, uint8_t value)
{
    GPIOSetPinState(pin, value);
}

uint8_t SC16IS750::digitalRead(uint8_t pin)
{
    return GPIOGetPinState(pin);
}

//--------------------------------------------------------------------
//  The remainder of the original 2014 implementation is unchanged.
//--------------------------------------------------------------------

uint8_t SC16IS750::ReadRegister(uint8_t reg_addr)
{
    uint8_t result;
    reg_addr = (uint8_t)(0x80 | (reg_addr << 3));

    SPIOperation_t xSPIOperation[] = {
        { .buffer = &reg_addr, .len = 1, .type = SPI_OP_WRITE, .restart_cs = false, },
        { .buffer = &result, .len = 1, .type = SPI_OP_READ, .restart_cs = false }
    };

    memset( &xSPISequence, 0, sizeof( SPISequence_t ) );

    xSPISequence.total_ops = 2;
    xSPISequence.operation = xSPIOperation;

    bool success = xSPITransaction( spi_params, &xSPISequence, 100 );
    if( !success ){
        printf( "Failed to read register\r\n" );
    }

    // gpio_put(spi_params->cs_pin, 0);
    // sleep_us(10);
    
    // uint8_t tx_data[2] = {(uint8_t)(0x80 | (reg_addr << 3)), 0xFF};
    // uint8_t rx_data[2];
    // spi_write_read_blocking(spi_params->spi_inst, tx_data, rx_data, 2);
    // result = rx_data[1];
    
    // sleep_us(10);
    // gpio_put(spi_params->cs_pin, 1);
    
    return result;
}

void SC16IS750::WriteRegister(uint8_t reg_addr, uint8_t val)
{
    // gpio_put(spi_params->cs_pin, 0);
    // sleep_us(10);
    
    uint8_t data[2] = {(uint8_t)(reg_addr << 3), val};

    SPIOperation_t xSPIOperation[] = {
        { .buffer = data, .len = 2, .type = SPI_OP_WRITE, .restart_cs = false, },
    };

    memset( &xSPISequence, 0, sizeof( SPISequence_t ) );

    xSPISequence.total_ops = 1;
    xSPISequence.operation = xSPIOperation;

    xSPITransaction( spi_params, &xSPISequence, 100 );
    
    // spi_write_blocking(spi_params->spi_inst, data, 2);
    
    // sleep_us(10);
    // gpio_put(spi_params->cs_pin, 1);
}

int16_t SC16IS750::SetBaudrate(uint32_t baudrate) //return error of baudrate parts per thousand
{
    uint16_t divisor;
    uint8_t prescaler;
    uint32_t actual_baudrate;
    int16_t error;
    uint8_t temp_lcr;
    if ( (ReadRegister(SC16IS750_REG_MCR)&0x80) == 0) { //if prescaler==1
        prescaler = 1;
    } else {
        prescaler = 4;
    }

    divisor = (SC16IS750_CRYSTCAL_FREQ/prescaler)/(baudrate*16);

    temp_lcr = ReadRegister(SC16IS750_REG_LCR);
    temp_lcr |= 0x80;
    WriteRegister(SC16IS750_REG_LCR,temp_lcr);
    //write to DLL
    WriteRegister(SC16IS750_REG_DLL,(uint8_t)divisor);
    //write to DLH
    WriteRegister(SC16IS750_REG_DLH,(uint8_t)(divisor>>8));
    temp_lcr &= 0x7F;
    WriteRegister(SC16IS750_REG_LCR,temp_lcr);


    actual_baudrate = (SC16IS750_CRYSTCAL_FREQ/prescaler)/(16*divisor);
    error = ((float)actual_baudrate-baudrate)*1000/baudrate;
#ifdef  SC16IS750_DEBUG_PRINT
    printf("Desired baudrate: %lu\n", baudrate);
    printf("Calculated divisor: %u\n", divisor);
    printf("Actual baudrate: %lu\n", actual_baudrate);
    printf("Baudrate error: %d\n", error);
#endif

    return error;

}

void SC16IS750::SetLine(uint8_t data_length, uint8_t parity_select, uint8_t stop_length )
{
    uint8_t temp_lcr;
    temp_lcr = ReadRegister(SC16IS750_REG_LCR);
    temp_lcr &= 0xC0; //Clear the lower six bit of LCR (LCR[0] to LCR[5]
#ifdef  SC16IS750_DEBUG_PRINT
    printf("LCR Register:0x%02X\n", temp_lcr);
#endif
    switch (data_length) {            //data length settings
        case 5:
            break;
        case 6:
            temp_lcr |= 0x01;
            break;
        case 7:
            temp_lcr |= 0x02;
            break;
        case 8:
            temp_lcr |= 0x03;
            break;
        default:
            temp_lcr |= 0x03;
            break;
    }

    if ( stop_length == 2 ) {
        temp_lcr |= 0x04;
    }

    switch (parity_select) {            //parity selection length settings
        case 0:                         //no parity
             break;
        case 1:                         //odd parity
            temp_lcr |= 0x08;
            break;
        case 2:                         //even parity
            temp_lcr |= 0x18;
            break;
        case 3:                         //force '1' parity
            temp_lcr |= 0x03;
            break;
        case 4:                         //force '0' parity
            break;
        default:
            break;
    }

    WriteRegister(SC16IS750_REG_LCR,temp_lcr);
}

void SC16IS750::GPIOSetPinMode(uint8_t pin_number, uint8_t i_o)
{
    uint8_t temp_iodir;

    temp_iodir = ReadRegister(SC16IS750_REG_IODIR);
    if ( i_o == OUTPUT ) {
      temp_iodir |= (0x01 << pin_number);
    } else {
      temp_iodir &= (uint8_t)~(0x01 << pin_number);
    }

    WriteRegister(SC16IS750_REG_IODIR, temp_iodir);
    return;
}

void SC16IS750::GPIOSetPinState(uint8_t pin_number, uint8_t pin_state)
{
    uint8_t temp_iostate;

    temp_iostate = ReadRegister(SC16IS750_REG_IOSTATE);
    if ( pin_state == 1 ) {
      temp_iostate |= (0x01 << pin_number);
    } else {
      temp_iostate &= (uint8_t)~(0x01 << pin_number);
    }

    WriteRegister(SC16IS750_REG_IOSTATE, temp_iostate);
    return;
}


uint8_t SC16IS750::GPIOGetPinState(uint8_t pin_number)
{
    uint8_t temp_iostate;

    temp_iostate = ReadRegister(SC16IS750_REG_IOSTATE);
    if ( temp_iostate & (0x01 << pin_number)== 0 ) {
      return 0;
    }
    return 1;
}

uint8_t SC16IS750::GPIOGetPortState(void)
{

    return ReadRegister(SC16IS750_REG_IOSTATE);

}

void SC16IS750::GPIOSetPortMode(uint8_t port_io)
{
    WriteRegister(SC16IS750_REG_IODIR, port_io);
    return;
}

void SC16IS750::GPIOSetPortState(uint8_t port_state)
{
    WriteRegister(SC16IS750_REG_IOSTATE, port_state);
    return;
}

void SC16IS750::SetPinInterrupt(uint8_t io_int_ena)
{
    WriteRegister(SC16IS750_REG_IOINTENA, io_int_ena);
    return;
}

void SC16IS750::ResetDevice(void)
{
    uint8_t reg;

    reg = ReadRegister(SC16IS750_REG_IOCONTROL);
    reg |= 0x08;
    WriteRegister(SC16IS750_REG_IOCONTROL, reg);

    return;
}

void SC16IS750::ModemPin(uint8_t gpio) //gpio == 0, gpio[7:4] are modem pins, gpio == 1 gpio[7:4] are gpios
{
    uint8_t temp_iocontrol;

    temp_iocontrol = ReadRegister(SC16IS750_REG_IOCONTROL);
    if ( gpio == 0 ) {
        temp_iocontrol |= 0x02;
    } else {
        temp_iocontrol &= 0xFD;
    }
    WriteRegister(SC16IS750_REG_IOCONTROL, temp_iocontrol);

    return;
}

void SC16IS750::GPIOLatch(uint8_t latch)
{
    uint8_t temp_iocontrol;

    temp_iocontrol = ReadRegister(SC16IS750_REG_IOCONTROL);
    if ( latch == 0 ) {
        temp_iocontrol &= 0xFE;
    } else {
        temp_iocontrol |= 0x01;
    }
    WriteRegister(SC16IS750_REG_IOCONTROL, temp_iocontrol);

    return;
}

void SC16IS750::InterruptControl(uint8_t int_ena)
{
    WriteRegister(SC16IS750_REG_IER, int_ena);
}

uint8_t SC16IS750::InterruptPendingTest(void)
{
    return (ReadRegister(SC16IS750_REG_IIR) & 0x01);
}

void SC16IS750::irqHandler(void)
{
    uint8_t irq_src;

    irq_src = ReadRegister(SC16IS750_REG_IIR);
    irq_src = (irq_src >> 1);
    irq_src &= 0x3F;

    switch (irq_src) {
        case 0x06:                  //Receiver Line Status Error
            break;
        case 0x0c:               //Receiver time-out interrupt
            break;
        case 0x04:               //RHR interrupt
            break;
        case 0x02:               //THR interrupt
            break;
        case 0x00:                  //modem interrupt;
            break;
        case 0x30:                  //input pin change of state
            break;
        case 0x10:                  //XOFF
            break;
        case 0x20:                  //CTS,RTS
            break;
        default:
            break;
    }
    return;
}

void SC16IS750::FIFOEnable(uint8_t fifo_enable)
{
    uint8_t temp_fcr;

    temp_fcr = ReadRegister(SC16IS750_REG_FCR);

    if (fifo_enable == 0){
        temp_fcr &= 0xFE;
    } else {
        temp_fcr |= 0x01;
    }
    WriteRegister(SC16IS750_REG_FCR,temp_fcr);

    return;
}

void SC16IS750::FIFOReset(uint8_t rx_fifo)
{
     uint8_t temp_fcr;

    temp_fcr = ReadRegister(SC16IS750_REG_FCR);

    if (rx_fifo == 0){
        temp_fcr |= 0x04;
    } else {
        temp_fcr |= 0x02;
    }
    WriteRegister(SC16IS750_REG_FCR,temp_fcr);

    return;

}

void SC16IS750::FIFOSetTriggerLevel(uint8_t rx_fifo, uint8_t length)
{
    uint8_t temp_reg;

    temp_reg = ReadRegister(SC16IS750_REG_MCR);
    temp_reg |= 0x04;
    WriteRegister(SC16IS750_REG_MCR,temp_reg); //SET MCR[2] to '1' to use TLR register or trigger level control in FCR register

    temp_reg = ReadRegister(SC16IS750_REG_EFR);
    WriteRegister(SC16IS750_REG_EFR, temp_reg|0x10); //set ERF[4] to '1' to use the  enhanced features
    if (rx_fifo == 0) {
        WriteRegister(SC16IS750_REG_TLR, length<<4); //Tx FIFO trigger level setting
    } else {
        WriteRegister(SC16IS750_REG_TLR, length);    //Rx FIFO Trigger level setting
    }
    WriteRegister(SC16IS750_REG_EFR, temp_reg); //restore EFR register

    return;
}

uint8_t SC16IS750::FIFOAvailableData(void)
{
#ifdef  SC16IS750_DEBUG_PRINT
    printf("=====Available data: %d\n", ReadRegister(SC16IS750_REG_RXLVL));
#endif
   return ReadRegister(SC16IS750_REG_RXLVL);
//    return ReadRegister(SC16IS750_REG_LSR) & 0x01;
}

uint8_t SC16IS750::FIFOAvailableSpace(void)
{
   return ReadRegister(SC16IS750_REG_TXLVL);

}

void SC16IS750::WriteByte(uint8_t val)
{
	uint8_t tmp_lsr;
//  /*   while ( FIFOAvailableSpace() == 0 ){
// #ifdef  SC16IS750_DEBUG_PRINT
// 		printf("No available space\n");
// #endif

// 	};

// #ifdef  SC16IS750_DEBUG_PRINT
//     printf("++++++++++++Data sent\n");
// #endif
//     WriteRegister(SC16IS750_REG_THR,val);
// */
	do {
		tmp_lsr = ReadRegister(SC16IS750_REG_LSR);
	} while ((tmp_lsr&0x20) ==0);

	WriteRegister(SC16IS750_REG_THR,val);



}

int SC16IS750::ReadByte(void)
{
	volatile uint8_t val;
	if (FIFOAvailableData() == 0) {
#ifdef  SC16IS750_DEBUG_PRINT
		printf("No data available\n");
#endif
		return -1;

	} else {

#ifdef  SC16IS750_DEBUG_PRINT
		printf("***********Data available***********\n");
#endif
	  val = ReadRegister(SC16IS750_REG_RHR);
	  return val;
	}


}

void SC16IS750::EnableTransmit(uint8_t tx_enable)
{
    uint8_t temp_efcr;
    temp_efcr = ReadRegister(SC16IS750_REG_EFCR);
    if ( tx_enable == 0) {
        temp_efcr |= 0x04;
    } else {
        temp_efcr &= 0xFB;
    }
    WriteRegister(SC16IS750_REG_EFCR,temp_efcr);

    return;
}

uint8_t SC16IS750::ping()
{
	WriteRegister(SC16IS750_REG_SPR,0x55);
	if (ReadRegister(SC16IS750_REG_SPR) !=0x55) {
		return 0;
	}

	WriteRegister(SC16IS750_REG_SPR,0xAA);
	if (ReadRegister(SC16IS750_REG_SPR) !=0xAA) {
		return 0;
	}

	return 1;

}
/*
void SC16IS750::setTimeout(uint32_t time_out)
{
	timeout = time_out;
}

size_t SC16IS750::readBytes(char *buffer, size_t length)
{
	size_t count=0;
	int16_t tmp;

	while (count < length) {
		tmp = readwithtimeout();
		if (tmp < 0) {
			break;
		}
		*buffer++ = (char)tmp;
		count++;
	}

	return count;
}

int16_t SC16IS750::readwithtimeout()
{
  int16_t tmp;
  uint32_t time_stamp;
  time_stamp = to_ms_since_boot(get_absolute_time());
  do {
    tmp = read();
    if (tmp >= 0) return tmp;
  } while(to_ms_since_boot(get_absolute_time()) - time_stamp < timeout);
  return -1;     // -1 indicates timeout
}
*/

void SC16IS750::flush()
{
	uint8_t tmp_lsr;

	do {
		tmp_lsr = ReadRegister(SC16IS750_REG_LSR);
	} while ((tmp_lsr&0x20) ==0);


}

int SC16IS750:: peek()
{
	if ( peek_flag == 0 ) {
		peek_buf =ReadByte();
		if (  peek_buf >= 0 ) {
			peek_flag = 1;
		}
	}

	return peek_buf;

}
