#ifndef __PINOUT_H__
#define __PINOUT_H__

#include "types.h"



// https://docs.google.com/spreadsheets/d/1kl673CO3CkXqD_staZ7KO1MUCZWGGHAwSOobooyKgSs/edit?gid=1539585132#gid=1539585132
// #define CANDUIT 1
#define PROTOBOARD 1

// // user libraries that contains the structures
// #include "UART/uart.h"

#define PIO0    0
#define PIO1    1
#define PIO2    2

// #define RESET_BUTTON_PIN    GPIO6_PIN

// #define CONTACT_1_PIN       GPIO22_PIN
// #define CONTACT_2_PIN       GPIO23_PIN
// #define CONTACT_3_PIN       GPIO24_PIN
// #define CONTACT_4_PIN       GPIO25_PIN

#define ON_BOARD_LED_PIN        GPIO25_PIN

// #define ESP_ENABLE_PIN      GPIO10_PIN
#define ESP_ENABLE_PIN      GPIO28_PIN

#define NUM_CAN_CHANNELS        3
#define NUM_UART_CHANNELS       2
#define NUM_DMX_CHANNELS        1
#define NUM_RGB_LED_CHANNELS    1
#define NUM_SPI_ENDPOINTS       2
#define NUM_I2C_CHANNELS        1

#define CAN_PARAMS_DEFAULT() { \
    { \
        .en = false, \
        .baudrate = 0, \
        .sys_clock = 0, \
        .irq_num = PIO0_IRQ_0, \
        .irq_cb_func = NULL, \
        .tx_pin = GPIO2_PIN, \
        .rx_pin = GPIO3_PIN, \
        .pio_num = PIO0, \
        .rx_cb_func = NULL \
    }, \
    { \
        .en = false, \
        .baudrate = 0, \
        .sys_clock = 0, \
        .irq_num = PIO1_IRQ_0, \
        .irq_cb_func = NULL, \
        .tx_pin = GPIO4_PIN, \
        .rx_pin = GPIO5_PIN, \
        .pio_num = PIO1, \
        .rx_cb_func = NULL \
    }, \
    { \
        .en = false, \
        .baudrate = 0, \
        .sys_clock = 0, \
        .irq_num = PIO2_IRQ_0, \
        .irq_cb_func = NULL, \
        .tx_pin = GPIO24_PIN, /* .tx_pin = GPIO26_PIN, */ \
        .rx_pin = GPIO25_PIN, /* .rx_pin = GPIO27_PIN, */ \
        .pio_num = PIO2, \
        .rx_cb_func = NULL \
    } \
};

#define UART_PARAMS_DEFAULT() { \
    {   /* Debugger */ \
        .baudrate = 115200, \
        .uart_inst = uart0, \
        .tx_pin = GPIO0_PIN, \
        .rx_pin = GPIO1_PIN, \
        .hw_ctrl = false, \
        .fifo_en = false, \
        .irq_num = 0, \
        .irq_cb_func = NULL, \
        .dma_rx = { 0 }, \
        .dma_tx = { 0 }, \
    }, \
    {   /* ESP32 */ \
        .baudrate = 921600, \
        .uart_inst = uart1, \
        .tx_pin = GPIO8_PIN, \
        .rx_pin = GPIO9_PIN, \
        .hw_ctrl = false, \
        .fifo_en = true, \
        .irq_num = 0, \
        .irq_cb_func = NULL, \
        .dma_rx = { \
            .en = true, \
            .channel = 0, \
            .dreq_num = DREQ_UART1_RX, \
            .irq_num = 0, \
            .buf_add = NULL, \
            .buf_len = 0, \
            .irq_cb_func = NULL, \
        }, \
        .dma_tx = { \
            /* Tx options */ \
            .en = true, \
            .channel = 0, \
            .dreq_num = DREQ_UART1_TX, \
            .irq_num = 0, \
            .buf_add = NULL, \
            .buf_len = 0, \
            .irq_cb_func = NULL, \
        }, \
    }, \
};

#ifdef PROTOBOARD
#define RF_PARAMS_DEFAULT() {\
    .nSDN_pin = GPIO7_PIN, /* .nSDN_pin = GPIO7_PIN, */ \
    .nIRQ_pin = GPIO10_PIN, /* .nIRQ_pin = GPIO10_PIN, */ \
    .irq_num = 0, \
    .irq_cb_func = NULL, \
    .rf_config = { \
        .frequency = RF4463_FREQ_915MHz, \
        .baudrate = RF4463_BAUDRATE_1200, \
        .syncword = 15U, \
        .tx_channel = 15U, \
        .rx_channel = 15U, \
        .tx_power = RF4463_TX_POWER_20_dB, \
        .op_mode = RF4463_GPIO_TX_NORMAL, \
        .match = { 0 }, \
        .mask = { 0 }, \
    }, \
    .spi_config = { \
        .spi_inst = spi0, \
        .cpha = SPI_CPHA_0, \
        .cpol = SPI_CPOL_0, \
        .order = SPI_MSB_FIRST, \
        .baudrate = SPI_BUS_CLK_SPEED_4MHZ, \
        .data_len = 8, \
        .cs_pin = GPIO17_PIN, \
        .sck_pin = GPIO18_PIN, \
        .miso_pin = GPIO16_PIN, \
        .mosi_pin = GPIO19_PIN, \
    } \
};
#endif

#ifdef CANDUIT
#define RF_PARAMS_DEFAULT() {\
    .nSDN_pin = GPIO21_PIN, /* .nSDN_pin = GPIO7_PIN, */ \
    .nIRQ_pin = GPIO20_PIN, /* .nIRQ_pin = GPIO10_PIN, */ \
    .irq_num = 0, \
    .irq_cb_func = NULL, \
    .rf_config = { \
        .frequency = RF4463_FREQ_915MHz, \
        .baudrate = RF4463_BAUDRATE_1200, \
        .syncword = 15U, \
        .tx_channel = 15U, \
        .rx_channel = 15U, \
        .tx_power = RF4463_TX_POWER_20_dB, \
        .op_mode = RF4463_GPIO_TX_NORMAL, \
        .match = { 0 }, \
        .mask = { 0 }, \
    }, \
    .spi_config = { \
        .spi_inst = spi0, \
        .cpha = SPI_CPHA_0, \
        .cpol = SPI_CPOL_0, \
        .order = SPI_MSB_FIRST, \
        .baudrate = SPI_BUS_CLK_SPEED_4MHZ, \
        .data_len = 8, \
        .cs_pin = GPIO17_PIN, \
        .sck_pin = GPIO18_PIN, \
        .miso_pin = GPIO16_PIN, \
        .mosi_pin = GPIO19_PIN, \
    } \
};
#endif

#define DMX_PARAMS_DEFAULT() {\
    .spi_config = { \
        .spi_inst = spi0, \
        .cpha = SPI_CPHA_0, \
        .cpol = SPI_CPOL_0, \
        .order = SPI_MSB_FIRST, \
        .baudrate = SPI_BUS_CLK_SPEED_4MHZ, \
        .data_len = 8, \
        .cs_pin = GPIO21_PIN, /* .cs_pin = GPIO15_PIN, */ \
        .sck_pin = GPIO18_PIN, \
        .miso_pin = GPIO16_PIN, \
        .mosi_pin = GPIO19_PIN, \
    } \
};

#define SD_PARAMS_DEFAULT() { \
    .spi_config = { \
        .spi_inst = spi0, \
        .cpha = SPI_CPHA_0, \
        .cpol = SPI_CPOL_0, \
        .order = SPI_MSB_FIRST, \
        .baudrate = SPI_BUS_CLK_SPEED_4MHZ, \
        .data_len = 8, \
        .cs_pin = GPIO17_PIN, \
        .sck_pin = GPIO18_PIN, \
        .miso_pin = GPIO16_PIN, \
        .mosi_pin = GPIO19_PIN, \
    } \
};

// extern RGB_LED_Params rgb_params[NUM_RGB_LED_CHANNELS] = {
//     {
//         .red_channel = GPIO13_PIN,
//         .green_channel = GPIO14_PIN,
//         .blue_channel = GPIO15_PIN,
//     },
// };

// SPI_Params spi_params[NUM_SPI_ENDPOINTS] = {
//     {   // SD Card
//         .spi_inst = spi0,
//         .cpha = SPI_CPHA_0,
//         .cpol = SPI_CPOL_0,
//         .order = SPI_LSB_FIRST,
//         .baudrate = (500) * (1000),
//         .data_len = 8,
//         .cs_pin = GPIO21_PIN,
//         .sck_pin = GPIO18_PIN,
//         .miso_pin = GPIO16_PIN,
//         .mosi_pin = GPIO19_PIN,
//     },
// };

// I2C_Params i2c_params[NUM_I2C_CHANNELS] = {
//     {
//         .i2c_inst = i2c0,
//         .sda_pin = GPIO26_PIN,
//         .scl_pin = GPIO27_PIN,
//         .baudrate = 100 * (1000),
//     },
// };

#endif