#ifndef __CAN_TASK_H__
#define __CAN_TASK_H__

// Include headers that may contain C++ code outside extern "C"
#include "uart_task.h"
#include "event_task.h"

#ifdef __cplusplus
extern "C" {
#endif

// C standard libraries
#include <stdio.h>
#include <string.h>

// pico sdk libraries
#include "FreeRTOS.h"
#include "task.h"
#include "queue.h"
#include "hardware/irq.h"

// user libraries
#include "types.h"
#include "pinout.h"
#include "can2040.h"
#include "can_varlen.h"

#include <pb_encode.h>
#include "commands.pb.h"

#include "message_handler.h"

// global definitions
extern TaskHandle_t xCAN0RxTaskHandle;
extern TaskHandle_t xCAN0TxTaskHandle;
extern QueueHandle_t xCANRxQueue;
extern QueueHandle_t xCANTxQueue;

// local definitions/enums
// >> CAN behaviour definitions
    #define DEBUG_CAN_PACKET_RX
    #define DEBUG_CAN_PACKET_TX
// >> END OF DEFINITIONS

typedef struct{
    uint8_t event_id;
    uint32_t channel;
    struct can2040_msg message;
} CANMessage_t;

#define CAN_BAUDRATE_125KBPS 125000UL
#define SYS_CLOCK_150MHZ 150000000UL

#define CANRX_QUEUE_LENGTH 30 // Max number of items in each individual queue
#define CANRX_MSG_ITEM_SIZE sizeof( CANMessage_t ) // Size of each item

#define CANTX_QUEUE_LENGTH 30 // Max number of items in each individual queue
#define CANTX_MSG_ITEM_SIZE sizeof( CANMessage_t ) // Size of each item

// function prototyping
static void can_varlen_rx_cb(struct can2040 *cd, uint32_t base_id, const uint8_t *data, uint32_t len);
static void can_varlen_tx_cb(struct can2040 *cd, struct can2040_msg *msg);

static void vCANCallback( struct can2040 *cd, uint32_t notify, struct can2040_msg *msg );

static void vCAN_CH0_IRQHandler( void );
static void vCAN_CH1_IRQHandler( void );
static void vCAN_CH2_IRQHandler( void );

void vCANRxTask( void * pvParameters );
void vCANTxTask( void * pvParameters );

void vCANBusSetup( CANParams_t * pxParams, uint8_t ucChannel );
void vCANBusTx( CANMessage_t * pxOutMessage );

#ifdef __cplusplus
}
#endif

#endif