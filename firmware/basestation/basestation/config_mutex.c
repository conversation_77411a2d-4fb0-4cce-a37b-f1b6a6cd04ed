#include "config_mutex.h"
#include <stdio.h>

// Static mutex handle
static SemaphoreHandle_t xConfigMutex = NULL;

bool vConfigMutexInit(void) {
    if (xConfigMutex == NULL) {
        xConfigMutex = xSemaphoreCreateMutex();
        if (xConfigMutex == NULL) {
            printf("ERROR: Failed to create configuration mutex\r\n");
            return false;
        }
        printf("Configuration mutex initialized\r\n");
    }
    return true;
}

bool xConfigLock(TickType_t xTimeout) {
    if (xConfigMutex == NULL) {
        printf("ERROR: Configuration mutex not initialized\r\n");
        return false;
    }
    
    if (xSemaphoreTake(xConfigMutex, xTimeout) == pdTRUE) {
        return true;
    }
    
    printf("WARNING: Failed to acquire configuration lock (timeout)\r\n");
    return false;
}

void vConfigUnlock(void) {
    if (xConfigMutex == NULL) {
        printf("ERROR: Configuration mutex not initialized\r\n");
        return;
    }
    
    xSemaphoreGive(xConfigMutex);
}