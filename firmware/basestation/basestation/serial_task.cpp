#include "serial_task.h"
#include "active_configuration.h"
#include "flash_storage.h"
#include "version.h"
#include "system_monitor.h"
#include "message_handler.h"
#include "pico/stdlib.h"
#include "pico/binary_info.h"
#include "pico/bootrom.h"
#include "hardware/watchdog.h"
#include "hardware/structs/watchdog.h"
#include <stdarg.h>

#ifdef __cplusplus
extern "C" {
#endif

static SerialMessage_t xSerialMessage;

// Task handles
TaskHandle_t xSerialRxTaskHandle;

// Base64 decode function
static uint16_t base64_decode( const char* pcBase64Data, uint16_t usBase64Len, uint8_t* pucOutput, uint16_t usMaxOutputLen ){
    if (!pcBase64Data || !pucOutput || usBase64Len == 0 || usMaxOutputLen == 0) {
        printf("[SERIAL] ERROR: Invalid parameters to base64_decode\r\n");
        return 0;
    }
    
    uint16_t usDecodedLen = 0;
    
    // Simple base64 decode implementation
    for( uint16_t i = 0; i < usBase64Len; i += 4 ){
        uint32_t temp = 0;
        uint8_t padding = 0;
        
        // Process 4 base64 characters at a time
        for( int j = 0; j < 4 && (i + j) < usBase64Len; j++ ){
            char c = pcBase64Data[i + j];
            uint8_t val = 0;
            
            if( c >= 'A' && c <= 'Z' ) val = c - 'A';
            else if( c >= 'a' && c <= 'z' ) val = c - 'a' + 26;
            else if( c >= '0' && c <= '9' ) val = c - '0' + 52;
            else if( c == '+' ) val = 62;
            else if( c == '/' ) val = 63;
            else if( c == '=' ) { padding++; val = 0; }
            
            temp = (temp << 6) | val;
        }
        
        // Extract 3 bytes from the 24-bit temp value
        if( padding < 3 && usDecodedLen < usMaxOutputLen ){
            pucOutput[usDecodedLen++] = (temp >> 16) & 0xFF;
        }
        if( padding < 2 && usDecodedLen < usMaxOutputLen ){
            pucOutput[usDecodedLen++] = (temp >> 8) & 0xFF;
        }
        if( padding < 1 && usDecodedLen < usMaxOutputLen ){
            pucOutput[usDecodedLen++] = temp & 0xFF;
        }
    }
    
    return usDecodedLen;
}

// Tasks
void vSerialRxTask( void * pvParameters ){
    char inputBuffer[ SERIAL_MAX_BUFFER_IN_LENGTH ];
    int c;
    int bufferIndex = 0;

        // Initialize buffers
    memset( inputBuffer, 0, SERIAL_MAX_BUFFER_IN_LENGTH );
    memset( xSerialMessage.payload, 0, SERIAL_MAX_BUFFER_IN_LENGTH );
    xSerialMessage.len = 0;

    printf( "[SERIAL] USB Serial initialized\r\n" );
    printf( "[SERIAL] Ready for configuration commands\r\n" );

    for(;;){
        // Use longer timeout and longer delay for minimal CPU usage
        c = getchar_timeout_us( 1000 ); // 1ms timeout
        
        if( c != PICO_ERROR_TIMEOUT ){
            // Echo the character back (optional)
            // putchar( c );
            
            // Add character to buffer
            if( bufferIndex < SERIAL_MAX_BUFFER_IN_LENGTH - 1 ){
                inputBuffer[ bufferIndex ] = (char)c;
                bufferIndex++;
            }
            
            // Check for end of line (CR, LF, or CR+LF)
            if( c == '\n' || c == '\r' ){
                if( bufferIndex > 0 ){
                    // Null terminate the string
                    inputBuffer[ bufferIndex ] = '\0';
                    
                    // Skip empty lines or lines with only whitespace
                    char *trimmed = inputBuffer;
                    while( *trimmed == ' ' || *trimmed == '\t' ) trimmed++;
                    
                    if( strlen(trimmed) > 0 ){
                        // Copy to message structure
                        xSerialMessage.len = strlen(trimmed);
                        memcpy( ( void * )xSerialMessage.payload, ( void * )trimmed, xSerialMessage.len );
                        
                        // Ensure null termination
                        xSerialMessage.payload[xSerialMessage.len] = '\0';

                        // Process the received command
                        vProcessSerialCommand( &xSerialMessage );

                        // Reset for next message
                        xSerialMessage.len = 0;
                        memset( ( void * )xSerialMessage.payload, 0, SERIAL_MAX_BUFFER_IN_LENGTH );
                    }
                }
                
                // Reset buffer for next line
                bufferIndex = 0;
                memset( ( void * )inputBuffer, 0, SERIAL_MAX_BUFFER_IN_LENGTH );
            }
        } else {
            // No character received, sleep longer to minimize CPU usage
            vTaskDelay( pdMS_TO_TICKS( 100 ) );
        }
    }
}

// Command processing function
void vProcessSerialCommand( SerialMessage_t * pxMessage ){
    if( pxMessage == NULL || pxMessage->len == 0 ){
        printf( "[SERIAL] Error: Invalid message\r\n" );
        return;
    }
    
    char * pcCommand = ( char * )pxMessage->payload;

    // Ensure null termination
    if( pxMessage->len < SERIAL_MAX_BUFFER_IN_LENGTH ){
        pcCommand[ pxMessage->len ] = '\0';
    }
    
    // Remove trailing CR/LF
    char * pcNewline = strchr( pcCommand, '\n' );
    if( pcNewline ) *pcNewline = '\0';
    pcNewline = strchr( pcCommand, '\r' );
    if( pcNewline ) *pcNewline = '\0';

    SerialDMAMessage_t xConfigMessage;

    // Check for configuration flash command: FLASH_CONFIG|<base64_data>
    if( strncmp( pcCommand, "FLASH_CONFIG|", 13 ) == 0 ){
        // Extract base64 data after "FLASH_CONFIG|"
        char * pcBase64Data = pcCommand + 13;
        uint16_t usBase64Len = strlen( pcBase64Data );
        
        if( usBase64Len > 0 ){
            printf( "[SERIAL] Flashing configuration, base64 length: %d\r\n", usBase64Len );
            
            // Validate base64 length is reasonable
            if (usBase64Len > SERIAL_MAX_BUFFER_IN_LENGTH - 13) {
                printf( "[SERIAL] ERROR: Base64 data too large (%d bytes)\r\n", usBase64Len );
                return;
            }
            
            // Create DMA message and decode base64 data directly into payload
            xConfigMessage.len = base64_decode(pcBase64Data, usBase64Len, xConfigMessage.payload, SERIAL_MAX_DMA_BUFFER_IN_LENGTH);
            
            if (xConfigMessage.len == 0) {
                printf( "[SERIAL] ERROR: Base64 decode failed\r\n" );
                return;
            }
            
            printf( "[SERIAL] Decoded length: %d bytes\r\n", xConfigMessage.len );

            vPrintSystemStats();
            
            // Send to event queue for processing
            if( xInEventQueueSend( EVENT_ID_SERIAL_RX, &xConfigMessage ) ){
                printf( "[SERIAL] Configuration sent to event queue\r\n" );
            } else {
                printf( "[SERIAL] ERROR: Failed to send configuration to event queue\r\n" );
            }
        } else {
            printf( "[SERIAL] Error: No configuration data provided\r\n" );
        }
    }
    // Check for SET_QR command: SET_QR|<qrcode>
    else if( strncmp( pcCommand, "SET_QR|", 7 ) == 0 ){
        // Extract QR code after "SET_QR|"
        char * pcQRCode = pcCommand + 7;
        
        if( strlen( pcQRCode ) > 0 ){
            // Save QR code to flash - flash storage functions handle their own critical sections
            if( flash_storage_set_qr_code( pcQRCode ) ){
                printf( "[SERIAL] QR code saved successfully (length: %d)\r\n", strlen( pcQRCode ) );
            } else {
                printf( "[SERIAL] Error: Failed to save QR code (too long or flash error)\r\n" );
            }
        } else {
            printf( "[SERIAL] Error: No QR code provided\r\n" );
        }
    }
    // Check for REMOVE command: REMOVE|<nodeId>
    else if( strncmp( pcCommand, "REMOVE|", 7 ) == 0 ){
        // Extract node ID after "REMOVE|"
        char * pcNodeIdStr = pcCommand + 7;
        
        if( strlen( pcNodeIdStr ) > 0 ){
            // Convert string to integer
            uint8_t ucNodeId = (uint8_t)atoi( pcNodeIdStr );
            
            printf( "[SERIAL] Removing device with node ID: %d\r\n", ucNodeId );
            
            // Find the device in node QR mappings
            bool bFound = false;
            const char* pcQrCode = NULL;
            uint16_t usNetwork = 0;
            uint8_t ucChannel = 0;
            
            for (uint8_t i = 0; i < g_active_configuration.config.node_qr_mappings_count; i++) {
                if (g_active_configuration.config.node_qr_mappings[i].node_id == ucNodeId) {
                    pcQrCode = g_active_configuration.config.node_qr_mappings[i].qr_code;
                    
                    // Get current RF network and channel for RF devices
                    if (g_active_configuration.config.node_qr_mappings[i].type == BasestationConfig_NodeQRMapping_DeviceType_RF) {
                        if (g_active_configuration.config.has_rf_config) {
                            usNetwork = g_active_configuration.config.rf_config.network;
                            ucChannel = g_active_configuration.config.rf_config.channel;
                        }
                    }
                    
                    bFound = true;
                    break;
                }
            }
            
            if (bFound && pcQrCode != NULL) {
                // Set device as not provisioned
                ProvisioningState *pProvisioningState = get_provisioned_device_state_from_node_id(ucNodeId);
                if (pProvisioningState) {
                    pProvisioningState->is_provisioned = false;
                    pProvisioningState->last_seen_time = get_time_in_ms();
                    pProvisioningState->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
                    printf( "[SERIAL] Device marked as not provisioned\r\n" );
                }
                
                // Try to remove RF device if it's an RF device
                for (uint8_t i = 0; i < g_active_configuration.config.node_qr_mappings_count; i++) {
                    if (g_active_configuration.config.node_qr_mappings[i].node_id == ucNodeId && 
                        g_active_configuration.config.node_qr_mappings[i].type == BasestationConfig_NodeQRMapping_DeviceType_RF) {
                        
                        printf( "[SERIAL] Attempting to remove RF device with QR code: %s\r\n", pcQrCode );
                        bool bRemoved = vRFRemoveDevice(pcQrCode, ucNodeId, usNetwork, ucChannel);
                        
                        if (bRemoved) {
                            printf( "[SERIAL] RF device removal successful\r\n" );
                        } else {
                            printf( "[SERIAL] RF device removal failed or timed out\r\n" );
                        }
                        break;
                    }
                }
                
                // Save configuration to flash
                // flash_storage_set_configuration(&g_active_configuration);
                printf( "[SERIAL] Configuration saved to flash\r\n" );
                
            } else {
                printf( "[SERIAL] Error: Device with node ID %d not found in configuration\r\n", ucNodeId );
            }
        } else {
            printf( "[SERIAL] Error: No node ID provided\r\n" );
        }
    }
    // Check for GET_QR command
    else if( strcmp( pcCommand, "GET_QR" ) == 0 || strcmp( pcCommand, "get_qr" ) == 0 ){
        char qrBuffer[FLASH_QR_CODE_MAX_LENGTH];
        bool hasQR = flash_storage_get_qr_code( qrBuffer, sizeof( qrBuffer ) );
        
        // Get version from active configuration, fallback to firmware version if not set
        const char* version = FIRMWARE_VERSION_STRING; // Default fallback
        
        if (g_active_configuration.config.version && strlen(g_active_configuration.config.version) > 0) {
            version = g_active_configuration.config.version;
        }
        
        // Return simple text response with QR code and config version separated by pipe
        printf( "%s|%s\r\n", 
                hasQR ? qrBuffer : "", 
                version );
    }
    // Check for PRINT_ACTIVE_CONFIGURATION command
    else if( strcmp( pcCommand, "PRINT_ACTIVE_CONFIGURATION" ) == 0 || strcmp( pcCommand, "print_active_configuration" ) == 0 ){
        pretty_print_active_configuration();
    }
    // Check for STATS command
    else if( strcmp( pcCommand, "STATS" ) == 0 || strcmp( pcCommand, "stats" ) == 0 ){
        vPrintSystemStats();
    }
    // Check for STATS_JSON command
    else if( strcmp( pcCommand, "STATS_JSON" ) == 0 || strcmp( pcCommand, "stats_json" ) == 0 ){
        vPrintSystemStatsJSON();
    }
    // Check for help command
    else if( strcmp( pcCommand, "help" ) == 0 || strcmp( pcCommand, "HELP" ) == 0 ){
        printf( "[SERIAL] Available commands:\r\n" );
        printf( "  FLASH_CONFIG|<base64_data> - Flash new configuration\r\n" );
        printf( "  SET_QR|<qrcode>     - Set QR code for base station\r\n" );
        printf( "  REMOVE|<nodeId>     - Remove device with specified node ID\r\n" );
        printf( "  GET_QR              - Get stored QR code\r\n" );
        printf( "  PRINT_ACTIVE_CONFIGURATION - Print active configuration\r\n" );
        printf( "  STATS               - Show CPU and memory usage statistics\r\n" );
        printf( "  STATS_JSON          - Show CPU and memory usage statistics in JSON format\r\n" );
        printf( "  SD_WRITE_TEST|<sector> - Write test data to SD card\r\n" );
        printf( "  help                - Show this help message\r\n" );
        printf( "  status              - Show system status\r\n" );
        printf( "  reboot              - Restart the Pico\r\n" );
        printf( "  ENTER_BOOTLOADER    - Enter bootloader mode for firmware update\r\n" );
    }
    // Check for status command
    else if( strcmp( pcCommand, "status" ) == 0 || strcmp( pcCommand, "STATUS" ) == 0 ){
        pretty_print_active_configuration();
    }
    // Check for reboot command
    else if( strcmp( pcCommand, "reboot" ) == 0 || strcmp( pcCommand, "REBOOT" ) == 0 ){
        printf( "[SERIAL] Rebooting Pico...\r\n" );
        sleep_ms(100); // Give time for the message to be sent
        watchdog_reboot(0, 0, 0);
    }
    // Check for SD write test command
    else if( strncmp( pcCommand, "SD_WRITE_TEST", 13 ) == 0 ){
        // Extract sector number if provided: SD_WRITE_TEST|<sector_number>
        uint32_t sector_number = 0;
        if( strchr( pcCommand, '|' ) != NULL ){
            char * pcSectorStr = strchr( pcCommand, '|' ) + 1;
            sector_number = (uint32_t)atoi( pcSectorStr );
        }
        
        printf( "[SERIAL] Writing test data to SD card, sector: %lu\r\n", sector_number );
        
        // Create test data
        uint8_t test_data[512]; // SD_SECTOR_SIZE
        snprintf((char*)test_data, sizeof(test_data), 
                "Test data from serial command - Sector: %lu, Timestamp: %lu\r\n", 
                sector_number, xTaskGetTickCount());
        
        // Send write request to SD task
        if( sd_send_write_request("serial_test.txt", test_data, sector_number, strlen((char*)test_data)) ){
            printf( "[SERIAL] Test write request sent to SD task successfully\r\n" );
        } else {
            printf( "[SERIAL] ERROR: Failed to send test write request to SD task\r\n" );
        }
    }
    // Check for enter bootloader command
    else if( strcmp( pcCommand, "enter_bootloader" ) == 0 || strcmp( pcCommand, "ENTER_BOOTLOADER" ) == 0 ){
        printf( "[SERIAL] Entering bootloader mode...\r\n" );
        sleep_ms(100); // Give time for the message to be sent
        
        // Set magic value in watchdog scratch register to signal bootloader mode
        watchdog_hw->scratch[0] = 1;
        watchdog_reboot(0, 0, 10);
        
        // This should never be reached
        while (1) {
            continue;
        }
    }
    // Unknown command
    else {
        printf( "[SERIAL] Unknown command: %s\r\n", pcCommand );
        printf( "[SERIAL] Type 'help' for available commands\r\n" );
    }
}

#ifdef __cplusplus
}
#endif