#include "dmx_task.h"

DMXParams_t dmx_params = DMX_PARAMS_DEFAULT();

SC16IS750 uart_spi(&dmx_params.spi_config);

TaskHandle_t xDmxTaskHandle;

void send_break() {
  uint8_t lcr = uart_spi.ReadRegister(SC16IS750_REG_LCR);
  uart_spi.WriteRegister(SC16IS750_REG_LCR, lcr | 0x40);       // BREAK on
  sleep_us(100); // 100 µs ≥88 µs
  uart_spi.WriteRegister(SC16IS750_REG_LCR, lcr & ~0x40);      // BREAK off
  sleep_us(10); // 10 µs ≥8 µs
}

void vDmxTask( void * pvParameters ){
    printf( "Dmx task created\r\n" );
    
    DmxUniverse dmxUniverse;
    uint8_t *data = dmxUniverse.getUniverse();

    uart_spi.begin(250000); // DMX has baud rate of 250000 b/sec

    for( ;; ){
        uint32_t now = time_us_32();

        dmxUniverse.buildDmxDataFrame();

        send_break();
        for (size_t i = 0; i <= 512; ++i) {
            uart_spi.write(data[i]);
        }

        uint32_t delta = time_us_32();
        delta = (delta < now) ? (delta + (0xFFFFFFFF - now)) : (delta - now);
        int32_t ms = pdMS_TO_TICKS( 1000 / DMX_REFRESH_RATE - (delta / 1000) ); 
        ms = (ms < 0) ? 0 : ms;

        vTaskDelay( ms); 
    }
}