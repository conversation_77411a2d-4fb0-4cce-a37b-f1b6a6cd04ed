#ifndef LIGHT_CONTROL_TASK_H
#define LIGHT_CONTROL_TASK_H

#include "FreeRTOS.h"
extern "C"
{
// C standard libraries
#include <stdio.h>

// pico sdk libraries
#include "FreeRTOS.h"
#include "task.h"
#include "queue.h"

// protobuf libraries
#include "active_configuration.h"
#include "message_handler.h"

extern TaskHandle_t xLightControlTaskHandle;

void vLightControlTask(void *pvParameters);
}
#endif // LIGHT_CONTROL_TASK_H