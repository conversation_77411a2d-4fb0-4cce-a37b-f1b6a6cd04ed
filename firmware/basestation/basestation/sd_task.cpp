#include "sd_task.h"
#include <string.h>

// Global queue handle for SD write operations
QueueHandle_t xSDWriteQueue = NULL;

// Global task handle for SD task
TaskHandle_t xSDTaskHandle = NULL;

// FatFS file system object
static FATFS fs;
static bool sd_card_mounted = false;

/**
 * Initialize SD card and FatFS
 * @return true if initialization successful, false otherwise
 */
bool sd_card_init(void) {
    printf("SD: Initializing SD card\n");

    // Configure SPI for SD card using the new macro
    SDParams_t sd_params = SD_PARAMS_DEFAULT();
    pico_fatfs_spi_config_t config = {
        .spi_inst = sd_params.spi_config.spi_inst,
        .clk_slow = CLK_SLOW_DEFAULT,
        .clk_fast = CLK_FAST_DEFAULT,
        .pin_miso = sd_params.spi_config.miso_pin,
        .pin_cs = sd_params.spi_config.cs_pin,
        .pin_sck = sd_params.spi_config.sck_pin,
        .pin_mosi = sd_params.spi_config.mosi_pin,
        .pullup = true
    };

    // Set the configuration
    if (!pico_fatfs_set_config(&config)) {
        printf("SD: Failed to set SPI configuration\n");
        return false;
    }

    FRESULT result;
    while (true) {
        result = f_mount(&fs, "", 1);
        if (result == FR_OK) { break; }
        printf("SD: Mount error %d - retrying...\n", result);
        pico_fatfs_reboot_spi();
        vTaskDelay(pdMS_TO_TICKS(1000)); // 1 second delay between attempts
    }

    printf("SD: SPI configured and initialized successfully\n");

    // Mount the file system
    result = f_mount(&fs, "", 1);
    if (result != FR_OK) {
        printf("SD: Failed to mount filesystem, error: %d\n", result);
        return false;
    }

    printf("SD: File system mounted successfully\n");

    sd_card_mounted = true;
    printf("SD: Card initialized successfully\n");
    return true;
}

/**
 * Write a sector to a file on the SD card
 * @param filename Name of the file to write to
 * @param data Pointer to data buffer
 * @param sector_number Sector number to write
 * @param data_length Length of data to write (max SD_SECTOR_SIZE)
 * @return FRESULT from FatFS
 */
FRESULT sd_write_sector(const char* filename, const uint8_t* data, uint32_t sector_number, uint32_t data_length) {
    if (!sd_card_mounted) {
        printf("SD: Card not mounted\n");
        return FR_NOT_READY;
    }

    if (data_length > SD_SECTOR_SIZE) {
        printf("SD: Data length exceeds sector size\n");
        return FR_INVALID_PARAMETER;
    }

    FIL file;
    FRESULT result;

    // Open file for writing (create if doesn't exist)
    result = f_open(&file, filename, FA_WRITE | FA_CREATE_ALWAYS);
    if (result != FR_OK) {
        printf("SD: Failed to open file %s, error: %d\n", filename, result);
        return result;
    }

    // Seek to the sector position
    FSIZE_t seek_pos = (FSIZE_t)sector_number * SD_SECTOR_SIZE;
    result = f_lseek(&file, seek_pos);
    if (result != FR_OK) {
        printf("SD: Failed to seek to position %lu, error: %d\n", seek_pos, result);
        f_close(&file);
        return result;
    }

    // Write the data
    UINT bytes_written;
    result = f_write(&file, data, data_length, &bytes_written);
    if (result != FR_OK) {
        printf("SD: Failed to write data, error: %d\n", result);
        f_close(&file);
        return result;
    }

    // Sync the file to ensure data is written to disk
    result = f_sync(&file);
    if (result != FR_OK) {
        printf("SD: Failed to sync file, error: %d\n", result);
        f_close(&file);
        return result;
    }

    // Close the file
    f_close(&file);

    printf("SD: Wrote %u bytes to %s at sector %lu\n", bytes_written, filename, sector_number);
    return FR_OK;
}

/**
 * Send a write request to the SD task queue
 * @param filename Name of the file to write to
 * @param data Pointer to data buffer
 * @param sector_number Sector number to write
 * @param data_length Length of data to write
 * @return true if request sent successfully, false otherwise
 */
bool sd_send_write_request(const char* filename, const uint8_t* data, uint32_t sector_number, uint32_t data_length) {
    if (xSDWriteQueue == NULL) {
        printf("SD: Write queue not initialized\n");
        return false;
    }

    sd_write_request_t request;
    
    // Copy filename
    strncpy(request.filename, filename, SD_MAX_FILENAME_LENGTH - 1);
    request.filename[SD_MAX_FILENAME_LENGTH - 1] = '\0';
    
    // Copy data
    if (data_length > SD_SECTOR_SIZE) {
        data_length = SD_SECTOR_SIZE;
    }
    memcpy(request.data, data, data_length);
    
    request.sector_number = sector_number;
    request.data_length = data_length;
    request.is_write_request = true;

    // Send to queue
    BaseType_t result = xQueueSend(xSDWriteQueue, &request, pdMS_TO_TICKS(100));
    if (result != pdPASS) {
        printf("SD: Failed to send write request to queue\n");
        return false;
    }

    return true;
}

/**
 * Test function to demonstrate SD write functionality
 * This function creates a test file and writes some sample data
 */
void sd_test_write(void) {
    static uint32_t test_counter = 0;
    uint8_t test_data[SD_SECTOR_SIZE];
    
    // Create test data
    snprintf((char*)test_data, SD_SECTOR_SIZE, "Test data sector %lu - Timestamp: %lu\r\n", 
             test_counter, xTaskGetTickCount());
    
    // Send write request to queue
    if (sd_send_write_request("test.txt", test_data, test_counter, strlen((char*)test_data))) {
        printf("SD: Test write request sent for sector %lu\n", test_counter);
        test_counter++;
    } else {
        printf("SD: Failed to send test write request\n");
    }
}

/**
 * Main SD task that processes write requests from the queue
 * @param pvParameters Task parameters (unused)
 */
void sd_task(void *pvParameters) {
    printf("SD: Task started\n");

    // Initialize SD card
    if (!sd_card_init()) {
        printf("SD: Failed to initialize SD card\n");
        vTaskDelete(NULL);
        return;
    }

    printf("SD: Card initialized successfully\n");

    // Create the write queue
    xSDWriteQueue = xQueueCreate(SD_WRITE_QUEUE_LENGTH, sizeof(sd_write_request_t));
    if (xSDWriteQueue == NULL) {
        printf("SD: Failed to create write queue\n");
        vTaskDelete(NULL);
        return;
    }

    printf("SD: Write queue created successfully\n");

    sd_write_request_t request;
    uint32_t last_test_time = 0;

    // Main task loop
    while (true) {
        // Wait for write requests from the queue
        if (xQueueReceive(xSDWriteQueue, &request, pdMS_TO_TICKS(100)) == pdPASS) {
            if (request.is_write_request) {
                // Process the write request
                FRESULT result = sd_write_sector(
                    request.filename,
                    request.data,
                    request.sector_number,
                    request.data_length
                );

                if (result != FR_OK) {
                    printf("SD: Write operation failed with error: %d\n", result);
                }
            }
        }

        // Small delay to prevent tight loop
        vTaskDelay(pdMS_TO_TICKS(10));
    }
}