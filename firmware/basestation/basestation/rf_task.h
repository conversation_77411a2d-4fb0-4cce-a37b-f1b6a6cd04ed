#ifndef __RF_TASK_H__
#define __RF_TASK_H__

#include "message_handler.h"
#include "dmx_task.h"

#ifdef __cplusplus
extern "C" {
#endif

// C standard libraries
#include <stdio.h>
#include <string.h>

// pico sdk libraries
#include "FreeRTOS.h"
#include "task.h"
#include "queue.h"

#include "hardware/gpio.h"
#include "hardware/irq.h"

// user libraries
#include "types.h"
#include "pinout.h"
#include "flash_storage.h"
#include "active_configuration.h"

#include "rf4463/rf4463pro.h"
#include "rf4463/RFPacketdefs.h"
#include "peripherals/irq_handler.h"

// global definitions
extern TaskHandle_t xRfRxTaskHandle;
extern TaskHandle_t xRFRxQueueTaskHandle;
extern SemaphoreHandle_t xRfDeviceOperationMutex;

// local definitions/enums
// >> RF4463 behaviour definitions
    #define DEBUG_RF_PACKET_RX
    #define DEBUG_RF_PACKET_TX
// >> END OF DEFINITIONS

#define RF_DEFAULT_DEVICE_NODE      ( unsigned char )2
#define RF_DEFAULT_BASETATION_NODE  ( unsigned char )1
#define RF_NODE_FACTORY             ( unsigned char )254
#define RF_NODE_BROADCAST           ( unsigned char )255

#define DEVICE_QR_FIELD_LENGTH          12
#define DEFAULT_DESTINATION             2

#define ADOPTION_DEFAULT_CHANNEL 15
#define ADOPTION_DEFAULT_NETWORK 15

#define QUEUE_TICKS_DELAY 20

typedef struct{
    uint8_t message[ RF4463_MAX_BUFFER_LENGTH ];
    uint8_t len;
    int rssi;
} RFMessage_t;

enum DeviceOperationMode{
    DEVICE_OP_MODE_NOT_DEFINED = 0,
    DEVICE_OP_MODE_SENSOR_PRIMARY,
    DEVICE_OP_MODE_SENSOR_SECONDARY,
    DEVICE_OP_MODE_SENSOR_AUXILIAR,
    DEVICE_OP_MODE_SERVICE_PAD_INTERIOR,
    DEVICE_OP_MODE_SERVICE_PAD_EXTERIOR,
    DEVICE_OP_MODE_SERVICE_PAD_DOORBELL,
    DEVICE_OP_MODE_SHADES_MOTOR,
    DEVICE_OP_MODE_SHADES_CONTROL_POINT,
    DEVICE_OP_MODE_FAN_CONTROLLER_THREE_SPEED,
    DEVICE_OP_MODE_FAN_CONTROLLER_ON_OFF,
    DEVICE_OP_MODE_DIMMER_PAD,
    // ------ placeholder, do not delete --------
    // DEVICE_OP_MODE_0_10_CONTROLLER_DIMMER,
    // DEVICE_OP_MODE_0_10_CONTROLLER_FAN_CONTROL,
    DEVICE_OP_MODE_TOTAL
};

#define RF_RX_QUEUE_LENGTH 30 // Max number of items in each individual queue
#define RF_RX_MSG_ITEM_SIZE sizeof( RFMessage_t ) // Size of each item

#define REMOTE_DEFAULT          2

// function prototyping
void vRfRxTask( void * pvParameters );
void vRFRxQueueTask( void * pvParameters );
uint8_t ucRfInit();
void vRfRxRestore();
void vRfRxRestoreNoDelay();
void vRfSetChannelAndNetwork(uint8_t ucChannel, uint16_t usNetwork);
void vRfRxMessage();
bool vRFAdoptDevice(const char * pcQrCode, uint8_t ucNodeId, enum DeviceOperationMode eOpMode);
bool vRFRemoveDevice(const char * pcQrCode, uint8_t ucNodeId, uint16_t usNetwork, uint8_t ucChannel);
bool vRfSendAndWaitForResponse(uint8_t * pcBuffer, uint8_t ucLength, uint8_t ucDestination, uint8_t ucTable, uint8_t ucCommand, uint32_t ulTimeoutMs, RFMessage_t * pResponse, uint8_t ucMaxRetries, uint8_t ucIdleNetwork, uint8_t ucIdleChannel);
uint8_t vRFPrepareKlevernessMessage( const uint8_t * pcBuffer, uint8_t ucLength, uint8_t ucTable, uint8_t ucCommand, uint8_t ucDestination, uint8_t* pcOutBuffer );
void vRfTxMessage( uint8_t * pcOutMessage, uint8_t ucLength );

static void send_rf_message(const uint8_t *data, uint32_t len);
void vRfUpdateChannelAndNetworkFromConfig();

#ifdef __cplusplus
}
#endif

#endif