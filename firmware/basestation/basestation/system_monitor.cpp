#include "system_monitor.h"
#include "active_configuration.h"
#include "hardware/timer.h"
#include <stdio.h>
#include <string.h>

// C standard libraries
#include "FreeRTOS.h"
#include "task.h"

/**********************************************************************************************
 * Runtime Stats Timer for CPU Usage Monitoring
**********************************************************************************************/
extern "C" {

void vConfigureTimerForRunTimeStats( void ) {
    // Use the hardware timer for runtime stats - no additional setup needed
    // The RP2350 hardware timer runs at 1MHz and is already initialized
}

uint32_t ulGetRunTimeCounterValue( void ) {
    // Return current time in microseconds for high resolution CPU usage tracking
    return (uint32_t)time_us_32();
}

}

/**********************************************************************************************
 * System Statistics Monitoring
**********************************************************************************************/
void vPrintSystemStats( void ){
    // Note: Using FreeRTOS Heap3 which uses malloc/free, so standard heap functions are not available
    // Instead we'll show task and stack information
    
    printf( "\r\n=== System RAM Usage ===\r\n" );
    
    // Get number of tasks
    UBaseType_t uxTaskCount = uxTaskGetNumberOfTasks();
    printf( "Active Tasks: %lu\r\n", (unsigned long)uxTaskCount );
    
    // Calculate total configured heap size from FreeRTOSConfig.h
    // Note: With Heap3, this represents the system heap, not FreeRTOS managed heap
    size_t xConfiguredHeapSize = configTOTAL_HEAP_SIZE;
    printf( "Configured System Heap: %zu bytes (%.1f KB)\r\n", 
            xConfiguredHeapSize, (float)xConfiguredHeapSize / 1024.0f );
    
    // Show all tasks (both cores) with CPU usage information
    printf( "\nTask Information:\r\n" );
    printf( "%-20s %-6s %-6s %-8s %-12s %s\r\n", "Task Name", "Core", "Prio", "CPU%%", "Stack Free", "Free Bytes" );
    printf( "%-20s %-6s %-6s %-8s %-12s %s\r\n", "--------------------", "------", "------", "--------", "------------", "-----------" );
    
    // Allocate buffer for task status array
    static TaskStatus_t pxTaskStatusArray[16]; // Adjust size as needed
    UBaseType_t uxArraySize = sizeof(pxTaskStatusArray) / sizeof(TaskStatus_t);
    uint32_t ulTotalRunTime;
    
    // Get task information for all tasks
    UBaseType_t uxActualTasks = uxTaskGetSystemState( pxTaskStatusArray, uxArraySize, &ulTotalRunTime );
    
    // Calculate total stack memory usage
    uint32_t ulTotalStackAllocated = 0;
    uint32_t ulTotalStackUsed = 0;
    
    for( UBaseType_t i = 0; i < uxActualTasks; i++ ){
        TaskStatus_t *pxTask = &pxTaskStatusArray[i];
        
        // Get stack high water mark
        UBaseType_t uxHighWaterMark = uxTaskGetStackHighWaterMark( pxTask->xHandle );
        
        // Get actual stack size using configRECORD_STACK_HIGH_ADDRESS
        // Stack grows downward on ARM, so size = pxEndOfStack - pxStackBase
        uint32_t ulStackSizeWords = 0;
        if( pxTask->pxEndOfStack && pxTask->pxStackBase ){
            // Calculate actual stack size in words
            ulStackSizeWords = (uint32_t)(pxTask->pxEndOfStack - pxTask->pxStackBase);
        } else {
            // Fallback to estimate if pxEndOfStack is not available
            ulStackSizeWords = 1024; // Default estimate
        }
        
        // Calculate stack used correctly - high water mark is minimum free space ever reached
        // So: Stack Used = Stack Size - High Water Mark (but avoid underflow)
        uint32_t ulStackUsedWords = 0;
        if( ulStackSizeWords > uxHighWaterMark ){
            ulStackUsedWords = ulStackSizeWords - uxHighWaterMark;
        }
        
        // Convert to bytes (each word is 4 bytes on ARM)
        ulTotalStackAllocated += ulStackSizeWords * 4;
        ulTotalStackUsed += ulStackUsedWords * 4;
        
        // Get core affinity (for SMP systems)
        UBaseType_t uxCoreAffinity = 0;
        #if configUSE_CORE_AFFINITY
        uxCoreAffinity = vTaskCoreAffinityGet( pxTask->xHandle );
        char coreStr[8];
        if( uxCoreAffinity == ( (1U << configNUMBER_OF_CORES) - 1 ) ){
            strcpy( coreStr, "Both" );
        } else if( uxCoreAffinity & 0x01 ){
            strcpy( coreStr, "0" );
        } else if( uxCoreAffinity & 0x02 ){
            strcpy( coreStr, "1" );
        } else {
            strcpy( coreStr, "?" );
        }
        #else
        strcpy( coreStr, "N/A" );
        #endif
        
        // Calculate CPU usage percentage for this task
        float fCPUPercent = 0.0f;
        if( ulTotalRunTime > 0 ){
            fCPUPercent = ((float)pxTask->ulRunTimeCounter / (float)ulTotalRunTime) * 100.0f;
        }
        
        // Add a warning if stack size couldn't be determined
        char sizeNote[16] = "";
        if( !pxTask->pxEndOfStack || !pxTask->pxStackBase ){
            strcpy( sizeNote, "*" ); // Mark tasks where actual size is unavailable
        }
        
        // Calculate free bytes and KB
        uint32_t ulFreeBytes = uxHighWaterMark * 4; // Convert words to bytes
        float fFreeKB = (float)ulFreeBytes / 1024.0f;
        
        printf( "%-20s %-6s %-6lu %-8.1f %-12lu %luB (%.1fKB)%s\r\n", 
                pxTask->pcTaskName,
                coreStr,
                (unsigned long)pxTask->uxCurrentPriority,
                fCPUPercent,
                (unsigned long)uxHighWaterMark,
                (unsigned long)ulFreeBytes,
                fFreeKB,
                sizeNote );
    }
    
    // Print memory usage summary
    printf( "\nMemory Usage Summary:\r\n" );
    printf( "Total Stack Allocated: %lu bytes (%.1f KB)\r\n", 
            (unsigned long)ulTotalStackAllocated, 
            (float)ulTotalStackAllocated / 1024.0f );
    printf( "Total Stack Used:      %lu bytes (%.1f KB)\r\n", 
            (unsigned long)ulTotalStackUsed, 
            (float)ulTotalStackUsed / 1024.0f );
    printf( "Stack Utilization:     %.1f%%\r\n", 
            ulTotalStackAllocated > 0 ? ((float)ulTotalStackUsed / (float)ulTotalStackAllocated) * 100.0f : 0.0f );
    
    // Calculate total CPU usage (should be close to 100% for active system)
    float fTotalCPUPercent = 0.0f;
    for( UBaseType_t i = 0; i < uxActualTasks; i++ ){
        if( ulTotalRunTime > 0 ){
            fTotalCPUPercent += ((float)pxTaskStatusArray[i].ulRunTimeCounter / (float)ulTotalRunTime) * 100.0f;
        }
    }
    
    printf( "\nCPU Usage Summary:\r\n" );
    printf( "Total CPU Accounted:   %.1f%%\r\n", fTotalCPUPercent );
    printf( "System Uptime:         %.2f seconds\r\n", (float)ulTotalRunTime / 1000000.0f );
    
    printf( "\nNote: Stack free shows remaining unused stack space\r\n" );
    printf( "Lower values indicate more stack usage (closer to overflow)\r\n" );
    printf( "Core affinity: 0=Core0, 1=Core1, Both=Either core\r\n" );
    printf( "* = Actual stack size unavailable\r\n" );
    printf( "========================\r\n\r\n" );
}

/**********************************************************************************************
 * JSON System Statistics Output
**********************************************************************************************/
void vPrintSystemStatsJSON( void ){
    // Get number of tasks
    UBaseType_t uxTaskCount = uxTaskGetNumberOfTasks();
    
    // Calculate total configured heap size from FreeRTOSConfig.h
    size_t xConfiguredHeapSize = configTOTAL_HEAP_SIZE;
    
    // Allocate buffer for task status array
    static TaskStatus_t pxTaskStatusArray[16]; // Adjust size as needed
    UBaseType_t uxArraySize = sizeof(pxTaskStatusArray) / sizeof(TaskStatus_t);
    uint32_t ulTotalRunTime;
    
    // Get task information for all tasks
    UBaseType_t uxActualTasks = uxTaskGetSystemState( pxTaskStatusArray, uxArraySize, &ulTotalRunTime );
    
    // Calculate total stack memory usage
    uint32_t ulTotalStackAllocated = 0;
    uint32_t ulTotalStackUsed = 0;
    
    // Start JSON output (compact format without newlines)
    printf( "{\"activeTasks\":%lu,", (unsigned long)uxTaskCount );
    printf( "\"configuredHeap\":{\"bytes\":%zu,\"kb\":%.1f},", 
            xConfiguredHeapSize, (float)xConfiguredHeapSize / 1024.0f );
    printf( "\"tasks\":[" );
    
    for( UBaseType_t i = 0; i < uxActualTasks; i++ ){
        TaskStatus_t *pxTask = &pxTaskStatusArray[i];
        
        // Get stack high water mark
        UBaseType_t uxHighWaterMark = uxTaskGetStackHighWaterMark( pxTask->xHandle );
        
        // Get actual stack size using configRECORD_STACK_HIGH_ADDRESS
        // Stack grows downward on ARM, so size = pxEndOfStack - pxStackBase
        uint32_t ulStackSizeWords = 0;
        if( pxTask->pxEndOfStack && pxTask->pxStackBase ){
            // Calculate actual stack size in words
            ulStackSizeWords = (uint32_t)(pxTask->pxEndOfStack - pxTask->pxStackBase);
        } else {
            // Fallback to estimate if pxEndOfStack is not available
            ulStackSizeWords = 1024; // Default estimate
        }
        
        // Calculate stack used correctly - high water mark is minimum free space ever reached
        // So: Stack Used = Stack Size - High Water Mark (but avoid underflow)
        uint32_t ulStackUsedWords = 0;
        if( ulStackSizeWords > uxHighWaterMark ){
            ulStackUsedWords = ulStackSizeWords - uxHighWaterMark;
        }
        
        // Convert to bytes (each word is 4 bytes on ARM)
        ulTotalStackAllocated += ulStackSizeWords * 4;
        ulTotalStackUsed += ulStackUsedWords * 4;
        
        // Get core affinity
        UBaseType_t uxCoreAffinity = 0;
        const char* coreStr = "0";
        #if configUSE_CORE_AFFINITY
        uxCoreAffinity = vTaskCoreAffinityGet( pxTask->xHandle );
        if( uxCoreAffinity == ( (1U << configNUMBER_OF_CORES) - 1 ) ){
            coreStr = "Both";
        } else if( uxCoreAffinity & 0x01 ){
            coreStr = "0";
        } else if( uxCoreAffinity & 0x02 ){
            coreStr = "1";
        } else {
            coreStr = "?";
        }
        #endif
        
        // Calculate CPU usage percentage for this task
        float fCPUPercent = 0.0f;
        if( ulTotalRunTime > 0 ){
            fCPUPercent = ((float)pxTask->ulRunTimeCounter / (float)ulTotalRunTime) * 100.0f;
        }
        
        // Check if stack size couldn't be determined
        bool hasEstimateWarning = ( !pxTask->pxEndOfStack || !pxTask->pxStackBase );
        
        // Calculate free bytes and KB
        uint32_t ulFreeBytes = uxHighWaterMark * 4;
        float fFreeKB = (float)ulFreeBytes / 1024.0f;
        
        printf( "{\"name\":\"%s\",\"core\":\"%s\",\"priority\":%lu,\"cpuPercent\":%.1f,\"stackFreeWords\":%lu,\"freeBytes\":%lu,\"freeKb\":%.1f,\"hasEstimateWarning\":%s}%s", 
                pxTask->pcTaskName,
                coreStr,
                (unsigned long)pxTask->uxCurrentPriority,
                fCPUPercent,
                (unsigned long)uxHighWaterMark,
                (unsigned long)ulFreeBytes,
                fFreeKB,
                hasEstimateWarning ? "true" : "false",
                (i < uxActualTasks - 1) ? "," : "" );
    }
    
    printf( "],\"memoryUsage\":{\"totalStackAllocated\":{\"bytes\":%lu,\"kb\":%.1f},\"totalStackUsed\":{\"bytes\":%lu,\"kb\":%.1f},\"stackUtilization\":%.1f},", 
            (unsigned long)ulTotalStackAllocated,
            (float)ulTotalStackAllocated / 1024.0f,
            (unsigned long)ulTotalStackUsed,
            (float)ulTotalStackUsed / 1024.0f,
            ulTotalStackAllocated > 0 ? ((float)ulTotalStackUsed / (float)ulTotalStackAllocated) * 100.0f : 0.0f );
    
    // Calculate total CPU usage
    float fTotalCPUPercent = 0.0f;
    for( UBaseType_t i = 0; i < uxActualTasks; i++ ){
        if( ulTotalRunTime > 0 ){
            fTotalCPUPercent += ((float)pxTaskStatusArray[i].ulRunTimeCounter / (float)ulTotalRunTime) * 100.0f;
        }
    }
    
    printf( "\"cpuUsage\":{\"totalCpuAccounted\":%.1f,\"systemUptime\":%.2f}}\r\n", 
            fTotalCPUPercent, 
            (float)ulTotalRunTime / 1000000.0f );
}