#ifndef __SERIAL_TASK_H__
#define __SERIAL_TASK_H__

// Include event_task.h outside extern "C" since it contains C++ headers
#include "event_task.h"
#include "sd_task.h"

#ifdef __cplusplus
    extern "C" {
#endif

// C standard libraries
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>

// pico sdk libraries
#include "FreeRTOS.h"
#include "task.h"

// user libraries
#include "types.h"
#include "pinout.h"
#include "active_configuration.h"

// local definitions/enums
#define SERIAL_MAX_BUFFER_OUT_LENGTH      ( unsigned int )256
#define SERIAL_MAX_BUFFER_IN_LENGTH       ( unsigned int )256

#define SERIAL_MAX_DMA_BUFFER_IN_LENGTH   INTERNAL_BUFFER_MAX_LENGTH
#define SERIAL_MAX_DMA_BUFFER_OUT_LENGTH  ( unsigned int )512

#define ENABLE_DEBUG_SERIAL_RX

// Serial message structure for configuration data
typedef struct{
    uint16_t len;
    uint8_t payload[ SERIAL_MAX_BUFFER_IN_LENGTH ];
} SerialMessage_t;

// Serial message structure for large configuration data (DMA)
typedef struct{
    uint16_t len;
    uint8_t payload[ SERIAL_MAX_DMA_BUFFER_IN_LENGTH ];
} SerialDMAMessage_t;

// global definitions
extern TaskHandle_t xSerialRxTaskHandle;

// function prototyping
void vSerialRxTask( void * pvParameters );

void vProcessSerialCommand( SerialMessage_t * pxMessage );

#ifdef __cplusplus
    }
#endif

#endif 