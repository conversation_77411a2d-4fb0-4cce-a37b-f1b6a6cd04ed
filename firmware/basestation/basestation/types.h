#ifndef __TYPES_H__
#define __TYPES_H__

#include "hardware/pio.h"
#include "hardware/spi.h"
#include "hardware/gpio.h"
#include "hardware/i2c.h"
#include "hardware/uart.h"
#include "hardware/dma.h"

#include "can2040.h"
#include "can_varlen.h"

#include "peripherals/spi.h"
#include "rf4463/rf4463pro.h"

// #define INTERNAL_BUFFER_MAX_LENGTH 65536U
// #define INTERNAL_BUFFER_MAX_LENGTH 32768U
#define INTERNAL_BUFFER_MAX_LENGTH 16384U
// #define INTERNAL_BUFFER_MAX_LENGTH 4096U

enum{
    CHANNEL_0 = 0,
    CHANNEL_1,
    CHANNEL_2,
    CHANNEL_LIST
};

typedef enum {
    GPIO0_PIN = 0,
    GP<PERSON>1_<PERSON>IN,
    GPIO2_PIN,
    GPIO3_<PERSON>IN,
    GP<PERSON>4_<PERSON><PERSON>,
    GP<PERSON><PERSON>_<PERSON><PERSON>,
    GP<PERSON><PERSON>_PIN,
    <PERSON><PERSON>7_<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>_<PERSON>IN,
    GP<PERSON><PERSON>_<PERSON><PERSON>,
    GP<PERSON><PERSON>_<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>_PIN,
    GPIO15_P<PERSON>,
    GPIO16_PIN,
    GPIO17_PIN,
    GPIO18_PIN,
    GPIO19_PIN,
    GPIO20_PIN,
    GPIO21_PIN,
    GPIO22_PIN,
    GPIO23_PIN,
    GPIO24_PIN,
    GPIO25_PIN,
    GPIO26_PIN,
    GPIO27_PIN,
    GPIO28_PIN,
    GPIO29_PIN,
    GPIO_PIN
} GPIO_Pinout;

typedef struct {
    bool en;
    uint32_t baudrate;
    uint32_t sys_clock;
    uint8_t irq_num;
    void * irq_cb_func;
    uint32_t tx_pin;
    uint32_t rx_pin;
    uint8_t pio_num;
    can2040_rx_cb rx_cb_func;
} CANParams_t;

typedef struct{
    uint32_t nSDN_pin;
    uint32_t nIRQ_pin;
    uint8_t irq_num;
    void * irq_cb_func;
    RF4463Config_t rf_config;
    SPIParams_t spi_config;
} RFParams_t;

typedef struct{
    SPIParams_t spi_config;
} DMXParams_t;

typedef struct{
    SPIParams_t spi_config;
} SDParams_t;

// typedef struct {
//     GPIO_Pinout red_channel;
//     GPIO_Pinout green_channel;
//     GPIO_Pinout blue_channel;
// } RGB_LED_Params;

// typedef struct {
//     i2c_inst_t *i2c_inst;
//     GPIO_Pinout sda_pin;
//     GPIO_Pinout scl_pin;
//     uint32_t baudrate;
// } I2C_Params;

// typedef struct {
//     float warm;
//     float cold;
//     float warmDim;
//     float warmBright;
//     float coldDim;
//     float coldBright;
// } DefaultParams;

#endif // TYPES_H