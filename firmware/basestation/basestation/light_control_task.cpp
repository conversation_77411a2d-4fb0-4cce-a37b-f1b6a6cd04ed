#include "light_control_task.h"
#include "active_configuration.h"

TaskHandle_t xLightControlTaskHandle;

void handle_light_activation(LightState *pLightState, uint32_t now) {
    if (pLightState == nullptr) {
        printf("ERROR: NULL light state pointer\r\n");
        return;
    }
    static bool previous_transitioning_states[10] = {false}; // Track previous state for each light
    
    if (pLightState->active_after_time > now) {
        // not active yet
        return;
    }
    // Check if transition is starting (is_transitioning just became true)
    if (pLightState->is_transitioning && !previous_transitioning_states[pLightState->id]) {
        printf("Light %d starting dimming transition: %.2f -> %.2f over %d ms\n", 
               pLightState->id, pLightState->brightness, pLightState->target_value, pLightState->dim_speed_msec);
    }
    
    // Check if transition is completing (is_transitioning just became false)
    if (!pLightState->is_transitioning && previous_transitioning_states[pLightState->id]) {
        printf("Light %d transition completed - brightness: %.2f\n", pLightState->id, pLightState->brightness);
    }
    
    // Update previous state
    previous_transitioning_states[pLightState->id] = pLightState->is_transitioning;


    LightConfig *pLightConfig = get_light_config_from_id(pLightState->id);
    if (pLightConfig == nullptr) {
        // not found
        printf("ERROR: Light config not found for light ID %d\r\n", pLightState->id);
        return;
    }
    
    // Validate fixtures count
    if (pLightConfig->fixtures_count == 0) {
        printf("WARNING: Light %d has no fixtures configured\r\n", pLightState->id);
        return;
    }

    // TODO, send RF commands if necessary
}

void process_all_lights(uint32_t now) {
    for (uint8_t i = 0; i < g_active_configuration.config.lights_count; i++) {
        LightState *pLightState = &g_active_configuration.state.lights[i];
        handle_light_activation(pLightState, now);
    }
}

void vLightControlTask(void *pvParameters) {
    printf("Light Control Task\r\n");

    for (;;) {
        uint32_t start_time = time_us_32();
        uint32_t now = get_time_in_ms();
    
        // Update light states first
        update_light_states(now);

        process_all_lights(now);

        // Calculate precise delay to maintain 40 Hz frequency
        uint32_t delta = time_us_32();
        delta = (delta < start_time) ? (delta + (0xFFFFFFFF - start_time)) : (delta - start_time);
        int32_t ms = pdMS_TO_TICKS(1000 / 40 - (delta / 1000)); // 40 Hz = 25ms period
        ms = (ms < 0) ? 0 : ms;

        vTaskDelay(ms);
    }
}
