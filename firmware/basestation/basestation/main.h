#ifndef __MAIN_H__
#define __MAIN_H__

// C standard libraries
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// pico sdk libraries
#include "pico/stdlib.h"
// #include "pico/unique_id.h"
// #include "hardware/flash.h"

#include "FreeRTOS.h"

// Include headers that may contain C++ code outside extern "C"
#include "dmx_task.h"
#include "uart_task.h"
#include "serial_task.h"
#include "can_task.h"
#include "rf_task.h"
#include "event_task.h"

#ifdef __cplusplus
extern "C" {
#endif
#include "task.h"
#include "sd_task.h"

// user libraries
#include "pinout.h"
#include "peripherals/irq_handler.h"
#include "peripherals/io.h"

#include "flash_storage.h"
#include "events.h"
#include "message_handler.h"


// global definitions

// local definitions/enums

#ifdef __cplusplus
}
#endif

// function prototyping
void vApplicationStackOverflowHook( TaskHandle_t xTask, char *pcTaskName );

#endif