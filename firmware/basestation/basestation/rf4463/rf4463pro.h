/*******************************************************************************************************************************************************************************************************
 * Begin of header
 *******************************************************************************************************************************************************************************************************/
#ifndef __RF4463PRO_H__
#define __RF4463PRO_H__
/******************************************************************************************************************************************************************************************************/
/**********************************************************************************************
 * Core Includes
 **********************************************************************************************/
#include <stdio.h>
#include <string.h>

#include "pico/stdlib.h"
#include "hardware/gpio.h"
/**********************************************
 * File Includes
 **********************************************/
#include "rf4463proconfig.h"
#include "rf4463prodefs.h"
#include "rf4463spi.h"

#ifdef __cplusplus
extern "C" {
#endif
/**********************************************************************************************
 * Documentation group assignation/include
 **********************************************************************************************/
/**@defgroup RF4463pro
 *
 *  @brief
 *  None
 *
 *  @details
 *  None
 */

/** @addtogroup  RF4463pro
 * @{
 */
/**********************************************************************************************
 * Definitions
 **********************************************************************************************/
#ifndef RF4463_PACKET_FIXED_LENGTH
    #define RF4463_MAX_BUFFER_LENGTH 64
#else
    #define RF4463_MAX_BUFFER_LENGTH 14
#endif

#define RF_MIN_CHANNEL ( unsigned char )1
#define RF_MAX_CHANNEL ( unsigned char )25
#define RF_MIN_NETWORK ( unsigned char )1
#define RF_MAX_NETWORK ( unsigned int )65534

#define RF_RSSI_POOR_THRESHOLD     ( int )-80
#define RF_RSSI_BAD_THRESHOLD      ( int )-100
#define RF_RSSI_BAD_READ_VALUE     ( int )-124

#define RF_MAX_ALLOWED_TX_POWER RF4463_TX_POWER_20_dB
/**********************************************************************************************
 * Structures
 **********************************************************************************************/
typedef union{
    uint32_t word[ 6 ];
    struct{
        uint32_t frequency;
        uint32_t baudrate;
        uint16_t syncword;
        uint8_t tx_channel;
        uint8_t rx_channel;
        uint8_t tx_power;
        uint8_t op_mode;
        uint8_t match[ 4 ];
        uint8_t mask[ 4 ];
    };
} RF4463Config_t;
static RF4463Config_t w;
/**********************************************************************************************
 * Enumerators
**********************************************************************************************/
/**********************************************************************************************
 * External References
**********************************************************************************************/
/*********************************************************************
 * Tasks
*********************************************************************/
/*********************************************************************
 * Variables
*********************************************************************/
extern const char RF4463_CMD_READ_BUF;
/**********************************************************************************************
 * Tasks
**********************************************************************************************/
/**********************************************************************************************
 * Functions
**********************************************************************************************/
uint8_t ucRF4463Init( uint32_t ucSDNPin, RF4463Config_t * pxConfigParam );
void vRF4463SetHeaderParam();
void vRF4463SDNReset( uint32_t ucSDNPin );
void vRF4463InitFreq(uint32_t ulFreqMhz, uint32_t ulChannelWidthKhz);
uint8_t ucRF4463SetGPIO(uint8_t ucOpMode);
uint8_t ucRF4463SetPacketMatch(uint8_t *pcMatch, uint8_t *pcMask);
void vRF4463ResetFIFO();
void vRF4463ClearInterrupts();
void vRF4463GetPacketInfo();
void vRF4463GetFIFOInfo();
uint8_t ucRF4463GetPartInfo(uint16_t usModel);
void vRF4463SetInterrupts();
uint16_t usRF4463GetInterrupt();
int16_t xRF4463GetRSSI();
int16_t xRF4463GetLastRSSI();
void vRF4463SetStandbyMode();
void vRF4463RxInit();
void vRF4463RxStart();
void vRF4463RxStartTest();
// void vRF4463RxReadFIFO(uint8_t *pcOutBuffer);
uint8_t ucRF4463RxReadFIFO( uint8_t * pcOutBuffer );
void vRF4463SetTxPktLength(uint8_t ucPacketLength);
void vRF4463TxStart();
void vRF4463TxData(uint8_t *pcBuffer, uint8_t ucPacketLength);
void vRF4463TxWriteFIFO(uint8_t *pcBuffer, uint8_t ucPacketLength);
uint8_t ucRF4463GetState();
uint8_t ucRF4463Check_UF_OF();

/**********************************************************************************************
 * Setters and Getters
**********************************************************************************************/
void vRF4463SetTxPower(uint8_t ucTxPower);
void vRF4463SetChannels(uint8_t ucChannel);
void vRF4463SetNetwork(uint16_t usNetwork);
void vRF4463ResetRxBuffer();
void vRF4463GetRxBuffer(uint8_t *pcOutBuffer, uint8_t ucLength);
uint8_t ucRF4463GetFlag();
void vRF4463BufferReset(uint8_t *pcBufferToClean, uint16_t uslength);

/**
 * @}
 */
/******************************************************************************************************************************************************************************************************/

#ifdef __cplusplus
}
#endif

#endif
/*******************************************************************************************************************************************************************************************************
 * End of File
 ******************************************************************************************************************************************************************************************************/