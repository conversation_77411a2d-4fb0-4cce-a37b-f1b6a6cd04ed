/*******************************************************************************************************************************************************************************************************
 * Begin of header
*******************************************************************************************************************************************************************************************************/
#ifndef __RFPACKETDEFS_H__
#define __RFPACKETDEFS_H__

#define RF_USE_HUB_TABLE

/******************************************************************************************************************************************************************************************************/
/**********************************************************************************************
 * Documentation group assignation/include
**********************************************************************************************/
/** @addtogroup  RF <- this module should be defined in SysDefinitions.h
 * @{
 */
/**********************************************************************************************
 * Definitions
**********************************************************************************************/
#if !defined( RF_USE_HUB_TABLE )            && \
    !defined( RF_USE_SWITCH_TABLE )         && \
    !defined( RF_USE_DIMMER_TABLE )         && \
    !defined( RF_USE_OUTLET_TABLE )         && \
    !defined( RF_USE_REED_TABLE )           && \
    !defined( RF_USE_PIR_TABLE )            && \
    !defined( RF_USE_THERMOSTAT_TABLE )     && \
    !defined( RF_USE_IR_TABLE )             && \
    !defined( RF_USE_SERVICE_PAD_TABLE )    && \
    !defined( RF_USE_SHADES_TABLE )         && \
    !defined( RF_USE_FAN_CONTROLLER )       && \
    !defined( RF_USE_DONGLE_TABLE )
    #error "Not defined usage of rf device table"
#endif
/**********************************************
 * Buffer Positions
**********************************************/
#define RF_MESSAGE_HEADER_SIZE     5U
/**********************************************/
#define RF_MESSAGE_SIZE           0U
#define RF_MESSAGE_DESTINATION    1U
#define RF_MESSAGE_SOURCE         2U
#define RF_MESSAGE_TABLE          3U
#define RF_MESSAGE_COMMAND        4U
#define RF_MESSAGE_CONTENT        5U
/**********************************************
 * Device Setup Steps
**********************************************/
#define RF_DEVICE_APPLY_CONFIG  2U
#define RF_DEVICE_SET_ADOPTION  1U
#define RF_DEVICE_REMOVE        0U
/**********************************************
 * Values for Database
**********************************************/
#define DB_STATE_ON             1U
#define DB_STATE_OFF            2U
#define DB_STATE_DISABLED       3U
/**********************************************
 *
**********************************************/
#define DEVICE_COMMMAND_APPLIED     1U
#define DEVICE_COMMMAND_INVALID     2U
/**********************************************
 * Status message response
**********************************************/
#define RF_DEVICE_STATUS_ADOPTED                5U
#define RF_DEVICE_STATUS_ADOPTION_PENDING       6U
#define RF_DEVICE_STATUS_REMOVAL_PENDING        7U
#define RF_DEVICE_STATUS_NODE                   8U
#define RF_DEVICE_STATUS_HUB_NODE               9U
#define RF_DEVICE_STATUS_NETWORK_MSB            10U
#define RF_DEVICE_STATUS_NETWORK_LSB            11U
/**********************************************
 * Adoption request settings posisitions
**********************************************/
#define RF_CONFIG_POSITION_UID_START        5U
#define RF_CONFIG_POSITION_NODE             17U
#define RF_CONFIG_POSITION_HUB_NODE         18U
#define RF_CONFIG_POSITION_CHANNEL          19U
#define RF_CONFIG_POSITION_TX_POWER         20U
#define RF_CONFIG_POSITION_NETWORK_MSB      21U
#define RF_CONFIG_POSITION_NETWORK_LSB      22U
#define RF_CONFIG_POSITION_BAUDRATE         23U
/**********************************************************************************************
 * Structures
**********************************************************************************************/
/**********************************************************************************************
 * Tables
**********************************************************************************************/
enum RFTables{
    TABLE_RF_TEST = 1,
    TABLE_RF_SETUP,
    TABLE_RF_OTA,
    TABLE_RF_GLOBAL,
    TABLE_RF_SWITCH,
    TABLE_RF_DIMMER,
    TABLE_RF_OUTLET,
    TABLE_RF_REED,
    TABLE_RF_PIR,
    TABLE_RF_THERMOSTAT,
    TABLE_RF_IR,
    TABLE_RF_SERVICE_PAD,
    TABLE_RF_SHADES,
    TABLE_RF_FAN,
    RF_TABLES
};
/**********************************************************************************************
 * Commands
**********************************************************************************************/
/**********************************************
 * Test Commands
**********************************************/
    enum RFTestCommands{
        RF_TEST_DEVICE_PING = 0,
        RF_TEST_STATUS,
        RF_TEST_QR,
        RF_TEST_OK,
        RF_TEST_STAGE,
        RF_TEST_LOOP,
        RF_TEST_CHANNEL,
        RF_TEST_MESSAGE,
        RF_TEST_INFO,
        RF_TEST_TABLE
    };

    #define RF_TEST_DONGLE_SEARCH 200U     // Reserved for Dongle Purposes
/**********************************************
 * Device Configuration
**********************************************/
    enum RFSetupTable{
        /**********************************************
         * Device administration commands
        **********************************************/
            RF_SETUP_DISCOVER = 0,
            RF_SETUP_FIND_BY_QR,
            RF_SETUP_REGISTER,
            RF_SETUP_REMOVE,
            RF_SETUP_HARD_RESET_REQUEST,
        /**********************************************
         * Reserved for replacement purposes
        **********************************************/
            RF_SETUP_REPLACEMENT,
            RF_SETUP_REPLACE_STATUS,
        /**********************************************
         * Reserved for recovery purposes
        **********************************************/
            RF_SETUP_FTY_RESTORE,
            RF_SETUP_FTY_RECONFIGURE,
            RF_SETUP_FTY_DISCOVER,
        /**********************************************/
            RF_SETUP_SET_ZONES,         // 5th Nov 2021
            RF_SETUP_SET_OP_MODE,       // ++
            RF_SETUP_SET_RELATION,      // ++
            RF_SETUP_REMOVE_RELATION,   // ++
        /*********************************************/
            RF_SETUP_OPERATION_IR,
            RF_SETUP_REGISTER_TIMES,
            RF_SETUP_REGISTER_BUTTON,
            RF_SETUP_EXECUTE_BUTTON,
            RF_SETUP_CLEAR_REGISTER,
            RF_SETUP_READ_IR,
            RF_SETUP_TRY_REGISTER,
            RF_SETUP_ERROR,
            RF_SETUP_SUCCESS,
        /*********************************************/
            RF_SETUP_SET_SHADES_DETECT_DELAY,   // 22
            RF_SETUP_SET_RACETIME_SHADES,
            RF_SETUP_RECALIBRATE_SHADES,
            RF_SETUP_STOP_SHADES,
            RF_SETUP_OPEN_SHADES,
            RF_SETUP_CLOSE_SHADES,
        /*********************************************/
            RF_SETUP_SET_MIN_DIMMER,
            RF_SETUP_SET_MAX_DIMMER,
            RF_SETUP_TABLE
    };
/**********************************************
 * File Transmission Commands
**********************************************/
    enum RFFOTACommands{
        RF_FOTA_START = 0,
        RF_FOTA_START_ACK,
        RF_FOTA_HEADER,
        RF_FOTA_HEADER_ACK,
        RF_FOTA_LINE,
        RF_FOTA_LINE_ACK,
        RF_FOTA_LINE_NACK,
        RF_FOTA_EOT,
        RF_FOTA_EOT_ACK,
        RF_FOTA_ERROR,
        RF_FOTA_DONE,
        RF_FOTA_TABLE
    };
/**********************************************
 * Global Commands ( Broadcast Transmission Purposes )
**********************************************/
    enum RFGlobalTable{
        RF_GLOBAL_ON = 0,
        RF_GLOBAL_OFF,
        RF_GLOBAL_ROUTINE_ON,
        RF_GLOBAL_ROUTINE_OFF,
        RF_GLOBAL_HVAC_ON,
        RF_GLOBAL_HVAC_OFF,
        RF_GLOBAL_HVAC_SET_TEMP,
        RF_GLOBAL_SHADES_STOP,
        RF_GLOBAL_SHADES_OPEN,
        RF_GLOBAL_SHADES_CLOSE,
        RF_GLOBAL_TABLE
    };
/**********************************************
 * Switch Commands
**********************************************/
    #if defined( RF_USE_SWITCH_TABLE ) || defined( RF_USE_HUB_TABLE ) || defined( RF_USE_DONGLE_TABLE )
        enum RFSwitchTable{
            RF_SWITCH_ON = 0,
            RF_SWITCH_OFF,
            RF_SWITCH_VIA1_ON,
            RF_SWITCH_VIA1_OFF,
            RF_SWITCH_VIA2_ON,
            RF_SWITCH_VIA2_OFF,
            RF_SWITCH_VIA3_ON,
            RF_SWITCH_VIA3_OFF,
            RF_SWITCH_SET_1_GANG,
            RF_SWITCH_SET_2_GANG,
            RF_SWITCH_SET_3_GANG,
            RF_SWITCH_SLEEP,
            RF_SWITCH_WAKE,
            RF_SWITCH_HAPTIC_ENABLE,
            RF_SWITCH_HAPTIC_DISABLE,
            RF_SWITCH_TOUCH_ENABLE,
            RF_SWITCH_TOUCH_DISABLE,
            RF_SWITCH_GET_CONSUMPTION,
            RF_SWITCH_LOAD_UPDATE,
            RF_SWITCH_ACTIVE_CONSUMPTION,
            RF_SWITCH_TABLE
        };
    #endif
/**********************************************
 * Dimmer Commands
**********************************************/
    #if defined( RF_USE_DIMMER_TABLE ) || defined( RF_USE_HUB_TABLE ) || defined( RF_USE_DONGLE_TABLE )
        enum RFDimmerCommands{
            RF_DIMMER_ON = 0,
            RF_DIMMER_OFF,
            RF_DIMMER_LEVEL_1,
            RF_DIMMER_LEVEL_2,
            RF_DIMMER_LEVEL_3,
            RF_DIMMER_LEVEL_4,
            RF_DIMMER_LEVEL_5,
            RF_DIMMER_GET_ACK,
            RF_DIMMER_TOUCH_ENABLE,
            RF_DIMMER_TOUCH_DISABLE,
            RF_DIMMER_SLEEP,
            RF_DIMMER_WAKE,
            RF_DIMMER_SET_DIMMING_TYPE,
            RF_DIMMER_IS_ONLINE,
            RF_DIMMER_SET_MIN_DIMMER,
            RF_DIMMER_SET_MAX_DIMMER,
            RF_DIMMER_SET_1_GANG,
            RF_DIMMER_SET_2_GANG,
            RF_DIMMER_SET_3_GANG,
            RF_DIMMER_SET_STEP_CYCLE,
            RF_DIMMER_TABLE
        };
        /* OLD TABLE FOR REFERENCE ONLY
            enum RFDimmerCommands{
                _OLD_RF_DIMMER_ON = 0,
                _OLD_RF_DIMMER_OFF,
                _OLD_RF_DIMMER_LEVEL_1,
                _OLD_RF_DIMMER_LEVEL_2,
                _OLD_RF_DIMMER_LEVEL_3,
                _OLD_RF_DIMMER_LEVEL_4,
                _OLD_RF_DIMMER_LEVEL_5,
                _OLD_RF_DIMMER_GET_ACK,
                _OLD_RF_DIMMER_TOUCH_ENABLE,
                _OLD_RF_DIMMER_TOUCH_DISABLE,
                _OLD_RF_DIMMER_SLEEP,
                _OLD_RF_DIMMER_WAKE,
                _OLD_RF_DIMMER_RECALIBRATE_SUPPLY,
                _OLD_RF_DIMMER_IS_ONLINE,
                _OLD_RF_DIMMER_GET_CONSUMPTION,
                _OLD_RF_DIMMER_CALIBRATE_OFFSET,
                _OLD_RF_DIMMER_SET_OFFSET,
                _OLD_RF_DIMMER_TABLE
            };
        */
    #endif
/**********************************************
 * Outlet Commands
**********************************************/
    #if defined( RF_USE_OUTLET_TABLE ) || defined( RF_USE_HUB_TABLE ) || defined( RF_USE_DONGLE_TABLE )
        enum RFOutletCommands{
            RF_OUTLET_ON = 0,
            RF_OUTLET_OFF,
            RF_OUTLET_BUTTON_ENABLE,
            RF_OUTLET_BUTTON_DISABLE,
            RF_OUTLET_SLEEP,
            RF_OUTLET_WAKE,
            RF_OUTLET_GET_CONSUMPTION,
            RF_OUTLET_TABLE
        };
    #endif
/**********************************************
 * Reed Commands
**********************************************/
    #if defined( RF_USE_REED_TABLE ) || defined( RF_USE_HUB_TABLE ) || defined( RF_USE_DONGLE_TABLE )
        enum RFReedTable{
            RF_REED_SWITCH_GET_STATUS = 0,
            RF_REED_SWITCH_CLOSE,
            RF_REED_SWITCH_OPEN,
            RF_REED_SWITCH_CLOSE_RETRANSMIT,
            RF_REED_SWITCH_OPEN_RETRANSMIT,
            RF_REED_SWITCH_STATE_CHANGE_ACK,
            RF_REED_SWITCH_WIRED_ONLINE,
            RF_REED_TABLE
        };
    #endif
/**********************************************
 * PIR Commands
**********************************************/
    #if defined( RF_USE_PIR_TABLE ) || defined( RF_USE_HUB_TABLE ) || defined( RF_USE_DONGLE_TABLE )
        enum RFPIRTable{
            RF_PIR_GET_STATUS = 0,
            RF_PIR_DETECT_ACTIVITY,
            RF_PIR_NO_ACTIVITY,
            RF_PIR_WIRED_ONLINE,
            RF_PIR_TABLE
        };
    #endif
/**********************************************
 * Thermostat Commands
**********************************************/
    #if defined( RF_USE_THERMOSTAT_TABLE ) || defined( RF_USE_HUB_TABLE ) || defined( RF_USE_DONGLE_TABLE )
        enum RFThermostatTable{
            RF_THERMOSTAT_GET_MEASURE = 0,
            RF_THERMOSTAT_SET_TEMP,
            RF_THERMOSTAT_SET_RANGE,
            RF_THERMOSTAT_SET_SCALE,
            RF_THERMOSTAT_SET_DATE,
            RF_THERMOSTAT_LOCK,
            RF_THERMOSTAT_UNLOCK,
            RF_THERMOSTAT_ON,
            RF_THERMOSTAT_OFF,
            RF_THERMOSTAT_TEMP_UPDATE,
            RF_THERMOSTAT_MODE,
            RF_THERMOSTAT_SET_FAN,
            RF_THERMOSTAT_TEMP_SET_UPDATE_TIME,
            RF_THERMOSTAT_TEMP_SET_ALARM_RETRIES,
            RF_THERMOSTAT_LOG_INFO,
            RF_THERMOSTAT_LOG_NEXT_MESSAGE,
            RF_THERMOSTAT_LOG_REINIT,
            RF_THERMOSTAT_NO_CHANGE_ALARM,
            RF_THERMOSTAT_WORKING_STATUS,
            RF_THERMOSTAT_STATUS,
            RF_THERMOSTAT_RESET_UNIT,
            RF_THERMOSTAT_UNIT_IS_ONLINE,
            RF_THERMOSTAT_MODBUS_INIT,
            RF_THERMOSTAT_MODBUS_DIAG,
            RF_THERMOSTAT_MODBUS_ERROR_MAX_COUNTER,
            RF_THERMOSTAT_MODBUS_RESET,
            RF_THERMOSTAT_TABLE
        };
    #endif
/**********************************************
 * IR Commands
**********************************************/
    #if defined( RF_USE_IR_TABLE ) || defined( RF_USE_HUB_TABLE ) || defined( RF_USE_DONGLE_TABLE )
        enum RFIRControllerTable{
            RF_IR_HVAC_GET_TEMP = 0,
            RF_IR_HVAC_SET_TEMP,
            RF_IR_HVAC_ON,
            RF_IR_HVAC_OFF,
            RF_IR_HVAC_SET_MODE,
            RF_IR_HVAC_UPDATE_TEMP,
            RF_IR_HVAC_SET_SCALE,
            RF_IR_HVAC_SET_FAN,
            RF_IR_HVAC_TEMP_SET_UPDATE_TIME,
            RF_IR_HVAC_TEMP_SET_ALARM_RETRIES,
            RF_IR_HVAC_TEMP_NO_CHANGE_ALARM,
            RF_IR_HVAC_BUTTON,
            RF_IR_HVAC_OVERIDE_OPERATION_IR,
            RF_IR_HVAC_OFFSET_TEMP_IR,
            RF_IR_HVAC_OFFSET_HUM_IR,
            RF_IR_HVAC_RESET,
            RF_IR_HVAC_GET_OPERATION,
            RF_IR_UNIT_IS_ONLINE,
            RF_IR_TABLE
        };
    #endif
/**********************************************
 * Service Pad Commands
**********************************************/
    #if defined( RF_USE_SERVICE_PAD_TABLE ) || defined( RF_USE_HUB_TABLE ) || defined( RF_USE_DONGLE_TABLE )
        enum RFServicePadTable{
            RF_SERVICE_PAD_HOUSEKEEPING_ON = 0,
            RF_SERVICE_PAD_HOUSEKEEPING_OFF,
            RF_SERVICE_PAD_PRIVACY_ON,
            RF_SERVICE_PAD_PRIVACY_OFF,
            RF_SERVICE_PAD_SLEEP_ON,
            RF_SERVICE_PAD_SLEEP_OFF,
            RF_SERVICE_PAD_SET_SENSITIVITY,
            RF_SERVICE_PAD_SET_DOORBELL_TIME,
            RF_SERVICE_PAD_SET_INDICATOR_MODE,
            RF_SERVICE_PAD_TABLE
        };
    #endif
/**********************************************
 * Shades Commands
**********************************************/
    #if defined( RF_USE_BLINDS_TABLE ) || defined( RF_USE_HUB_TABLE ) || defined( RF_USE_DONGLE_TABLE )
        enum RFShadesTable{
            RF_SHADES_STOP = 0,
            RF_SHADES_OPEN,
            RF_SHADES_CLOSE,
            RF_SHADES_SET_POINT,
            RF_SHADES_GET_STATUS,
            RF_SHADES_SET_RACETIME,
            RF_SHADES_AUTO_CALIBRATE,
            RF_SHADES_SLEEP_ON,
            RF_SHADES_SLEEP_OFF,
            RF_SHADES_SET_CONTROL_TYPE,
            RF_SHADES_TABLE
        };
    #endif
/**********************************************
 * Fan Controller Commands
**********************************************/
    #if defined( RF_USE_FAN_CONTROLLER ) || defined( RF_USE_HUB_TABLE ) || defined( RF_USE_DONGLE_TABLE )
        enum RFFanControllerTable{
            RF_FAN_ON = 0,
            RF_FAN_OFF,
            RF_FAN_SPEED,
            RF_FAN_SLEEP_ON,
            RF_FAN_SLEEP_OFF,
            RF_FAN_HIGH_SENSITIVITY,
            RF_FAN_LOW_SENSITIVITY,
            RF_FAN_TABLE
        };
    #endif
/**
 * @}
 */
/******************************************************************************************************************************************************************************************************/
#endif
/*******************************************************************************************************************************************************************************************************
 * End of File
******************************************************************************************************************************************************************************************************/