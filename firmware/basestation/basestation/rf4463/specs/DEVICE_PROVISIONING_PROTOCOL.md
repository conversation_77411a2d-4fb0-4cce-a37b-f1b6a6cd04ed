# Device Provisioning Protocol

## Overview

The device provisioning protocol enables a basestation to discover and register new devices on the RF network. This protocol consists of two main phases: **Device Discovery** and **Device Registration**.

## Protocol Phases

### Phase 1: Device Discovery

#### Purpose
Locate and identify a device using its QR code, establishing initial communication.

#### Protocol Flow

1. **Channel/Network Switch**
   - Basestation switches to adoption default channel and network
   - Default values: `ADOPTION_DEFAULT_CHANNEL` and `ADOPTION_DEFAULT_NETWORK`

2. **Discovery Message Construction**
   ```
   Message Structure:
   [QR Code (12 bytes)] + [Node ID (1 byte)] + [Channel (1 byte)] + [Network (2 bytes)] + [TX Power (1 byte)] + [Baudrate (1 byte)] + [Device Mode (1 byte)]
   ```

   **Field Details:**
   - **QR Code**: 12-byte device identifier (e.g., "KW3223005845")
   - **Node ID**: Target node ID for the device (provided by caller)
   - **Channel**: Target channel for device operation
   - **Network**: Target network ID for device operation (16-bit)
   - **TX Power**: RF transmission power setting
   - **Baudrate**: RF communication baudrate (e.g., `RF4463_BAUDRATE_1200`)
   - **Device Mode**: Device operation mode (e.g., `RF4463_DEV_OP_MODE_SLAVE`)

3. **Discovery Request**
   - **Command**: `RF_SETUP_FIND_BY_QR`
   - **Table**: `TABLE_RF_SETUP`
   - **Destination**: `DEFAULT_DESTINATION` (typically node ID 2)
   - **Timeout**: 5 seconds per attempt
   - **Retries**: 3 attempts with exponential backoff (1s, 2s, 4s delays)

4. **Discovery Response**
   - Device responds with pairing code in message content
   - Response contains the same command and table identifiers
   - Source field indicates responding device's node ID

### Phase 2: Device Registration

#### Purpose
Register the discovered device with the pairing code received during discovery.

#### Protocol Flow

1. **Channel/Network Restoration**
   - Basestation switches back to target channel and network
   - Device should now be listening on the target channel/network

2. **Registration Message Construction**
   ```
   Message Structure:
   [Pairing Code (12 bytes)] + [Padding (4 bytes)]
   ```

   **Field Details:**
   - **Pairing Code**: 12-byte code received from device during discovery
   - **Padding**: 4 bytes of 0xFF for message alignment

3. **Registration Request**
   - **Command**: `RF_SETUP_REGISTER`
   - **Table**: `TABLE_RF_SETUP`
   - **Destination**: Target node ID (from discovery phase)
   - **Timeout**: 5 seconds per attempt
   - **Retries**: 3 attempts with exponential backoff (1s, 2s, 4s delays)

4. **Registration Response**
   - Device confirms successful registration
   - Response indicates registration completion

## Message Format

### RF Message Header Structure
```
[Message Size (1 byte)] + [Destination (1 byte)] + [Source (1 byte)] + [Table (1 byte)] + [Command (1 byte)] + [Content (variable)]
```

### Field Definitions
- **Message Size**: Total message length including header
- **Destination**: Target node ID
- **Source**: Sending node ID (basestation = 1)
- **Table**: Message category (`TABLE_RF_SETUP`)
- **Command**: Specific operation (`RF_SETUP_FIND_BY_QR`, `RF_SETUP_REGISTER`)
- **Content**: Variable-length payload

## Error Handling

### Timeout Scenarios
- **Discovery Timeout**: Device not found or not responding
- **Registration Timeout**: Device not accepting registration

### Retry Strategy
- **Exponential Backoff**: 1s, 2s, 4s delays between retries
- **Maximum Retries**: 3 attempts per phase
- **Graceful Degradation**: Clear error reporting for each failure

## State Transitions

```
Basestation State Machine:
[Idle] → [Discovery Mode] → [Device Found] → [Registration Mode] → [Device Registered] → [Idle]
                ↓                    ↓                    ↓
            [Timeout]            [Timeout]            [Timeout]
                ↓                    ↓                    ↓
            [Retry]              [Retry]              [Retry]
```

## Implementation Notes

### Channel Management
- Basestation temporarily switches to adoption defaults
- Device must be listening on adoption channel/network for discovery
- After discovery, both parties switch to target channel/network

### Message Processing
- Normal message processing is disabled during provisioning
- Queue-based message handling ensures reliable communication
- Response validation includes source, table, and command matching

### Security Considerations
- QR codes provide device identification
- Pairing codes ensure authorized registration
- Channel/network isolation prevents interference

## Example Usage

```cpp
// Provision a new device
vRFAdoptDevice("KW3223005845", 50);
```

This will:
1. Discover device with QR code "KW3223005845"
2. Assign it node ID 50
3. Register it on the target channel/network
4. Return success/failure status

## Constants and Definitions

```cpp
#define DEFAULT_DESTINATION             2
#define ADOPTION_DEFAULT_CHANNEL        15
#define ADOPTION_DEFAULT_NETWORK        15
#define DEVICE_QR_FIELD_LENGTH          12
#define RF_SETUP_FIND_BY_QR             // Discovery command
#define RF_SETUP_REGISTER               // Registration command
#define TABLE_RF_SETUP                  // Setup table
```

## Troubleshooting

### Common Issues
1. **Discovery Fails**: Device not in adoption mode or wrong QR code
2. **Registration Fails**: Device not switched to target channel/network
3. **Timeout Errors**: Network interference or device out of range

### Debug Information
- RSSI values indicate signal strength
- Retry attempts show communication reliability
- Channel/network switching confirms proper state transitions 