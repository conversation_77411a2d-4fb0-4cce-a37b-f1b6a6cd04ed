/*******************************************************************************************************************************************************************************************************
 * Begin of header
 *******************************************************************************************************************************************************************************************************/
#ifndef __RF4463SPI_H__
#define __RF4463SPI_H__
/******************************************************************************************************************************************************************************************************/
/**********************************************************************************************
 * Core Includes
 **********************************************************************************************/
/**********************************************
 * File Includes
 **********************************************/
#include "rf4463pro.h"
#include "rf4463prodefs.h"
/**********************************************
 * File Includes
 **********************************************/
#include "../peripherals/spi.h"

#ifdef __cplusplus
extern "C" {
#endif
/**********************************************************************************************
 * Documentation group assignation/include
 **********************************************************************************************/
/** @addtogroup  RF4463pro
 * @{
 */

/** @addtogroup  SPIDriver
 * @{
 */
/**********************************************************************************************
 * Definitions
 **********************************************************************************************/
/**********************************************************************************************
 * Structures
 **********************************************************************************************/
/**********************************************************************************************
 * Enumerators
 **********************************************************************************************/
/**********************************************************************************************
 * External References
 **********************************************************************************************/
/*********************************************************************
 * Tasks
 *********************************************************************/
/*********************************************************************
 * Variables
 *********************************************************************/
/**********************************************************************************************
 * Tasks
 **********************************************************************************************/
/**********************************************************************************************
 * Functions
 **********************************************************************************************/
uint8_t ucRF4463CheckCTS();

void vRF4463SPISendCmdGetResponse(uint8_t *pcCommandBuffer, uint8_t ucCommandLength, uint8_t *pcResponseBuffer, uint8_t ucResponseLength);
void vRF4463SPIWrite(uint8_t *pcBuffer, uint8_t ucTxLength);
void vRF4463SPIWriteCmdNoCTS(uint8_t ucCommand, uint8_t *pcBuffer, uint8_t ucTxLength);
uint8_t ucRF4463SPIByte(uint8_t ucData);
void vRF4463RWBuffer(uint8_t ucCommand, uint8_t *pcReadBuffer, uint8_t ucLength);
// int sRF4463SPIBusInit();

void vRF4463SPISetConfig( SPIParams_t * pxParams );

/**********************************************************************************************
 * Setters and Getters
 **********************************************************************************************/
/**
 * @}
 */

/**
 * @}
 */
/******************************************************************************************************************************************************************************************************/

#ifdef __cplusplus
}
#endif

#endif
/*******************************************************************************************************************************************************************************************************
 * End of File
 ******************************************************************************************************************************************************************************************************/