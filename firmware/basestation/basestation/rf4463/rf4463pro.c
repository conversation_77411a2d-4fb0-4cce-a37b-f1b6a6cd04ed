/*******************************************************************************************************************************************************************************************************
 * File:
 * Author:
 * Editor:
 * Project:
 * Version:
 *
 * Compiler:
 * IDE:
 * Platform:
 * Kernel:
 *
 * Notes:
*******************************************************************************************************************************************************************************************************/
/**********************************************************************************************
 * Prototyping Header
**********************************************************************************************/
#include "rf4463pro.h"
/**********************************************************************************************
 * Global Variables
**********************************************************************************************/
/**********************************************************************************************
 * Local Variables
**********************************************************************************************/
uint8_t pcRF4463SPIRxBuffer[ RF4463_MAX_BUFFER_LENGTH ] = { 0x00 };
static int16_t xRFLastRSSI = 0;
const char RF4463_CMD_READ_BUF = 0x44;
/**********************************************************************************************
 * Configuration Variables
**********************************************************************************************/
const uint8_t RF_POWER_UP_data[] = { RF_POWER_UP };

#ifdef RF4463_USE_SET_CONFIGURATION
    const uint8_t RF4463_CONFIGURATION_DATA[] = RADIO_CONFIGURATION_DATA_ARRAY;
#endif
/**********************************************************************************************
 * Instances
**********************************************************************************************/
RF4463FlagType_t xRF4463Flag;
static RF4463Config_t xRF4463Configuration = { 0U };
/**********************************************************************************************
 * IRQ Handlers
**********************************************************************************************/
/**********************************************************************************************
 * Function declaration
**********************************************************************************************/
/*********************************************************************
 *
*********************************************************************/
uint8_t ucRF4463Init( uint32_t ucSDNPin, RF4463Config_t * pxConfigParam ){
    uint8_t pcAppCommandBuf[ 20 ];
    uint8_t ucPosition = 0;
    /*********************************************************************
     * Set all pre-configured parameters
    *********************************************************************/
        memcpy( &xRF4463Configuration, pxConfigParam, sizeof( RF4463Config_t ) );
    /*********************************************************************
     * Debug parameters
    *********************************************************************/
        #ifdef DEBUG_RF4463_DRIVER_CONFIG
            printf( "********* RF4463 initial config parameters *********\r\n" );
            #if defined( RF4463_PACKET_VARIABLE_LENGTH )
                printf( "[ RF4463 ] Packet Variable Length\r\n" );
            #elif defined( RF4463_PACKET_FIXED_LENGTH )
                printf( "[ RF4463 ] Packet Fixed Length\r\n" );
            #endif
            printf( "[ RF4463 ] Passed configuration\r\n" );
            printf( "[ RF4463 ] Frequency: %ld\r\n",      xRF4463Configuration.frequency );
            printf( "[ RF4463 ] Baudrate: 1200\r\n" );
            printf( "[ RF4463 ] SyncWord: %d\r\n",        xRF4463Configuration.syncword );
            printf( "[ RF4463 ] Tx Channel: %d\r\n",      xRF4463Configuration.tx_channel );
            printf( "[ RF4463 ] Rx Channel: %d\r\n",      xRF4463Configuration.rx_channel );
            printf( "[ RF4463 ] Tx Power: %d\r\n",        xRF4463Configuration.tx_power );
            printf( "[ RF4463 ] Operation mode: %d\r\n",  xRF4463Configuration.op_mode );
            #ifdef RF4463_ENABLE_MATCH
                printf( "[ RF4463 ] Match: 0x%02x 0x%02x 0x%02x 0x%02x\r\n",  xRF4463Configuration.Match[ 0 ], xRF4463Configuration.Match[ 1 ], xRF4463Configuration.Match[ 2 ], xRF4463Configuration.Match[ 3 ] );
                printf( "[ RF4463 ] Mask: 0x%02x 0x%02x 0x%02x 0x%02x\r\n",   xRF4463Configuration.Mask[ 0 ], xRF4463Configuration.Mask[ 1 ], xRF4463Configuration.Mask[ 2 ], xRF4463Configuration.Mask[ 3 ] );
            #endif
            printf( "***************************************************\r\n" );
        #endif /* DEBUG_RF4463_DRIVER_CONFIG */
    /*********************************************************************
     * Set power up configuration
     * RF_POWER_UP
    *********************************************************************/
        vRF4463SDNReset( ucSDNPin );
    /*********************************************************************
     * Send GPIO configuration for defined operation mode
     * RF_GPIO_PIN_CFG
    *********************************************************************/
        if( ucRF4463SetGPIO( xRF4463Configuration.op_mode ) ){
            printf( "[ RF4463 ] Failed to configure GPIO\r\n" );
            return 1;
        }
    /*********************************************************************
     * Configuration header settings
     * Seems these settings are considered the patch before setting the
     * user ones
    *********************************************************************/
        #ifndef RF4463_USE_SET_CONFIGURATION
            /*********************************************************************
             * Replaces vRF4463SetConfiguration();
             * Leave out Power Up and GPIO configuration
            *********************************************************************/
            /*********************************************************************
             * RF_GLOBAL_XO_TUNE_2
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_GLOBAL;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 2;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_GLOBAL_XO_TUNE;
                pcAppCommandBuf[ ucPosition++ ] = 0x62; ///< GLOBAL_XO_TUNE
                pcAppCommandBuf[ ucPosition++ ] = 0x00; ///< GLOBAL_CLK_CFG
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_GLOBAL_CONFIG_1
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_GLOBAL;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x01;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_GLOBAL_CONFIG;
                pcAppCommandBuf[ ucPosition++ ] = 0x20;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_INT_CTL_ENABLE_4
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_INT_CTL;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x04;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_INT_CTL_ENABLE;
                pcAppCommandBuf[ ucPosition++ ] = RF4463_INT_STATUS_CHIP_INT_STATUS | RF4463_INT_STATUS_MODEM_INT_STATUS | RF4463_INT_STATUS_PH_INT_STATUS;
                pcAppCommandBuf[ ucPosition++ ] = RF4463_INT_STATUS_PH_PACKET_SENT | RF4463_INT_STATUS_PH_PACKET_RX | RF4463_INT_STATUS_PH_CRC_ERROR;
                pcAppCommandBuf[ ucPosition++ ] = RF4463_INT_STATUS_MODEM_RSSI_JUMP | RF4463_INT_STATUS_MODEM_RSSI;
                pcAppCommandBuf[ ucPosition++ ] = RF4463_INT_STATUS_CHIP_CMD_ERROR | RF4463_INT_STATUS_CHIP_CHIP_READY | RF4463_INT_STATUS_CHIP_LOW_BATT | RF4463_INT_STATUS_CHIP_WUT;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_FRR_CTL_A_MODE_4
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_FRR_CTL;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x04;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_FRR_CTL_A_MODE;
                pcAppCommandBuf[ ucPosition++ ] = 0x01;
                pcAppCommandBuf[ ucPosition++ ] = 0x09;
                pcAppCommandBuf[ ucPosition++ ] = 0x0A;
                pcAppCommandBuf[ ucPosition++ ] = 0x08;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_PREAMBLE_TX_LENGTH_9
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PREAMBLE;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x09;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PREAMBLE_TX_LENGTH;
                pcAppCommandBuf[ ucPosition++ ] = 0x08;
                pcAppCommandBuf[ ucPosition++ ] = 0x14;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x0F;
                pcAppCommandBuf[ ucPosition++ ] = 0x31;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_SYNC_CONFIG_6
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_SYNC;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x06;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_SYNC_CONFIG;
                pcAppCommandBuf[ ucPosition++ ] = 0x01;
                pcAppCommandBuf[ ucPosition++ ] = 0xB4;
                pcAppCommandBuf[ ucPosition++ ] = 0x2B;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_PKT_CRC_CONFIG_12
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_CRC_CONFIG;
                pcAppCommandBuf[ ucPosition++ ] = 0x04;
                pcAppCommandBuf[ ucPosition++ ] = 0x01;
                pcAppCommandBuf[ ucPosition++ ] = 0x08;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0x20;
                pcAppCommandBuf[ ucPosition++ ] = 0x02;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x30;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_PKT_RX_THRESHOLD_12
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_RX_THRESHOLD;
                pcAppCommandBuf[ ucPosition++ ] = 0x30;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x40;
                pcAppCommandBuf[ ucPosition++ ] = 0x06;
                pcAppCommandBuf[ ucPosition++ ] = 0xA8;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_PKT_FIELD_3_CRC_CONFIG_12
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_FIELD_3_CRC_CONFIG;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_PKT_RX_FIELD_1_CRC_CONFIG_12
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_RX_FIELD_1_CRC_CONFIG;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_PKT_RX_FIELD_4_CRC_CONFIG_5
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x05;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_RX_FIELD_4_CRC_CONFIG;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_PKT_CRC_SEED_31_24_4
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x04;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_PKT_CRC_SEED_31_24;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_MOD_TYPE_12
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_MOD_TYPE;
                pcAppCommandBuf[ ucPosition++ ] = 0x02;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x07;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x2E;
                pcAppCommandBuf[ ucPosition++ ] = 0xE0;
                pcAppCommandBuf[ ucPosition++ ] = 0x01;
                pcAppCommandBuf[ ucPosition++ ] = 0xC9;
                pcAppCommandBuf[ ucPosition++ ] = 0xC3;
                pcAppCommandBuf[ ucPosition++ ] = 0x80;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x01;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_FREQ_DEV_0_1
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x01;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_FREQ_DEV_0;
                pcAppCommandBuf[ ucPosition++ ] = 0x18;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_TX_RAMP_DELAY_12
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_TX_RAMP_DELAY;
                pcAppCommandBuf[ ucPosition++ ] = 0x01;
                pcAppCommandBuf[ ucPosition++ ] = 0x80;
                pcAppCommandBuf[ ucPosition++ ] = 0x0a;
                pcAppCommandBuf[ ucPosition++ ] = 0x03;
                pcAppCommandBuf[ ucPosition++ ] = 0xC0;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x32;
                pcAppCommandBuf[ ucPosition++ ] = 0x20;
                pcAppCommandBuf[ ucPosition++ ] = 0x0C;
                pcAppCommandBuf[ ucPosition++ ] = 0xf9;
                pcAppCommandBuf[ ucPosition++ ] = 0x03;
                pcAppCommandBuf[ ucPosition++ ] = 0x0D;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_BCR_NCO_OFFSET_2_12
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_BCR_NCO_OFFSET_2;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0xA7;
                pcAppCommandBuf[ ucPosition++ ] = 0xC6;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x54;
                pcAppCommandBuf[ ucPosition++ ] = 0x02;
                pcAppCommandBuf[ ucPosition++ ] = 0xC2;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x04;
                pcAppCommandBuf[ ucPosition++ ] = 0x23;
                pcAppCommandBuf[ ucPosition++ ] = 0xC0;
                pcAppCommandBuf[ ucPosition++ ] = 0x01;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_AFC_LIMITER_1_3
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x03;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_AFC_LIMITER_1;
                pcAppCommandBuf[ ucPosition++ ] = 0x54;
                pcAppCommandBuf[ ucPosition++ ] = 0x2E;
                pcAppCommandBuf[ ucPosition++ ] = 0xC0;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_AGC_CONTROL_1
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x01;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_AGC_CONTROL;
                pcAppCommandBuf[ ucPosition++ ] = 0xE2;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_AGC_WINDOW_SIZE_12
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_AGC_WINDOW_SIZE;
                pcAppCommandBuf[ ucPosition++ ] = 0x11;
                pcAppCommandBuf[ ucPosition++ ] = 0xAB;
                pcAppCommandBuf[ ucPosition++ ] = 0xAB;
                pcAppCommandBuf[ ucPosition++ ] = 0x80;
                pcAppCommandBuf[ ucPosition++ ] = 0x02;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x2B;
                pcAppCommandBuf[ ucPosition++ ] = 0x0C;
                pcAppCommandBuf[ ucPosition++ ] = 0xA4;
                pcAppCommandBuf[ ucPosition++ ] = 0x22;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_RAW_CONTROL_10
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0A;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_RAW_CONTROL;
                pcAppCommandBuf[ ucPosition++ ] = 0x82;
                pcAppCommandBuf[ ucPosition++ ] = 0x04;
                pcAppCommandBuf[ ucPosition++ ] = 0x43;
                pcAppCommandBuf[ ucPosition++ ] = 0x02;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0x0f;
                pcAppCommandBuf[ ucPosition++ ] = 0x20;
                pcAppCommandBuf[ ucPosition++ ] = 0x18;
                pcAppCommandBuf[ ucPosition++ ] = 0x40;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_RAW_SEARCH2_2
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x02;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_RAW_SEARCH2;
                pcAppCommandBuf[ ucPosition++ ] = 0x84;
                pcAppCommandBuf[ ucPosition++ ] = 0x08;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_SPIKE_DET_2
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x02;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_SPIKE_DET;
                pcAppCommandBuf[ ucPosition++ ] = 0x03;
                pcAppCommandBuf[ ucPosition++ ] = 0x07;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_RSSI_MUTE_1
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x01;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_RSSI_MUTE;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_DSA_CTRL1_5
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x05;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_DSA_CTRL1;
                pcAppCommandBuf[ ucPosition++ ] = 0x40;
                pcAppCommandBuf[ ucPosition++ ] = 0x04;
                pcAppCommandBuf[ ucPosition++ ] = 0x0B;
                pcAppCommandBuf[ ucPosition++ ] = 0x78;
                pcAppCommandBuf[ ucPosition++ ] = 0x20;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_CHFLT_RX1_CHFLT_COE13_7_0_12
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM_CHFLT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_CHFLT_RX1_CHFLT_COE13_7_0;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0xBA;
                pcAppCommandBuf[ ucPosition++ ] = 0x0F;
                pcAppCommandBuf[ ucPosition++ ] = 0x51;
                pcAppCommandBuf[ ucPosition++ ] = 0xCF;
                pcAppCommandBuf[ ucPosition++ ] = 0xA9;
                pcAppCommandBuf[ ucPosition++ ] = 0xC9;
                pcAppCommandBuf[ ucPosition++ ] = 0xFC;
                pcAppCommandBuf[ ucPosition++ ] = 0x1B;
                pcAppCommandBuf[ ucPosition++ ] = 0x1E;
                pcAppCommandBuf[ ucPosition++ ] = 0x0F;
                pcAppCommandBuf[ ucPosition++ ] = 0x01;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_CHFLT_RX1_CHFLT_COE1_7_0_12
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM_CHFLT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_CHFLT_RX1_CHFLT_COE1_7_0;
                pcAppCommandBuf[ ucPosition++ ] = 0xFC;
                pcAppCommandBuf[ ucPosition++ ] = 0xFD;
                pcAppCommandBuf[ ucPosition++ ] = 0x15;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x0F;
                pcAppCommandBuf[ ucPosition++ ] = 0xD6;
                pcAppCommandBuf[ ucPosition++ ] = 0xD0;
                pcAppCommandBuf[ ucPosition++ ] = 0xBE;
                pcAppCommandBuf[ ucPosition++ ] = 0xA3;
                pcAppCommandBuf[ ucPosition++ ] = 0x82;
                pcAppCommandBuf[ ucPosition++ ] = 0x61;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MODEM_CHFLT_RX2_CHFLT_COE7_7_0_12
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM_CHFLT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_CHFLT_RX2_CHFLT_COE7_7_0;
                pcAppCommandBuf[ ucPosition++ ] = 0x41;
                pcAppCommandBuf[ ucPosition++ ] = 0x27;
                pcAppCommandBuf[ ucPosition++ ] = 0x13;
                pcAppCommandBuf[ ucPosition++ ] = 0x06;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0xFD;
                pcAppCommandBuf[ ucPosition++ ] = 0xFD;
                pcAppCommandBuf[ ucPosition++ ] = 0xFD;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0xF0;
                pcAppCommandBuf[ ucPosition++ ] = 0x0F;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_PA_MODE_4
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PA;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x04;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PA_MODE;
                pcAppCommandBuf[ ucPosition++ ] = 0x08;
                pcAppCommandBuf[ ucPosition++ ] = 0x7F;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x1E;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_SYNTH_PFDCP_CPFF_7
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_SYNTH;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x07;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_SYNTH_PFDCP_CPFF;
                pcAppCommandBuf[ ucPosition++ ] = 0x2C;
                pcAppCommandBuf[ ucPosition++ ] = 0x0E;
                pcAppCommandBuf[ ucPosition++ ] = 0x0B;
                pcAppCommandBuf[ ucPosition++ ] = 0x04;
                pcAppCommandBuf[ ucPosition++ ] = 0x0C;
                pcAppCommandBuf[ ucPosition++ ] = 0x73;
                pcAppCommandBuf[ ucPosition++ ] = 0x03;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_MATCH_VALUE_1_12
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MATCH;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MATCH_VALUE_1;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF_FREQ_CONTROL_INTE_8
            *********************************************************************/
                vRF4463BufferReset( pcAppCommandBuf, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_FREQ_CONTROL;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x08;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_FREQ_CONTROL_INTE;
                pcAppCommandBuf[ ucPosition++ ] = 0x3C;
                pcAppCommandBuf[ ucPosition++ ] = 0x08;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x22;
                pcAppCommandBuf[ ucPosition++ ] = 0x22;
                pcAppCommandBuf[ ucPosition++ ] = 0x20;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
        #else
            vRF4463SetHeaderParam();
        #endif
    /*********************************************************************
     * GLOBAL_XO_TUNE
     * Configure the internal capacitor frequency tuning bank for the crystal oscillator.
     *
     * Repeated configuration values
    *********************************************************************/
        #ifndef RF4463_DISABLE_HEADER_OVERRIDE
            vRF4463BufferReset( pcAppCommandBuf, 20 );
            ucPosition = RF4463_BUFFER_DATA_POS;
            pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
            pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_GLOBAL;
            pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x01;
            pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_GLOBAL_XO_TUNE;
            pcAppCommandBuf[ ucPosition++ ] = 98;
            #ifndef RF4463_STM32G071
                vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
            #else
                vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
            #endif
        #endif
    /*********************************************************************
     * Global Configuration Settings
     *
     * Repeated configuration values
    *********************************************************************/
        #ifndef RF4463_DISABLE_HEADER_OVERRIDE
            ucPosition = RF4463_BUFFER_DATA_POS;
            pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
            pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_GLOBAL;
            pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x01;
            pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_GLOBAL_CONFIG;
            pcAppCommandBuf[ ucPosition++ ] = 0x20;
            #ifndef RF4463_STM32G071
                vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
            #else
                vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
            #endif
        #endif
    /*********************************************************************
     * RF4463 Preamble
     *
     * Repeated configuration values
    *********************************************************************/
        #ifndef RF4463_DISABLE_HEADER_OVERRIDE
            vRF4463BufferReset( pcAppCommandBuf, 20 );
            ucPosition = RF4463_BUFFER_DATA_POS;
            pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
            pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PREAMBLE;
            pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x09;
            pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PREAMBLE_TX_LENGTH;
            pcAppCommandBuf[ ucPosition++ ] = 0x08;
            pcAppCommandBuf[ ucPosition++ ] = 0x14;
            pcAppCommandBuf[ ucPosition++ ] = 0x00;
            pcAppCommandBuf[ ucPosition++ ] = 0x0F;
            pcAppCommandBuf[ ucPosition++ ] = RF4463_PREAMBLE_FIRST_1 | RF4463_PREAMBLE_LENGTH_BYTES | RF4463_PREAMBLE_STANDARD_1010;
            pcAppCommandBuf[ ucPosition++ ] = 0x00;
            pcAppCommandBuf[ ucPosition++ ] = 0x00;
            pcAppCommandBuf[ ucPosition++ ] = 0x00;
            pcAppCommandBuf[ ucPosition++ ] = 0x00;
            #ifndef RF4463_STM32G071
                vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
            #else
                vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
            #endif
        #endif
    /*********************************************************************
     * Start of main changes from driver 1 to driver 3
    *********************************************************************/
        #ifdef ENABLE_RF4463_DRIVER_CONFIG_MOD
            /*********************************************************************
             * RF4463_PROPERTY_MODEM_MOD_TYPE
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_MOD_TYPE;
                pcAppCommandBuf[ ucPosition++ ] = 0x02;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x07;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x2E;
                pcAppCommandBuf[ ucPosition++ ] = 0xE0;
                pcAppCommandBuf[ ucPosition++ ] = 0x01;
                pcAppCommandBuf[ ucPosition++ ] = 0xC9;
                pcAppCommandBuf[ ucPosition++ ] = 0xC3;
                pcAppCommandBuf[ ucPosition++ ] = 0x80;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF4463_PROPERTY_MODEM_TX_RAMP_DELAY
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x09;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_TX_RAMP_DELAY;
                pcAppCommandBuf[ ucPosition++ ] = 0x01;
                pcAppCommandBuf[ ucPosition++ ] = 0x80;
                pcAppCommandBuf[ ucPosition++ ] = 0x08;
                pcAppCommandBuf[ ucPosition++ ] = 0x03;
                pcAppCommandBuf[ ucPosition++ ] = 0xC0;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x32;
                pcAppCommandBuf[ ucPosition++ ] = 0x20;
                pcAppCommandBuf[ ucPosition++ ] = 0x0C;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF4463_PROPERTY_MODEM_BCR_OSR_1
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x09;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_BCR_OSR_1;
                pcAppCommandBuf[ ucPosition++ ] = 0x03;
                pcAppCommandBuf[ ucPosition++ ] = 0x0D;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0xA7;
                pcAppCommandBuf[ ucPosition++ ] = 0xC6;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x54;
                pcAppCommandBuf[ ucPosition++ ] = 0x02;
                pcAppCommandBuf[ ucPosition++ ] = 0xC2;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF4463_PROPERTY_MODEM_AFC_GEAR
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x07;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_AFC_GEAR;
                pcAppCommandBuf[ ucPosition++ ] = 0x04;
                pcAppCommandBuf[ ucPosition++ ] = 0x23;
                pcAppCommandBuf[ ucPosition++ ] = 0xC0;
                pcAppCommandBuf[ ucPosition++ ] = 0x01;
                pcAppCommandBuf[ ucPosition++ ] = 0x52;
                pcAppCommandBuf[ ucPosition++ ] = 0x30;
                pcAppCommandBuf[ ucPosition++ ] = 0xC0;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF4463_PROPERTY_MODEM_AGC_WINDOW_SIZE
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x09;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_AGC_WINDOW_SIZE;
                pcAppCommandBuf[ ucPosition++ ] = 0x11;
                pcAppCommandBuf[ ucPosition++ ] = 0xAB;
                pcAppCommandBuf[ ucPosition++ ] = 0xAB;
                pcAppCommandBuf[ ucPosition++ ] = 0x80;
                pcAppCommandBuf[ ucPosition++ ] = 0x02;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x2B;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF4463_PROPERTY_MODEM_OOK_CNT1
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0B;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_OOK_CNT1;
                pcAppCommandBuf[ ucPosition++ ] = 0xA4;
                pcAppCommandBuf[ ucPosition++ ] = 0x22;
                pcAppCommandBuf[ ucPosition++ ] = 0x81;
                pcAppCommandBuf[ ucPosition++ ] = 0x81;
                pcAppCommandBuf[ ucPosition++ ] = 0xAA;
                pcAppCommandBuf[ ucPosition++ ] = 0x02;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0x0F;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x18;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF4463_PROPERTY_GROUP_MODEM_CHFLT
             * RF4463_PROPERTY_MODEM_CHFLT_RX1_CHFLT_COE13_7_0
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM_CHFLT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_CHFLT_RX1_CHFLT_COE13_7_0;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0xC4;
                pcAppCommandBuf[ ucPosition++ ] = 0x30;
                pcAppCommandBuf[ ucPosition++ ] = 0x7F;
                pcAppCommandBuf[ ucPosition++ ] = 0xF5;
                pcAppCommandBuf[ ucPosition++ ] = 0xB5;
                pcAppCommandBuf[ ucPosition++ ] = 0xB8;
                pcAppCommandBuf[ ucPosition++ ] = 0xDE;
                pcAppCommandBuf[ ucPosition++ ] = 0x05;
                pcAppCommandBuf[ ucPosition++ ] = 0x17;
                pcAppCommandBuf[ ucPosition++ ] = 0x16;
                pcAppCommandBuf[ ucPosition++ ] = 0x0C;
                pcAppCommandBuf[ ucPosition++ ] = 0x0C;
                pcAppCommandBuf[ ucPosition++ ] = 0x0C;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF4463_PROPERTY_GROUP_MODEM_CHFLT
             * RF4463_PROPERTY_MODEM_CHFLT_RX1_CHFLT_COE1_7_0
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM_CHFLT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_CHFLT_RX1_CHFLT_COE13_7_0;
                pcAppCommandBuf[ ucPosition++ ] = 0x03;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x15;
                pcAppCommandBuf[ ucPosition++ ] = 0xFF;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0xA2;
                pcAppCommandBuf[ ucPosition++ ] = 0xA0;
                pcAppCommandBuf[ ucPosition++ ] = 0x97;
                pcAppCommandBuf[ ucPosition++ ] = 0x8A;
                pcAppCommandBuf[ ucPosition++ ] = 0x79;
                pcAppCommandBuf[ ucPosition++ ] = 0x66;
                pcAppCommandBuf[ ucPosition++ ] = 0x66;
                pcAppCommandBuf[ ucPosition++ ] = 0x66;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF4463_PROPERTY_GROUP_MODEM_CHFLT
             * RF4463_PROPERTY_MODEM_CHFLT_RX2_CHFLT_COE7_7_0
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM_CHFLT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_CHFLT_RX1_CHFLT_COE13_7_0;
                pcAppCommandBuf[ ucPosition++ ] = 0x52;
                pcAppCommandBuf[ ucPosition++ ] = 0x3F;
                pcAppCommandBuf[ ucPosition++ ] = 0x2E;
                pcAppCommandBuf[ ucPosition++ ] = 0x1F;
                pcAppCommandBuf[ ucPosition++ ] = 0x14;
                pcAppCommandBuf[ ucPosition++ ] = 0x0B;
                pcAppCommandBuf[ ucPosition++ ] = 0x06;
                pcAppCommandBuf[ ucPosition++ ] = 0x02;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                pcAppCommandBuf[ ucPosition++ ] = 0x00;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
            /*********************************************************************
             * RF4463_PROPERTY_GROUP_SYNTH
             * RF4463_PROPERTY_SYNTH_PFDCP_CPFF
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM_CHFLT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0B;
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_CHFLT_RX1_CHFLT_COE13_7_0;
                pcAppCommandBuf[ ucPosition++ ] = 0x2C;
                pcAppCommandBuf[ ucPosition++ ] = 0x0E;
                pcAppCommandBuf[ ucPosition++ ] = 0x0B;
                pcAppCommandBuf[ ucPosition++ ] = 0x04;
                pcAppCommandBuf[ ucPosition++ ] = 0x0C;
                pcAppCommandBuf[ ucPosition++ ] = 0x73;
                pcAppCommandBuf[ ucPosition++ ] = 0x03;
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif
        #endif /* ENABLE_RF4463_DRIVER_CONFIG_MOD */
    /*********************************************************************
     * RF4463 Sync Bytes
    *********************************************************************/
        vRF4463SetNetwork( xRF4463Configuration.syncword );
    /*********************************************************************
     * Set Packet configuration and behaviour
    *********************************************************************/
        #if defined( RF4463_PACKET_VARIABLE_LENGTH ) && !defined( RF4463_PACKET_FIXED_LENGTH )
            /*********************************************************************
             * RF4463 Set CRC
             * Settings change from configuration header
            *********************************************************************/
                #ifndef RF4463_DISABLE_HEADER_PACKET_CONFIG_OVERRIDE
                    vRF4463BufferReset( pcAppCommandBuf, 20 );
                    ucPosition = RF4463_BUFFER_DATA_POS;
                    pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                    pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                    pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x01;
                    pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_CRC_CONFIG;
                    pcAppCommandBuf[ ucPosition++ ] = RF4463_CRC_SEED_ALL_1S | RF4463_CRC_ITU_T;
                    #ifndef RF4463_STM32G071
                        vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                    #else
                        vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                    #endif /* RF4463_STM32G071 */

                    vRF4463BufferReset( pcAppCommandBuf, 20 );
                    ucPosition = RF4463_BUFFER_DATA_POS;
                    pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                    pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                    pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x01;
                    pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_CONFIG1;
                    pcAppCommandBuf[ ucPosition++ ] = RF4463_CRC_ENDIAN;
                    #ifndef RF4463_STM32G071
                        vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                    #else
                        vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                    #endif /* RF4463_STM32G071 */

                    vRF4463BufferReset( pcAppCommandBuf, 20 );
                    ucPosition = RF4463_BUFFER_DATA_POS;
                    pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                    pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                    pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x05;
                    pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_LEN;
                    pcAppCommandBuf[ ucPosition++ ] = RF4463_IN_FIFO | RF4463_DST_FIELD_ENUM_2;
                    pcAppCommandBuf[ ucPosition++ ] = RF4463_SRC_FIELD_ENUM_1;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = 0x20;
                    pcAppCommandBuf[ ucPosition++ ] = 0x05;
                    #ifndef RF4463_STM32G071
                        vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                    #else
                        vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                    #endif /* RF4463_STM32G071 */
                #endif /* RF4463_DISABLE_HEADER_PACKET_CONFIG_OVERRIDE */
            /*********************************************************************
             * RF4463 Set Length of Field
             * Settings change from configuration header
            *********************************************************************/
                #ifndef RF4463_DISABLE_HEADER_PACKET_FIELD_OVERRIDE
                    vRF4463BufferReset( pcAppCommandBuf, 20 );
                    ucPosition = RF4463_BUFFER_DATA_POS;
                    pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                    pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                    pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
                    pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_FIELD_1_LENGTH_12_8;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = 0x01;
                    pcAppCommandBuf[ ucPosition++ ] = RF4463_FIELD_CONFIG_PN_START;
                    // pcAppCommandBuf[ ucPosition++ ] = RF4463_FIELD_CONFIG_PN_START | RF4463_FIELD_CONFIG_WHITEN;
                    // pcAppCommandBuf[ ucPosition++ ] = RF4463_FIELD_CONFIG_MANCH;
                    pcAppCommandBuf[ ucPosition++ ] = RF4463_FIELD_CONFIG_CRC_START | RF4463_FIELD_CONFIG_SEND_CRC | RF4463_FIELD_CONFIG_CHECK_CRC | RF4463_FIELD_CONFIG_CRC_ENABLE;
                    // PKT_FIELD_2_LENGTH
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = RF4463_MAX_BUFFER_LENGTH; // 50 - og value
                    pcAppCommandBuf[ ucPosition++ ] = RF4463_FIELD_CONFIG_PN_START;
                    // pcAppCommandBuf[ ucPosition++ ] = RF4463_FIELD_CONFIG_PN_START | RF4463_FIELD_CONFIG_WHITEN;
                    // pcAppCommandBuf[ ucPosition++ ] = RF4463_FIELD_CONFIG_MANCH;
                    pcAppCommandBuf[ ucPosition++ ] = RF4463_FIELD_CONFIG_CRC_START | RF4463_FIELD_CONFIG_SEND_CRC | RF4463_FIELD_CONFIG_CHECK_CRC | RF4463_FIELD_CONFIG_CRC_ENABLE;
                    // PKT_FIELD_3_LENGTH
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    #ifndef RF4463_STM32G071
                        vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                    #else
                        vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                    #endif /* RF4463_STM32G071 */

                    vRF4463BufferReset( pcAppCommandBuf, 20 );

                    ucPosition = RF4463_BUFFER_DATA_POS;
                    pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                    pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                    pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x08;
                    pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_FIELD_4_LENGTH_12_8;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    pcAppCommandBuf[ ucPosition++ ] = 0x00;
                    #ifndef RF4463_STM32G071
                        vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                    #else
                        vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                    #endif /* RF4463_STM32G071 */
                #endif /* RF4463_DISABLE_HEADER_PACKET_CONFIG_OVERRIDE */
        #elif !defined( RF4463_PACKET_VARIABLE_LENGTH ) && defined( RF4463_PACKET_FIXED_LENGTH )
            /*********************************************************************
             * RF4463 Set Packet Configuration
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x03; // Total 3 Parameters
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_LEN;
                pcAppCommandBuf[ ucPosition++ ] = 0x00; // Length Field = LSB,  length Only 1 byte,length Not put In FiFo, fixed packet length mode
                pcAppCommandBuf[ ucPosition++ ] = 0x00; // Used for variable packet length mode, which defines which Field contains length Filed
                pcAppCommandBuf[ ucPosition++ ] = 0x00; // Used for variable packet length mode,    adjust the length of variable packet length
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif /* RF4463_STM32G071 */
            /*********************************************************************
             * RF4463 Set Tx and Rx Threshold
             * Tx Threshold - Almost Empty
             * Rx Threshold - Almost Full
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT;
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x02; // Total 3 Parameters
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_TX_THRESHOLD;
                pcAppCommandBuf[ ucPosition++ ] = RF4463_MAX_BUFFER_LENGTH; // Used for variable packet length mode, which defines which Field contains length Filed
                pcAppCommandBuf[ ucPosition++ ] = RF4463_MAX_BUFFER_LENGTH; // Used for variable packet length mode,    adjust the length of variable packet length
                RF4463_MAX_PAYLOAD_LENGTH
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif /* RF4463_STM32G071 */
            /*********************************************************************
             * RF4463 Set Length of Field
            *********************************************************************/
                memset( pcAppCommandBuf, 0, 20 );
                ucPosition = RF4463_BUFFER_DATA_POS;
                pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
                pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT; // 0x120d
                pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x0C; // Total 12 Parameters
                pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_FIELD_1_LENGTH_12_8; // 0x120d
                /*********************************************************************
                 * Field 1
                *********************************************************************/
                    pcAppCommandBuf[ ucPosition++ ] = 0x00; // 0x0D  // Field 1 length (?4?)                             // 0x0D
                    pcAppCommandBuf[ ucPosition++ ] = RF4463_MAX_BUFFER_LENGTH; // / 2 ;  // field 1 length, (? 8?), ?Total14???        // 0x0E
                    pcAppCommandBuf[ ucPosition++ ] = RF4463_FIELD_CONFIG_PN_START; //0x04 0x0F  // field 1 Is not 4FSK,manchest, whiting, PN9,       // 0x0F
                    pcAppCommandBuf[ ucPosition++ ] = RF4463_FIELD_CONFIG_CRC_START | RF4463_FIELD_CONFIG_SEND_CRC | RF4463_FIELD_CONFIG_CHECK_CRC | RF4463_FIELD_CONFIG_CRC_ENABLE; // field 1 crc enble, check enbale, There are also launchingCRC,cRC?Seed ?CRC_seed?????
                /*********************************************************************
                 * Field 2
                *********************************************************************/
                    pcAppCommandBuf[ ucPosition++ ] = 0x00; // 0x11  // field 2 length(?4?)
                    pcAppCommandBuf[ ucPosition++ ] = 0x00; // 0x12  // field 2 length, (? 8?), length = 0 Means thisfield did not use
                    pcAppCommandBuf[ ucPosition++ ] = 0x00; // 0x13  // field 2 Is not 4FSK,manchest, whiting, PN9
                    pcAppCommandBuf[ ucPosition++ ] = 0x00; // 0x14  // field 2 ?CRCSet up
                /*********************************************************************
                 * Field 3
                *********************************************************************/
                    pcAppCommandBuf[ ucPosition++ ] = 0x00; // 0x15  // field 3 length, (? 8?), length = 0 Means thisfield did not use
                    pcAppCommandBuf[ ucPosition++ ] = 0x00; // 0x16  // field 3 length, (? 8?), length = 0 Means thisfield did not use
                    pcAppCommandBuf[ ucPosition++ ] = 0x00; // 0x17  // field 3 Is not 4FSK,manchest, whiting, PN9
                    pcAppCommandBuf[ ucPosition++ ] = 0x00; // 0x18  // field 3 ?CRCSet up
                #ifndef RF4463_STM32G071
                    vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
                #else
                    vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
                #endif /* RF4463_STM32G071 */
        #endif /* RF4463_PACKET_VARIABLE_LENGTH && RF4463_PACKET_FIXED_LENGTH */
    /*********************************************************************
     * RSSI compensation value
    *********************************************************************/
        #ifndef RF4463_DISABLE_HEADER_RSSI_OVERRIDE
            memset( pcAppCommandBuf, 0, 20 );
            ucPosition = RF4463_BUFFER_DATA_POS;
            pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
            pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
            pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x01;
            pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_RSSI_COMP;
            pcAppCommandBuf[ ucPosition++ ] = 0x40;  //  rssi Reading deviation, the difference with the true value
            #ifndef RF4463_STM32G071
                vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
            #else
                vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
            #endif /* RF4463_STM32G071 */

            memset( pcAppCommandBuf, 0, 20 );
            ucPosition = RF4463_BUFFER_DATA_POS;
            pcAppCommandBuf[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
            pcAppCommandBuf[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_MODEM;
            pcAppCommandBuf[ RF4463_BUFFER_LENGTH_POS ] = 0x01;
            pcAppCommandBuf[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_MODEM_RSSI_CONTROL;
            pcAppCommandBuf[ ucPosition++ ] = 0x01;
            #ifndef RF4463_STM32G071
                vRF4463SPIWrite( pcAppCommandBuf, ucPosition );
            #else
                vRF4463SPIWrite( &pcAppCommandBuf, ucPosition );
            #endif /* RF4463_STM32G071 */
        #endif /* RF4463_DISABLE_HEADER_RSSI_OVERRIDE */
    /*********************************************************************
     * Set Interrupts
    *********************************************************************/
        vRF4463SetInterrupts();
    /*********************************************************************
     * Set Tx Power
    *********************************************************************/
        vRF4463SetTxPower( xRF4463Configuration.tx_power );
    /*********************************************************************
     * Set Packet Match
    *********************************************************************/
        #ifdef RF4463_ENABLE_MATCH
            #ifdef RF4463_HARDCODE_MATCH
                ucRF4463SetPacketMatch( &pcMatch, &pcMask );
            #else
                ucRF4463SetPacketMatch( xRF4463Configuration.Match, xRF4463Configuration.Mask );
            #endif
        #endif
    /*********************************************************************
     * Set Frequency
    *********************************************************************/
        #ifndef RF4463_DISABLE_FREQUENCY_FIX
            vRF4463InitFreq( xRF4463Configuration.frequency, RF4463_WIDTH_250KHz );
        #endif
    /*********************************************************************
     * Clear Interrupt and exit setup
    *********************************************************************/
        vRF4463ClearInterrupts();
        return 0;
}

/*********************************************************************
 *
*********************************************************************/
#ifdef RF4463_USE_SET_CONFIGURATION
void vRF4463SetHeaderParam(){
    uint8_t pcAppCommandBuffer[ 20 ];
    uint16_t usPosition = 0;
    uint16_t usParametersLength = 0;
    uint8_t ucCommandLength = 0;
    uint8_t ucCounter = 0;

    usParametersLength = sizeof( RF4463_CONFIGURATION_DATA );
    // printf( "[ RF4463 ] Parameters Length: %d\r\n", usParametersLength );

    while( usPosition < usParametersLength ){
        vRF4463BufferReset( pcAppCommandBuffer, 20 );
        ucCommandLength = RF4463_CONFIGURATION_DATA[ usPosition++ ];
        // printf( "[ RF4463 ] Command Length: %d\r\n", ucCommandLength );
        for( ucCounter = 0; ucCounter < ucCommandLength; ucCounter++ ){
            pcAppCommandBuffer[ ucCounter ] = RF4463_CONFIGURATION_DATA[ usPosition++ ];
        }
        vRF4463SPIWrite( pcAppCommandBuffer, ucCommandLength );
    }
}
#endif

/*********************************************************************
 *
*********************************************************************/
void vRF4463SDNReset( uint32_t ucSDNPin ){
    gpio_put( ucSDNPin, 1 );
    sleep_ms( 1 );
    gpio_put( ucSDNPin, 0 );
    sleep_ms( 20 );

    vRF4463SPIWrite( ( uint8_t * )RF_POWER_UP_data, sizeof( RF_POWER_UP_data ) );
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463InitFreq( uint32_t ulFreqMhz, uint32_t ulChannelWidthKhz ){
    uint8_t ucOUTDIV, ucDIV, ucVCO, ucINTE, ucBAND = 0;
    uint8_t ucFRAC_2, ucFRAC_1, ucFRAC_0 = 0;
    uint32_t ulFRAC, ulData, ulFrequency = 0;
    uint8_t pcBuffer[ 20 ] = { 0x00 };
    /*********************************************************************
     *
    *********************************************************************/
        ulFrequency = ( ulFreqMhz * FREQUENCY_MHz_MULTIPLIER );
        /*********************************************************************
         * 760~1050
        *********************************************************************/
            if( ulFreqMhz >= RF4463_FREQ_760MHz ){
                ucOUTDIV = 4;
                ucBAND = 0;
                ucVCO = 0xFF;
            }
        /*********************************************************************
         * 546~759.9
        *********************************************************************/
            else if( ulFreqMhz >= RF4463_FREQ_546MHz ){
                ucOUTDIV = 6;
                ucBAND   = 1;
                ucVCO    = 0xFE;
            }
        /*********************************************************************
         * 385~545.9
        *********************************************************************/
            else if( ulFreqMhz >= RF4463_FREQ_385MHz ){
                ucOUTDIV = 8;
                ucBAND   = 2;
                ucVCO    = 0xFE;
            }
        /*********************************************************************
         * 273~384.9
        *********************************************************************/
            else if( ulFreqMhz >= RF4463_FREQ_273MHz ){
                ucOUTDIV = 12;
                ucBAND   = 3;
                ucVCO    = 0xFD;
            }
        /*********************************************************************
         * 194~272.9
        *********************************************************************/
            else if( ulFreqMhz >= RF4463_FREQ_194MHz ){
                ucOUTDIV = 16;
                ucBAND   = 4;
                ucVCO    = 0xFC;
            }
        /*********************************************************************
         * 142~193.9
        *********************************************************************/
            else{
                ucOUTDIV = 24;
                ucBAND   = 5;
                ucVCO    = 0xFA;
            }
    /*********************************************************************
     *
    *********************************************************************/
        ucDIV = ucOUTDIV / 2;

        ulData = ( ulFrequency * ucDIV ) / 3;          // ucDIV = 88.5 = 88   //// UCdiv = 2
        ucINTE = ( ulData / 100000 ) - 1;               // ulData = 6 000 000  //// ULdATA = 9 000 000
        ulFRAC = ( ulData - ( ucINTE + 1 ) * 100000 ) * 16384 / 3125;         // ulFRAC = 15 728 640
        ulFRAC = ulFRAC + 0x80000;

        ucFRAC_0 = ulFRAC;
        ulFRAC >>= 8;
        ucFRAC_1 = ulFRAC;
        ulFRAC >>= 8;
        ucFRAC_2 = ulFRAC;
    /*********************************************************************
     * Set Frequency command
    *********************************************************************/
        pcBuffer[ 0 ] = RF4463_CMD_SET_PROPERTY;   //0x11; // SET property MODEM_CLKGEN_BAND
        pcBuffer[ 1 ] = RF4463_PROPERTY_GROUP_MODEM; // Group  // 0x2051
        pcBuffer[ 2 ] = 0x01; // Num Arguments // Total 1 Parameters
        pcBuffer[ 3 ] = RF4463_PROPERTY_MODEM_CLKGEN_BAND; // Number  // 0x2051
        pcBuffer[ 4 ] = 0x08 | ucBAND;  // Argumento  //  high performance mode , clk outpu = osc /4
        #ifndef RF4463_STM32G071
            vRF4463SPIWrite( pcBuffer, 5 );
        #else
            vRF4463SPIWrite( &pcBuffer, 5 );
        #endif
    /*********************************************************************
     *
    *********************************************************************/
        pcBuffer[ 0 ] = RF4463_CMD_SET_PROPERTY;  // 0x11;
        pcBuffer[ 1 ] = RF4463_PROPERTY_GROUP_FREQ_CONTROL;          // 0x4000         FREQ_CONTROL_INTE
        pcBuffer[ 2 ] = 0x08;          // Total 8 Parameters
        pcBuffer[ 3 ] = RF4463_PROPERTY_FREQ_CONTROL_INTE; // 0x4000
        pcBuffer[ 4 ] = ucINTE;        // default value
        pcBuffer[ 5 ] = ucFRAC_2;      // defaul value
        pcBuffer[ 6 ] = ucFRAC_1;      // default value
        pcBuffer[ 7 ] = ucFRAC_0;      // frac ,from WDS
        /*********************************************************************
         * Channel Width
        *********************************************************************/
            if( ulChannelWidthKhz == RF4463_WIDTH_250KHz ){
                pcBuffer[ 8 ]  = RF4463_CHANNEL_STEP_250K_step1;   // channel step1  from wds  // al parecer aqui esta el ancho del canal
                pcBuffer[ 9 ]  = RF4463_CHANNEL_STEP_250K_step0;   // channel step0  from wds
            }
            else if( ulChannelWidthKhz == RF4463_WIDTH_500KHz ){
                pcBuffer[ 8 ]  = RF4463_CHANNEL_STEP_500K_step1;   // channel step1  from wds  // al parecer aqui esta el ancho del canal
                pcBuffer[ 9 ]  = RF4463_CHANNEL_STEP_500K_step0;   // channel step0  from wds
            }
        pcBuffer[ 10 ] = 0x20;  //  from wds Without this Values
        pcBuffer[ 11 ] = ucVCO;    // from wds Without this Values

        #ifndef RF4463_STM32G071
            vRF4463SPIWrite( pcBuffer, 12 );
        #else
            vRF4463SPIWrite( &pcBuffer, 12 );
        #endif
}

/*********************************************************************
 *
*********************************************************************/
uint8_t ucRF4463SetGPIO( uint8_t ucOpMode ){
    uint8_t pcBuffer[ 7 ] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };

    pcBuffer[ 0 ] = RF4463_CMD_GPIO_PIN_CFG;

    switch( ucOpMode ){
        case RF4463_GPIO_TX_NORMAL:
        case RF4463_GPIO_RX_NORMAL:
            pcBuffer[ RF4463_GPIO_GPIO0_POS ] = RF4463_GPIO_MODE_HIGH;
            pcBuffer[ RF4463_GPIO_GPIO1_POS ] = RF4463_GPIO_MODE_LOW;
            // pcBuffer[ RF4463_GPIO_GPIO0_POS ] = RF4463_GPIO_MODE_NO_CHANGE;
            // pcBuffer[ RF4463_GPIO_GPIO1_POS ] = RF4463_GPIO_MODE_NO_CHANGE;
            break;
        case RF4463_GPIO_TX_TEST:
            pcBuffer[ RF4463_GPIO_GPIO0_POS ] = RF4463_GPIO_MODE_HIGH;
            pcBuffer[ RF4463_GPIO_GPIO1_POS ] = RF4463_GPIO_MODE_HIGH;
            break;
        case RF4463_GPIO_RX_TEST:
            pcBuffer[ RF4463_GPIO_GPIO0_POS ] = RF4463_GPIO_MODE_LOW;
            pcBuffer[ RF4463_GPIO_GPIO1_POS ] = RF4463_GPIO_MODE_RX_DATA;
            break;
        case RF4463_GPIO_RF_OFF:
            pcBuffer[ RF4463_GPIO_GPIO0_POS ] = RF4463_GPIO_MODE_LOW;
            pcBuffer[ RF4463_GPIO_GPIO1_POS ] = RF4463_GPIO_MODE_LOW;
            break;
        default:
            printf( "[ RF4463 ] Undefined OpMode\r\n" );
            return 1;
    }

    pcBuffer[ RF4463_GPIO_GPIO2_POS ]   = RF4463_GPIO_MODE_RX_STATE;
    pcBuffer[ RF4463_GPIO_GPIO3_POS ]   = RF4463_GPIO_MODE_TX_STATE;
    // pcBuffer[ RF4463_GPIO_NIRQ_POS ]    = RF4463_GPIO_MODE_HOP_TABLE_WRAPPED;
    pcBuffer[ RF4463_GPIO_NIRQ_POS ]    = RF4463_NIRQ_MODE_INTERRUPT_SIGNAL;
    pcBuffer[ RF4463_GPIO_SDO_POS ]     = RF4463_GPIO_MODE_SPI_DATA_OUT;

    #ifndef RF4463_STM32G071
        vRF4463SPIWrite( pcBuffer, 7 );
    #else
        vRF4463SPIWrite( &pcBuffer, 7 );
    #endif

    return 0;
}

/*********************************************************************
 *
*********************************************************************/
uint8_t ucRF4463SetPacketMatch( uint8_t * pcMatch, uint8_t * pcMask ){
    uint8_t pcBuffer[ 20 ];
    /*********************************************************************
     *
    *********************************************************************/
        if( !pcMatch || !pcMask ){
            return 1;
        }
    /*********************************************************************
     *
    *********************************************************************/
        pcBuffer[ RF4463_BUFFER_CMD_POS ] = RF4463_CMD_SET_PROPERTY;
        pcBuffer[ RF4463_BUFFER_GROUP_POS ] = RF4463_PROPERTY_GROUP_MATCH;
        pcBuffer[ RF4463_BUFFER_LENGTH_POS ] = 0x0C;
        pcBuffer[ RF4463_BUFFER_INDEX_POS ] = RF4463_PROPERTY_MATCH_VALUE_1;

        pcBuffer[ 4 ] = pcMatch[ 0 ]; // match 1 Value of
        pcBuffer[ 5 ] = pcMask[ 0 ]; // match 1 mask
        pcBuffer[ 6 ] = 0x40; //  0x40; // enable match 1, match 1 The distance between the value of and the synchronization word, 0 = match
        pcBuffer[ 7 ] = pcMatch[ 1 ]; // match 2 Value of
        pcBuffer[ 8 ] = pcMask[ 1 ]; // match 2 mask
        pcBuffer[ 9 ] = 0x01; // enable match 2, match 2 The distance between the value of and the synchronization word  , and function
        pcBuffer[ 10 ] = pcMatch[ 2 ]; // match 3 Value of
        pcBuffer[ 11 ] = pcMask[ 2 ]; // match 3 mask
        pcBuffer[ 12 ] = 0x02; // enable match 3, match 3 The distance between the value of and the synchronization word    and function
        pcBuffer[ 13 ] = pcMatch[ 3 ]; // match 4 Value of
        pcBuffer[ 14 ] = pcMask[ 3 ]; // match 4 mask
        pcBuffer[ 15 ] = 0x03; // enable match 4, match 4 The distance between the value of and the synchronization word    and function

        #ifndef RF4463_STM32G071
            vRF4463SPIWrite( pcBuffer, 16 );
        #else
            vRF4463SPIWrite( &pcBuffer, 16 );
        #endif
    /*********************************************************************
     *
    *********************************************************************/
        return 0;
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463ResetFIFO(){
    uint8_t pcBuffer[ 3 ] = { 0x00, 0x00, 0x00 };

    pcBuffer[ RF4463_BUFFER_CMD_POS ] = RF4463_CMD_FIFO_INFO;
    pcBuffer[ 1 ] = 0x03;

    #ifndef RF4463_STM32G071
        vRF4463SPIWrite( pcBuffer, 2 );
    #else
        vRF4463SPIWrite( &pcBuffer, 2 );
    #endif
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463ClearInterrupts(){
    uint8_t pcBuffer[ 5 ] = { 0x00, 0x00, 0x00, 0x00, 0x00 };

    pcBuffer[ RF4463_BUFFER_CMD_POS ] = RF4463_CMD_GET_INT_STATUS;
    pcBuffer[ 1 ] = 0x00; // Packet Handler interrupt pending
    pcBuffer[ 2 ] = 0x00; // Chip interrupt pending
    pcBuffer[ 3 ] = 0x00; // Modem interrupt pending

    #ifndef RF4463_STM32G071
        vRF4463SPIWrite( pcBuffer, 4 );
    #else
        vRF4463SPIWrite( &pcBuffer, 4 );
    #endif
}

/*********************************************************************
 * This function needs to be evaluated since the parameters sent
 * to RF are not valid neither the response length, according to
 * RF documentation response is at least 2 bytes in length and the
 * request changes the packet length
*********************************************************************/
void vRF4463GetPacketInfo(){
    uint8_t pcBuffer[ 5 ] = { 0x00, 0x00, 0x00, 0x00, 0x00 };

    pcBuffer[ RF4463_BUFFER_CMD_POS ] = RF4463_CMD_PACKET_INFO;
    pcBuffer[ 1 ] = 0U; // Clear Packet Handler pending
    pcBuffer[ 2 ] = 0U; // Clear Modem pending
    pcBuffer[ 3 ] = 0U; // Clear Chip pending

    #ifndef RF4463_STM32G071
        vRF4463SPIWrite( pcBuffer, 4 );
    #else
        vRF4463SPIWrite( &pcBuffer, 4 );
    #endif
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463GetFIFOInfo(){
    uint8_t pcBuffer[ 3 ] = { 0x00, 0x00, 0x00 };
    uint8_t ucCounter = 0;

    vRF4463ResetRxBuffer();

    pcBuffer[ RF4463_BUFFER_CMD_POS ] = RF4463_CMD_FIFO_INFO;
    pcBuffer[ 1 ] = 0x00;
    #ifndef RF4463_STM32G071
        vRF4463SPISendCmdGetResponse( pcBuffer, 2, pcRF4463SPIRxBuffer, 3 );
    #else
        vRF4463SPISendCmdGetResponse( &pcBuffer, 2, pcRF4463SPIRxBuffer, 3 );
    #endif

    #ifdef DEBUG_RF4463_FIFO_INFO
        for( ucCounter = 0; ucCounter < 3; ucCounter++ ){
            printf( "[ RF4463 ] FIFO Info - Pos: %d Data: 0x%02X\r\n", ucCounter, pcRF4463SPIRxBuffer[ ucCounter ] );
        }
    #endif
}

/*********************************************************************
 *
*********************************************************************/
uint8_t ucRF4463GetPartInfo( uint16_t usModel ){
    uint8_t pcBuffer[ 9 ] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
    uint16_t usPartInfo = 0;

    vRF4463ResetRxBuffer();

    pcBuffer[ RF4463_BUFFER_CMD_POS ] = RF4463_CMD_PART_INFO;

    #ifndef RF4463_STM32G071
        vRF4463SPISendCmdGetResponse( pcBuffer, 1, pcRF4463SPIRxBuffer, 9 );
    #else
        vRF4463SPISendCmdGetResponse( &pcBuffer, 1, pcRF4463SPIRxBuffer, 9 );
    #endif

    usPartInfo = ( pcRF4463SPIRxBuffer[ 2 ] << 8 ) | pcRF4463SPIRxBuffer[ 3 ];
    // usPartInfo = ( pcRF4463SPIRxBuffer[ 1 ] << 8 ) | pcRF4463SPIRxBuffer[ 2 ];

    #ifdef DEBUG_RF4463_PART_INFO
        for( ucCounter = 0; ucCounter < 9; ucCounter++ ){
            printf( "[ RF4463 ] Part Info - Position: %d Data: 0x%02X\r\n", ucCounter, pcRF4463SPIRxBuffer[ ucCounter ] );
        }
    #endif

    if( usPartInfo != usModel ){
        return 1;
    }

    return 0;
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463SetInterrupts(){
    uint8_t pcBuffer[ 8 ] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };

    pcBuffer[ RF4463_BUFFER_CMD_POS ]       = RF4463_CMD_SET_PROPERTY;
    pcBuffer[ RF4463_BUFFER_GROUP_POS ]     = RF4463_PROPERTY_GROUP_INT_CTL;
    pcBuffer[ RF4463_BUFFER_LENGTH_POS ]    = 0x03;
    pcBuffer[ RF4463_BUFFER_INDEX_POS ]     = RF4463_PROPERTY_INT_CTL_ENABLE;
    pcBuffer[ 4 ] = RF4463_PH_INT_STATUS_EN | RF4463_CHIP_INT_STATUS_EN;
    pcBuffer[ 5 ] = RF4463_INT_STATUS_PH_PACKET_SENT | RF4463_INT_STATUS_PH_PACKET_RX | RF4463_INT_STATUS_PH_CRC_ERROR;
    pcBuffer[ 6 ] = 0U; // No modem interrupts enabled
    pcBuffer[ 7 ] = RF4463_INT_STATUS_CHIP_FIFO_UNDERFLOW_OVERFLOW_ERROR; // No chip interrupts enabled

    // pcBuffer[ 4 ] = 0x07;
    // pcBuffer[ 5 ] = 0xFF;
    // pcBuffer[ 6 ] = 0xFF; // No modem interrupts enabled
    // pcBuffer[ 7 ] = 0xFF; // No chip interrupts enabled

    #ifndef RF4463_STM32G071
        vRF4463SPIWrite( pcBuffer, 8 );
    #else
        vRF4463SPIWrite( &pcBuffer, 8 );
    #endif
}

/*********************************************************************
 *
*********************************************************************/
uint16_t usRF4463GetInterrupt(){
    uint8_t pcBuffer[ 10 ];

    #ifdef DEBUG_RF4463_IRQ_VALUES
        uint8_t ucCounter = 0;
    #endif

    // printf( "[ RF4463 ] Get Interrupt\r\n" );

    pcBuffer[ RF4463_BUFFER_CMD_POS ] = RF4463_CMD_GET_INT_STATUS;
    #ifndef DEBUG_RF4463_IRQ_VALUES
        pcBuffer[ 1 ] = 0x00; // Packet Handler interrupt pending
        pcBuffer[ 2 ] = 0x00; // Chip interrupt pending
        pcBuffer[ 3 ] = 0x00; // Modem interrupt pending
    #else
        // Values with 1
        pcBuffer[ 1 ] = 0xFF; // Packet Handler interrupt pending
        pcBuffer[ 2 ] = 0xFF; // Chip interrupt pending
        pcBuffer[ 3 ] = 0xFF; // Modem interrupt pending
    #endif /* DEBUG_RF4463_IRQ_VALUES */

    vRF4463ResetRxBuffer();

    #ifndef RF4463_STM32G071
        vRF4463SPISendCmdGetResponse( pcBuffer, 4, pcRF4463SPIRxBuffer, 8 );
    #else
        vRF4463SPISendCmdGetResponse( &pcBuffer, 4, pcRF4463SPIRxBuffer, 8 );
    #endif

    #ifdef DEBUG_RF4463_IRQ_VALUES
        for( ucCounter = 0; ucCounter < 8; ucCounter++ ){
            printf( "[ RF4463 ] INT = Position: %d Value: 0x%02X\r\n", ucCounter, pcRF4463SPIRxBuffer[ ucCounter ] );
        }
        vRF4463ClearInterrupts();
    #endif /* DEBUG_RF4463_IRQ_VALUES */

    if( ( pcRF4463SPIRxBuffer[ RF4463_INT_STATUS_PH_STATUS ] & RF4463_INT_STATUS_PH_CRC_ERROR ) == RF4463_INT_STATUS_PH_CRC_ERROR ){
        printf( "[ RF4463 ] PH Int CRC Error\r\n" );
    }

    if( ( pcRF4463SPIRxBuffer[ 4 ] & RF4463_INT_STATUS_PH_PACKET_RX ) == RF4463_INT_STATUS_PH_PACKET_RX ){
        // printf( "[ RF4463 ] Packet Rx Interrupt\r\n" );
        return ( ( uint16_t )RF4463_INT_STATUS_PH_INT_STATUS << 8 ) | RF4463_INT_STATUS_PH_PACKET_RX;
    }
    else if( ( ( pcRF4463SPIRxBuffer[ 4 ] & RF4463_INT_STATUS_PH_RX_FIFO_ALMOST_FULL ) == RF4463_INT_STATUS_PH_RX_FIFO_ALMOST_FULL ) || ( ( pcRF4463SPIRxBuffer[ 4 ] & RF4463_INT_STATUS_PH_TX_FIFO_ALMOST_EMPTY ) == RF4463_INT_STATUS_PH_TX_FIFO_ALMOST_EMPTY ) ){
        // printf( "[ RF4463 ] Almost Full/Empty Interrupt\r\n" );
        if( xRF4463Flag.is_tx == 0 ){
            return ( ( uint16_t )RF4463_INT_STATUS_PH_INT_STATUS << 8 ) | RF4463_INT_STATUS_PH_PACKET_RX;
        }
        else{
            xRF4463Flag.is_tx = 0;
            return ( ( uint16_t )RF4463_INT_STATUS_PH_INT_STATUS << 8 ) | RF4463_INT_STATUS_PH_PACKET_SENT;
        }
    }
    else if( ( pcRF4463SPIRxBuffer[ 4 ] & RF4463_INT_STATUS_PH_PACKET_SENT ) == RF4463_INT_STATUS_PH_PACKET_SENT ){
        // printf( "[ RF4463 ] Packet Sent Interrupt\r\n" );
        xRF4463Flag.is_tx = 0;
        return ( ( uint16_t )RF4463_INT_STATUS_PH_INT_STATUS << 8 ) | RF4463_INT_STATUS_PH_PACKET_SENT;
    }
    else if( ( pcRF4463SPIRxBuffer[ 8 ] & RF4463_INT_STATUS_CHIP_FIFO_UNDERFLOW_OVERFLOW_ERROR ) == RF4463_INT_STATUS_CHIP_FIFO_UNDERFLOW_OVERFLOW_ERROR ){
        // printf( "[ RF4463 ] FIFO Overflow Underflow\r\n" );
        return ( ( uint16_t )RF4463_INT_STATUS_CHIP_INT_STATUS << 8 ) | RF4463_INT_STATUS_CHIP_FIFO_UNDERFLOW_OVERFLOW_ERROR;
    }
    else if( ( pcRF4463SPIRxBuffer[ 8 ] & RF4463_INT_STATUS_CHIP_CHIP_READY ) == RF4463_INT_STATUS_CHIP_CHIP_READY ){
        // printf( "[ RF4463 ] Chip ready\r\n" );
        return ( ( uint16_t )RF4463_INT_STATUS_CHIP_INT_STATUS << 8 ) | RF4463_INT_STATUS_CHIP_CHIP_READY;
        }
    else if( ( pcRF4463SPIRxBuffer[ 8 ] & RF4463_INT_STATUS_CHIP_STATE_CHANGE ) == RF4463_INT_STATUS_CHIP_STATE_CHANGE ){
        // printf( "[ RF4463 ] State Change\r\n" );
        return ( ( uint16_t )RF4463_INT_STATUS_CHIP_INT_STATUS << 8 ) | RF4463_INT_STATUS_CHIP_STATE_CHANGE;
    }
    else{
        // printf( "[ RF4463 ] Unhandled\r\n" );
        return 0x0000;
    }
}

/*********************************************************************
 *
*********************************************************************/
int16_t xRF4463GetRSSI(){
    uint8_t pcBuffer[ 10 ] = { 0x00, 0x00, 0x00, 0x00 };
    float fRSSIRawValue = 0.0f;
    short xRSSICalculated = 0;

    vRF4463ResetRxBuffer();
    // printf( "[ RF4463: %ld ] Rx buffer cleared\r\n", __LINE__ );

    pcBuffer[ RF4463_BUFFER_CMD_POS ] = RF4463_CMD_GET_MODEM_STATUS;
    pcBuffer[ 1 ] = 0xFF;
    #ifndef RF4463_STM32G071
        vRF4463SPISendCmdGetResponse( pcBuffer, 2, pcRF4463SPIRxBuffer, 8 );
    #else
        vRF4463SPISendCmdGetResponse( &pcBuffer, 2, pcRF4463SPIRxBuffer, 8 );
    #endif

    #ifdef DEBUG_RF4463_RSSI_VALUES
        for( ucCounter = 0; ucCounter < 8; ucCounter++ ){
            printf( "[ RF4463 ] RSSI - Position: %d Value: 0x%02X\r\n", ucCounter, pcRF4463SPIRxBuffer[ ucCounter ] );
        }
    #endif

    // fRSSIRawValue = ( float )pcRF4463SPIRxBuffer[ 3 ];
    // xRSSICalculated = ( ( ( fRSSIRawValue / 2 ) - 57 ) - 70 );

    fRSSIRawValue = ( float )pcRF4463SPIRxBuffer[ 4 ];
    // xRSSICalculated = ( ( ( fRSSIRawValue / 2 ) - 57 ) - 70 );

    // fRSSIRawValue = ( float )pcRF4463SPIRxBuffer[ 4 ];
    xRSSICalculated = ( ( ( fRSSIRawValue / 2 ) - 64 ) - 60 );

    xRFLastRSSI = 0;
    xRFLastRSSI = xRSSICalculated;

    // printf( "[ RF4463 ] RSSI: %d\r\n", xRSSICalculated );

    return xRSSICalculated;
}

/*********************************************************************
 *
*********************************************************************/
int16_t xRF4463GetLastRSSI(){
    printf( "[ RF4463 ] Last Measured RSSI: %i\r\n", xRFLastRSSI );
    return xRFLastRSSI;
}

/*********************************************************************
 *
*********************************************************************/
// uint8_t ucRF4463CheckCTS(){
//     #ifdef RF4463_STM32G071
//         uint8_t ucDummyNull = 0U;
//         uint8_t ucReadCmd = RF4463_CMD_READ_BUF;

//         RF4463nSEL = 1; // GPIO
//         RF4463nSEL = 0; // GPIO
//         ucRF4463SPIByte( &ucReadCmd, 1 );
//         ucResult = ucRF4463SPIByte( &ucDummyNull, 1 );
//         RF4463nSEL = 1; // GPIO
//     #else
//         uint8_t pcDummyNull[ 2 ] = { 0 };
//         uint8_t ucByte = RF4463_CMD_READ_BUF;

//         // gpio_put( RF4463nSEL, 1 );
//         // gpio_put( RF4463nSEL, 0 );
//         vRF4463RWBuffer( ucByte, pcDummyNull, 1 );
//         // gpio_put( RF4463nSEL, 1 );

//         // printf( "CTS: 0x%02X\n", pcDummyNull[ 0 ] );
//         // sleep_ms( 100 );
//         return pcDummyNull[ 0 ];
//     #endif
// }

/*********************************************************************
 *
*********************************************************************/
void vRF4463SetStandbyMode(){
    uint8_t pcBuffer[ 2 ] = { 0x00, 0x00 };

    pcBuffer[ RF4463_BUFFER_CMD_POS ] = RF4463_CMD_CHANGE_STATE;
    pcBuffer[ 1 ] = RF4463_OPERATION_STATE_SLEEP;

    #ifndef RF4463_STM32G071
        vRF4463SPIWrite( pcBuffer, 2 );
    #else
        vRF4463SPIWrite( &pcBuffer, 2 );
    #endif
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463SetRxPktLength(){
    uint8_t pcBuffer[ 6 ] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };

    pcBuffer[ RF4463_BUFFER_CMD_POS ]    = RF4463_CMD_SET_PROPERTY;
    pcBuffer[ RF4463_BUFFER_GROUP_POS ]  = RF4463_PROPERTY_GROUP_PKT; // 0x120d
    pcBuffer[ RF4463_BUFFER_LENGTH_POS ] = 0x02; // Total 12 Parameters
    pcBuffer[ RF4463_BUFFER_INDEX_POS ]  = RF4463_PROPERTY_PKT_FIELD_1_LENGTH_12_8; // 0x120d
    pcBuffer[ 4 ] = 0x00;
    pcBuffer[ 5 ] = RF4463_MAX_BUFFER_LENGTH;

    #ifndef RF4463_STM32G071
        vRF4463SPIWrite( pcBuffer, 6 );
    #else
        vRF4463SPIWrite( &pcBuffer, 6 );
    #endif
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463RxInit(){
    // printf( "[ RF4463 ] Rx Init\r\n" );
    #ifdef RF4463_PACKET_VARIABLE_LENGTH
        // Set this field to reuse the FIFO area, since after sending several variable packets it seems
        // to do a type of ring buffer and does not receive properly
        vRF4463SetTxPktLength( RF4463_MAX_BUFFER_LENGTH );
    #endif
    vRF4463ClearInterrupts();
    vRF4463ResetFIFO();
    vRF4463RxStart();
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463RxStart(){
    uint8_t pcBuffer[ 8 ] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };

    pcBuffer[ RF4463_BUFFER_CMD_POS ] = RF4463_CMD_START_RX;
    pcBuffer[ 1 ] = xRF4463Configuration.rx_channel;
    pcBuffer[ 2 ] = 0x00; ///< Use RX parameters to enter RX mode.  Start RX immediately.
    pcBuffer[ 3 ] = 0x00; ///< If this field is zero, the number of data bytes to be received is specified by the value(s) of the PKT_FIELD_X_LENGTH properties.
    pcBuffer[ 4 ] = 0x00; ///< Same as above
    pcBuffer[ 5 ] = RF4463_OPERATION_STATE_RX; ///< This parameter selects the desired operational state of the chip to automatically enter upon timeout of Preamble detection.
    pcBuffer[ 6 ] = RF4463_OPERATION_STATE_READY; ///< This parameter selects the desired operational state of the chip to automatically enter upon reception of a valid packet
    pcBuffer[ 7 ] = RF4463_OPERATION_STATE_RX; ///< This parameter selects the desired operational state of the chip to automatically enter upon reception of an invalid packet

    // printf( "[ RF4463Pro ] Rx Channel: %02d\r\n", xRF4463Configuration.rx_channel );

    #ifndef RF4463_STM32G071
        vRF4463SPIWrite( pcBuffer, 8 );
    #else
        vRF4463SPIWrite( &pcBuffer, 8 );
    #endif
}

/*********************************************************************
 *
*********************************************************************/
uint8_t ucRF4463RxReadFIFO( uint8_t * pcOutBuffer ){

    #ifdef RF4463_STM32G071
        uint8_t ucDummyNull = 0U;
        uint8_t ucReadCmd = RF4463_CMD_RX_FIFO_READ;
    #endif
    /*********************************************************************
     *
    *********************************************************************/
        while( ucRF4463CheckCTS() != RF4463_CTS_AVAILABLE_REPLY );
    /*********************************************************************
     *
    *********************************************************************/
        uint8_t ucByte = RF4463_CMD_RX_FIFO_READ;
        vRF4463RWBuffer( ucByte, pcOutBuffer, RF4463_MAX_BUFFER_LENGTH );
    /*********************************************************************
     *
    *********************************************************************/
        #ifdef DEBUG_RF4463_FIFO_INFO
            vRF4463GetFIFOInfo();
        #endif
    /*********************************************************************
     *
    *********************************************************************/
        #ifdef DEBUG_RF4463_RX_PACKET_INFO
            for( ucCounter = 0; ucCounter < 3; ucCounter++ ){
                printf( "[ RF4463 ] Packet Info - Pos: %d Value: 0x%02X\r\n", ucCounter, pcRF4463SPIRxBuffer[ ucCounter ] );
            }

            printf( "[ RF4463 ] Packet: " );
            for( ucCounter = 0; ucCounter < RF4463_MAX_BUFFER_LENGTH; ucCounter++ ){
                UART1_Write( pcOutBuffer[ ucCounter ] );
            }
            UART1_Write( '\r' );
            UART1_Write( '\n' );
        #endif

        #if defined( DEBUG_RF4463_SPI_TX ) || defined( DEBUG_RF4463_SPI_RX )
            UART1_Write( '\r' );
            UART1_Write( '\n' );
        #endif

        return pcOutBuffer[ 0 ];
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463SetTxPktLength( uint8_t ucPacketLength ){
    uint8_t pcBuffer[ 6 ] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };

    pcBuffer[ RF4463_BUFFER_CMD_POS ]       = RF4463_CMD_SET_PROPERTY;
    pcBuffer[ RF4463_BUFFER_GROUP_POS ]     = RF4463_PROPERTY_GROUP_PKT;
    pcBuffer[ RF4463_BUFFER_LENGTH_POS ]    = 0x01;
    pcBuffer[ RF4463_BUFFER_INDEX_POS ]     = RF4463_PROPERTY_PKT_FIELD_2_LENGTH_7_0;
    pcBuffer[ 4 ] = ucPacketLength;

    #ifndef RF4463_STM32G071
        vRF4463SPIWrite( pcBuffer, 5 );
    #else
        vRF4463SPIWrite( &pcBuffer, 5 );
    #endif
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463TxStart(){
    uint8_t pcBuffer[ 5 ] = { 0x00, 0x00, 0x00, 0x00, 0x00 };

    pcBuffer[ RF4463_BUFFER_CMD_POS ] = RF4463_CMD_START_TX;
    pcBuffer[ 1 ] = xRF4463Configuration.tx_channel;
    pcBuffer[ 2 ] = ( ( uint8_t )RF4463_OPERATION_STATE_READY << 4 ); ///< Back after launch Readymode, Do not retransmit, launch immediately
    pcBuffer[ 3 ] = 0x00; ///< If this field is zero, the number of data bytes to be transmitted is specified by the value(s) of the PKT_FIELD_X_LENGTH properties.
    pcBuffer[ 4 ] = 0x00; ///< Same as above

    // printf( "[ RF4463Pro ] Tx Channel: %02d\r\n", xRF4463Configuration.tx_channel );

    #ifndef RF4463_STM32G071
        vRF4463SPIWrite( pcBuffer, 5 );
    #else
        vRF4463SPIWrite( &pcBuffer, 5 );
    #endif
}

#ifndef RF4463_STM32G071
    /*********************************************************************
     *
    *********************************************************************/
    void vRF4463TxData( uint8_t * pcBuffer, uint8_t ucPacketLength ){
        xRF4463Flag.is_tx = 0;

        vRF4463ResetFIFO();
        #ifdef RF4463_PACKET_VARIABLE_LENGTH
            // printf( "[ RF4463:%d ]\r\n", __LINE__ );
            vRF4463SetTxPktLength( ucPacketLength );
        #endif
        vRF4463TxWriteFIFO( pcBuffer, ucPacketLength );
        vRF4463ClearInterrupts();
        vRF4463TxStart();

        xRF4463Flag.is_tx = 1;

        // printf( "[ RF4463:%d ] Wait for interrupt\r\n", __LINE__ );
        // while( gpio_get( RF4463nIRQ ) ){
        //     ulCounter--;
        //     if( ulCounter == 0 ){
        //         printf( "Tx timeout\r\n" );
        //         break;
        //     }
        // }
    }

    /*********************************************************************
     *
    *********************************************************************/
    void vRF4463TxWriteFIFO( uint8_t * pcBuffer, uint8_t ucPacketLength ){
        uint8_t ucCounter = 0;
        uint8_t ucDummyNull = 0x00;
        uint8_t pcTemp[ 1 ] = { RF4463_CMD_TX_FIFO_WRITE };
        /*********************************************************************
         *
        *********************************************************************/
            while( ucRF4463CheckCTS() != RF4463_CTS_AVAILABLE_REPLY );
        /*********************************************************************
         *
        *********************************************************************/
            #if defined( DEBUG_RF4463_DRIVER_TX )
                printf( "[ RF4463:%ld ] Tx Length: %d\r\n", __LINE__, ucPacketLength );
                printf( "[ RF4463:%ld ] Packet: ", __LINE__ );
                for( ucCounter = 0; ucCounter < ucPacketLength; ucCounter++ ){
                    printf( "%02X ", pcBuffer[ ucCounter ] );
                }
                printf( "\r\n" );
            #endif

            vRF4463SPIWriteCmdNoCTS( pcTemp[ 0 ], pcBuffer, ucPacketLength );
    }
#else
    /*********************************************************************
     *
    *********************************************************************/
    void vRF4463TxData( uint32_t plBuffer, uint8_t ucPacketLength ){
        xRF4463Flag.is_tx = 0;

        vRF4463ResetFIFO();
        #ifdef RF4463_PACKET_VARIABLE_LENGTH
            vRF4463SetTxPktLength( ucPacketLength );
        #endif
        vRF4463TxWriteFIFO( plBuffer, ucPacketLength );
        vRF4463ClearInterrupts();
        vRF4463TxStart();

        xRF4463Flag.is_tx = 1;

        while( RF4463nIRQ );
        /*********************************************************************
         * Disable RF4463 voltage transistor if the MCU uses battery, this
         * should be done
        *********************************************************************/
            #ifdef RF4463_MCU_USE_BATTERY
                RF4463Enable = 0; // On sleep sequence
            #endif
    }

    /*********************************************************************
     * *
    *********************************************************************/
    void vRF4463TxWriteFIFO( uint32_t plBuffer, uint8_t ucPacketLength ){
        uint8_t ucCounter = 0;
        uint8_t ucDummyNull = 0x00;
        uint8_t pcTemp[ 1 ] = { RF4463_CMD_TX_FIFO_WRITE };
        /*********************************************************************
         *
        *********************************************************************/
            while( ucRF4463CheckCTS() != RF4463_CTS_AVAILABLE_REPLY );
        /*********************************************************************
         *
        *********************************************************************/
            RF4463nSEL = 1; // GPIO
            RF4463nSEL = 0; // GPIO
            ucRF4463SPIByte( &pcTemp, 1 );
            ucRF4463SPIByte( plBuffer, ucPacketLength );
            RF4463nSEL = 1; // GPIO

            #if defined( DEBUG_RF4463_SPI_TX ) || defined( DEBUG_RF4463_SPI_RX )
                UART1_Write( '\r' );
                UART1_Write( '\n' );
            #endif
    }
#endif

/*********************************************************************
 *
*********************************************************************/
void vRF4463SetState(){
    uint8_t pcBuffer[ 3 ] = { RF4463_CMD_CHANGE_STATE, RF4463_OPERATION_STATE_READY, 0x00 };

    vRF4463SPIWrite( pcBuffer, 2 );

    ucRF4463GetState();
}

/*********************************************************************
 *
*********************************************************************/
uint8_t ucRF4463GetState(){
    uint8_t pcBuffer[ 3 ] = { 0x00, 0x00, 0x00 };
    uint8_t ucByte = RF4463_CMD_REQUEST_DEVICE_STATE;

    vRF4463RWBuffer( ucByte, pcBuffer, 3 );
    #ifdef ENABLE_DEBUG_RF4463_DRIVER_GET_STATE
        #if LOG_LEVEL > LOG_LEVEL_INFO
            printf( "[ RF4463 ] State: %s\r\n", pcRFOperationState[ pcBuffer[ 1 ] ] )
        #else
            printf( "[ RF4463 ] State: 0x%02X\r\n", pcBuffer[ 1 ] );
        #endif
        printf( "[ RF4463 ] Channel: %d\r\n", pcBuffer[ 2 ] );
    #endif

    return pcBuffer[ 1 ];
}

/*********************************************************************
 *
*********************************************************************/
uint8_t ucRF4463Check_UF_OF(){
    uint8_t pcData[ 10 ];
    uint8_t pcOutData[ 12 ];

    pcData[ RF4463_BUFFER_CMD_POS ] = RF4463_CMD_GET_INT_STATUS;
    pcData[ 1 ] = 0x00;
    pcData[ 2 ] = 0x00;
    pcData[ 3 ] = 0x00;

    vRF4463SPISendCmdGetResponse( pcData, 4, pcOutData, 8 );
    // printf( "[ RF ] RF4463_SATAUS CTS:            0x%02X\r\n", pcOutData[ 0 ] );
    // printf( "[ RF ] RF4463_SATAUS INT_PEN:        0x%02X\r\n", pcOutData[ 1 ] );
    // printf( "[ RF ] RF4463_SATAUS INT_STATUS:     0x%02X\r\n", pcOutData[ 2 ] );
    // printf( "[ RF ] RF4463_SATAUS PH_PEND:        0x%02X\r\n", pcOutData[ 3 ] );
    // printf( "[ RF ] RF4463_SATAUS PH_STATUS:      0x%02X\r\n", pcOutData[ 4 ] );
    // printf( "[ RF ] RF4463_SATAUS MODEM_PEND:     0x%02X\r\n", pcOutData[ 5 ] );
    // printf( "[ RF ] RF4463_SATAUS MODEM_STATUS:   0x%02X\r\n", pcOutData[ 6 ] );
    // printf( "[ RF ] RF4463_SATAUS CHIP_PEND:      0x%02X\r\n", pcOutData[ 7 ] );
    // printf( "[ RF ] RF4463_SATAUS CHIP_STATUS:    0x%02X\r\n", pcOutData[ 8 ] );

    if( ( pcOutData[ RF4463_INT_STATUS_CHIP_STATUS ] & ( RF4463_INT_STATUS_CHIP_FIFO_UNDERFLOW_OVERFLOW_ERROR | RF4463_INT_STATUS_CHIP_STATE_CHANGE ) ) == ( RF4463_INT_STATUS_CHIP_FIFO_UNDERFLOW_OVERFLOW_ERROR | RF4463_INT_STATUS_CHIP_STATE_CHANGE ) ){
        printf( "[ RF4463 ] Underflow Overflow flag is active\r\n" );
        return 1;
    }
    if( ( pcOutData[ RF4463_INT_STATUS_PH_STATUS ] & RF4463_INT_STATUS_PH_RX_FIFO_ALMOST_FULL ) == RF4463_INT_STATUS_PH_RX_FIFO_ALMOST_FULL ){
        printf( "[ RF4463 ] Rx RF buffer is full\r\n" );
        return 2;
    }
    return 0;
}

/**********************************************************************************************
 * Setters and Getters
**********************************************************************************************/
/*********************************************************************
 *
*********************************************************************/
void vRF4463SetTxPower( uint8_t ucTxPower ){
    uint8_t pcBuffer[ 8 ] = { 0x00 };

    pcBuffer[ 0 ]  = RF4463_CMD_SET_PROPERTY;
    pcBuffer[ 1 ]  = RF4463_PROPERTY_GROUP_PA;    // 0x2200
    pcBuffer[ 2 ]  = 0x04;    // Total 4Parameters
    pcBuffer[ 3 ]  = RF4463_PROPERTY_PA_MODE;    // 0x2200
    pcBuffer[ 4 ]  = 0x08;    // 0x10; // PA mode  = default , ??Class E mode,?Is not Switch Current mode  ????????
    #ifdef RF4463_LIMIT_TX_POWER
        if( ucTxPower >= RF4463_TX_POWER_0_dB && ucTxPower <= RF4463_TX_POWER_20_dB ){
            pcBuffer[ 5 ]  = ucTxPower;
        }
        else{
            printf( "[ RF4463 ] Invalid Tx Power, set to default max power\r\n" );
            pcBuffer[ 5 ]  = RF_MAX_ALLOWED_TX_POWER; //  Set to maximum power
        }
    #else
        if( ucTxPower <= RF4463_TX_POWER_20_dB ){
            pcBuffer[ 5 ]  = ucTxPower;
        }
        else{
            printf( "[ RF4463 ] Invalid Tx Power, set to default max power\r\n" );
            pcBuffer[ 5 ]  = RF_MAX_ALLOWED_TX_POWER; //  Set to maximum power
        }
    #endif
    pcBuffer[ 6 ] = 0x00; // CLK duty = 50%, other = Default
    pcBuffer[ 7 ] = 0x3D;

    printf( "[ RF4463 ] Tx Power: %d\r\n", pcBuffer[ 5 ] );

    #ifndef RF4463_STM32G071
        vRF4463SPIWrite( pcBuffer, 8 );
    #else
        vRF4463SPIWrite( &pcBuffer, 8 );
    #endif
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463SetChannels( uint8_t ucChannel ){
    if( ucChannel > RF4463_MAX_CHANNEL_AVAILABLE ){
        printf( "[ RF4463 ] Invalid Channel\r\n" );
        return;
    }

    xRF4463Configuration.rx_channel = ucChannel;
    xRF4463Configuration.tx_channel = ucChannel;

    printf( "[ RF4463 ] Rx Channel: %d\r\n", ucChannel );
    printf( "[ RF4463 ] Tx Channel: %d\r\n", ucChannel );
    
    // Important: When changing channels, we need to ensure the RF chip is properly configured
    // The START_RX command uses the channel parameter from xRF4463Configuration.rx_channel
    // but the chip needs to be in the correct state for the channel change to take effect
    
    // Get current state
    uint8_t ucCurrentState = ucRF4463GetState();
    
    // If we're in RX mode, we need to restart RX with the new channel
    if (ucCurrentState == RF4463_OPERATION_STATE_RX) {
        // Clear any pending interrupts and reset FIFO before changing channel
        vRF4463ClearInterrupts();
        vRF4463ResetFIFO();
        
        // Restart RX with new channel - this will use the updated xRF4463Configuration.rx_channel
        vRF4463RxStart();
        
        // Add a small delay to ensure the channel change takes effect
        sleep_ms(10);
    }
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463SetNetwork( uint16_t usNetwork ){
    uint8_t pcBuffer[ 10 ] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
    xRF4463Configuration.syncword = usNetwork;
    printf( "[ RF4463 ] Network: %d\r\n", usNetwork );

    pcBuffer[ RF4463_BUFFER_CMD_POS ]       = RF4463_CMD_SET_PROPERTY;
    pcBuffer[ RF4463_BUFFER_GROUP_POS ]     = RF4463_PROPERTY_GROUP_SYNC;
    pcBuffer[ RF4463_BUFFER_LENGTH_POS ]    = 0x05;
    pcBuffer[ RF4463_BUFFER_INDEX_POS ]     = RF4463_PROPERTY_SYNC_CONFIG;
    pcBuffer[ 4 ] = 0x01;
    pcBuffer[ 5 ] = ( xRF4463Configuration.syncword >> 8 );
    pcBuffer[ 6 ] = xRF4463Configuration.syncword;
    pcBuffer[ 7 ] = 0x00;
    pcBuffer[ 8 ] = 0x00;

    #ifndef RF4463_STM32G071
        vRF4463SPIWrite( pcBuffer, 9 );
    #else
        vRF4463SPIWrite( &pcBuffer, 9 );
    #endif
}

/*********************************************************************
 *
*********************************************************************/
void vRF4463ResetRxBuffer(){
    // uint8_t ucCounter = 0;
    #ifndef DMA_RF4463_ENABLED
        // for( ucCounter = 0; ucCounter < RF4463_MAX_BUFFER_LENGTH; ucCounter++ ){
        //     pcRF4463SPIRxBuffer[ ucCounter ] = 0x00;
        // }
        memset( pcRF4463SPIRxBuffer, 0, RF4463_MAX_BUFFER_LENGTH );
    #else
        ucDMAStartMEMtoMEMClear( pcRF4463SPIRxBuffer, RF4463_MAX_BUFFER_LENGTH );
    #endif /* DMA_RF4463_ENABLED */
}

/*********************************************************************
 *
*********************************************************************/
#ifndef DMA_RF4463_ENABLED
    void vRF4463GetSPIBuffer( uint8_t * pcOutBuffer, uint8_t ucLength ){
#else
    void vRF4463GetSPIBuffer( uint32_t * pcOutBuffer, uint8_t ucLength ){
#endif
    #ifndef DMA_MEMTOMEM_COPY_ENABLED
        memcpy( pcOutBuffer, pcRF4463SPIRxBuffer, ucLength );
    #else
        ucDMAStartMEMtoMEM ( pcRF4463SPIRxBuffer, pcOutBuffer, ucLength );
    #endif /* DMA_MEMTOMEM_COPY_ENABLED */
}

/*********************************************************************
 *
*********************************************************************/
uint8_t ucRF4463GetFlag(){
    return xRF4463Flag.Flags;
}

/*********************************************************************
 *
*********************************************************************/
#ifndef DMA_RF4463_ENABLED
    void vRF4463BufferReset( uint8_t * pcBufferToClean, uint16_t uslength ){
#else
    void vRF4463BufferReset( uint32_t * pcBufferToClean, uint16_t uslength ){
#endif
    uint16_t usPosition = 0;

    #ifndef DMA_RF4463_ENABLED
        for( usPosition = 0; usPosition < uslength; usPosition++ ){
            pcBufferToClean[ usPosition ] = 0x00;
        }
    #else
        ucDMAStartMEMtoMEMClear( pcBufferToClean, uslength );
    #endif
}

/*******************************************************************************************************************************************************************************************************
 * End of File
*******************************************************************************************************************************************************************************************************/