#include "can_task.h"

TaskHandle_t xCAN0RxTaskHandle;
TaskHandle_t xCAN0TxTaskHandle;

CANParams_t can_params[ NUM_CAN_CHANNELS] = CAN_PARAMS_DEFAULT();
static struct can2040 cbus[ NUM_CAN_CHANNELS ];

QueueHandle_t xCANRxQueue;
QueueHandle_t xCANTxQueue;

static void send_can_message(const uint8_t *data, uint32_t len) {
    #if defined( DEBUG_CAN_PACKET_TX )
        printf( "[ CAN ] Tx - size: %02d | pkt: ", len );
        for( uint32_t i = 0; i < len; ++i ){
            printf( "%02X ", data[ i ] );
        }
        printf("\r\n");
    #endif

    can_var_send(&cbus[0], g_active_configuration.config.id, data, len);
    can_var_send(&cbus[1], g_active_configuration.config.id, data, len);
    can_var_send(&cbus[2], g_active_configuration.config.id, data, len);
}

static void can_varlen_rx_cb(struct can2040 *cd, uint32_t base_id, const uint8_t *data, uint32_t len) {
    #if defined( DEBUG_CAN_PACKET_RX )
        printf( "[ CAN ] Rx - ch: %02d | id: %02d | size: %02d | pkt: ", cd->pio_num, base_id, len );
        for( uint32_t i = 0; i < len; ++i ){
            printf( "%02X ", data[ i ] );
        }
        printf("\r\n");
    #endif
    handle_command(data, len, base_id);
}

static void can_varlen_tx_cb(struct can2040 *cd, struct can2040_msg *msg) {
    CANMessage_t cb_msg;

    // Clear the buffer to prevent possible malformations
    memset( &cb_msg, 0, sizeof( CANMessage_t ) );
    // Get the message for future circutit handling
    cb_msg.channel = cd->pio_num;
    // copy to temporal structure
    memcpy( &cb_msg.message, msg, sizeof( struct can2040_msg ) );

    // Motify via queue
    xQueueSend(xCANTxQueue, &cb_msg, portMAX_DELAY);
}

// ISRs & IRQs
static void vCANCallback( struct can2040 *cd, uint32_t notify, struct can2040_msg *msg ){
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    CANMessage_t cb_msg;

    switch( notify ){
        case CAN2040_NOTIFY_RX:
            // Clear the buffer to prevent possible malformations
            memset( &cb_msg, 0, sizeof( CANMessage_t ) );
            // Get the message for future circutit handling
            cb_msg.channel = cd->pio_num;
            // copy to temporal structure
            memcpy( &cb_msg.message, msg, sizeof( struct can2040_msg ) );
            // Motify via queue
            xQueueSendFromISR( xCANRxQueue, &cb_msg, &xHigherPriorityTaskWoken );
            portYIELD_FROM_ISR( xHigherPriorityTaskWoken );
            break;
        case CAN2040_NOTIFY_TX:
            break;
        case CAN2040_NOTIFY_ERROR:
            break;
        default:
            break;
    }
}

static void vCAN_CH0_IRQHandler( void ){
    can2040_pio_irq_handler( &cbus[ CHANNEL_0 ] );
}

static void vCAN_CH1_IRQHandler( void ){
    can2040_pio_irq_handler( &cbus[ CHANNEL_1 ] );
}

static void vCAN_CH2_IRQHandler( void ){
    can2040_pio_irq_handler( &cbus[ CHANNEL_2 ] );
}

// tasks
void vCANRxTask( void * pvParameters ){
    CANMessage_t xRxMessage;

    xCANRxQueue = xQueueCreate( CANRX_QUEUE_LENGTH, CANRX_MSG_ITEM_SIZE );

    printf( "CAN Rx Task\r\n" );

    for( ;; ){
        while( xQueueReceive( xCANRxQueue, &xRxMessage, portMAX_DELAY ) != errQUEUE_EMPTY ){
            // pass it on to varlen
            can_var_on_can2040_callback(&cbus[ xRxMessage.channel ], CAN2040_NOTIFY_RX, &xRxMessage.message);
            // TODO: Insert event queueing function
            //xInEventQueueSend( EVENT_ID_CAN_RX, &xRxMessage );
        }
    }
}

void vCANTxTask( void * pvParameters ){
    vTaskDelay(1000);

    CANMessage_t xTxMessage;
    // TODO: review if it's possible to handle with just one task/queue messages
    xCANTxQueue = xQueueCreate( CANTX_QUEUE_LENGTH, CANTX_MSG_ITEM_SIZE );
    // Declare and set addresses for callbacks
    // Channel 0
    can_params[ CHANNEL_0 ].en = true;
    can_params[ CHANNEL_0 ].sys_clock = SYS_CLOCK_150MHZ;
    can_params[ CHANNEL_0 ].baudrate = CAN_BAUDRATE_125KBPS;
    can_params[ CHANNEL_0 ].irq_cb_func = (void*)&vCAN_CH0_IRQHandler;
    can_params[ CHANNEL_0 ].rx_cb_func = vCANCallback;
    // Channel 1
    can_params[ CHANNEL_1 ].en = true;
    can_params[ CHANNEL_1 ].sys_clock = SYS_CLOCK_150MHZ;
    can_params[ CHANNEL_1 ].baudrate = CAN_BAUDRATE_125KBPS;
    can_params[ CHANNEL_1 ].irq_cb_func = (void*)&vCAN_CH1_IRQHandler;
    can_params[ CHANNEL_1 ].rx_cb_func = vCANCallback;
    // Channel 2
    can_params[ CHANNEL_2 ].en = true;
    can_params[ CHANNEL_2 ].sys_clock = SYS_CLOCK_150MHZ;
    can_params[ CHANNEL_2 ].baudrate = CAN_BAUDRATE_125KBPS;
    can_params[ CHANNEL_2 ].irq_cb_func = (void*)&vCAN_CH2_IRQHandler;
    can_params[ CHANNEL_2 ].rx_cb_func = vCANCallback;
    // Start the can bus depending on the number of CAN channels available in device
    can_var_set_rx_callback(&can_varlen_rx_cb);
    can_var_set_tx_callback(&can_varlen_tx_cb);
    register_send_can_message_callback(&send_can_message);

    xTaskCreate( vCANRxTask, "CAN0RxTask", 4096, NULL, 1, &xCAN0RxTaskHandle );
    vTaskCoreAffinitySet( xCAN0RxTaskHandle, 1 );

    vCANBusSetup( &can_params[ CHANNEL_0 ], CHANNEL_0 );
    vCANBusSetup( &can_params[ CHANNEL_1 ], CHANNEL_1 );
    vCANBusSetup( &can_params[ CHANNEL_2 ], CHANNEL_2 );

    printf( "CAN Tx Task\r\n" );

    for( ;; ){
        while( xQueueReceive( xCANTxQueue, &xTxMessage, portMAX_DELAY ) != errQUEUE_EMPTY ){
            if (can2040_check_transmit(&cbus[ xTxMessage.channel ])) {
                int ret = can2040_transmit(&cbus[ xTxMessage.channel ], &(xTxMessage.message));
            }
        }
    }
}

// functions
void vCANBusSetup( CANParams_t * pxParams, uint8_t ucChannel ){
    // Setup canbus
    can2040_setup( &cbus[ ucChannel ], pxParams->pio_num );
    can2040_callback_config( &cbus[ ucChannel ], pxParams->rx_cb_func );
    // Enable irqs for canbus
    irq_set_exclusive_handler( pxParams->irq_num, (irq_handler_t)pxParams->irq_cb_func );
    // TODO: Check if it's even neccesary to set this one
    // irq_set_priority(PIO0_IRQ_0, 20);
    irq_set_enabled( pxParams->irq_num, true );

    // TODO: Maybe set as a on-demand task so if any data is needed to know it may get logged
    // can2040_get_statistics(&cbus, &stats);

    // Start canbus
    can2040_start( &cbus[ ucChannel ], pxParams->sys_clock, pxParams->baudrate, pxParams->rx_pin, pxParams->tx_pin );
}