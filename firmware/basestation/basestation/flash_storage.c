#include "hardware/flash.h"
#include "hardware/sync.h"
#include <string.h>
#include <stddef.h>

#include "flash_storage.h"

// Static buffer for flash operations to avoid large stack allocations
// This is safe because flash operations are protected by interrupt disable
static uint8_t flash_operation_buffer[FLASH_SECTOR_SIZE];

void flash_storage_set_configuration(const ActiveConfiguration* config) {
    size_t config_size = sizeof(ActiveConfiguration);
    size_t offset = 0;
    size_t base_offset = FLASH_STORAGE_BASE_OFFSET + FLASH_ACTIVE_CONFIG_OFFSET;
    size_t flash_sector_size = FLASH_SECTOR_SIZE;
    const uint8_t* config_bytes = (const uint8_t*)config;

    while (offset < config_size) {
        size_t sector_offset = base_offset + offset;
        size_t sector_base = sector_offset - (sector_offset % flash_sector_size);
        size_t chunk_offset = sector_offset % flash_sector_size;
        size_t write_len = flash_sector_size - chunk_offset;
        if (write_len > config_size - offset) {
            write_len = config_size - offset;
        }
        // Read the whole sector into RAM
        const uint8_t *flash_ptr = (const uint8_t *)(XIP_BASE + sector_base);
        
        uint32_t ints = save_and_disable_interrupts();
        memcpy(flash_operation_buffer, flash_ptr, flash_sector_size);
        // Update only the relevant bytes in the buffer
        memcpy(flash_operation_buffer + chunk_offset, config_bytes + offset, write_len);
        
        flash_range_erase(sector_base, flash_sector_size);
        flash_range_program(sector_base, flash_operation_buffer, flash_sector_size);
        restore_interrupts_from_disabled(ints);
        offset += write_len;
    }
}

bool flash_storage_get_configuration(ActiveConfiguration* config) {
    // Read the entire ActiveConfiguration in one memcpy
    const uint8_t* flash_ptr = (const uint8_t*)(XIP_BASE + FLASH_STORAGE_BASE_OFFSET + FLASH_ACTIVE_CONFIG_OFFSET);
    memcpy(config, flash_ptr, sizeof(ActiveConfiguration));
    return true;
}


void flash_storage_set_node_id(uint32_t device_id) {
    // Read the current sector into RAM
    const uint8_t *flash_ptr = (const uint8_t *)(XIP_BASE + FLASH_STORAGE_BASE_OFFSET);
    
    uint32_t ints = save_and_disable_interrupts();
    memcpy(flash_operation_buffer, flash_ptr, FLASH_SECTOR_SIZE);

    // Update only the device_id region
    memcpy(flash_operation_buffer + FLASH_NODE_ID_OFFSET, &device_id, sizeof(uint32_t));

    flash_range_erase(FLASH_STORAGE_BASE_OFFSET, FLASH_SECTOR_SIZE);
    flash_range_program(FLASH_STORAGE_BASE_OFFSET, flash_operation_buffer, FLASH_SECTOR_SIZE);
    restore_interrupts_from_disabled(ints);
}

void flash_setup(void) {
    const uint8_t* init_addr = (const uint8_t*)(XIP_BASE + FLASH_INITIALIZED_ABS_OFFSET);
    uint8_t initialized = *init_addr;
    if (initialized != 1) {
        flash_reset();
        // Set initialized and provisioned flags and device id to 0
        uint32_t ints = save_and_disable_interrupts();
        memset(flash_operation_buffer, 0, FLASH_SECTOR_SIZE);
        flash_operation_buffer[FLASH_INITIALIZED_OFFSET] = 1;
        flash_operation_buffer[FLASH_PROVISIONED_OFFSET] = 0;
        uint32_t device_id_zero = 0;
        memcpy(flash_operation_buffer + FLASH_NODE_ID_OFFSET, &device_id_zero, sizeof(uint32_t));
        
        flash_range_erase(FLASH_STORAGE_BASE_OFFSET, FLASH_SECTOR_SIZE);
        flash_range_program(FLASH_STORAGE_BASE_OFFSET, flash_operation_buffer, FLASH_SECTOR_SIZE);
        restore_interrupts_from_disabled(ints);

        // use the default configuration for now until provisioned
        // reset_active_configuration();
    }
}

uint32_t flash_storage_get_node_id() {
    uint32_t device_id;
    const uint8_t* addr = (const uint8_t*)(XIP_BASE + FLASH_NODE_ID_ABS_OFFSET);
    memcpy(&device_id, addr, sizeof(uint32_t));
    return device_id;
}

void flash_reset(void) {
    uint32_t ints = save_and_disable_interrupts();
    flash_range_erase(FLASH_STORAGE_BASE_OFFSET, FLASH_SECTOR_SIZE);
    restore_interrupts_from_disabled(ints);
}

bool flash_storage_is_provisioned(void) {
    const uint8_t* prov_addr = (const uint8_t*)(XIP_BASE + FLASH_PROVISIONED_ABS_OFFSET);
    return (*prov_addr) ? true : false;
}

void flash_storage_set_provisioned(bool value) {
    // Read the current sector into RAM
    const uint8_t *flash_ptr = (const uint8_t *)(XIP_BASE + FLASH_STORAGE_BASE_OFFSET);
    
    uint32_t ints = save_and_disable_interrupts();
    memcpy(flash_operation_buffer, flash_ptr, FLASH_SECTOR_SIZE);

    // Update only the provisioned flag
    flash_operation_buffer[FLASH_PROVISIONED_OFFSET] = value ? 1 : 0;

    flash_range_erase(FLASH_STORAGE_BASE_OFFSET, FLASH_SECTOR_SIZE);
    flash_range_program(FLASH_STORAGE_BASE_OFFSET, flash_operation_buffer, FLASH_SECTOR_SIZE);
    restore_interrupts_from_disabled(ints);
}

bool flash_storage_set_qr_code(const char* qr_code) {
    if (qr_code == NULL) {
        return false;
    }
    
    size_t qr_len = strlen(qr_code);
    if (qr_len >= FLASH_QR_CODE_MAX_LENGTH) {
        return false;  // QR code too long
    }
    
    // Calculate which sector contains the QR code offset
    size_t qr_sector_offset = FLASH_QR_CODE_OFFSET - (FLASH_QR_CODE_OFFSET % FLASH_SECTOR_SIZE);
    size_t qr_offset_in_sector = FLASH_QR_CODE_OFFSET % FLASH_SECTOR_SIZE;
    
    // Read the sector containing the QR code area
    const uint8_t *flash_ptr = (const uint8_t *)(XIP_BASE + FLASH_STORAGE_BASE_OFFSET + qr_sector_offset);
    
    uint32_t ints = save_and_disable_interrupts();
    memcpy(flash_operation_buffer, flash_ptr, FLASH_SECTOR_SIZE);
    
    // Clear the QR code area and copy new QR code
    memset(flash_operation_buffer + qr_offset_in_sector, 0, FLASH_QR_CODE_MAX_LENGTH);
    memcpy(flash_operation_buffer + qr_offset_in_sector, qr_code, qr_len + 1);  // Include null terminator
    
    flash_range_erase(FLASH_STORAGE_BASE_OFFSET + qr_sector_offset, FLASH_SECTOR_SIZE);
    flash_range_program(FLASH_STORAGE_BASE_OFFSET + qr_sector_offset, flash_operation_buffer, FLASH_SECTOR_SIZE);
    restore_interrupts_from_disabled(ints);
    
    return true;
}

bool flash_storage_get_qr_code(char* buffer, size_t buffer_size) {
    if (buffer == NULL || buffer_size == 0) {
        return false;
    }
    
    const char* qr_flash_ptr = (const char*)(XIP_BASE + FLASH_QR_CODE_ABS_OFFSET);
    
    // Check if there's a QR code stored (first byte not 0xFF)
    if (*qr_flash_ptr == (char)0xFF) {
        // No QR code stored, return empty string
        buffer[0] = '\0';
        return false;
    }
    
    // Copy the QR code, ensuring null termination
    size_t max_copy = buffer_size - 1;
    if (max_copy > FLASH_QR_CODE_MAX_LENGTH - 1) {
        max_copy = FLASH_QR_CODE_MAX_LENGTH - 1;
    }
    
    size_t i;
    for (i = 0; i < max_copy; i++) {
        buffer[i] = qr_flash_ptr[i];
        if (buffer[i] == '\0') {
            break;
        }
    }
    buffer[i] = '\0';  // Ensure null termination
    
    return true;
}