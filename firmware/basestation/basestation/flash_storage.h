#ifndef FLASH_STORAGE_H
#define FLASH_STORAGE_H

#include <stdint.h>
#include "active_configuration.h"

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>


// Base offset for flash storage area (choose a safe area, e.g., 1.5MB)
#define FLASH_STORAGE_BASE_OFFSET (1536 * 1024)

// Offsets within the reserved sector
#define FLASH_INITIALIZED_OFFSET      0x00  // 1 byte
#define FLASH_PROVISIONED_OFFSET      0x01  // 1 byte
#define FLASH_NODE_ID_OFFSET        0x04  // 4 bytes

// Offset and size for ActiveConfiguration
#define FLASH_ACTIVE_CONFIG_OFFSET    0x10  // Start after device ID (align to 16 bytes)
#define FLASH_ACTIVE_CONFIG_SIZE      sizeof(ActiveConfiguration)
#define FLASH_ACTIVE_CONFIG_ABS_OFFSET (FLASH_STORAGE_BASE_OFFSET + FLASH_ACTIVE_CONFIG_OFFSET)

// QR Code storage definitions
#define FLASH_QR_CODE_MAX_LENGTH      16   // Maximum QR code length including null terminator
#define FLASH_QR_CODE_OFFSET          (FLASH_ACTIVE_CONFIG_OFFSET + FLASH_ACTIVE_CONFIG_SIZE)
#define FLASH_QR_CODE_ABS_OFFSET      (FLASH_STORAGE_BASE_OFFSET + FLASH_QR_CODE_OFFSET)

// Total reserved area for user flash storage
#define FLASH_STORAGE_TOTAL_SIZE      FLASH_SECTOR_SIZE

// Absolute address helpers
#define FLASH_INITIALIZED_ABS_OFFSET   (FLASH_STORAGE_BASE_OFFSET + FLASH_INITIALIZED_OFFSET)
#define FLASH_PROVISIONED_ABS_OFFSET   (FLASH_STORAGE_BASE_OFFSET + FLASH_PROVISIONED_OFFSET)
#define FLASH_NODE_ID_ABS_OFFSET     (FLASH_STORAGE_BASE_OFFSET + FLASH_NODE_ID_OFFSET)

/**
 * @brief Writes the device ID to flash memory.
 * @param device_id The device ID to store.
 */
void flash_storage_set_node_id(uint32_t device_id);

/**
 * @brief Reads the device ID from flash memory.
 * @return The device ID read from flash.
 */
uint32_t flash_storage_get_node_id(void);

/**
 * @brief Erases the reserved flash storage sector for user data.
 */
void flash_reset(void);

/**
 * @brief Initializes the flash storage.
 * This function should be called once at startup to ensure the flash storage is initialized.
 */
void flash_setup(void);
/**
 * @brief Returns whether a device is provisioned (i.e., whether the device ID was set).
 * @returns true if the device is provisioned, false otherwise.
 */
bool flash_storage_is_provisioned(void);

/**
 * @brief Sets the provisioned status of the device.
 * @param value true if the device is provisioned, false otherwise.
 */
void flash_storage_set_provisioned(bool value);

/**
 * @brief Saves the ActiveConfiguration struct to flash memory.
 * @param config Pointer to the ActiveConfiguration to save.
 */
void flash_storage_set_configuration(const ActiveConfiguration* config);

/**
 * @brief Loads the ActiveConfiguration struct from flash memory.
 * @param config Pointer to the ActiveConfiguration to load into.
 * @return true if valid configuration was loaded, false otherwise.
 */
bool flash_storage_get_configuration(ActiveConfiguration* config);

/**
 * @brief Saves a QR code string to flash memory.
 * @param qr_code The QR code string to save (null-terminated).
 * @return true if successfully saved, false if QR code is too long or on error.
 */
bool flash_storage_set_qr_code(const char* qr_code);

/**
 * @brief Reads the QR code string from flash memory.
 * @param buffer Buffer to store the QR code string.
 * @param buffer_size Size of the buffer.
 * @return true if QR code was read, false if no QR code stored or buffer too small.
 */
bool flash_storage_get_qr_code(char* buffer, size_t buffer_size);

#ifdef __cplusplus
}
#endif

#endif // FLASH_STORAGE_H
