#ifndef __TASK_CLEANUP_H__
#define __TASK_CLEANUP_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "FreeRTOS.h"
#include "semphr.h"
#include "queue.h"
#include "task.h"
#include <stdbool.h>

// Structure to track task resources
typedef struct {
    QueueHandle_t* queue_handles;
    uint8_t queue_count;
    SemaphoreHandle_t* mutex_handles;
    uint8_t mutex_count;
    TaskHandle_t task_handle;
    bool is_initialized;
} TaskResources_t;

// Initialize resource tracking for a task
bool vTaskResourcesInit(TaskResources_t* pResources, TaskHandle_t taskHandle);

// Register a queue handle for cleanup
bool vTaskResourcesAddQueue(TaskResources_t* pResources, QueueHandle_t queueHandle);

// Register a mutex handle for cleanup
bool vTaskResourcesAddMutex(TaskResources_t* pResources, SemaphoreHandle_t mutexHandle);

// Clean up all resources and delete the task
void vTaskCleanupAndDelete(TaskResources_t* pResources);

// Clean up resources without deleting the task
void vTaskCleanupResources(TaskResources_t* pResources);

#ifdef __cplusplus
}
#endif

#endif // __TASK_CLEANUP_H__