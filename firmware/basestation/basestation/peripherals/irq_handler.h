#ifndef __IRQ_HANDLER_H__
#define __IRQ_HANDLER_H__

// Include rf_task.h outside extern "C" since it contains C++ headers
#include "rf_task.h"

// C standard libraries
#include <stdio.h>
#include <string.h>

// pico sdk libraries
#include "FreeRTOS.h"
#include "task.h"
#include "queue.h"

#include "hardware/gpio.h"
#include "hardware/irq.h"

// user libraries
#include "types.h"
// #include "pinout.h"

#ifdef __cplusplus
extern "C" {
#endif

// global definitions

// local definitions/enums

// function prototyping
// void gpio_irq_handler( uint gpio, uint32_t events );
gpio_irq_callback_t gpio_irq_handler( uint gpio, uint32_t events );
void vIRQInitData();
void vIRQSetPinAwareness( uint32_t gpio, TaskHandle_t pxTaskHandle );

#ifdef __cplusplus
}
#endif

#endif