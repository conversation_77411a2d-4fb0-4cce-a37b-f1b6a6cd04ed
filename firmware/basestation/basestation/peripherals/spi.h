#ifndef __SPI_H__
#define __SPI_H__

// C standard libraries
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// pico sdk libraries
#include "pico/stdlib.h"

#include "FreeRTOS.h"
#include "task.h"
#include "semphr.h"

// user libraries
#include "hardware/spi.h"

#ifdef __cplusplus
extern "C" {
#endif

// global definitions
extern SemaphoreHandle_t xSPIMutex;

#define SPI_BUS_CLK_SPEED_2MHZ 2000000UL
#define SPI_BUS_CLK_SPEED_4MHZ 4000000UL
#define SPI_BUS_CLK_SPEED_8MHZ 8000000UL

// local definitions/enums
enum SPIOperationType{
    SPI_OP_WRITE,
    SPI_OP_READ,
    SPI_OP_WRITE_READ,
    SPI_OP_DELAY,
    SPI_OP_CS_TOGGLE,
    SPI_OP_BAUDRATE_CHANGE
};

typedef struct {
    spi_inst_t * spi_inst;
    spi_cpha_t cpha;
    spi_cpol_t cpol;
    spi_order_t order;
    uint32_t baudrate;
    uint32_t data_len;
    uint32_t cs_pin;
    uint32_t sck_pin;
    uint32_t miso_pin;
    uint32_t mosi_pin;
} SPIParams_t;

typedef struct{
    uint8_t * buffer;
    union{
        uint32_t Word;
        struct{
            uint16_t len;
            uint8_t type;
            bool restart_cs;
        };
    };

} SPIOperation_t;

typedef struct{
    SPIOperation_t * operation;
    uint8_t total_ops;
} SPISequence_t;

// function prototyping
bool xSPITransaction( SPIParams_t * pxParams, SPISequence_t * pxSequence, TickType_t xTimeout );
bool xSPIInit( SPIParams_t * pxParams, bool xStartMutex );
bool xSPIInitMutex();
void vSPICleanup();

#ifdef __cplusplus
}
#endif

#endif