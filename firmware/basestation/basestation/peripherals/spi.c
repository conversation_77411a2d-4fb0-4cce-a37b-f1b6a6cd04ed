#include "spi.h"

SemaphoreHandle_t xSPIMutex = NULL;

bool xSPITransaction( SPIParams_t * pxParams, SPISequence_t * pxSequence, TickType_t xTimeout ){
    uint8_t ucOpCounter = 0;

    if( !pxParams || !pxSequence ){
        printf("ERROR: Invalid SPI parameters\r\n");
        return false;
    }
    
    if( !xSPIMutex ){
        printf("ERROR: SPI mutex not initialized - call xSPIInit with mutex enabled first\r\n");
        return false;
    }

    if( xSemaphoreTake( xSPIMutex, xTimeout ) == pdTRUE ){
        gpio_put(pxParams->cs_pin, 0);

        for( ucOpCounter = 0 ; ucOpCounter < pxSequence->total_ops; ucOpCounter++ ){

            switch( pxSequence->operation[ ucOpCounter ].type ){
                case SPI_OP_WRITE:
                    spi_write_blocking( pxParams->spi_inst, pxSequence->operation[ ucOpCounter ].buffer, pxSequence->operation[ ucOpCounter ].len );
                    break;
                case SPI_OP_READ:
                    //TODO: Check if the repeated_tx_data should be dynamc
                    spi_read_blocking( pxParams->spi_inst, 0x00, pxSequence->operation[ ucOpCounter ].buffer, pxSequence->operation[ ucOpCounter ].len );
                    break;
            }

            while( spi_is_busy( pxParams->spi_inst ) );
        }

        gpio_put( pxParams->cs_pin, 1 );

        xSemaphoreGive( xSPIMutex );

        return true;
    }

    return false;
}

bool xSPIInit( SPIParams_t * pxParams, bool xStartMutex ){
    if( !pxParams ){
        printf("ERROR: Invalid SPI parameters for initialization\r\n");
        return false;
    }
    
    if( !pxParams->spi_inst ){
        printf("ERROR: Invalid SPI instance\r\n");
        return false;
    }

    spi_set_slave( pxParams->spi_inst, false );

    spi_init( pxParams->spi_inst, pxParams->baudrate );

    gpio_set_function( pxParams->miso_pin, GPIO_FUNC_SPI );
    gpio_set_function( pxParams->sck_pin, GPIO_FUNC_SPI );
    gpio_set_function( pxParams->mosi_pin, GPIO_FUNC_SPI );

    while( spi_is_busy( pxParams->spi_inst ) );

    if( xStartMutex == true ){
        if( !xSPIInitMutex() ){
            printf("ERROR: Failed to initialize SPI mutex\r\n");
            return false;
        }
    }

    printf("SPI initialized successfully (mutex: %s)\r\n", xStartMutex ? "enabled" : "disabled");
    return true;
}

bool xSPIInitMutex(){
    // Don't reinitialize if already created
    if( xSPIMutex != NULL ){
        return true;
    }

    xSPIMutex = xSemaphoreCreateBinary();

    // Check if semaphore was created successfully
    if( !xSPIMutex ){
        printf("ERROR: Failed to create SPI mutex semaphore - insufficient memory\r\n");
        return false;
    }

    // Give the semaphore initially to make it available
    if( xSemaphoreGive( xSPIMutex ) != pdTRUE ){
        printf("ERROR: Failed to initialize SPI mutex semaphore state\r\n");
        vSemaphoreDelete( xSPIMutex );
        xSPIMutex = NULL;
        return false;
    }

    printf("SPI mutex initialized successfully\r\n");
    return true;
}

void vSPICleanup(){
    if( xSPIMutex != NULL ){
        printf("Cleaning up SPI mutex\r\n");
        vSemaphoreDelete( xSPIMutex );
        xSPIMutex = NULL;
    }
}