# CMake minimum required version
cmake_minimum_required(VERSION 3.12)

set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# If you enable this make sure you first flash ../picoboot3 on your pico
set(PICO_USE_OTA ON)

add_subdirectory(../UART uart)

# Add source files
set(SOURCES
    main.cpp
    flash_storage.c
    task_cleanup.c
    #### hardware/peripheral tasks
    uart_task.cpp
    serial_task.cpp
    can_task.cpp
    rf_task.cpp
    dmx_task.cpp
    light_control_task.cpp
    system_monitor.cpp
    sd_task.cpp
    #### SD card and FatFS
    sd_card/tf_card.c
    sd_card/fatfs/ff.c
    sd_card/fatfs/ffsystem.c
    sd_card/fatfs/ffunicode.c
    sd_card/sd_spi/pio_spi.c
    #### hardwre handlers
    peripherals/irq_handler.c
    peripherals/spi.c
    peripherals/io.cpp
    rf4463/rf4463spi.c
    rf4463/rf4463pro.c
    SC16IS750/SC16IS750.cpp
    #### business logic tasks
    event_task.cpp
)

# Add executable. Default name is the project name, version 0.1
add_executable(basestation ${SOURCES})

# Generate PIO header
pico_generate_pio_header(basestation ${CMAKE_CURRENT_SOURCE_DIR}/sd_card/sd_spi/spi.pio)

# Add RP2350 definition for our target
target_compile_definitions(basestation PRIVATE PICO_RP2350)

# Pull in FreeRTOS Kernel
set(FREERTOS_KERNEL_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../lib/FreeRTOS-Kernel)
include(FreeRTOS_Kernel_import.cmake)

pico_set_program_name(basestation "basestation")
pico_set_program_version(basestation "0.1")

# Modify the below lines to enable/disable output over UART/USB
pico_enable_stdio_uart(basestation 1)
pico_enable_stdio_usb(basestation 1)

# Add the standard include files to the build
target_include_directories(basestation PRIVATE
    ${CMAKE_CURRENT_LIST_DIR}/../can
    ${CMAKE_CURRENT_LIST_DIR}/../lib/FreeRTOS-Kernel/include
    ${CMAKE_CURRENT_LIST_DIR}/../common
    ${CMAKE_CURRENT_LIST_DIR}/../
    ${CMAKE_CURRENT_LIST_DIR}/
    ${CMAKE_CURRENT_BINARY_DIR}
)

# Add any user requested libraries
target_link_libraries(basestation
    # pico sdk libraries
    pico_stdlib
    pico_time
    hardware_dma
    hardware_spi
    hardware_i2c
    hardware_uart
    hardware_dma
    hardware_flash
    # FreeRTOS SMP support libraries
    pico_async_context_freertos
    FreeRTOS-Kernel-Heap3
    # User defined libraries
    bs-proto
    commands-proto
    can
    common
    uart
)

pico_add_extra_outputs(basestation)

# Linker
if (PICO_USE_OTA)
    # this removes the default boot2 loader and offsets the start of the flash memory
    set_property(TARGET basestation APPEND_STRING PROPERTY LINK_FLAGS "-Wl,--print-memory-usage")
    pico_set_linker_script(basestation ${CMAKE_CURRENT_LIST_DIR}/memmap_default_rp2350.ld)
endif()
