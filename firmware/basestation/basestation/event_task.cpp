#include "event_task.h"
#include "flash_storage.h"
#include "serial_task.h"
#include "system_monitor.h"
#include "task_cleanup.h"

// FreeRTOS handler declaration
QueueHandle_t xInEventQueueHandle;
// QueueHandle_t xOutEventQueueHandle;

TaskHandle_t xInEventTaskHandle;
TaskHandle_t xOutEventTaskHandle;

// structure declaration


// Task declaration
void vInEventTask( void * pvParameters ){
    Event_t xEvent;
    TaskResources_t taskResources;

    // Initialize resource tracking
    if (!vTaskResourcesInit(&taskResources, xInEventTaskHandle)) {
        printf("ERROR: Failed to initialize task resources for InEventTask\r\n");
        vTaskDelete(NULL);
        return;
    }

    xInEventQueueHandle = xQueueCreate( EVENT_QUEUE_LENGTH, EVENT_ITEM_SIZE );
    if (xInEventQueueHandle == NULL) {
        printf("ERROR: Failed to create InEvent queue\r\n");
        vTaskCleanupAndDelete(&taskResources);
        return;
    }
    
    // Register the queue for cleanup
    if (!vTaskResourcesAddQueue(&taskResources, xInEventQueueHandle)) {
        printf("ERROR: Failed to register InEvent queue for cleanup\r\n");
        vTaskCleanupAndDelete(&taskResources);
        return;
    }

    printf( "[ EVENT ] In event task\r\n" );

    for( ;; ){
        while( xQueueReceive( xInEventQueueHandle, &xEvent, portMAX_DELAY ) != errQUEUE_EMPTY ){
            printf( "[ EVENT ] Digest message\r\n" );
            vInEventDigest( &xEvent );
        }
    }

    printf( "[ EVENT ] In event task - critical\r\n" );
    vTaskCleanupAndDelete(&taskResources);
}

void vOutEventTask( void * pvParameters ){
    printf( "[ EVENT ] Out event task\r\n" );

    for( ;; ){
        // if(){}
    }

    printf( "[ EVENT ] Out event task - critical\r\n" );
    vTaskDelete( NULL );
}

// Fetch from any input peripheral and notify the queue
bool xInEventQueueSend( uint8_t ucEventId, void * pvEventBuffer ){
    Event_t xEventToSend;
    BaseType_t ret = pdFALSE;
    xEventToSend.id = ucEventId;

    printf( "[ EVENT ] Receive event %d\r\n", ucEventId );

    switch( ucEventId ){
        case EVENT_ID_CAN_RX:{
            // CANMessage_t * pxIncomingData = ( CANMessage_t * )pvEventBuffer;
            // memcpy( xEventToSend.data, pxIncomingData->message, pxIncomingData->message.dlc );
            break;
        }
        case EVENT_ID_RF_RX:
            break;
        case EVENT_ID_UART_RX:{
            UARTMessage_t * pxIncomingData = ( UARTMessage_t * )pvEventBuffer;

            printf( "[ EVENT ] UART message received\r\n" );

            // printf( "Payload: %02X\r\n", pxIncomingData->payload[ 0 ] );
            // printf( "Payload: %02X\r\n", pxIncomingData->payload[ 1 ] );
            // printf( "Payload: %02X\r\n", pxIncomingData->payload[ 2 ] );
            // printf( "Payload: %02X\r\n", pxIncomingData->payload[ 3 ] );
            // printf( "Payload: %02X\r\n", pxIncomingData->payload[ 4 ] );
            // printf( "Payload: %02X\r\n", pxIncomingData->payload[ 5 ] );

            xEventToSend.len = ( ( uint32_t )pxIncomingData->payload[ 0 ] << 24 ) | ( ( uint32_t )pxIncomingData->payload[ 1 ] << 16 )
                                | ( ( uint32_t )pxIncomingData->payload[ 2 ] << 8 ) | ( ( uint32_t )pxIncomingData->payload[ 3 ] );

            xEventToSend.len--; // remove the type byte

            xEventToSend.type = pxIncomingData->payload[ 4 ];

            printf( "[ EVENT ] Len: %d\r\n", xEventToSend.len );
            printf( "[ EVENT ] Type: %02X\r\n", xEventToSend.type );

            // Add bounds checking before memcpy to prevent buffer overflow
            if (xEventToSend.len > INTERNAL_BUFFER_MAX_LENGTH) {
                printf("[ EVENT ] ERROR: UART event data too large: %d > %d\n", xEventToSend.len, INTERNAL_BUFFER_MAX_LENGTH);
                return false;
            }

            memcpy( xEventToSend.data, &pxIncomingData->payload[ 5 ], xEventToSend.len );
            break;
        }
        case EVENT_ID_SERIAL_RX:{
            SerialDMAMessage_t * pxIncomingData = ( SerialDMAMessage_t * )pvEventBuffer;

            printf( "[ EVENT ] Serial message received\r\n" );

            // For serial configuration, treat as configuration update
            xEventToSend.len = pxIncomingData->len;
            xEventToSend.type = BACKEND_MESSAGE_UPDATE;

            printf( "[ EVENT ] Serial config len: %d\r\n", xEventToSend.len );

            // Add bounds checking before memcpy to prevent buffer overflow
            if (xEventToSend.len > INTERNAL_BUFFER_MAX_LENGTH) {
                printf("[ EVENT ] ERROR: Serial event data too large: %d > %d\n", xEventToSend.len, INTERNAL_BUFFER_MAX_LENGTH);
                return false;
            }

            memcpy( xEventToSend.data, pxIncomingData->payload, xEventToSend.len );
            break;
        }
    }

    printf( "[ EVENT ] Send to queue\r\n" );

    ret = xQueueSend( xInEventQueueHandle, &xEventToSend, portMAX_DELAY );

    return ret == pdTRUE ? true : false;
}

// Function declaration
void vInEventDigest( Event_t * pxEvent ){
    #ifdef ENABLE_DEBUG_EVENT_DIGEST
        printf( "event: %02X\r\n", pxEvent->type );

        printf( "[ Event ] Rx pkt >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\r\n" );
        for( uint32_t ulPosition = 0; ulPosition < pxEvent->len; ulPosition++ ){
            if( ulPosition > 0 && ulPosition % 32 == 0 ){
                printf( "\r\n" );
            }
            printf( "%02X ", pxEvent->data[ ulPosition ] );
        }
        printf( "\r\n" );
        printf( "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< Rx pkt end\r\n" );
    #endif

    switch( pxEvent->type ){
        case BACKEND_MESSAGE_UPDATE:
            // Get the QR code from flash storage for validation
            char qr_code[FLASH_QR_CODE_MAX_LENGTH];
            bool has_qr_code = flash_storage_get_qr_code(qr_code, sizeof(qr_code));
            
            if (!has_qr_code) {
                printf("[ EVENT ] No QR code stored in flash, rejecting configuration update\n");
                break;
            }

            // Save old RF network and channel before updating config
            uint16_t old_network = 0;
            uint8_t old_channel = 0;
            if (g_active_configuration.has_config && g_active_configuration.config.has_rf_config) {
                old_network = g_active_configuration.config.rf_config.network;
                old_channel = g_active_configuration.config.rf_config.channel;
            }
            
            set_configuration_from_update_message_protobuf( pxEvent->data, pxEvent->len, qr_code );

            printf( "[ EVENT ] Set configuration to flash\r\n" );
            // Flash storage functions handle their own critical sections internally
            flash_storage_set_configuration(&g_active_configuration);
            printf( "[ EVENT ] Set configuration to flash done\r\n" );


            if (g_active_configuration.config.has_rf_config) {
                // Check if RF network or channel changed
                if (old_network != 0 && old_channel != 0 &&
                    (old_network != g_active_configuration.config.rf_config.network ||
                    old_channel != g_active_configuration.config.rf_config.channel)) {
                    
                    printf("[ EVENT ] RF configuration changed - removing all provisioned devices\n");
                    
                    // Iterate through all provisioned devices and remove them
                    for (uint8_t i = 0; i < g_active_configuration.state.provisioned_devices_count; i++) {
                        ProvisioningState *device = &g_active_configuration.state.provisioned_devices[i];
                        if (device->is_provisioned) {
                            // Check if this device is an RF reed sensor - if so, skip removal
                            bool is_rf_reed = false;
                            for (uint8_t k = 0; k < g_active_configuration.config.rf_reed_configs_count; k++) {
                                if (g_active_configuration.config.rf_reed_configs[k].node_id == device->node_id) {
                                    is_rf_reed = true;
                                    break;
                                }
                            }
                            
                            // Only remove if not an RF reed sensor
                            if (!is_rf_reed) {
                                // Find QR code for this node ID from mappings
                                for (uint8_t j = 0; j < g_active_configuration.config.node_qr_mappings_count; j++) {
                                    if (g_active_configuration.config.node_qr_mappings[j].node_id == device->node_id && g_active_configuration.config.node_qr_mappings[j].type == BasestationConfig_NodeQRMapping_DeviceType_RF) {
                                        vRFRemoveDevice(g_active_configuration.config.node_qr_mappings[j].qr_code, device->node_id, old_network, old_channel);
                                        break;
                                    }
                                }
                            }
                            device->is_provisioned = false;
                            device->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
                        }
                    }
                }
            }

            pretty_print_active_configuration();

            printf( "Set configuration to flash\r\n" );

            vPrintSystemStats();

            flash_storage_set_node_id( g_active_configuration.config.id );
            printf( "[ EVENT ] Set node id to flash done\r\n" );
            flash_storage_set_provisioned( true );
            printf( "[ EVENT ] Set provisioned to flash done\r\n" );

            vRfUpdateChannelAndNetworkFromConfig();
            
            printf( "[ EVENT ] Flash done\r\n" );

            break;
    }
}

void vOutEventDigest( Event_t * pxEvent ){
    printf( "event: %02X\r\n", pxEvent->type );

    switch( pxEvent->type ){
        case BACKEND_MESSAGE_UPDATE:
            // if(){}
            break;
    }
}