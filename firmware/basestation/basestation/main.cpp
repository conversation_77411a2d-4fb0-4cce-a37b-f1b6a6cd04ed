#include "main.h"
#include "active_configuration.h"
#include "flash_storage.h"
#include "light_control_task.h"
#include "version.h"

TaskHandle_t xLEDTaskHandle;

/**********************************************************************************************
 * DMX Simulation Test
**********************************************************************************************/
void simulate_dmx_test() {
    static bool dimming_up = true;
    static uint32_t last_dim_time = 0;
    uint32_t current_time = xTaskGetTickCount();

    // Check if 5 seconds have passed
    if (current_time - last_dim_time >= pdMS_TO_TICKS(5000)) {
        if (g_active_configuration.state.lights_count > 0) {
            LightState *light = &g_active_configuration.state.lights[0];
            
            // Toggle between dimming up and down
            if (dimming_up) {
                light->target_value = 100.0f;
            } else {
                light->target_value = 0.0f;
            }
            light->dim_speed_msec = 1000; // 1 second dim duration
            light->last_modified_time = get_time_in_ms();
            
            dimming_up = !dimming_up;

            printf( "Dimming light %d to %f\r\n", light->id, light->target_value );
        }
        last_dim_time = current_time;
    }
}

/**********************************************************************************************
 * LED
**********************************************************************************************/
void vLEDTask( void * pvParameters ){
    gpio_init( ON_BOARD_LED_PIN );
    gpio_set_dir( ON_BOARD_LED_PIN, GPIO_OUT );

    printf( "LED task created\r\n" );

    for( ;; ){
        gpio_put( ON_BOARD_LED_PIN, 1 );
        vTaskDelay( pdMS_TO_TICKS( 250 ) );
        gpio_put( ON_BOARD_LED_PIN, 0 );
        vTaskDelay( pdMS_TO_TICKS( 250 ) );

        // TODO: enable below for debugging
        // simulate_dmx_test();
    }
}

int main(){
    stdio_init_all();
    printf( "\r\n>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\n" );
    printf( "> Basestation booting...\n" );
    printf( "> Version: %s\r\n", FIRMWARE_VERSION_STRING );
    printf( "> Time: %s\r\n", __TIME__ );
    printf( "> Date: %s\r\n", __DATE__ );
    /*************************************************************
     * Start peripherals
    **************************************************************/
    
    // vIOESPReset();
    vIRQInitData();
    
    /*************************************************************
     * Fetch the active configuration from flash
    **************************************************************/
    // flash_setup();
    // g_active_configuration = ActiveConfiguration_init_zero;

    // if( flash_storage_is_provisioned() ){
    //     flash_storage_get_configuration( &g_active_configuration );
    // }
    // // TODO: enable below for debugging
    // else{
    //     printf( "No configuration found in flash. Using debug configuration.\r\n" );
    //     set_debug_configuration();
    // }

    pretty_print_active_configuration();

    /*************************************************************
     * Create business logic tasks in core 1
    **************************************************************/
    // xTaskCreate( vUART0RxTask, "UART0RxTask", 4096, NULL, 1, &xUART0RxTaskHandle );
    // Since INTERNAL_BUFFER_MAX_LENGTH is 16384 bytes, and on a 32-bit ARM processor (like the RP2040 in the Raspberry Pi Pico), a word is 4 bytes, the stack size in words would be:
    //   4096 words (16384 ÷ 4 = 4096)
    //   So the UART1RxTask is being created with a stack size of:
    //   4096 words (from INTERNAL_BUFFER_MAX_LENGTH)
    //   Plus 1024 words (4096 ÷ 4 = 1024)
    //   Total: 5120 words
    // xTaskCreate( vUART1RxTask, "UART1RxTask", 5120, NULL, 1, &xUART1RxTaskHandle );
    xTaskCreate( vLEDTask, "LEDTask", 512, NULL, 1, &xLEDTaskHandle );
    xTaskCreate( vInEventTask, "InEventTask", 30 * 1024, NULL, 1, &xInEventTaskHandle );
    xTaskCreate( vLightControlTask, "LightControlTask", 4 * 1024, NULL, 1, &xLightControlTaskHandle );
    xTaskCreate( vSerialRxTask, "SerialRxTask", 25 * 1024, NULL, 1, &xSerialRxTaskHandle );
    // xTaskCreate( sd_task, "SDTask", 8 * 1024, NULL, 1, &xSDTaskHandle );

    // vTaskCoreAffinitySet( xUART0RxTaskHandle, 1 );
    // vTaskCoreAffinitySet( xUART1RxTaskHandle, 1 );
    vTaskCoreAffinitySet( xLEDTaskHandle, 1 );
    // vTaskCoreAffinitySet( xInEventTaskHandle, 1 );
    // vTaskCoreAffinitySet( xLightControlTaskHandle, 1 );
    vTaskCoreAffinitySet( xSerialRxTaskHandle, 1 );
    // vTaskCoreAffinitySet( xSDTaskHandle, 1 );

    /*************************************************************
     * Create peripherals in core 2
     * These peripherals should operate in HA mode (high availability)
     * Also, these peripherals reception tasks shall not be blocked
     * by any business logic process
    **************************************************************/
    // xTaskCreate( vCANTxTask, "CAN0TxTask", 4096, NULL, 1, &xCAN0TxTaskHandle );
    xTaskCreate( vRfRxTask, "RfRxTask", 4096, NULL, 1, &xRfRxTaskHandle );
    xTaskCreate( vDmxTask, "DmxTask", 4096, NULL, 1, &xDmxTaskHandle );

    // vTaskCoreAffinitySet( xCAN0TxTaskHandle, 2 );
    vTaskCoreAffinitySet( xRfRxTaskHandle, 2 );
    vTaskCoreAffinitySet( xDmxTaskHandle, 2 );
    /*************************************************************
     * Start RTOS scheduler,
     * No code below this instruction will be executed
    **************************************************************/
    vTaskStartScheduler();

    return 0;
}

//
// void vApplicationIdleHook( void );

//
void vApplicationStackOverflowHook( TaskHandle_t xTask, char *pcTaskName ){
    /* Optional: log, blink LED, or reset */
    printf("Stack overflow in task: %s\n", pcTaskName);
    taskDISABLE_INTERRUPTS();
    for( ;; ); // Halt
}