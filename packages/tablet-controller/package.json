{"name": "@somo/tablet-controller", "version": "0.0.1", "private": true, "type": "module", "exports": {"./package.json": "./package.json", ".": {"development": "./src/main.ts", "types": "./dist/main.d.ts", "import": "./dist/main.js", "default": "./dist/main.js"}}, "main": "./dist/main.js", "module": "./dist/main.js", "types": "./dist/main.d.ts", "scripts": {"build": "tsc -b && vite build", "dev": "vite --host 0.0.0.0", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@tanstack/react-query": "5.62.11", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "lucide-react": "0.469.0", "mqtt": "5.10.4", "react": "18.3.1", "react-dom": "18.3.1", "tailwind-merge": "2.6.0", "tailwindcss-animate": "1.0.7", "zod": "4.0.8"}, "devDependencies": {"@types/node": "22.10.3", "@types/react": "18.3.18", "@types/react-dom": "18.3.5", "@vitejs/plugin-react": "4.3.4", "autoprefixer": "10.4.20", "globals": "15.14.0", "postcss": "8.4.49", "tailwindcss": "3.4.17"}, "packageManager": "pnpm@9.15.2+sha512.93e57b0126f0df74ce6bff29680394c0ba54ec47246b9cf321f0121d8d9bb03f750a705f24edc3c1180853afd7c2c3b94196d0a3d53d3e069d9e2793ef11f321"}