import * as Sentry from "@sentry/node";

export function initSentry() {
  const dsn = process.env.SENTRY_DSN;
  const environment = process.env.NODE_ENV || "development";

  Sentry.init({
    dsn,
    environment,
    sendDefaultPii: true,
    // Only set true when installing/debugging Sentry
    debug: false,

    // Sampling
    tracesSampleRate: environment === "production" ? 0.1 : 1.0,
    profilesSampleRate: environment === "production" ? 0.1 : 1.0,

    integrations: [
      Sentry.httpIntegration({}),
      Sentry.expressIntegration(),
      Sentry.prismaIntegration(),
      Sentry.captureConsoleIntegration({
        levels: ["error", "warn", "debug"],
      }),
    ],

    beforeSend(event) {
      // We can use this function to filter out noisy events
      return event;
    },
  });
}

// Helpers (so we don't import Sentry directly from everywhere)
export const captureException: typeof Sentry.captureException = (...params) => {
  return Sentry.captureException(...params);
};

export const captureMessage: typeof Sentry.captureMessage = (...params) => {
  return Sentry.captureMessage(...params);
};
