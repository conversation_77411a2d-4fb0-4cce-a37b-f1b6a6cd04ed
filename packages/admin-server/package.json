{"name": "@somo/admin-server", "version": "0.0.1", "private": true, "type": "module", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "main": "./dist/index.js", "module": "./dist/index.js", "scripts": {"build": "tsup src/index.ts --format esm --cjsInterop --platform node --outDir dist --target node16 --external clerk --external @clerk/express --external @hocuspocus/server --external express --external express-ws --external ws --external yjs --external zod --external jsonwebtoken --external jwks-rsa --external winston --external express-winston --external cors --external body-parser --external dotenv --external ip --external uploadthing --external prisma", "dev": "nodemon --exec 'tsx' src/index.ts", "postinstall": "prisma generate", "migrate": "prisma migrate deploy", "prod": "pnpm run build && node ./dist/index.js"}, "dependencies": {"@clerk/express": "1.3.33", "@hocuspocus/server": "2.15.0", "@prisma/client": "6.2.1", "@sentry/node": "9.39.0", "@somo/shared": "workspace:*", "body-parser": "1.20.2", "clerk": "0.8.3", "cors": "2.8.5", "dotenv": "16.4.5", "express": "4.19.2", "express-async-errors": "3.1.1", "express-winston": "4.2.0", "express-ws": "5.0.2", "ip": "2.0.1", "jsonwebtoken": "9.0.2", "jwks-rsa": "3.1.0", "uploadthing": "7.4.4", "winston": "3.13.1", "ws": "8.18.0", "yjs": "13.6.18", "zod": "4.0.8"}, "devDependencies": {"@bufbuild/protobuf": "^2.4.0", "@types/async-lock": "1.4.2", "@types/body-parser": "1.19.5", "@types/cors": "2.8.17", "@types/express": "4.17.21", "@types/express-ws": "3.0.5", "@types/ip": "1.1.3", "@types/jsonwebtoken": "9.0.6", "@types/ws": "8.5.13", "concurrently": "8.2.2", "nodemon": "3.1.4", "prisma": "6.2.1", "ts-node": "10.9.2", "tsup": "^8.4.0", "tsx": "^4.20.3"}, "packageManager": "pnpm@9.15.2+sha512.93e57b0126f0df74ce6bff29680394c0ba54ec47246b9cf321f0121d8d9bb03f750a705f24edc3c1180853afd7c2c3b94196d0a3d53d3e069d9e2793ef11f321"}