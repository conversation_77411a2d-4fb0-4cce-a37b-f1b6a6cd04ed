# Somo Admin Server

Deployed at https://server.somo.ai

## Pre-requisites

You'll need [Node.js](https://nodejs.org/en/) and [pnpm](https://pnpm.io/installation) installed.

You'll also need a [Postgres](https://www.postgresql.org/) database. You can use [Docker](https://www.docker.com/) to run one:

```sh
docker run --name somo-postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 -d postgres
```

## Run locally

1. Install dependencies: `pnpm install`
2. Copy the `.env.example` file to `.env` and fill in the values. Ask your coworkers for help.
3. Run migrations: `npx nx run @somo/admin-server:migrate`
4. Run the app: `pnpm dev @somo/admin-server`

It will start the server and give you an URL to access the API.

### Common errors

```txt
src/prisma.ts(1,10): error TS2305: Module '"@prisma/client"' has no exported member 'PrismaClient'.
```

This means you need to generate the Prisma client. Run:

```
npx nx run @somo/admin-server:init
```

## Questions

- How can we get new devices to be discovered automatically?
