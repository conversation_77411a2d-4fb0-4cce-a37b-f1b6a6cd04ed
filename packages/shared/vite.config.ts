import { defineConfig, LibraryFormats, Plugin } from "vite";
import dts from "vite-plugin-dts";

export default defineConfig({
  root: __dirname,
  cacheDir: "../../node_modules/.vite/packages/shared",
  plugins: [
    dts({
      tsconfigPath: "./tsconfig.lib.json",
      rollupTypes: false,
    }) as Plugin,
  ],
  build: {
    lib: {
      entry: "./src/index.ts",
      name: "SomoShared",
      fileName: (format) => `index.${format === "es" ? "js" : "cjs"}`,
      formats: ["cjs", "es"] as LibraryFormats[],
    },
    rollupOptions: {
      external: [
        "@bufbuild/protobuf",
        "@xyflow/react",
        "ts-pattern",
        "tslib",
        "vitest",
        "zod",
        "react",
        "react-dom",
      ],
      output: {
        globals: {
          "@bufbuild/protobuf": "protobuf",
          "@xyflow/react": "XyflowReact",
          "ts-pattern": "tsPattern",
          tslib: "tslib",
          vitest: "vitest",
          zod: "zod",
          react: "React",
          "react-dom": "ReactDOM",
        },
      },
    },
    sourcemap: true,
    minify: false,
  },
  test: {
    watch: false,
    globals: true,
    environment: "node",
    include: ["src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"],
    reporters: ["default"],
    coverage: {
      reportsDirectory: "./test-output/vitest/coverage",
      provider: "v8" as const,
    },
  },
});
