{"name": "@somo/shared", "version": "0.0.1", "private": true, "type": "module", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "vite build", "test": "vitest", "typecheck": "tsc --noEmit --project tsconfig.lib.json"}, "dependencies": {"@bufbuild/protobuf": "^2.4.0", "@xyflow/react": "12.3.6", "ts-pattern": "^5.7.0", "vitest": "3.0.5", "zod": "4.0.8"}, "devDependencies": {"vite-plugin-dts": "~4.5.0"}}