import { CanBusControllerContainerNodeData } from "src/components";
import { match } from "ts-pattern";
import {
  getConnectorId,
  RelayOutputDevice,
  ZeroToTenVoltDimmerDevice,
} from "../components/devices/CanBusController";
import { DmxDevice } from "../components/devices/Dmx";
import { DoorSensorContainerNodeData } from "../components/devices/DoorSensor";
import { OutletDimmerDevice } from "../components/devices/OutletDimmer";
import { SomoDimmerDevice } from "../components/devices/SomoDimmer";
import { SomoSwitchDevice } from "../components/devices/SomoSwitch";
import { filterNodes, GraphNode } from "../components/GraphNode";
import {
  ActiveConfiguration,
  BasestationConfig_NodeQRMapping,
  BasestationConfig_NodeQRMapping_DeviceType,
  CanboConfig,
  CanboConfig_ThreePinInput,
  CanboConfig_ThreePinInput_Action,
  CanboConfig_ThreePinInput_ConnectorType,
  CanboConfig_TwoPinInput,
  CanboConfig_TwoPinInput_Action,
  CanboConfig_TwoPinInput_ConnectorType,
  LightConfig,
  LightConfig_FixtureConfig,
  LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig,
  LightConfig_FixtureConfig_FixtureType,
  LightConfig_FixtureConfig_RFConfig_Type,
  LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type,
  LightState,
  RFReedSensorConfig,
  RFReedSensorConfig_Action,
  RFReedState,
  RFReedState_Status,
} from "../proto/basestation-config";

interface Params {
  name: string;
  roomId: string;
  version: string;
  nodes: GraphNode[];
  wifiConfig?: {
    ssid: string;
    password: string;
  };
  rfConfig?: {
    channel: number;
    network: number;
  };
  nodeQrMappings?: {
    deviceId: string;
    qrCode: string;
  }[];
}

/**
 * Converts our graph into a data stream to update the basestation.
 *
 * It uses [ts-proto](https://github.com/stephenh/ts-proto) to generate the data
 * for the basestation, according to its protobuf definition.
 */
export function getActiveConfigurationForBasestation(params: Params) {
  const lightsIds = collectLightsIds(params.nodes);

  const nodeQrMappings = createNodeQrMappings(
    params.nodes,
    params.nodeQrMappings,
  );

  const deviceIdToNodeId: {
    deviceId: string;
    nodeId: number;
  }[] = [];
  params.nodeQrMappings?.forEach((mapping) => {
    const nodeId = nodeQrMappings.find(
      (m) => m.qrCode === mapping.qrCode,
    )?.nodeId;
    deviceIdToNodeId.push({ deviceId: mapping.deviceId, nodeId: nodeId ?? -1 });
  });

  // find all CanBusControllerContainerNodes
  const canbusControllers = params.nodes.filter(
    (node) => node.type === "canbusControllerContainer",
  );

  const canboConfigs = createCanboConfigs(
    canbusControllers,
    lightsIds,
    nodeQrMappings,
    params.nodeQrMappings,
  );

  // find all door sensor container nodes
  const doorSensors = params.nodes.filter(
    (node) => node.type === "doorSensorContainer",
  );

  const rfReedConfigs = createRFReedConfigs(
    doorSensors,
    lightsIds,
    nodeQrMappings,
    params.nodeQrMappings,
  );

  const activeConfiguration = {
    config: {
      id: 1,
      version: params.version,
      lights: toLights(params.nodes, lightsIds, deviceIdToNodeId),
      rfConfig: params.rfConfig,
      wifiConfig: params.wifiConfig,
      nodeQrMappings,
      canboConfigs,
      rfReedConfigs,
    },
    state: {
      lights: toLightStates(params.nodes, lightsIds),
      buttons: [],
      provisionedDevices: [],
      reeds: toRFReedStates(doorSensors, nodeQrMappings, params.nodeQrMappings),
    },
  } satisfies ActiveConfiguration;

  return {
    lightsIds,
    activeConfiguration,
  };
}

/**
 * Creates node QR mappings for the basestation configuration.
 * Maps physical device containers to their QR codes and assigns sequential node IDs.
 */
function createNodeQrMappings(
  nodes: GraphNode[],
  nodeQrMappings?: { deviceId: string; qrCode: string }[],
): BasestationConfig_NodeQRMapping[] {
  const mappings: BasestationConfig_NodeQRMapping[] = [];

  // Build the container to QR mapping
  const containerToQrMapping = new Map<string, string>();
  nodeQrMappings?.forEach((mapping) => {
    containerToQrMapping.set(mapping.deviceId, mapping.qrCode);
  });

  // Track which containers we've already added to avoid duplicates
  const processedContainers = new Set<string>();

  // Helper function to determine device type based on container type
  const getDeviceType = (containerType: string) => {
    return containerType === "controllerContainer" ||
      containerType === "canbusControllerContainer"
      ? BasestationConfig_NodeQRMapping_DeviceType.CAN
      : BasestationConfig_NodeQRMapping_DeviceType.RF;
  };

  let currentDeviceIndex = 40;

  for (const node of nodes) {
    // "container" nodes refers to the physical devices have QR codes
    if (node.type?.endsWith("Container")) {
      const qrCode = containerToQrMapping.get(node.id);

      if (qrCode && !processedContainers.has(node.id)) {
        // Use the json represenation of the node, then compute a bounded CRC8 and use that
        // as the nodeId. Ensure that there's no collision with the existing nodeIds.
        // and make sure that its larger than 40.
        // const currentDeviceIndex =
        //   boundedCRC8(JSON.stringify(node.data), 215, 10) + 40;

        currentDeviceIndex++;

        mappings.push({
          qrCode,
          nodeId: currentDeviceIndex,
          type: getDeviceType(node.type),
        });

        processedContainers.add(node.id);
      }
    }
  }

  return mappings;
}

export function serializeNodesForBasestation(params: Params) {
  const { lightsIds, activeConfiguration } =
    getActiveConfigurationForBasestation(params);

  const data = ActiveConfiguration.encode(activeConfiguration).finish();

  return { lightsIds, data };
}

type LightsIds = { basestationId: number; deviceId: string }[];

/**
 * The basestation uses int32 IDs so it's smaller than UUIDs.
 * Generate a mapping from our UUIDs -> basestation IDs, so we can use them
 * in the protobuf messages.
 */
function collectLightsIds(nodes: GraphNode[]): LightsIds {
  return getLights(nodes).map((device, index) => ({
    deviceId: device.id,
    basestationId: index,
  }));
}

function getLightIndex(lightsIds: LightsIds, deviceId: string) {
  const light = lightsIds.find((l) => l.deviceId === deviceId);
  if (!light) {
    console.error("Can't find light that should have been mapped", {
      deviceId,
      lightsIds,
    });
    return;
  }

  return light.basestationId;
}

function toLightStates(nodes: GraphNode[], lightsIds: LightsIds): LightState[] {
  return getLights(nodes)
    .map((device) => {
      const id = getLightIndex(lightsIds, device.id);
      if (id === undefined) {
        return;
      }

      return match(device)
        .with({ type: "dmx" }, (device) => ({
          id,
          brightness: 0,
          targetValue: 0,
          dimSpeedMsec: device.defaultDimmingSpeed * 1000,
          lastModifiedTime: Date.now(),
          activeAfterTime: 0,
          isTransitioning: false,
        }))
        .with({ type: "somoSwitch" }, (_device) => ({
          id,
          brightness: 0,
          targetValue: 0,
          dimSpeedMsec: 0,
          lastModifiedTime: Date.now(),
          activeAfterTime: 0,
          isTransitioning: false,
        }))
        .with({ type: "somoDimmer" }, { type: "outletDimmer" }, (device) => ({
          id,
          brightness: 0,
          targetValue: 0,
          dimSpeedMsec: (device.defaultDimmingSpeed ?? 0) * 1000,
          lastModifiedTime: Date.now(),
          activeAfterTime: 0,
          isTransitioning: false,
        }))
        .with({ type: "zeroToTenVoltDimmer" }, (_device) => ({
          id,
          brightness: 0,
          targetValue: 0,
          dimSpeedMsec: 0, // 0-10V dimmers don't have dimming speed in our current implementation
          lastModifiedTime: Date.now(),
          activeAfterTime: 0,
          isTransitioning: false,
        }))
        .with({ type: "relayOutput" }, (_device) => ({
          id,
          brightness: 0,
          targetValue: 0,
          dimSpeedMsec: 0, // Relay outputs don't dim, they're on/off
          lastModifiedTime: Date.now(),
          activeAfterTime: 0,
          isTransitioning: false,
        }))
        .exhaustive() satisfies LightState;
    })
    .filter((state) => state !== undefined);
}

function toRFReedStates(
  doorSensors: GraphNode[],
  nodeQrMappings: BasestationConfig_NodeQRMapping[],
  nodeQrMappingsParam?: { deviceId: string; qrCode: string }[],
): RFReedState[] {
  return doorSensors.map((doorSensor) => {
    const qrCode = nodeQrMappingsParam?.find(
      (mapping) => mapping.deviceId === doorSensor.id,
    )?.qrCode;
    if (!qrCode) {
      throw new Error(`Can't find QR code for door sensor ${doorSensor.id}`);
    }
    const nodeId = nodeQrMappings.find(
      (mapping) => mapping.qrCode === qrCode,
    )?.nodeId;
    if (!nodeId) {
      throw new Error(`Can't find node ID for door sensor ${doorSensor.id}`);
    }

    return {
      nodeId,
      sensorStatus: RFReedState_Status.UNKNOWN,
      lastModifiedTime: Date.now(),
      batteryVoltage: 0.0,
    } satisfies RFReedState;
  });
}

function toLights(
  nodes: GraphNode[],
  lightsIds: LightsIds,
  deviceIdToNodeId: { deviceId: string; nodeId: number }[],
): LightConfig[] {
  return getLights(nodes)
    .map((device) => {
      const id = getLightIndex(lightsIds, device.id);
      if (id === undefined) {
        return;
      }

      return match(device)
        .with(
          { type: "dmx" },
          (device) =>
            ({
              id,
              dimSpeedMsec: device.defaultDimmingSpeed * 1000,
              fixtures: toLightFixture(device),
            }) satisfies LightConfig,
        )
        .with(
          { type: "somoSwitch" },
          (_device) =>
            ({
              id,
              dimSpeedMsec: 0,
              fixtures: [],
            }) satisfies LightConfig,
        )
        .with(
          { type: "somoDimmer" },
          (device) =>
            ({
              id,
              dimSpeedMsec: (device.defaultDimmingSpeed ?? 0) * 1000,
              fixtures: [
                {
                  minBrightness: 0,
                  maxBrightness: 100,
                  type: LightConfig_FixtureConfig_FixtureType.RF,
                  rf: {
                    type: LightConfig_FixtureConfig_RFConfig_Type.DIMMER,
                    nodeId:
                      deviceIdToNodeId.find((d) => d.deviceId === device.nodeId)
                        ?.nodeId ?? -1,
                  },
                },
              ],
            }) satisfies LightConfig,
        )
        .with(
          { type: "outletDimmer" },
          (device) =>
            ({
              id,
              dimSpeedMsec: (device.defaultDimmingSpeed ?? 0) * 1000,
              fixtures: [],
            }) satisfies LightConfig,
        )
        .with({ type: "zeroToTenVoltDimmer" }, (device) => {
          // Find the actual dimmer from the CanBus controller to get current settings
          const canBusNode = nodes.find(
            (n) =>
              n.type === "canbusControllerContainer" && n.id === device.nodeId,
          );

          // Type guard to ensure we have the correct node type
          const actualDimmer =
            canBusNode?.type === "canbusControllerContainer"
              ? (canBusNode.data as CanBusControllerContainerNodeData)
                  .zeroToTenVoltDimmers?.[device.viaId]
              : undefined;

          const dimmingType = actualDimmer?.dimmingType || "sinking";
          const useRelay = actualDimmer?.useRelay || false;

          // Extract connector number from relay connector ID (e.g., "relay-connector-1" -> 1)
          const connectorId = actualDimmer?.relayConnectorId
            ? parseInt(actualDimmer.relayConnectorId.split("-").pop() || "0")
            : 0;

          return {
            id,
            dimSpeedMsec: (actualDimmer?.defaultDimmingSpeed || 0.2) * 1000,
            fixtures: [
              {
                minBrightness: actualDimmer?.minBrightness || 0,
                maxBrightness: actualDimmer?.maxBrightness || 100,
                type: LightConfig_FixtureConfig_FixtureType.ZERO_TO_TEN_VOLT,
                zeroToTenVolt: {
                  type:
                    dimmingType === "sourcing"
                      ? LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type.SOURCING
                      : LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type.SINKING,
                  useRelay,
                  outConnectorId: connectorId,
                  nodeId:
                    deviceIdToNodeId.find((d) => d.deviceId === canBusNode?.id)
                      ?.nodeId ?? -1,
                },
              },
            ],
          } satisfies LightConfig;
        })
        .with({ type: "relayOutput" }, (device) => {
          // Find the actual relay output from the CanBus controller to get current settings
          const canBusNode = nodes.find(
            (n) =>
              n.type === "canbusControllerContainer" && n.id === device.nodeId,
          );

          // Type guard to ensure we have the correct node type
          const actualRelayOutput =
            canBusNode?.type === "canbusControllerContainer"
              ? (canBusNode.data as CanBusControllerContainerNodeData)
                  .relayOutputs?.[device.viaId]
              : undefined;

          // Extract connector number from relay connector ID (e.g., "relay-connector-1" -> 1)
          const connectorId = actualRelayOutput?.relayConnectorId
            ? parseInt(
                actualRelayOutput.relayConnectorId.split("-").pop() || "0",
              )
            : 0;

          return {
            id,
            dimSpeedMsec: 0, // Relay outputs don't dim, they're on/off
            fixtures: [
              {
                minBrightness: 0,
                maxBrightness: 100,
                type: LightConfig_FixtureConfig_FixtureType.ON_OFF_LIGHT,
                onOffLight: {
                  nodeId:
                    deviceIdToNodeId.find((d) => d.deviceId === canBusNode?.id)
                      ?.nodeId ?? -1,
                  outConnectorId: connectorId,
                },
              },
            ],
          } satisfies LightConfig;
        })
        .exhaustive();
    })
    .filter((light) => light !== undefined);
}

type SectionDevice =
  | DmxDevice
  | SomoSwitchDevice
  | SomoDimmerDevice
  | OutletDimmerDevice
  | ZeroToTenVoltDimmerDevice
  | RelayOutputDevice;

function getLights(nodes: GraphNode[]): SectionDevice[] {
  return filterNodes(nodes, "section").flatMap<SectionDevice>((node) => {
    const devices = Object.values(node.data.devices);
    return devices.filter(
      (device) =>
        device.type === "dmx" ||
        device.type === "somoSwitch" ||
        device.type === "somoDimmer" ||
        device.type === "outletDimmer" ||
        device.type === "zeroToTenVoltDimmer" ||
        device.type === "relayOutput",
    );
  });
}

function toLightFixture(device: DmxDevice): LightConfig_FixtureConfig[] {
  return Object.values(device.fixtures).map<LightConfig_FixtureConfig>(
    (fixture) =>
      match(fixture.type)
        // TODO FIXME: Add support for analog fixtures
        .with("Analog", () => ({
          minBrightness: fixture.minBrightness,
          maxBrightness: fixture.maxBrightness,
          type: LightConfig_FixtureConfig_FixtureType.DMX,
          dmx: {
            type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig.TUNABLE_WHITE,
            channels: [fixture.channel],
            params: undefined,
            rgb: undefined,
          },
        }))
        .with("D4", () => ({
          minBrightness: fixture.minBrightness,
          maxBrightness: fixture.maxBrightness,
          type: LightConfig_FixtureConfig_FixtureType.DMX,
          dmx: {
            type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig.D4,
            channels: [fixture.channel],
            params: undefined,
            rgb: undefined,
          },
        }))
        .with("DF_12", () => ({
          minBrightness: fixture.minBrightness,
          maxBrightness: fixture.maxBrightness,
          type: LightConfig_FixtureConfig_FixtureType.DMX,
          dmx: {
            type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig.DF_12,
            channels: [fixture.channel],
            params: undefined,
            rgb: undefined,
          },
        }))
        .with("ELV", () => ({
          minBrightness: fixture.minBrightness,
          maxBrightness: fixture.maxBrightness,
          type: LightConfig_FixtureConfig_FixtureType.DMX,
          dmx: {
            type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig.ELV,
            channels: [fixture.channel],
            params: undefined,
            rgb: undefined,
          },
        }))
        .with("EST", () => ({
          minBrightness: fixture.minBrightness,
          maxBrightness: fixture.maxBrightness,
          type: LightConfig_FixtureConfig_FixtureType.DMX,
          dmx: {
            type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig.EST,
            channels: [fixture.channel],
            params: undefined,
            rgb: undefined,
          },
        }))
        .with("Tunable White", () => ({
          minBrightness: fixture.minBrightness,
          maxBrightness: fixture.maxBrightness,
          type: LightConfig_FixtureConfig_FixtureType.DMX,
          dmx: {
            type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig.TUNABLE_WHITE,
            channels: [fixture.channel],
            params: {
              min1: 0.0,
              max1: 1.0,
              gamma1: 1.5,
              min2: 0.0,
              max2: 1.0,
              gamma2: 1.5,
            },
            rgb: undefined,
          },
        }))
        .exhaustive(),
  );
}

/**
 * Creates CAN bus controller configurations for the basestation.
 */
function createCanboConfigs(
  canbusControllers: GraphNode[],
  lightsIds: LightsIds,
  nodeQrMappings: BasestationConfig_NodeQRMapping[],
  nodeQrMappingsParam?: { deviceId: string; qrCode: string }[],
): CanboConfig[] {
  return canbusControllers.map((controller) => {
    const data = controller.data as CanBusControllerContainerNodeData;
    const qrCode = nodeQrMappingsParam?.find(
      (mapping) => mapping.deviceId === controller.id,
    )?.qrCode;
    if (!qrCode) {
      throw new Error(
        `Can't find QR code for canbus controller ${controller.id}`,
      );
    }
    const nodeId = nodeQrMappings.find(
      (mapping) => mapping.qrCode === qrCode,
    )?.nodeId;
    if (!nodeId) {
      throw new Error(
        `Can't find node ID for canbus controller ${controller.id}`,
      );
    }

    // Process 3-pin inputs (toggle and momentary buttons)
    const threePinInputs: CanboConfig_ThreePinInput[] = Object.values(
      data.controllers,
    )
      .filter((controller) => controller.portId?.startsWith("3pin-"))
      .map((controller) => {
        const connectorId = getConnectorId(controller);

        if (controller.type === "toggle") {
          const upClickActions = Object.values(controller.onUpClick).map(
            (action) => {
              const lightId = lightsIds.find(
                (l) => l.deviceId === action.deviceId,
              )?.basestationId;
              if (lightId === undefined) {
                throw new Error(
                  `Can't find light ID for action ${action.deviceId}`,
                );
              }
              return {
                dimSpeedMsec: action.dimSpeed * 1000,
                lightId,
                targetBrightness: action.targetValue,
                onBrightness: action.onValue,
                offBrightness: action.offValue,
              } satisfies CanboConfig_ThreePinInput_Action;
            },
          );

          const upHoldActions = Object.values(controller.onUpHold).map(
            (action) => {
              const lightId = lightsIds.find(
                (l) => l.deviceId === action.deviceId,
              )?.basestationId;
              if (lightId === undefined) {
                throw new Error(
                  `Can't find light ID for action ${action.deviceId}`,
                );
              }
              return {
                dimSpeedMsec: action.dimSpeed * 1000,
                lightId,
                targetBrightness: action.targetValue,
                onBrightness: action.onValue,
                offBrightness: action.offValue,
              } satisfies CanboConfig_ThreePinInput_Action;
            },
          );

          const downClickActions = Object.values(controller.onDownClick).map(
            (action) => {
              const lightId = lightsIds.find(
                (l) => l.deviceId === action.deviceId,
              )?.basestationId;
              if (lightId === undefined) {
                throw new Error(
                  `Can't find light ID for action ${action.deviceId}`,
                );
              }
              return {
                dimSpeedMsec: action.dimSpeed * 1000,
                lightId,
                targetBrightness: action.targetValue,
                onBrightness: action.onValue,
                offBrightness: action.offValue,
              } satisfies CanboConfig_ThreePinInput_Action;
            },
          );

          const downHoldActions = Object.values(controller.onDownHold).map(
            (action) => {
              const lightId = lightsIds.find(
                (l) => l.deviceId === action.deviceId,
              )?.basestationId;
              if (lightId === undefined) {
                throw new Error(
                  `Can't find light ID for action ${action.deviceId}`,
                );
              }
              return {
                dimSpeedMsec: action.dimSpeed * 1000,
                lightId,
                targetBrightness: action.targetValue,
                onBrightness: action.onValue,
                offBrightness: action.offValue,
              } satisfies CanboConfig_ThreePinInput_Action;
            },
          );

          return {
            connectorId,
            type: CanboConfig_ThreePinInput_ConnectorType.TOGGLE,
            toggle: {
              upClick: upClickActions,
              upHold: upHoldActions,
              downClick: downClickActions,
              downHold: downHoldActions,
            },
          } satisfies CanboConfig_ThreePinInput;
        }

        if (controller.type === "momentary") {
          const upClickActions = Object.values(controller.onUpClick).map(
            (action) => {
              const lightId = lightsIds.find(
                (l) => l.deviceId === action.deviceId,
              )?.basestationId;
              if (lightId === undefined) {
                throw new Error(
                  `Can't find light ID for action ${action.deviceId}`,
                );
              }
              return {
                dimSpeedMsec: action.dimSpeed * 1000,
                lightId,
                targetBrightness: action.targetValue,
                onBrightness: action.onValue,
                offBrightness: action.offValue,
              } satisfies CanboConfig_ThreePinInput_Action;
            },
          );

          const upHoldActions = Object.values(controller.onUpHold).map(
            (action) => {
              const lightId = lightsIds.find(
                (l) => l.deviceId === action.deviceId,
              )?.basestationId;
              if (lightId === undefined) {
                throw new Error(
                  `Can't find light ID for action ${action.deviceId}`,
                );
              }
              return {
                dimSpeedMsec: action.dimSpeed * 1000,
                lightId,
                targetBrightness: action.targetValue,
                onBrightness: action.onValue,
                offBrightness: action.offValue,
              } satisfies CanboConfig_ThreePinInput_Action;
            },
          );

          return {
            connectorId,
            type: CanboConfig_ThreePinInput_ConnectorType.MOMENTARY,
            momentary: {
              upClick: upClickActions,
              upHold: upHoldActions,
            },
          } satisfies CanboConfig_ThreePinInput;
        }

        throw new Error(`Unknown controller type:`);
      });

    // Process 2-pin inputs (PIR sensors, momentary buttons, door sensors)
    const twoPinInputs: CanboConfig_TwoPinInput[] = [];

    // Add PIR sensors
    Object.values(data.pirSensors || {})
      .filter((sensor) => sensor.portId?.startsWith("2pin-"))
      .forEach((sensor) => {
        const connectorId = parseInt(sensor.portId?.split("-").pop() || "0");

        const onActivateActions = Object.values(sensor.onActivate).map(
          (action) => {
            const lightId = lightsIds.find(
              (l) => l.deviceId === action.deviceId,
            )?.basestationId;
            if (lightId === undefined) {
              throw new Error(
                `Can't find light ID for action ${action.deviceId}`,
              );
            }
            return {
              dimSpeedMsec: action.dimSpeed * 1000,
              lightId,
              delayInMsec: 0, // PIR sensors don't have delay in our current implementation
              targetBrightness: action.targetValue,
              onBrightness: action.onValue,
              offBrightness: action.offValue,
            } satisfies CanboConfig_TwoPinInput_Action;
          },
        );

        const onDeactivateActions = Object.values(sensor.onDeactivate).map(
          (action) => {
            const lightId = lightsIds.find(
              (l) => l.deviceId === action.deviceId,
            )?.basestationId;
            if (lightId === undefined) {
              throw new Error(
                `Can't find light ID for action ${action.deviceId}`,
              );
            }
            return {
              dimSpeedMsec: action.dimSpeed * 1000,
              lightId,
              delayInMsec: 0, // PIR sensors don't have delay in our current implementation
              targetBrightness: action.targetValue,
              onBrightness: action.onValue,
              offBrightness: action.offValue,
            } satisfies CanboConfig_TwoPinInput_Action;
          },
        );

        twoPinInputs.push({
          connectorId,
          type: CanboConfig_TwoPinInput_ConnectorType.PIR,
          pir: {
            onActivate: onActivateActions,
            onDeactivate: onDeactivateActions,
          },
        } satisfies CanboConfig_TwoPinInput);
      });

    // Add 2-pin momentary buttons (if any)
    Object.values(data.controllers)
      .filter((controller) => controller.portId?.startsWith("2pin-"))
      .forEach((controller) => {
        if (controller.type === "momentary") {
          const connectorId = getConnectorId(controller);

          const upClickActions = Object.values(controller.onUpClick).map(
            (action) => {
              const lightId = lightsIds.find(
                (l) => l.deviceId === action.deviceId,
              )?.basestationId;
              if (lightId === undefined) {
                throw new Error(
                  `Can't find light ID for action ${action.deviceId}`,
                );
              }
              return {
                dimSpeedMsec: action.dimSpeed * 1000,
                lightId,
                delayInMsec: 0,
                targetBrightness: action.targetValue,
                onBrightness: action.onValue,
                offBrightness: action.offValue,
              } satisfies CanboConfig_TwoPinInput_Action;
            },
          );

          const upHoldActions = Object.values(controller.onUpHold).map(
            (action) => {
              const lightId = lightsIds.find(
                (l) => l.deviceId === action.deviceId,
              )?.basestationId;
              if (lightId === undefined) {
                throw new Error(
                  `Can't find light ID for action ${action.deviceId}`,
                );
              }
              return {
                dimSpeedMsec: action.dimSpeed * 1000,
                lightId,
                delayInMsec: 0,
                targetBrightness: action.targetValue,
                onBrightness: action.onValue,
                offBrightness: action.offValue,
              } satisfies CanboConfig_TwoPinInput_Action;
            },
          );

          twoPinInputs.push({
            connectorId,
            type: CanboConfig_TwoPinInput_ConnectorType.MOMENTARY,
            momentary: {
              upClick: upClickActions,
              upHold: upHoldActions,
            },
          } satisfies CanboConfig_TwoPinInput);
        }
      });

    return {
      nodeId,
      threePinInputs,
      twoPinInputs,
    } satisfies CanboConfig;
  });
}

/**
 * Creates RF reed sensor configurations for the basestation.
 */
function createRFReedConfigs(
  doorSensors: GraphNode[],
  lightsIds: LightsIds,
  nodeQrMappings: BasestationConfig_NodeQRMapping[],
  nodeQrMappingsParam?: { deviceId: string; qrCode: string }[],
): RFReedSensorConfig[] {
  return doorSensors.map((doorSensor) => {
    const data = doorSensor.data as DoorSensorContainerNodeData;
    const qrCode = nodeQrMappingsParam?.find(
      (mapping) => mapping.deviceId === doorSensor.id,
    )?.qrCode;
    if (!qrCode) {
      throw new Error(`Can't find QR code for door sensor ${doorSensor.id}`);
    }
    const nodeId = nodeQrMappings.find(
      (mapping) => mapping.qrCode === qrCode,
    )?.nodeId;
    if (!nodeId) {
      throw new Error(`Can't find node ID for door sensor ${doorSensor.id}`);
    }

    const doorOpenDelayMsec = (data.onOpen?.offDelay ?? 0) * 1000;
    const doorCloseDelayMsec = (data.onClose?.offDelay ?? 0) * 1000;
    // Process door open actions
    const doorOpenActions: RFReedSensorConfig_Action[] = Object.values(
      data.onOpen?.onUpClick ?? {},
    )
      .map((action) => {
        if (action.type === "thermostat") {
          //TODO: Add support for thermostat actions when we add hvac to the basestation
          return null;
        }
        const lightId = lightsIds.find(
          (l) => l.deviceId === action.deviceId,
        )?.basestationId;
        if (lightId === undefined) {
          throw new Error(`Can't find light ID for action ${action.deviceId}`);
        }
        return {
          dimSpeedMsec: action.dimSpeed * 1000,
          lightId,
          targetBrightness: action.targetValue,
          activateDelayMsec: doorOpenDelayMsec,
        } satisfies RFReedSensorConfig_Action;
      })
      .filter((action): action is RFReedSensorConfig_Action => action !== null);

    // Process door close actions
    const doorCloseActions: RFReedSensorConfig_Action[] = Object.values(
      data.onClose?.onUpClick ?? {},
    )
      .map((action) => {
        if (action.type === "thermostat") {
          //TODO: Add support for thermostat actions when we add hvac to the basestation
          return null;
        }
        const lightId = lightsIds.find(
          (l) => l.deviceId === action.deviceId,
        )?.basestationId;
        if (lightId === undefined) {
          throw new Error(`Can't find light ID for action ${action.deviceId}`);
        }
        return {
          dimSpeedMsec: action.dimSpeed * 1000,
          lightId,
          targetBrightness: action.targetValue,
          activateDelayMsec: doorCloseDelayMsec,
        } satisfies RFReedSensorConfig_Action;
      })
      .filter((action): action is RFReedSensorConfig_Action => action !== null);

    return {
      nodeId,
      doorOpen: doorOpenActions,
      doorClose: doorCloseActions,
    } satisfies RFReedSensorConfig;
  });
}
