export function crc8(data: Uint8Array, poly = 0x07, init = 0x00): number {
  let crc = init;
  for (const byte of data) {
    crc ^= byte;
    for (let i = 0; i < 8; i++) {
      if ((crc & 0x80) !== 0) {
        crc = ((crc << 1) ^ poly) & 0xff;
      } else {
        crc = (crc << 1) & 0xff;
      }
    }
  }
  return crc;
}

export function boundedCRC8(
  inputStr: string,
  maxValue = 215,
  maxRetries = 10,
): number {
  const encoder = new TextEncoder();
  const baseData = encoder.encode(inputStr);

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    const salted = new Uint8Array([...baseData, attempt]); // Append salt
    const crc = crc8(salted);
    if (crc <= maxValue) {
      return crc;
    }
  }

  throw new Error(
    `Failed to compute CRC ≤ ${maxValue} after ${maxRetries} retries.`,
  );
}
