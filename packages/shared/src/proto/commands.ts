// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v5.29.3
// source: commands.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "";

export interface MomentaryButtonCommand {
  connectorId: number;
  state: MomentaryButtonCommand_State;
}

export enum MomentaryButtonCommand_State {
  Pressed = 0,
  Released = 1,
  UNRECOGNIZED = -1,
}

export interface ToggleButtonCommand {
  connectorId: number;
  state: ToggleButtonCommand_State;
}

export enum ToggleButtonCommand_State {
  Up = 0,
  Released = 1,
  Down = 2,
  UNRECOGNIZED = -1,
}

export interface MotionDetectedCommand {
  connectorId: number;
  state: MotionDetectedCommand_State;
}

export enum MotionDetectedCommand_State {
  Detected = 0,
  Stopped = 1,
  UNRECOGNIZED = -1,
}

export interface RFDoorSensorCommand {
  nodeId: number;
  state: RFDoorSensorCommand_State;
}

export enum RFDoorSensorCommand_State {
  Opened = 0,
  Closed = 1,
  UNRECOGNIZED = -1,
}

export interface DoorSensorCommand {
  connectorId: number;
  state: DoorSensorCommand_State;
}

export enum DoorSensorCommand_State {
  Opened = 0,
  Closed = 1,
  UNRECOGNIZED = -1,
}

export interface KnobCommand {
  connectorId: number;
  percentTurn: number;
}

export interface ThermostatCommand {
  connectorId: number;
  degreeCelcius: number;
}

export interface PingCommand {
  qrCode: string;
  isProvisioned: boolean;
  version: string;
}

export interface CanboProvisioningCommand {
  nodeId: number;
  version: string;
  qrCode: string;
  threePinInputs: CanboProvisioningCommand_ThreePinInput[];
  twoPinInputs: CanboProvisioningCommand_TwoPinInput[];
  adcInputs: CanboProvisioningCommand_ADCInput | undefined;
  outputs: CanboProvisioningCommand_Output[];
  zeroToTenVoltConfig: CanboProvisioningCommand_ZeroToTenVoltConfig | undefined;
  klevernessConnector: CanboProvisioningCommand_KlevernessConnector | undefined;
}

export interface CanboProvisioningCommand_ThreePinInput {
  connectorId: number;
  connectorType: CanboProvisioningCommand_ThreePinInput_ConnectorType;
}

export enum CanboProvisioningCommand_ThreePinInput_ConnectorType {
  TOGGLE = 0,
  MOMENTARY = 1,
  UNRECOGNIZED = -1,
}

export interface CanboProvisioningCommand_TwoPinInput {
  connectorId: number;
  connectorType: CanboProvisioningCommand_TwoPinInput_ConnectorType;
}

export enum CanboProvisioningCommand_TwoPinInput_ConnectorType {
  PIR = 0,
  MOMENTARY = 1,
  DOOR_SENSOR = 2,
  UNRECOGNIZED = -1,
}

export interface CanboProvisioningCommand_ADCInput {
  offsetScaling?: CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange | undefined;
  minMax?: CanboProvisioningCommand_ADCInput_MinMaxRange | undefined;
  connectorType: CanboProvisioningCommand_ADCInput_ConnectorType;
}

export enum CanboProvisioningCommand_ADCInput_ConnectorType {
  KNOB = 0,
  THERMOSTAT = 1,
  UNRECOGNIZED = -1,
}

export interface CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange {
  offset: number;
  scalingFactor: number;
}

export interface CanboProvisioningCommand_ADCInput_MinMaxRange {
  min: number;
  max: number;
}

export interface CanboProvisioningCommand_ZeroToTenVoltConfig {
  type: CanboProvisioningCommand_ZeroToTenVoltConfig_Type;
  useRelay: boolean;
  relayConnectorId: number;
  minBrightness: number;
  maxBrightness: number;
}

export enum CanboProvisioningCommand_ZeroToTenVoltConfig_Type {
  SOURCING = 0,
  SINKING = 1,
  UNRECOGNIZED = -1,
}

export interface CanboProvisioningCommand_Output {
  connectorId: number;
  connectorType: CanboProvisioningCommand_Output_ConnectorType;
}

export enum CanboProvisioningCommand_Output_ConnectorType {
  Relay = 0,
  UNRECOGNIZED = -1,
}

export interface CanboProvisioningCommand_KlevernessConnector {
  isEnabled: boolean;
  button1Enabled: boolean;
  button2Enabled: boolean;
  button3Enabled: boolean;
  lowLightLedEnabled: boolean;
  highLightLedEnabled: boolean;
}

export interface StartDimmingCommand {
  nodeId: number;
  brightness: number;
  dimSpeedMsec: number;
}

export interface StopDimmingCommand {
  nodeId: number;
}

export interface DimmingStateCommand {
  nodeId: number;
  brightness: number;
}

export interface ToggleRelayCommand {
  connectorId: number;
  state: ToggleRelayCommand_State;
}

export enum ToggleRelayCommand_State {
  On = 0,
  Off = 1,
  UNRECOGNIZED = -1,
}

function createBaseMomentaryButtonCommand(): MomentaryButtonCommand {
  return { connectorId: 0, state: 0 };
}

export const MomentaryButtonCommand: MessageFns<MomentaryButtonCommand> = {
  encode(message: MomentaryButtonCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.state !== 0) {
      writer.uint32(16).int32(message.state);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MomentaryButtonCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMomentaryButtonCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<MomentaryButtonCommand>, I>>(base?: I): MomentaryButtonCommand {
    return MomentaryButtonCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MomentaryButtonCommand>, I>>(object: I): MomentaryButtonCommand {
    const message = createBaseMomentaryButtonCommand();
    message.connectorId = object.connectorId ?? 0;
    message.state = object.state ?? 0;
    return message;
  },
};

function createBaseToggleButtonCommand(): ToggleButtonCommand {
  return { connectorId: 0, state: 0 };
}

export const ToggleButtonCommand: MessageFns<ToggleButtonCommand> = {
  encode(message: ToggleButtonCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.state !== 0) {
      writer.uint32(16).int32(message.state);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ToggleButtonCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseToggleButtonCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ToggleButtonCommand>, I>>(base?: I): ToggleButtonCommand {
    return ToggleButtonCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ToggleButtonCommand>, I>>(object: I): ToggleButtonCommand {
    const message = createBaseToggleButtonCommand();
    message.connectorId = object.connectorId ?? 0;
    message.state = object.state ?? 0;
    return message;
  },
};

function createBaseMotionDetectedCommand(): MotionDetectedCommand {
  return { connectorId: 0, state: 0 };
}

export const MotionDetectedCommand: MessageFns<MotionDetectedCommand> = {
  encode(message: MotionDetectedCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.state !== 0) {
      writer.uint32(16).int32(message.state);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MotionDetectedCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMotionDetectedCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<MotionDetectedCommand>, I>>(base?: I): MotionDetectedCommand {
    return MotionDetectedCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MotionDetectedCommand>, I>>(object: I): MotionDetectedCommand {
    const message = createBaseMotionDetectedCommand();
    message.connectorId = object.connectorId ?? 0;
    message.state = object.state ?? 0;
    return message;
  },
};

function createBaseRFDoorSensorCommand(): RFDoorSensorCommand {
  return { nodeId: 0, state: 0 };
}

export const RFDoorSensorCommand: MessageFns<RFDoorSensorCommand> = {
  encode(message: RFDoorSensorCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.state !== 0) {
      writer.uint32(16).int32(message.state);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RFDoorSensorCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRFDoorSensorCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RFDoorSensorCommand>, I>>(base?: I): RFDoorSensorCommand {
    return RFDoorSensorCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RFDoorSensorCommand>, I>>(object: I): RFDoorSensorCommand {
    const message = createBaseRFDoorSensorCommand();
    message.nodeId = object.nodeId ?? 0;
    message.state = object.state ?? 0;
    return message;
  },
};

function createBaseDoorSensorCommand(): DoorSensorCommand {
  return { connectorId: 0, state: 0 };
}

export const DoorSensorCommand: MessageFns<DoorSensorCommand> = {
  encode(message: DoorSensorCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.state !== 0) {
      writer.uint32(16).int32(message.state);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DoorSensorCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDoorSensorCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<DoorSensorCommand>, I>>(base?: I): DoorSensorCommand {
    return DoorSensorCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DoorSensorCommand>, I>>(object: I): DoorSensorCommand {
    const message = createBaseDoorSensorCommand();
    message.connectorId = object.connectorId ?? 0;
    message.state = object.state ?? 0;
    return message;
  },
};

function createBaseKnobCommand(): KnobCommand {
  return { connectorId: 0, percentTurn: 0 };
}

export const KnobCommand: MessageFns<KnobCommand> = {
  encode(message: KnobCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.percentTurn !== 0) {
      writer.uint32(21).float(message.percentTurn);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): KnobCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseKnobCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 21) {
            break;
          }

          message.percentTurn = reader.float();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<KnobCommand>, I>>(base?: I): KnobCommand {
    return KnobCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KnobCommand>, I>>(object: I): KnobCommand {
    const message = createBaseKnobCommand();
    message.connectorId = object.connectorId ?? 0;
    message.percentTurn = object.percentTurn ?? 0;
    return message;
  },
};

function createBaseThermostatCommand(): ThermostatCommand {
  return { connectorId: 0, degreeCelcius: 0 };
}

export const ThermostatCommand: MessageFns<ThermostatCommand> = {
  encode(message: ThermostatCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.degreeCelcius !== 0) {
      writer.uint32(21).float(message.degreeCelcius);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ThermostatCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseThermostatCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 21) {
            break;
          }

          message.degreeCelcius = reader.float();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ThermostatCommand>, I>>(base?: I): ThermostatCommand {
    return ThermostatCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ThermostatCommand>, I>>(object: I): ThermostatCommand {
    const message = createBaseThermostatCommand();
    message.connectorId = object.connectorId ?? 0;
    message.degreeCelcius = object.degreeCelcius ?? 0;
    return message;
  },
};

function createBasePingCommand(): PingCommand {
  return { qrCode: "", isProvisioned: false, version: "" };
}

export const PingCommand: MessageFns<PingCommand> = {
  encode(message: PingCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.qrCode !== "") {
      writer.uint32(10).string(message.qrCode);
    }
    if (message.isProvisioned !== false) {
      writer.uint32(16).bool(message.isProvisioned);
    }
    if (message.version !== "") {
      writer.uint32(26).string(message.version);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PingCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePingCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.qrCode = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isProvisioned = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.version = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PingCommand>, I>>(base?: I): PingCommand {
    return PingCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PingCommand>, I>>(object: I): PingCommand {
    const message = createBasePingCommand();
    message.qrCode = object.qrCode ?? "";
    message.isProvisioned = object.isProvisioned ?? false;
    message.version = object.version ?? "";
    return message;
  },
};

function createBaseCanboProvisioningCommand(): CanboProvisioningCommand {
  return {
    nodeId: 0,
    version: "",
    qrCode: "",
    threePinInputs: [],
    twoPinInputs: [],
    adcInputs: undefined,
    outputs: [],
    zeroToTenVoltConfig: undefined,
    klevernessConnector: undefined,
  };
}

export const CanboProvisioningCommand: MessageFns<CanboProvisioningCommand> = {
  encode(message: CanboProvisioningCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.version !== "") {
      writer.uint32(18).string(message.version);
    }
    if (message.qrCode !== "") {
      writer.uint32(26).string(message.qrCode);
    }
    for (const v of message.threePinInputs) {
      CanboProvisioningCommand_ThreePinInput.encode(v!, writer.uint32(34).fork()).join();
    }
    for (const v of message.twoPinInputs) {
      CanboProvisioningCommand_TwoPinInput.encode(v!, writer.uint32(42).fork()).join();
    }
    if (message.adcInputs !== undefined) {
      CanboProvisioningCommand_ADCInput.encode(message.adcInputs, writer.uint32(50).fork()).join();
    }
    for (const v of message.outputs) {
      CanboProvisioningCommand_Output.encode(v!, writer.uint32(58).fork()).join();
    }
    if (message.zeroToTenVoltConfig !== undefined) {
      CanboProvisioningCommand_ZeroToTenVoltConfig.encode(message.zeroToTenVoltConfig, writer.uint32(66).fork()).join();
    }
    if (message.klevernessConnector !== undefined) {
      CanboProvisioningCommand_KlevernessConnector.encode(message.klevernessConnector, writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboProvisioningCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboProvisioningCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.version = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.qrCode = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.threePinInputs.push(CanboProvisioningCommand_ThreePinInput.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.twoPinInputs.push(CanboProvisioningCommand_TwoPinInput.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.adcInputs = CanboProvisioningCommand_ADCInput.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.outputs.push(CanboProvisioningCommand_Output.decode(reader, reader.uint32()));
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.zeroToTenVoltConfig = CanboProvisioningCommand_ZeroToTenVoltConfig.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.klevernessConnector = CanboProvisioningCommand_KlevernessConnector.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboProvisioningCommand>, I>>(base?: I): CanboProvisioningCommand {
    return CanboProvisioningCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboProvisioningCommand>, I>>(object: I): CanboProvisioningCommand {
    const message = createBaseCanboProvisioningCommand();
    message.nodeId = object.nodeId ?? 0;
    message.version = object.version ?? "";
    message.qrCode = object.qrCode ?? "";
    message.threePinInputs = object.threePinInputs?.map((e) => CanboProvisioningCommand_ThreePinInput.fromPartial(e)) ||
      [];
    message.twoPinInputs = object.twoPinInputs?.map((e) => CanboProvisioningCommand_TwoPinInput.fromPartial(e)) || [];
    message.adcInputs = (object.adcInputs !== undefined && object.adcInputs !== null)
      ? CanboProvisioningCommand_ADCInput.fromPartial(object.adcInputs)
      : undefined;
    message.outputs = object.outputs?.map((e) => CanboProvisioningCommand_Output.fromPartial(e)) || [];
    message.zeroToTenVoltConfig = (object.zeroToTenVoltConfig !== undefined && object.zeroToTenVoltConfig !== null)
      ? CanboProvisioningCommand_ZeroToTenVoltConfig.fromPartial(object.zeroToTenVoltConfig)
      : undefined;
    message.klevernessConnector = (object.klevernessConnector !== undefined && object.klevernessConnector !== null)
      ? CanboProvisioningCommand_KlevernessConnector.fromPartial(object.klevernessConnector)
      : undefined;
    return message;
  },
};

function createBaseCanboProvisioningCommand_ThreePinInput(): CanboProvisioningCommand_ThreePinInput {
  return { connectorId: 0, connectorType: 0 };
}

export const CanboProvisioningCommand_ThreePinInput: MessageFns<CanboProvisioningCommand_ThreePinInput> = {
  encode(message: CanboProvisioningCommand_ThreePinInput, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.connectorType !== 0) {
      writer.uint32(16).int32(message.connectorType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboProvisioningCommand_ThreePinInput {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboProvisioningCommand_ThreePinInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.connectorType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboProvisioningCommand_ThreePinInput>, I>>(
    base?: I,
  ): CanboProvisioningCommand_ThreePinInput {
    return CanboProvisioningCommand_ThreePinInput.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboProvisioningCommand_ThreePinInput>, I>>(
    object: I,
  ): CanboProvisioningCommand_ThreePinInput {
    const message = createBaseCanboProvisioningCommand_ThreePinInput();
    message.connectorId = object.connectorId ?? 0;
    message.connectorType = object.connectorType ?? 0;
    return message;
  },
};

function createBaseCanboProvisioningCommand_TwoPinInput(): CanboProvisioningCommand_TwoPinInput {
  return { connectorId: 0, connectorType: 0 };
}

export const CanboProvisioningCommand_TwoPinInput: MessageFns<CanboProvisioningCommand_TwoPinInput> = {
  encode(message: CanboProvisioningCommand_TwoPinInput, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.connectorType !== 0) {
      writer.uint32(16).int32(message.connectorType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboProvisioningCommand_TwoPinInput {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboProvisioningCommand_TwoPinInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.connectorType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboProvisioningCommand_TwoPinInput>, I>>(
    base?: I,
  ): CanboProvisioningCommand_TwoPinInput {
    return CanboProvisioningCommand_TwoPinInput.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboProvisioningCommand_TwoPinInput>, I>>(
    object: I,
  ): CanboProvisioningCommand_TwoPinInput {
    const message = createBaseCanboProvisioningCommand_TwoPinInput();
    message.connectorId = object.connectorId ?? 0;
    message.connectorType = object.connectorType ?? 0;
    return message;
  },
};

function createBaseCanboProvisioningCommand_ADCInput(): CanboProvisioningCommand_ADCInput {
  return { offsetScaling: undefined, minMax: undefined, connectorType: 0 };
}

export const CanboProvisioningCommand_ADCInput: MessageFns<CanboProvisioningCommand_ADCInput> = {
  encode(message: CanboProvisioningCommand_ADCInput, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.offsetScaling !== undefined) {
      CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange.encode(message.offsetScaling, writer.uint32(10).fork())
        .join();
    }
    if (message.minMax !== undefined) {
      CanboProvisioningCommand_ADCInput_MinMaxRange.encode(message.minMax, writer.uint32(18).fork()).join();
    }
    if (message.connectorType !== 0) {
      writer.uint32(24).int32(message.connectorType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboProvisioningCommand_ADCInput {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboProvisioningCommand_ADCInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.offsetScaling = CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange.decode(
            reader,
            reader.uint32(),
          );
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.minMax = CanboProvisioningCommand_ADCInput_MinMaxRange.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.connectorType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboProvisioningCommand_ADCInput>, I>>(
    base?: I,
  ): CanboProvisioningCommand_ADCInput {
    return CanboProvisioningCommand_ADCInput.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboProvisioningCommand_ADCInput>, I>>(
    object: I,
  ): CanboProvisioningCommand_ADCInput {
    const message = createBaseCanboProvisioningCommand_ADCInput();
    message.offsetScaling = (object.offsetScaling !== undefined && object.offsetScaling !== null)
      ? CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange.fromPartial(object.offsetScaling)
      : undefined;
    message.minMax = (object.minMax !== undefined && object.minMax !== null)
      ? CanboProvisioningCommand_ADCInput_MinMaxRange.fromPartial(object.minMax)
      : undefined;
    message.connectorType = object.connectorType ?? 0;
    return message;
  },
};

function createBaseCanboProvisioningCommand_ADCInput_OffsetScalingFactorRange(): CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange {
  return { offset: 0, scalingFactor: 0 };
}

export const CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange: MessageFns<
  CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange
> = {
  encode(
    message: CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.offset !== 0) {
      writer.uint32(13).float(message.offset);
    }
    if (message.scalingFactor !== 0) {
      writer.uint32(21).float(message.scalingFactor);
    }
    return writer;
  },

  decode(
    input: BinaryReader | Uint8Array,
    length?: number,
  ): CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboProvisioningCommand_ADCInput_OffsetScalingFactorRange();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 13) {
            break;
          }

          message.offset = reader.float();
          continue;
        }
        case 2: {
          if (tag !== 21) {
            break;
          }

          message.scalingFactor = reader.float();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange>, I>>(
    base?: I,
  ): CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange {
    return CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange>, I>>(
    object: I,
  ): CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange {
    const message = createBaseCanboProvisioningCommand_ADCInput_OffsetScalingFactorRange();
    message.offset = object.offset ?? 0;
    message.scalingFactor = object.scalingFactor ?? 0;
    return message;
  },
};

function createBaseCanboProvisioningCommand_ADCInput_MinMaxRange(): CanboProvisioningCommand_ADCInput_MinMaxRange {
  return { min: 0, max: 0 };
}

export const CanboProvisioningCommand_ADCInput_MinMaxRange: MessageFns<CanboProvisioningCommand_ADCInput_MinMaxRange> =
  {
    encode(
      message: CanboProvisioningCommand_ADCInput_MinMaxRange,
      writer: BinaryWriter = new BinaryWriter(),
    ): BinaryWriter {
      if (message.min !== 0) {
        writer.uint32(13).float(message.min);
      }
      if (message.max !== 0) {
        writer.uint32(21).float(message.max);
      }
      return writer;
    },

    decode(input: BinaryReader | Uint8Array, length?: number): CanboProvisioningCommand_ADCInput_MinMaxRange {
      const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
      let end = length === undefined ? reader.len : reader.pos + length;
      const message = createBaseCanboProvisioningCommand_ADCInput_MinMaxRange();
      while (reader.pos < end) {
        const tag = reader.uint32();
        switch (tag >>> 3) {
          case 1: {
            if (tag !== 13) {
              break;
            }

            message.min = reader.float();
            continue;
          }
          case 2: {
            if (tag !== 21) {
              break;
            }

            message.max = reader.float();
            continue;
          }
        }
        if ((tag & 7) === 4 || tag === 0) {
          break;
        }
        reader.skip(tag & 7);
      }
      return message;
    },

    create<I extends Exact<DeepPartial<CanboProvisioningCommand_ADCInput_MinMaxRange>, I>>(
      base?: I,
    ): CanboProvisioningCommand_ADCInput_MinMaxRange {
      return CanboProvisioningCommand_ADCInput_MinMaxRange.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CanboProvisioningCommand_ADCInput_MinMaxRange>, I>>(
      object: I,
    ): CanboProvisioningCommand_ADCInput_MinMaxRange {
      const message = createBaseCanboProvisioningCommand_ADCInput_MinMaxRange();
      message.min = object.min ?? 0;
      message.max = object.max ?? 0;
      return message;
    },
  };

function createBaseCanboProvisioningCommand_ZeroToTenVoltConfig(): CanboProvisioningCommand_ZeroToTenVoltConfig {
  return { type: 0, useRelay: false, relayConnectorId: 0, minBrightness: 0, maxBrightness: 0 };
}

export const CanboProvisioningCommand_ZeroToTenVoltConfig: MessageFns<CanboProvisioningCommand_ZeroToTenVoltConfig> = {
  encode(
    message: CanboProvisioningCommand_ZeroToTenVoltConfig,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.type !== 0) {
      writer.uint32(8).int32(message.type);
    }
    if (message.useRelay !== false) {
      writer.uint32(16).bool(message.useRelay);
    }
    if (message.relayConnectorId !== 0) {
      writer.uint32(24).uint32(message.relayConnectorId);
    }
    if (message.minBrightness !== 0) {
      writer.uint32(37).float(message.minBrightness);
    }
    if (message.maxBrightness !== 0) {
      writer.uint32(45).float(message.maxBrightness);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboProvisioningCommand_ZeroToTenVoltConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboProvisioningCommand_ZeroToTenVoltConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.useRelay = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.relayConnectorId = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 37) {
            break;
          }

          message.minBrightness = reader.float();
          continue;
        }
        case 5: {
          if (tag !== 45) {
            break;
          }

          message.maxBrightness = reader.float();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboProvisioningCommand_ZeroToTenVoltConfig>, I>>(
    base?: I,
  ): CanboProvisioningCommand_ZeroToTenVoltConfig {
    return CanboProvisioningCommand_ZeroToTenVoltConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboProvisioningCommand_ZeroToTenVoltConfig>, I>>(
    object: I,
  ): CanboProvisioningCommand_ZeroToTenVoltConfig {
    const message = createBaseCanboProvisioningCommand_ZeroToTenVoltConfig();
    message.type = object.type ?? 0;
    message.useRelay = object.useRelay ?? false;
    message.relayConnectorId = object.relayConnectorId ?? 0;
    message.minBrightness = object.minBrightness ?? 0;
    message.maxBrightness = object.maxBrightness ?? 0;
    return message;
  },
};

function createBaseCanboProvisioningCommand_Output(): CanboProvisioningCommand_Output {
  return { connectorId: 0, connectorType: 0 };
}

export const CanboProvisioningCommand_Output: MessageFns<CanboProvisioningCommand_Output> = {
  encode(message: CanboProvisioningCommand_Output, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.connectorType !== 0) {
      writer.uint32(16).int32(message.connectorType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboProvisioningCommand_Output {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboProvisioningCommand_Output();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.connectorType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboProvisioningCommand_Output>, I>>(base?: I): CanboProvisioningCommand_Output {
    return CanboProvisioningCommand_Output.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboProvisioningCommand_Output>, I>>(
    object: I,
  ): CanboProvisioningCommand_Output {
    const message = createBaseCanboProvisioningCommand_Output();
    message.connectorId = object.connectorId ?? 0;
    message.connectorType = object.connectorType ?? 0;
    return message;
  },
};

function createBaseCanboProvisioningCommand_KlevernessConnector(): CanboProvisioningCommand_KlevernessConnector {
  return {
    isEnabled: false,
    button1Enabled: false,
    button2Enabled: false,
    button3Enabled: false,
    lowLightLedEnabled: false,
    highLightLedEnabled: false,
  };
}

export const CanboProvisioningCommand_KlevernessConnector: MessageFns<CanboProvisioningCommand_KlevernessConnector> = {
  encode(
    message: CanboProvisioningCommand_KlevernessConnector,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.isEnabled !== false) {
      writer.uint32(8).bool(message.isEnabled);
    }
    if (message.button1Enabled !== false) {
      writer.uint32(16).bool(message.button1Enabled);
    }
    if (message.button2Enabled !== false) {
      writer.uint32(24).bool(message.button2Enabled);
    }
    if (message.button3Enabled !== false) {
      writer.uint32(32).bool(message.button3Enabled);
    }
    if (message.lowLightLedEnabled !== false) {
      writer.uint32(40).bool(message.lowLightLedEnabled);
    }
    if (message.highLightLedEnabled !== false) {
      writer.uint32(48).bool(message.highLightLedEnabled);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboProvisioningCommand_KlevernessConnector {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboProvisioningCommand_KlevernessConnector();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isEnabled = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.button1Enabled = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.button2Enabled = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.button3Enabled = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.lowLightLedEnabled = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.highLightLedEnabled = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboProvisioningCommand_KlevernessConnector>, I>>(
    base?: I,
  ): CanboProvisioningCommand_KlevernessConnector {
    return CanboProvisioningCommand_KlevernessConnector.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboProvisioningCommand_KlevernessConnector>, I>>(
    object: I,
  ): CanboProvisioningCommand_KlevernessConnector {
    const message = createBaseCanboProvisioningCommand_KlevernessConnector();
    message.isEnabled = object.isEnabled ?? false;
    message.button1Enabled = object.button1Enabled ?? false;
    message.button2Enabled = object.button2Enabled ?? false;
    message.button3Enabled = object.button3Enabled ?? false;
    message.lowLightLedEnabled = object.lowLightLedEnabled ?? false;
    message.highLightLedEnabled = object.highLightLedEnabled ?? false;
    return message;
  },
};

function createBaseStartDimmingCommand(): StartDimmingCommand {
  return { nodeId: 0, brightness: 0, dimSpeedMsec: 0 };
}

export const StartDimmingCommand: MessageFns<StartDimmingCommand> = {
  encode(message: StartDimmingCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.brightness !== 0) {
      writer.uint32(16).uint32(message.brightness);
    }
    if (message.dimSpeedMsec !== 0) {
      writer.uint32(24).uint32(message.dimSpeedMsec);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StartDimmingCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStartDimmingCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.brightness = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.dimSpeedMsec = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<StartDimmingCommand>, I>>(base?: I): StartDimmingCommand {
    return StartDimmingCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StartDimmingCommand>, I>>(object: I): StartDimmingCommand {
    const message = createBaseStartDimmingCommand();
    message.nodeId = object.nodeId ?? 0;
    message.brightness = object.brightness ?? 0;
    message.dimSpeedMsec = object.dimSpeedMsec ?? 0;
    return message;
  },
};

function createBaseStopDimmingCommand(): StopDimmingCommand {
  return { nodeId: 0 };
}

export const StopDimmingCommand: MessageFns<StopDimmingCommand> = {
  encode(message: StopDimmingCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StopDimmingCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStopDimmingCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<StopDimmingCommand>, I>>(base?: I): StopDimmingCommand {
    return StopDimmingCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StopDimmingCommand>, I>>(object: I): StopDimmingCommand {
    const message = createBaseStopDimmingCommand();
    message.nodeId = object.nodeId ?? 0;
    return message;
  },
};

function createBaseDimmingStateCommand(): DimmingStateCommand {
  return { nodeId: 0, brightness: 0 };
}

export const DimmingStateCommand: MessageFns<DimmingStateCommand> = {
  encode(message: DimmingStateCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.brightness !== 0) {
      writer.uint32(16).uint32(message.brightness);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DimmingStateCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDimmingStateCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.brightness = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<DimmingStateCommand>, I>>(base?: I): DimmingStateCommand {
    return DimmingStateCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DimmingStateCommand>, I>>(object: I): DimmingStateCommand {
    const message = createBaseDimmingStateCommand();
    message.nodeId = object.nodeId ?? 0;
    message.brightness = object.brightness ?? 0;
    return message;
  },
};

function createBaseToggleRelayCommand(): ToggleRelayCommand {
  return { connectorId: 0, state: 0 };
}

export const ToggleRelayCommand: MessageFns<ToggleRelayCommand> = {
  encode(message: ToggleRelayCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.state !== 0) {
      writer.uint32(16).int32(message.state);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ToggleRelayCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseToggleRelayCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ToggleRelayCommand>, I>>(base?: I): ToggleRelayCommand {
    return ToggleRelayCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ToggleRelayCommand>, I>>(object: I): ToggleRelayCommand {
    const message = createBaseToggleRelayCommand();
    message.connectorId = object.connectorId ?? 0;
    message.state = object.state ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
