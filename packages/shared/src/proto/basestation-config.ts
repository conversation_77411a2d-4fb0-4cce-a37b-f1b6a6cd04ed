// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v4.25.3
// source: basestation-config.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "";

export interface ActiveConfiguration {
  config: BasestationConfig | undefined;
  state: BasestationState | undefined;
}

export interface BasestationUpdateMessage {
  qrCode: string;
  config: BasestationConfig | undefined;
}

export interface BasestationConfig {
  /** Unique identifier for this basestation on the network */
  id: number;
  /** Version number incremented on each config change */
  version: string;
  /** Configuration for radio-frequency devices */
  rfConfig:
    | BasestationConfig_RFConfig
    | undefined;
  /** Configurations for the inputs */
  canboConfigs: CanboConfig[];
  rfReedConfigs: RFReedSensorConfig[];
  /** Configurations for the lights */
  lights: LightConfig[];
  /** Maps CAN and RF devices to their QR code */
  nodeQrMappings: BasestationConfig_NodeQRMapping[];
  wifiConfig: BasestationConfig_WifiConfig | undefined;
}

export interface BasestationConfig_RFConfig {
  channel: number;
  network: number;
}

export interface BasestationConfig_WifiConfig {
  ssid: string;
  password: string;
}

export interface BasestationConfig_NodeQRMapping {
  /** The QR code string value for this device */
  qrCode: string;
  /** Either the CAN or the RF node ID */
  nodeId: number;
  type: BasestationConfig_NodeQRMapping_DeviceType;
}

export enum BasestationConfig_NodeQRMapping_DeviceType {
  CAN = 0,
  RF = 1,
  UNRECOGNIZED = -1,
}

export interface RFReedSensorConfig {
  nodeId: number;
  doorClose: RFReedSensorConfig_Action[];
  doorOpen: RFReedSensorConfig_Action[];
}

export interface RFReedSensorConfig_Action {
  dimSpeedMsec: number;
  lightId: number;
  targetBrightness: number;
  activateDelayMsec: number;
}

export interface CanboConfig {
  nodeId: number;
  threePinInputs: CanboConfig_ThreePinInput[];
  twoPinInputs: CanboConfig_TwoPinInput[];
}

export interface CanboConfig_ThreePinInput {
  connectorId: number;
  type: CanboConfig_ThreePinInput_ConnectorType;
  toggle?: CanboConfig_ThreePinInput_ToggleConfig | undefined;
  momentary?: CanboConfig_ThreePinInput_MomentaryConfig | undefined;
}

export enum CanboConfig_ThreePinInput_ConnectorType {
  TOGGLE = 0,
  MOMENTARY = 1,
  UNRECOGNIZED = -1,
}

export interface CanboConfig_ThreePinInput_ToggleConfig {
  upClick: CanboConfig_ThreePinInput_Action[];
  upHold: CanboConfig_ThreePinInput_Action[];
  downClick: CanboConfig_ThreePinInput_Action[];
  downHold: CanboConfig_ThreePinInput_Action[];
}

export interface CanboConfig_ThreePinInput_MomentaryConfig {
  upClick: CanboConfig_ThreePinInput_Action[];
  upHold: CanboConfig_ThreePinInput_Action[];
}

export interface CanboConfig_ThreePinInput_Action {
  dimSpeedMsec: number;
  lightId: number;
  targetBrightness: number;
  onBrightness: number;
  offBrightness: number;
}

export interface CanboConfig_TwoPinInput {
  connectorId: number;
  type: CanboConfig_TwoPinInput_ConnectorType;
  pir?: CanboConfig_TwoPinInput_PIRConfig | undefined;
  momentary?: CanboConfig_TwoPinInput_MomentaryConfig | undefined;
  doorSensor?: CanboConfig_TwoPinInput_DoorSensorConfig | undefined;
}

export enum CanboConfig_TwoPinInput_ConnectorType {
  PIR = 0,
  MOMENTARY = 1,
  DOOR_SENSOR = 2,
  UNRECOGNIZED = -1,
}

export interface CanboConfig_TwoPinInput_PIRConfig {
  onActivate: CanboConfig_TwoPinInput_Action[];
  onDeactivate: CanboConfig_TwoPinInput_Action[];
}

export interface CanboConfig_TwoPinInput_MomentaryConfig {
  upClick: CanboConfig_TwoPinInput_Action[];
  upHold: CanboConfig_TwoPinInput_Action[];
}

export interface CanboConfig_TwoPinInput_DoorSensorConfig {
  onOpen: CanboConfig_TwoPinInput_Action[];
  onClose: CanboConfig_TwoPinInput_Action[];
}

export interface CanboConfig_TwoPinInput_Action {
  dimSpeedMsec: number;
  lightId: number;
  delayInMsec: number;
  targetBrightness: number;
  onBrightness: number;
  offBrightness: number;
}

export interface LightConfig {
  id: number;
  /** How fast the light dims in milliseconds */
  dimSpeedMsec: number;
  /** Configurations for the fixtures */
  fixtures: LightConfig_FixtureConfig[];
}

export interface LightConfig_FixtureConfig {
  /** Min and max brightness of the fixture */
  minBrightness: number;
  maxBrightness: number;
  /** Configuration for the fixture */
  type: LightConfig_FixtureConfig_FixtureType;
  dmx?: LightConfig_FixtureConfig_DMXConfig | undefined;
  rf?: LightConfig_FixtureConfig_RFConfig | undefined;
  zeroToTenVolt?:
    | LightConfig_FixtureConfig_ZeroToTenVoltConfig
    | undefined;
  /** Analog0To10VConfig analog_0_10 = 7; */
  onOffLight?: LightConfig_FixtureConfig_OnOffLightConfig | undefined;
}

export enum LightConfig_FixtureConfig_FixtureType {
  DMX = 0,
  RF = 1,
  ZERO_TO_TEN_VOLT = 2,
  ON_OFF_LIGHT = 3,
  UNRECOGNIZED = -1,
}

export interface LightConfig_FixtureConfig_DMXConfig {
  params: LightConfig_FixtureConfig_DMXConfig_LightParams | undefined;
  rgb: LightConfig_FixtureConfig_DMXConfig_RGBConfig | undefined;
  channels: number[];
  type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig;
}

export enum LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig {
  D4 = 0,
  TUNABLE_WHITE = 1,
  ELV = 2,
  DF_12 = 3,
  EST = 4,
  RGB_STRIP = 5,
  UNRECOGNIZED = -1,
}

export interface LightConfig_FixtureConfig_DMXConfig_LightParams {
  min1: number;
  max1: number;
  gamma1: number;
  min2: number;
  max2: number;
  gamma2: number;
}

export interface LightConfig_FixtureConfig_DMXConfig_RGBConfig {
  red: number;
  green: number;
  blue: number;
}

export interface LightConfig_FixtureConfig_RFConfig {
  type: LightConfig_FixtureConfig_RFConfig_Type;
  nodeId: number;
}

export enum LightConfig_FixtureConfig_RFConfig_Type {
  DIMMER = 0,
  SWITCH = 1,
  UNRECOGNIZED = -1,
}

export interface LightConfig_FixtureConfig_ZeroToTenVoltConfig {
  type: LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type;
  nodeId: number;
  useRelay: boolean;
  outConnectorId: number;
}

export enum LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type {
  SOURCING = 0,
  SINKING = 1,
  UNRECOGNIZED = -1,
}

export interface LightConfig_FixtureConfig_OnOffLightConfig {
  nodeId: number;
  outConnectorId: number;
}

export interface BasestationState {
  lights: LightState[];
  buttons: ButtonState[];
  provisionedDevices: ProvisioningState[];
  reeds: RFReedState[];
}

export interface RFReedState {
  nodeId: number;
  sensorStatus: RFReedState_Status;
  lastModifiedTime: number;
  batteryVoltage: number;
}

export enum RFReedState_Status {
  UNKNOWN = 0,
  OPEN = 1,
  CLOSED = 2,
  UNRECOGNIZED = -1,
}

export interface ProvisioningState {
  nodeId: number;
  isProvisioned: boolean;
  errorCode: ProvisioningState_ProvisioningErrorCode;
  lastSeenTime: number;
}

export enum ProvisioningState_ProvisioningErrorCode {
  NONE = 0,
  NOT_FOUND = 1,
  NO_CANBO_CONFIG = 2,
  COULD_NOT_SEND_PROVISIONING_COMMAND = 3,
  NO_REED_CONFIG = 4,
  UNRECOGNIZED = -1,
}

export interface LightState {
  id: number;
  brightness: number;
  targetValue: number;
  dimSpeedMsec: number;
  lastModifiedTime: number;
  activeAfterTime: number;
  isTransitioning: boolean;
}

export interface ButtonState {
  nodeId: number;
  currentState: ButtonState_State;
  lastModifiedTime: number;
}

export enum ButtonState_State {
  BUTTON_STATE_RELEASED = 0,
  BUTTON_STATE_UP_PRESSED = 1,
  BUTTON_STATE_DOWN_PRESSED = 2,
  UNRECOGNIZED = -1,
}

function createBaseActiveConfiguration(): ActiveConfiguration {
  return { config: undefined, state: undefined };
}

export const ActiveConfiguration: MessageFns<ActiveConfiguration> = {
  encode(message: ActiveConfiguration, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.config !== undefined) {
      BasestationConfig.encode(message.config, writer.uint32(10).fork()).join();
    }
    if (message.state !== undefined) {
      BasestationState.encode(message.state, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActiveConfiguration {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActiveConfiguration();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.config = BasestationConfig.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.state = BasestationState.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ActiveConfiguration>, I>>(base?: I): ActiveConfiguration {
    return ActiveConfiguration.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActiveConfiguration>, I>>(object: I): ActiveConfiguration {
    const message = createBaseActiveConfiguration();
    message.config = (object.config !== undefined && object.config !== null)
      ? BasestationConfig.fromPartial(object.config)
      : undefined;
    message.state = (object.state !== undefined && object.state !== null)
      ? BasestationState.fromPartial(object.state)
      : undefined;
    return message;
  },
};

function createBaseBasestationUpdateMessage(): BasestationUpdateMessage {
  return { qrCode: "", config: undefined };
}

export const BasestationUpdateMessage: MessageFns<BasestationUpdateMessage> = {
  encode(message: BasestationUpdateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.qrCode !== "") {
      writer.uint32(10).string(message.qrCode);
    }
    if (message.config !== undefined) {
      BasestationConfig.encode(message.config, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationUpdateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationUpdateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.qrCode = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.config = BasestationConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationUpdateMessage>, I>>(base?: I): BasestationUpdateMessage {
    return BasestationUpdateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationUpdateMessage>, I>>(object: I): BasestationUpdateMessage {
    const message = createBaseBasestationUpdateMessage();
    message.qrCode = object.qrCode ?? "";
    message.config = (object.config !== undefined && object.config !== null)
      ? BasestationConfig.fromPartial(object.config)
      : undefined;
    return message;
  },
};

function createBaseBasestationConfig(): BasestationConfig {
  return {
    id: 0,
    version: "",
    rfConfig: undefined,
    canboConfigs: [],
    rfReedConfigs: [],
    lights: [],
    nodeQrMappings: [],
    wifiConfig: undefined,
  };
}

export const BasestationConfig: MessageFns<BasestationConfig> = {
  encode(message: BasestationConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint32(message.id);
    }
    if (message.version !== "") {
      writer.uint32(18).string(message.version);
    }
    if (message.rfConfig !== undefined) {
      BasestationConfig_RFConfig.encode(message.rfConfig, writer.uint32(26).fork()).join();
    }
    for (const v of message.canboConfigs) {
      CanboConfig.encode(v!, writer.uint32(34).fork()).join();
    }
    for (const v of message.rfReedConfigs) {
      RFReedSensorConfig.encode(v!, writer.uint32(42).fork()).join();
    }
    for (const v of message.lights) {
      LightConfig.encode(v!, writer.uint32(50).fork()).join();
    }
    for (const v of message.nodeQrMappings) {
      BasestationConfig_NodeQRMapping.encode(v!, writer.uint32(58).fork()).join();
    }
    if (message.wifiConfig !== undefined) {
      BasestationConfig_WifiConfig.encode(message.wifiConfig, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.version = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rfConfig = BasestationConfig_RFConfig.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.canboConfigs.push(CanboConfig.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.rfReedConfigs.push(RFReedSensorConfig.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.lights.push(LightConfig.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.nodeQrMappings.push(BasestationConfig_NodeQRMapping.decode(reader, reader.uint32()));
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.wifiConfig = BasestationConfig_WifiConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationConfig>, I>>(base?: I): BasestationConfig {
    return BasestationConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationConfig>, I>>(object: I): BasestationConfig {
    const message = createBaseBasestationConfig();
    message.id = object.id ?? 0;
    message.version = object.version ?? "";
    message.rfConfig = (object.rfConfig !== undefined && object.rfConfig !== null)
      ? BasestationConfig_RFConfig.fromPartial(object.rfConfig)
      : undefined;
    message.canboConfigs = object.canboConfigs?.map((e) => CanboConfig.fromPartial(e)) || [];
    message.rfReedConfigs = object.rfReedConfigs?.map((e) => RFReedSensorConfig.fromPartial(e)) || [];
    message.lights = object.lights?.map((e) => LightConfig.fromPartial(e)) || [];
    message.nodeQrMappings = object.nodeQrMappings?.map((e) => BasestationConfig_NodeQRMapping.fromPartial(e)) || [];
    message.wifiConfig = (object.wifiConfig !== undefined && object.wifiConfig !== null)
      ? BasestationConfig_WifiConfig.fromPartial(object.wifiConfig)
      : undefined;
    return message;
  },
};

function createBaseBasestationConfig_RFConfig(): BasestationConfig_RFConfig {
  return { channel: 0, network: 0 };
}

export const BasestationConfig_RFConfig: MessageFns<BasestationConfig_RFConfig> = {
  encode(message: BasestationConfig_RFConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.channel !== 0) {
      writer.uint32(8).uint32(message.channel);
    }
    if (message.network !== 0) {
      writer.uint32(16).uint32(message.network);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationConfig_RFConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationConfig_RFConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.channel = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.network = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationConfig_RFConfig>, I>>(base?: I): BasestationConfig_RFConfig {
    return BasestationConfig_RFConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationConfig_RFConfig>, I>>(object: I): BasestationConfig_RFConfig {
    const message = createBaseBasestationConfig_RFConfig();
    message.channel = object.channel ?? 0;
    message.network = object.network ?? 0;
    return message;
  },
};

function createBaseBasestationConfig_WifiConfig(): BasestationConfig_WifiConfig {
  return { ssid: "", password: "" };
}

export const BasestationConfig_WifiConfig: MessageFns<BasestationConfig_WifiConfig> = {
  encode(message: BasestationConfig_WifiConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ssid !== "") {
      writer.uint32(10).string(message.ssid);
    }
    if (message.password !== "") {
      writer.uint32(18).string(message.password);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationConfig_WifiConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationConfig_WifiConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ssid = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.password = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationConfig_WifiConfig>, I>>(base?: I): BasestationConfig_WifiConfig {
    return BasestationConfig_WifiConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationConfig_WifiConfig>, I>>(object: I): BasestationConfig_WifiConfig {
    const message = createBaseBasestationConfig_WifiConfig();
    message.ssid = object.ssid ?? "";
    message.password = object.password ?? "";
    return message;
  },
};

function createBaseBasestationConfig_NodeQRMapping(): BasestationConfig_NodeQRMapping {
  return { qrCode: "", nodeId: 0, type: 0 };
}

export const BasestationConfig_NodeQRMapping: MessageFns<BasestationConfig_NodeQRMapping> = {
  encode(message: BasestationConfig_NodeQRMapping, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.qrCode !== "") {
      writer.uint32(10).string(message.qrCode);
    }
    if (message.nodeId !== 0) {
      writer.uint32(16).uint32(message.nodeId);
    }
    if (message.type !== 0) {
      writer.uint32(24).int32(message.type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationConfig_NodeQRMapping {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationConfig_NodeQRMapping();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.qrCode = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationConfig_NodeQRMapping>, I>>(base?: I): BasestationConfig_NodeQRMapping {
    return BasestationConfig_NodeQRMapping.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationConfig_NodeQRMapping>, I>>(
    object: I,
  ): BasestationConfig_NodeQRMapping {
    const message = createBaseBasestationConfig_NodeQRMapping();
    message.qrCode = object.qrCode ?? "";
    message.nodeId = object.nodeId ?? 0;
    message.type = object.type ?? 0;
    return message;
  },
};

function createBaseRFReedSensorConfig(): RFReedSensorConfig {
  return { nodeId: 0, doorClose: [], doorOpen: [] };
}

export const RFReedSensorConfig: MessageFns<RFReedSensorConfig> = {
  encode(message: RFReedSensorConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    for (const v of message.doorClose) {
      RFReedSensorConfig_Action.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.doorOpen) {
      RFReedSensorConfig_Action.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RFReedSensorConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRFReedSensorConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.doorClose.push(RFReedSensorConfig_Action.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.doorOpen.push(RFReedSensorConfig_Action.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RFReedSensorConfig>, I>>(base?: I): RFReedSensorConfig {
    return RFReedSensorConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RFReedSensorConfig>, I>>(object: I): RFReedSensorConfig {
    const message = createBaseRFReedSensorConfig();
    message.nodeId = object.nodeId ?? 0;
    message.doorClose = object.doorClose?.map((e) => RFReedSensorConfig_Action.fromPartial(e)) || [];
    message.doorOpen = object.doorOpen?.map((e) => RFReedSensorConfig_Action.fromPartial(e)) || [];
    return message;
  },
};

function createBaseRFReedSensorConfig_Action(): RFReedSensorConfig_Action {
  return { dimSpeedMsec: 0, lightId: 0, targetBrightness: 0, activateDelayMsec: 0 };
}

export const RFReedSensorConfig_Action: MessageFns<RFReedSensorConfig_Action> = {
  encode(message: RFReedSensorConfig_Action, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.dimSpeedMsec !== 0) {
      writer.uint32(8).uint32(message.dimSpeedMsec);
    }
    if (message.lightId !== 0) {
      writer.uint32(16).uint32(message.lightId);
    }
    if (message.targetBrightness !== 0) {
      writer.uint32(29).float(message.targetBrightness);
    }
    if (message.activateDelayMsec !== 0) {
      writer.uint32(32).uint32(message.activateDelayMsec);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RFReedSensorConfig_Action {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRFReedSensorConfig_Action();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.dimSpeedMsec = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lightId = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 29) {
            break;
          }

          message.targetBrightness = reader.float();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.activateDelayMsec = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RFReedSensorConfig_Action>, I>>(base?: I): RFReedSensorConfig_Action {
    return RFReedSensorConfig_Action.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RFReedSensorConfig_Action>, I>>(object: I): RFReedSensorConfig_Action {
    const message = createBaseRFReedSensorConfig_Action();
    message.dimSpeedMsec = object.dimSpeedMsec ?? 0;
    message.lightId = object.lightId ?? 0;
    message.targetBrightness = object.targetBrightness ?? 0;
    message.activateDelayMsec = object.activateDelayMsec ?? 0;
    return message;
  },
};

function createBaseCanboConfig(): CanboConfig {
  return { nodeId: 0, threePinInputs: [], twoPinInputs: [] };
}

export const CanboConfig: MessageFns<CanboConfig> = {
  encode(message: CanboConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    for (const v of message.threePinInputs) {
      CanboConfig_ThreePinInput.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.twoPinInputs) {
      CanboConfig_TwoPinInput.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.threePinInputs.push(CanboConfig_ThreePinInput.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.twoPinInputs.push(CanboConfig_TwoPinInput.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig>, I>>(base?: I): CanboConfig {
    return CanboConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig>, I>>(object: I): CanboConfig {
    const message = createBaseCanboConfig();
    message.nodeId = object.nodeId ?? 0;
    message.threePinInputs = object.threePinInputs?.map((e) => CanboConfig_ThreePinInput.fromPartial(e)) || [];
    message.twoPinInputs = object.twoPinInputs?.map((e) => CanboConfig_TwoPinInput.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCanboConfig_ThreePinInput(): CanboConfig_ThreePinInput {
  return { connectorId: 0, type: 0, toggle: undefined, momentary: undefined };
}

export const CanboConfig_ThreePinInput: MessageFns<CanboConfig_ThreePinInput> = {
  encode(message: CanboConfig_ThreePinInput, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    if (message.toggle !== undefined) {
      CanboConfig_ThreePinInput_ToggleConfig.encode(message.toggle, writer.uint32(26).fork()).join();
    }
    if (message.momentary !== undefined) {
      CanboConfig_ThreePinInput_MomentaryConfig.encode(message.momentary, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_ThreePinInput {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_ThreePinInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.toggle = CanboConfig_ThreePinInput_ToggleConfig.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.momentary = CanboConfig_ThreePinInput_MomentaryConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_ThreePinInput>, I>>(base?: I): CanboConfig_ThreePinInput {
    return CanboConfig_ThreePinInput.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_ThreePinInput>, I>>(object: I): CanboConfig_ThreePinInput {
    const message = createBaseCanboConfig_ThreePinInput();
    message.connectorId = object.connectorId ?? 0;
    message.type = object.type ?? 0;
    message.toggle = (object.toggle !== undefined && object.toggle !== null)
      ? CanboConfig_ThreePinInput_ToggleConfig.fromPartial(object.toggle)
      : undefined;
    message.momentary = (object.momentary !== undefined && object.momentary !== null)
      ? CanboConfig_ThreePinInput_MomentaryConfig.fromPartial(object.momentary)
      : undefined;
    return message;
  },
};

function createBaseCanboConfig_ThreePinInput_ToggleConfig(): CanboConfig_ThreePinInput_ToggleConfig {
  return { upClick: [], upHold: [], downClick: [], downHold: [] };
}

export const CanboConfig_ThreePinInput_ToggleConfig: MessageFns<CanboConfig_ThreePinInput_ToggleConfig> = {
  encode(message: CanboConfig_ThreePinInput_ToggleConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.upClick) {
      CanboConfig_ThreePinInput_Action.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.upHold) {
      CanboConfig_ThreePinInput_Action.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.downClick) {
      CanboConfig_ThreePinInput_Action.encode(v!, writer.uint32(26).fork()).join();
    }
    for (const v of message.downHold) {
      CanboConfig_ThreePinInput_Action.encode(v!, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_ThreePinInput_ToggleConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_ThreePinInput_ToggleConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.upClick.push(CanboConfig_ThreePinInput_Action.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.upHold.push(CanboConfig_ThreePinInput_Action.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.downClick.push(CanboConfig_ThreePinInput_Action.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.downHold.push(CanboConfig_ThreePinInput_Action.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_ThreePinInput_ToggleConfig>, I>>(
    base?: I,
  ): CanboConfig_ThreePinInput_ToggleConfig {
    return CanboConfig_ThreePinInput_ToggleConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_ThreePinInput_ToggleConfig>, I>>(
    object: I,
  ): CanboConfig_ThreePinInput_ToggleConfig {
    const message = createBaseCanboConfig_ThreePinInput_ToggleConfig();
    message.upClick = object.upClick?.map((e) => CanboConfig_ThreePinInput_Action.fromPartial(e)) || [];
    message.upHold = object.upHold?.map((e) => CanboConfig_ThreePinInput_Action.fromPartial(e)) || [];
    message.downClick = object.downClick?.map((e) => CanboConfig_ThreePinInput_Action.fromPartial(e)) || [];
    message.downHold = object.downHold?.map((e) => CanboConfig_ThreePinInput_Action.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCanboConfig_ThreePinInput_MomentaryConfig(): CanboConfig_ThreePinInput_MomentaryConfig {
  return { upClick: [], upHold: [] };
}

export const CanboConfig_ThreePinInput_MomentaryConfig: MessageFns<CanboConfig_ThreePinInput_MomentaryConfig> = {
  encode(message: CanboConfig_ThreePinInput_MomentaryConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.upClick) {
      CanboConfig_ThreePinInput_Action.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.upHold) {
      CanboConfig_ThreePinInput_Action.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_ThreePinInput_MomentaryConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_ThreePinInput_MomentaryConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.upClick.push(CanboConfig_ThreePinInput_Action.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.upHold.push(CanboConfig_ThreePinInput_Action.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_ThreePinInput_MomentaryConfig>, I>>(
    base?: I,
  ): CanboConfig_ThreePinInput_MomentaryConfig {
    return CanboConfig_ThreePinInput_MomentaryConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_ThreePinInput_MomentaryConfig>, I>>(
    object: I,
  ): CanboConfig_ThreePinInput_MomentaryConfig {
    const message = createBaseCanboConfig_ThreePinInput_MomentaryConfig();
    message.upClick = object.upClick?.map((e) => CanboConfig_ThreePinInput_Action.fromPartial(e)) || [];
    message.upHold = object.upHold?.map((e) => CanboConfig_ThreePinInput_Action.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCanboConfig_ThreePinInput_Action(): CanboConfig_ThreePinInput_Action {
  return { dimSpeedMsec: 0, lightId: 0, targetBrightness: 0, onBrightness: 0, offBrightness: 0 };
}

export const CanboConfig_ThreePinInput_Action: MessageFns<CanboConfig_ThreePinInput_Action> = {
  encode(message: CanboConfig_ThreePinInput_Action, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.dimSpeedMsec !== 0) {
      writer.uint32(8).uint32(message.dimSpeedMsec);
    }
    if (message.lightId !== 0) {
      writer.uint32(16).uint32(message.lightId);
    }
    if (message.targetBrightness !== 0) {
      writer.uint32(29).float(message.targetBrightness);
    }
    if (message.onBrightness !== 0) {
      writer.uint32(37).float(message.onBrightness);
    }
    if (message.offBrightness !== 0) {
      writer.uint32(45).float(message.offBrightness);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_ThreePinInput_Action {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_ThreePinInput_Action();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.dimSpeedMsec = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lightId = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 29) {
            break;
          }

          message.targetBrightness = reader.float();
          continue;
        }
        case 4: {
          if (tag !== 37) {
            break;
          }

          message.onBrightness = reader.float();
          continue;
        }
        case 5: {
          if (tag !== 45) {
            break;
          }

          message.offBrightness = reader.float();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_ThreePinInput_Action>, I>>(
    base?: I,
  ): CanboConfig_ThreePinInput_Action {
    return CanboConfig_ThreePinInput_Action.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_ThreePinInput_Action>, I>>(
    object: I,
  ): CanboConfig_ThreePinInput_Action {
    const message = createBaseCanboConfig_ThreePinInput_Action();
    message.dimSpeedMsec = object.dimSpeedMsec ?? 0;
    message.lightId = object.lightId ?? 0;
    message.targetBrightness = object.targetBrightness ?? 0;
    message.onBrightness = object.onBrightness ?? 0;
    message.offBrightness = object.offBrightness ?? 0;
    return message;
  },
};

function createBaseCanboConfig_TwoPinInput(): CanboConfig_TwoPinInput {
  return { connectorId: 0, type: 0, pir: undefined, momentary: undefined, doorSensor: undefined };
}

export const CanboConfig_TwoPinInput: MessageFns<CanboConfig_TwoPinInput> = {
  encode(message: CanboConfig_TwoPinInput, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    if (message.pir !== undefined) {
      CanboConfig_TwoPinInput_PIRConfig.encode(message.pir, writer.uint32(26).fork()).join();
    }
    if (message.momentary !== undefined) {
      CanboConfig_TwoPinInput_MomentaryConfig.encode(message.momentary, writer.uint32(34).fork()).join();
    }
    if (message.doorSensor !== undefined) {
      CanboConfig_TwoPinInput_DoorSensorConfig.encode(message.doorSensor, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_TwoPinInput {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_TwoPinInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pir = CanboConfig_TwoPinInput_PIRConfig.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.momentary = CanboConfig_TwoPinInput_MomentaryConfig.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.doorSensor = CanboConfig_TwoPinInput_DoorSensorConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_TwoPinInput>, I>>(base?: I): CanboConfig_TwoPinInput {
    return CanboConfig_TwoPinInput.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_TwoPinInput>, I>>(object: I): CanboConfig_TwoPinInput {
    const message = createBaseCanboConfig_TwoPinInput();
    message.connectorId = object.connectorId ?? 0;
    message.type = object.type ?? 0;
    message.pir = (object.pir !== undefined && object.pir !== null)
      ? CanboConfig_TwoPinInput_PIRConfig.fromPartial(object.pir)
      : undefined;
    message.momentary = (object.momentary !== undefined && object.momentary !== null)
      ? CanboConfig_TwoPinInput_MomentaryConfig.fromPartial(object.momentary)
      : undefined;
    message.doorSensor = (object.doorSensor !== undefined && object.doorSensor !== null)
      ? CanboConfig_TwoPinInput_DoorSensorConfig.fromPartial(object.doorSensor)
      : undefined;
    return message;
  },
};

function createBaseCanboConfig_TwoPinInput_PIRConfig(): CanboConfig_TwoPinInput_PIRConfig {
  return { onActivate: [], onDeactivate: [] };
}

export const CanboConfig_TwoPinInput_PIRConfig: MessageFns<CanboConfig_TwoPinInput_PIRConfig> = {
  encode(message: CanboConfig_TwoPinInput_PIRConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.onActivate) {
      CanboConfig_TwoPinInput_Action.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.onDeactivate) {
      CanboConfig_TwoPinInput_Action.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_TwoPinInput_PIRConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_TwoPinInput_PIRConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.onActivate.push(CanboConfig_TwoPinInput_Action.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.onDeactivate.push(CanboConfig_TwoPinInput_Action.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_TwoPinInput_PIRConfig>, I>>(
    base?: I,
  ): CanboConfig_TwoPinInput_PIRConfig {
    return CanboConfig_TwoPinInput_PIRConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_TwoPinInput_PIRConfig>, I>>(
    object: I,
  ): CanboConfig_TwoPinInput_PIRConfig {
    const message = createBaseCanboConfig_TwoPinInput_PIRConfig();
    message.onActivate = object.onActivate?.map((e) => CanboConfig_TwoPinInput_Action.fromPartial(e)) || [];
    message.onDeactivate = object.onDeactivate?.map((e) => CanboConfig_TwoPinInput_Action.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCanboConfig_TwoPinInput_MomentaryConfig(): CanboConfig_TwoPinInput_MomentaryConfig {
  return { upClick: [], upHold: [] };
}

export const CanboConfig_TwoPinInput_MomentaryConfig: MessageFns<CanboConfig_TwoPinInput_MomentaryConfig> = {
  encode(message: CanboConfig_TwoPinInput_MomentaryConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.upClick) {
      CanboConfig_TwoPinInput_Action.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.upHold) {
      CanboConfig_TwoPinInput_Action.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_TwoPinInput_MomentaryConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_TwoPinInput_MomentaryConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.upClick.push(CanboConfig_TwoPinInput_Action.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.upHold.push(CanboConfig_TwoPinInput_Action.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_TwoPinInput_MomentaryConfig>, I>>(
    base?: I,
  ): CanboConfig_TwoPinInput_MomentaryConfig {
    return CanboConfig_TwoPinInput_MomentaryConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_TwoPinInput_MomentaryConfig>, I>>(
    object: I,
  ): CanboConfig_TwoPinInput_MomentaryConfig {
    const message = createBaseCanboConfig_TwoPinInput_MomentaryConfig();
    message.upClick = object.upClick?.map((e) => CanboConfig_TwoPinInput_Action.fromPartial(e)) || [];
    message.upHold = object.upHold?.map((e) => CanboConfig_TwoPinInput_Action.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCanboConfig_TwoPinInput_DoorSensorConfig(): CanboConfig_TwoPinInput_DoorSensorConfig {
  return { onOpen: [], onClose: [] };
}

export const CanboConfig_TwoPinInput_DoorSensorConfig: MessageFns<CanboConfig_TwoPinInput_DoorSensorConfig> = {
  encode(message: CanboConfig_TwoPinInput_DoorSensorConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.onOpen) {
      CanboConfig_TwoPinInput_Action.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.onClose) {
      CanboConfig_TwoPinInput_Action.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_TwoPinInput_DoorSensorConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_TwoPinInput_DoorSensorConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.onOpen.push(CanboConfig_TwoPinInput_Action.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.onClose.push(CanboConfig_TwoPinInput_Action.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_TwoPinInput_DoorSensorConfig>, I>>(
    base?: I,
  ): CanboConfig_TwoPinInput_DoorSensorConfig {
    return CanboConfig_TwoPinInput_DoorSensorConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_TwoPinInput_DoorSensorConfig>, I>>(
    object: I,
  ): CanboConfig_TwoPinInput_DoorSensorConfig {
    const message = createBaseCanboConfig_TwoPinInput_DoorSensorConfig();
    message.onOpen = object.onOpen?.map((e) => CanboConfig_TwoPinInput_Action.fromPartial(e)) || [];
    message.onClose = object.onClose?.map((e) => CanboConfig_TwoPinInput_Action.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCanboConfig_TwoPinInput_Action(): CanboConfig_TwoPinInput_Action {
  return { dimSpeedMsec: 0, lightId: 0, delayInMsec: 0, targetBrightness: 0, onBrightness: 0, offBrightness: 0 };
}

export const CanboConfig_TwoPinInput_Action: MessageFns<CanboConfig_TwoPinInput_Action> = {
  encode(message: CanboConfig_TwoPinInput_Action, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.dimSpeedMsec !== 0) {
      writer.uint32(8).uint32(message.dimSpeedMsec);
    }
    if (message.lightId !== 0) {
      writer.uint32(16).uint32(message.lightId);
    }
    if (message.delayInMsec !== 0) {
      writer.uint32(24).uint32(message.delayInMsec);
    }
    if (message.targetBrightness !== 0) {
      writer.uint32(37).float(message.targetBrightness);
    }
    if (message.onBrightness !== 0) {
      writer.uint32(45).float(message.onBrightness);
    }
    if (message.offBrightness !== 0) {
      writer.uint32(53).float(message.offBrightness);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_TwoPinInput_Action {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_TwoPinInput_Action();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.dimSpeedMsec = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lightId = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.delayInMsec = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 37) {
            break;
          }

          message.targetBrightness = reader.float();
          continue;
        }
        case 5: {
          if (tag !== 45) {
            break;
          }

          message.onBrightness = reader.float();
          continue;
        }
        case 6: {
          if (tag !== 53) {
            break;
          }

          message.offBrightness = reader.float();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_TwoPinInput_Action>, I>>(base?: I): CanboConfig_TwoPinInput_Action {
    return CanboConfig_TwoPinInput_Action.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_TwoPinInput_Action>, I>>(
    object: I,
  ): CanboConfig_TwoPinInput_Action {
    const message = createBaseCanboConfig_TwoPinInput_Action();
    message.dimSpeedMsec = object.dimSpeedMsec ?? 0;
    message.lightId = object.lightId ?? 0;
    message.delayInMsec = object.delayInMsec ?? 0;
    message.targetBrightness = object.targetBrightness ?? 0;
    message.onBrightness = object.onBrightness ?? 0;
    message.offBrightness = object.offBrightness ?? 0;
    return message;
  },
};

function createBaseLightConfig(): LightConfig {
  return { id: 0, dimSpeedMsec: 0, fixtures: [] };
}

export const LightConfig: MessageFns<LightConfig> = {
  encode(message: LightConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint32(message.id);
    }
    if (message.dimSpeedMsec !== 0) {
      writer.uint32(16).uint32(message.dimSpeedMsec);
    }
    for (const v of message.fixtures) {
      LightConfig_FixtureConfig.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.dimSpeedMsec = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fixtures.push(LightConfig_FixtureConfig.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightConfig>, I>>(base?: I): LightConfig {
    return LightConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightConfig>, I>>(object: I): LightConfig {
    const message = createBaseLightConfig();
    message.id = object.id ?? 0;
    message.dimSpeedMsec = object.dimSpeedMsec ?? 0;
    message.fixtures = object.fixtures?.map((e) => LightConfig_FixtureConfig.fromPartial(e)) || [];
    return message;
  },
};

function createBaseLightConfig_FixtureConfig(): LightConfig_FixtureConfig {
  return {
    minBrightness: 0,
    maxBrightness: 0,
    type: 0,
    dmx: undefined,
    rf: undefined,
    zeroToTenVolt: undefined,
    onOffLight: undefined,
  };
}

export const LightConfig_FixtureConfig: MessageFns<LightConfig_FixtureConfig> = {
  encode(message: LightConfig_FixtureConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.minBrightness !== 0) {
      writer.uint32(13).float(message.minBrightness);
    }
    if (message.maxBrightness !== 0) {
      writer.uint32(21).float(message.maxBrightness);
    }
    if (message.type !== 0) {
      writer.uint32(24).int32(message.type);
    }
    if (message.dmx !== undefined) {
      LightConfig_FixtureConfig_DMXConfig.encode(message.dmx, writer.uint32(34).fork()).join();
    }
    if (message.rf !== undefined) {
      LightConfig_FixtureConfig_RFConfig.encode(message.rf, writer.uint32(42).fork()).join();
    }
    if (message.zeroToTenVolt !== undefined) {
      LightConfig_FixtureConfig_ZeroToTenVoltConfig.encode(message.zeroToTenVolt, writer.uint32(50).fork()).join();
    }
    if (message.onOffLight !== undefined) {
      LightConfig_FixtureConfig_OnOffLightConfig.encode(message.onOffLight, writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightConfig_FixtureConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 13) {
            break;
          }

          message.minBrightness = reader.float();
          continue;
        }
        case 2: {
          if (tag !== 21) {
            break;
          }

          message.maxBrightness = reader.float();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.dmx = LightConfig_FixtureConfig_DMXConfig.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.rf = LightConfig_FixtureConfig_RFConfig.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.zeroToTenVolt = LightConfig_FixtureConfig_ZeroToTenVoltConfig.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.onOffLight = LightConfig_FixtureConfig_OnOffLightConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightConfig_FixtureConfig>, I>>(base?: I): LightConfig_FixtureConfig {
    return LightConfig_FixtureConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig>, I>>(object: I): LightConfig_FixtureConfig {
    const message = createBaseLightConfig_FixtureConfig();
    message.minBrightness = object.minBrightness ?? 0;
    message.maxBrightness = object.maxBrightness ?? 0;
    message.type = object.type ?? 0;
    message.dmx = (object.dmx !== undefined && object.dmx !== null)
      ? LightConfig_FixtureConfig_DMXConfig.fromPartial(object.dmx)
      : undefined;
    message.rf = (object.rf !== undefined && object.rf !== null)
      ? LightConfig_FixtureConfig_RFConfig.fromPartial(object.rf)
      : undefined;
    message.zeroToTenVolt = (object.zeroToTenVolt !== undefined && object.zeroToTenVolt !== null)
      ? LightConfig_FixtureConfig_ZeroToTenVoltConfig.fromPartial(object.zeroToTenVolt)
      : undefined;
    message.onOffLight = (object.onOffLight !== undefined && object.onOffLight !== null)
      ? LightConfig_FixtureConfig_OnOffLightConfig.fromPartial(object.onOffLight)
      : undefined;
    return message;
  },
};

function createBaseLightConfig_FixtureConfig_DMXConfig(): LightConfig_FixtureConfig_DMXConfig {
  return { params: undefined, rgb: undefined, channels: [], type: 0 };
}

export const LightConfig_FixtureConfig_DMXConfig: MessageFns<LightConfig_FixtureConfig_DMXConfig> = {
  encode(message: LightConfig_FixtureConfig_DMXConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.params !== undefined) {
      LightConfig_FixtureConfig_DMXConfig_LightParams.encode(message.params, writer.uint32(10).fork()).join();
    }
    if (message.rgb !== undefined) {
      LightConfig_FixtureConfig_DMXConfig_RGBConfig.encode(message.rgb, writer.uint32(18).fork()).join();
    }
    writer.uint32(26).fork();
    for (const v of message.channels) {
      writer.uint32(v);
    }
    writer.join();
    if (message.type !== 0) {
      writer.uint32(32).int32(message.type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig_DMXConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightConfig_FixtureConfig_DMXConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.params = LightConfig_FixtureConfig_DMXConfig_LightParams.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rgb = LightConfig_FixtureConfig_DMXConfig_RGBConfig.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.channels.push(reader.uint32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.channels.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightConfig_FixtureConfig_DMXConfig>, I>>(
    base?: I,
  ): LightConfig_FixtureConfig_DMXConfig {
    return LightConfig_FixtureConfig_DMXConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig_DMXConfig>, I>>(
    object: I,
  ): LightConfig_FixtureConfig_DMXConfig {
    const message = createBaseLightConfig_FixtureConfig_DMXConfig();
    message.params = (object.params !== undefined && object.params !== null)
      ? LightConfig_FixtureConfig_DMXConfig_LightParams.fromPartial(object.params)
      : undefined;
    message.rgb = (object.rgb !== undefined && object.rgb !== null)
      ? LightConfig_FixtureConfig_DMXConfig_RGBConfig.fromPartial(object.rgb)
      : undefined;
    message.channels = object.channels?.map((e) => e) || [];
    message.type = object.type ?? 0;
    return message;
  },
};

function createBaseLightConfig_FixtureConfig_DMXConfig_LightParams(): LightConfig_FixtureConfig_DMXConfig_LightParams {
  return { min1: 0, max1: 0, gamma1: 0, min2: 0, max2: 0, gamma2: 0 };
}

export const LightConfig_FixtureConfig_DMXConfig_LightParams: MessageFns<
  LightConfig_FixtureConfig_DMXConfig_LightParams
> = {
  encode(
    message: LightConfig_FixtureConfig_DMXConfig_LightParams,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.min1 !== 0) {
      writer.uint32(13).float(message.min1);
    }
    if (message.max1 !== 0) {
      writer.uint32(21).float(message.max1);
    }
    if (message.gamma1 !== 0) {
      writer.uint32(29).float(message.gamma1);
    }
    if (message.min2 !== 0) {
      writer.uint32(37).float(message.min2);
    }
    if (message.max2 !== 0) {
      writer.uint32(45).float(message.max2);
    }
    if (message.gamma2 !== 0) {
      writer.uint32(53).float(message.gamma2);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig_DMXConfig_LightParams {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightConfig_FixtureConfig_DMXConfig_LightParams();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 13) {
            break;
          }

          message.min1 = reader.float();
          continue;
        }
        case 2: {
          if (tag !== 21) {
            break;
          }

          message.max1 = reader.float();
          continue;
        }
        case 3: {
          if (tag !== 29) {
            break;
          }

          message.gamma1 = reader.float();
          continue;
        }
        case 4: {
          if (tag !== 37) {
            break;
          }

          message.min2 = reader.float();
          continue;
        }
        case 5: {
          if (tag !== 45) {
            break;
          }

          message.max2 = reader.float();
          continue;
        }
        case 6: {
          if (tag !== 53) {
            break;
          }

          message.gamma2 = reader.float();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightConfig_FixtureConfig_DMXConfig_LightParams>, I>>(
    base?: I,
  ): LightConfig_FixtureConfig_DMXConfig_LightParams {
    return LightConfig_FixtureConfig_DMXConfig_LightParams.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig_DMXConfig_LightParams>, I>>(
    object: I,
  ): LightConfig_FixtureConfig_DMXConfig_LightParams {
    const message = createBaseLightConfig_FixtureConfig_DMXConfig_LightParams();
    message.min1 = object.min1 ?? 0;
    message.max1 = object.max1 ?? 0;
    message.gamma1 = object.gamma1 ?? 0;
    message.min2 = object.min2 ?? 0;
    message.max2 = object.max2 ?? 0;
    message.gamma2 = object.gamma2 ?? 0;
    return message;
  },
};

function createBaseLightConfig_FixtureConfig_DMXConfig_RGBConfig(): LightConfig_FixtureConfig_DMXConfig_RGBConfig {
  return { red: 0, green: 0, blue: 0 };
}

export const LightConfig_FixtureConfig_DMXConfig_RGBConfig: MessageFns<LightConfig_FixtureConfig_DMXConfig_RGBConfig> =
  {
    encode(
      message: LightConfig_FixtureConfig_DMXConfig_RGBConfig,
      writer: BinaryWriter = new BinaryWriter(),
    ): BinaryWriter {
      if (message.red !== 0) {
        writer.uint32(8).uint32(message.red);
      }
      if (message.green !== 0) {
        writer.uint32(16).uint32(message.green);
      }
      if (message.blue !== 0) {
        writer.uint32(24).uint32(message.blue);
      }
      return writer;
    },

    decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig_DMXConfig_RGBConfig {
      const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
      let end = length === undefined ? reader.len : reader.pos + length;
      const message = createBaseLightConfig_FixtureConfig_DMXConfig_RGBConfig();
      while (reader.pos < end) {
        const tag = reader.uint32();
        switch (tag >>> 3) {
          case 1: {
            if (tag !== 8) {
              break;
            }

            message.red = reader.uint32();
            continue;
          }
          case 2: {
            if (tag !== 16) {
              break;
            }

            message.green = reader.uint32();
            continue;
          }
          case 3: {
            if (tag !== 24) {
              break;
            }

            message.blue = reader.uint32();
            continue;
          }
        }
        if ((tag & 7) === 4 || tag === 0) {
          break;
        }
        reader.skip(tag & 7);
      }
      return message;
    },

    create<I extends Exact<DeepPartial<LightConfig_FixtureConfig_DMXConfig_RGBConfig>, I>>(
      base?: I,
    ): LightConfig_FixtureConfig_DMXConfig_RGBConfig {
      return LightConfig_FixtureConfig_DMXConfig_RGBConfig.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig_DMXConfig_RGBConfig>, I>>(
      object: I,
    ): LightConfig_FixtureConfig_DMXConfig_RGBConfig {
      const message = createBaseLightConfig_FixtureConfig_DMXConfig_RGBConfig();
      message.red = object.red ?? 0;
      message.green = object.green ?? 0;
      message.blue = object.blue ?? 0;
      return message;
    },
  };

function createBaseLightConfig_FixtureConfig_RFConfig(): LightConfig_FixtureConfig_RFConfig {
  return { type: 0, nodeId: 0 };
}

export const LightConfig_FixtureConfig_RFConfig: MessageFns<LightConfig_FixtureConfig_RFConfig> = {
  encode(message: LightConfig_FixtureConfig_RFConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.type !== 0) {
      writer.uint32(8).int32(message.type);
    }
    if (message.nodeId !== 0) {
      writer.uint32(16).uint32(message.nodeId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig_RFConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightConfig_FixtureConfig_RFConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightConfig_FixtureConfig_RFConfig>, I>>(
    base?: I,
  ): LightConfig_FixtureConfig_RFConfig {
    return LightConfig_FixtureConfig_RFConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig_RFConfig>, I>>(
    object: I,
  ): LightConfig_FixtureConfig_RFConfig {
    const message = createBaseLightConfig_FixtureConfig_RFConfig();
    message.type = object.type ?? 0;
    message.nodeId = object.nodeId ?? 0;
    return message;
  },
};

function createBaseLightConfig_FixtureConfig_ZeroToTenVoltConfig(): LightConfig_FixtureConfig_ZeroToTenVoltConfig {
  return { type: 0, nodeId: 0, useRelay: false, outConnectorId: 0 };
}

export const LightConfig_FixtureConfig_ZeroToTenVoltConfig: MessageFns<LightConfig_FixtureConfig_ZeroToTenVoltConfig> =
  {
    encode(
      message: LightConfig_FixtureConfig_ZeroToTenVoltConfig,
      writer: BinaryWriter = new BinaryWriter(),
    ): BinaryWriter {
      if (message.type !== 0) {
        writer.uint32(8).int32(message.type);
      }
      if (message.nodeId !== 0) {
        writer.uint32(16).uint32(message.nodeId);
      }
      if (message.useRelay !== false) {
        writer.uint32(24).bool(message.useRelay);
      }
      if (message.outConnectorId !== 0) {
        writer.uint32(32).uint32(message.outConnectorId);
      }
      return writer;
    },

    decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig_ZeroToTenVoltConfig {
      const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
      let end = length === undefined ? reader.len : reader.pos + length;
      const message = createBaseLightConfig_FixtureConfig_ZeroToTenVoltConfig();
      while (reader.pos < end) {
        const tag = reader.uint32();
        switch (tag >>> 3) {
          case 1: {
            if (tag !== 8) {
              break;
            }

            message.type = reader.int32() as any;
            continue;
          }
          case 2: {
            if (tag !== 16) {
              break;
            }

            message.nodeId = reader.uint32();
            continue;
          }
          case 3: {
            if (tag !== 24) {
              break;
            }

            message.useRelay = reader.bool();
            continue;
          }
          case 4: {
            if (tag !== 32) {
              break;
            }

            message.outConnectorId = reader.uint32();
            continue;
          }
        }
        if ((tag & 7) === 4 || tag === 0) {
          break;
        }
        reader.skip(tag & 7);
      }
      return message;
    },

    create<I extends Exact<DeepPartial<LightConfig_FixtureConfig_ZeroToTenVoltConfig>, I>>(
      base?: I,
    ): LightConfig_FixtureConfig_ZeroToTenVoltConfig {
      return LightConfig_FixtureConfig_ZeroToTenVoltConfig.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig_ZeroToTenVoltConfig>, I>>(
      object: I,
    ): LightConfig_FixtureConfig_ZeroToTenVoltConfig {
      const message = createBaseLightConfig_FixtureConfig_ZeroToTenVoltConfig();
      message.type = object.type ?? 0;
      message.nodeId = object.nodeId ?? 0;
      message.useRelay = object.useRelay ?? false;
      message.outConnectorId = object.outConnectorId ?? 0;
      return message;
    },
  };

function createBaseLightConfig_FixtureConfig_OnOffLightConfig(): LightConfig_FixtureConfig_OnOffLightConfig {
  return { nodeId: 0, outConnectorId: 0 };
}

export const LightConfig_FixtureConfig_OnOffLightConfig: MessageFns<LightConfig_FixtureConfig_OnOffLightConfig> = {
  encode(message: LightConfig_FixtureConfig_OnOffLightConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.outConnectorId !== 0) {
      writer.uint32(16).uint32(message.outConnectorId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig_OnOffLightConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightConfig_FixtureConfig_OnOffLightConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.outConnectorId = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightConfig_FixtureConfig_OnOffLightConfig>, I>>(
    base?: I,
  ): LightConfig_FixtureConfig_OnOffLightConfig {
    return LightConfig_FixtureConfig_OnOffLightConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig_OnOffLightConfig>, I>>(
    object: I,
  ): LightConfig_FixtureConfig_OnOffLightConfig {
    const message = createBaseLightConfig_FixtureConfig_OnOffLightConfig();
    message.nodeId = object.nodeId ?? 0;
    message.outConnectorId = object.outConnectorId ?? 0;
    return message;
  },
};

function createBaseBasestationState(): BasestationState {
  return { lights: [], buttons: [], provisionedDevices: [], reeds: [] };
}

export const BasestationState: MessageFns<BasestationState> = {
  encode(message: BasestationState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.lights) {
      LightState.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.buttons) {
      ButtonState.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.provisionedDevices) {
      ProvisioningState.encode(v!, writer.uint32(26).fork()).join();
    }
    for (const v of message.reeds) {
      RFReedState.encode(v!, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.lights.push(LightState.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.buttons.push(ButtonState.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.provisionedDevices.push(ProvisioningState.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.reeds.push(RFReedState.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationState>, I>>(base?: I): BasestationState {
    return BasestationState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationState>, I>>(object: I): BasestationState {
    const message = createBaseBasestationState();
    message.lights = object.lights?.map((e) => LightState.fromPartial(e)) || [];
    message.buttons = object.buttons?.map((e) => ButtonState.fromPartial(e)) || [];
    message.provisionedDevices = object.provisionedDevices?.map((e) => ProvisioningState.fromPartial(e)) || [];
    message.reeds = object.reeds?.map((e) => RFReedState.fromPartial(e)) || [];
    return message;
  },
};

function createBaseRFReedState(): RFReedState {
  return { nodeId: 0, sensorStatus: 0, lastModifiedTime: 0, batteryVoltage: 0 };
}

export const RFReedState: MessageFns<RFReedState> = {
  encode(message: RFReedState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.sensorStatus !== 0) {
      writer.uint32(16).int32(message.sensorStatus);
    }
    if (message.lastModifiedTime !== 0) {
      writer.uint32(24).uint64(message.lastModifiedTime);
    }
    if (message.batteryVoltage !== 0) {
      writer.uint32(37).float(message.batteryVoltage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RFReedState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRFReedState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.sensorStatus = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.lastModifiedTime = longToNumber(reader.uint64());
          continue;
        }
        case 4: {
          if (tag !== 37) {
            break;
          }

          message.batteryVoltage = reader.float();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RFReedState>, I>>(base?: I): RFReedState {
    return RFReedState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RFReedState>, I>>(object: I): RFReedState {
    const message = createBaseRFReedState();
    message.nodeId = object.nodeId ?? 0;
    message.sensorStatus = object.sensorStatus ?? 0;
    message.lastModifiedTime = object.lastModifiedTime ?? 0;
    message.batteryVoltage = object.batteryVoltage ?? 0;
    return message;
  },
};

function createBaseProvisioningState(): ProvisioningState {
  return { nodeId: 0, isProvisioned: false, errorCode: 0, lastSeenTime: 0 };
}

export const ProvisioningState: MessageFns<ProvisioningState> = {
  encode(message: ProvisioningState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.isProvisioned !== false) {
      writer.uint32(16).bool(message.isProvisioned);
    }
    if (message.errorCode !== 0) {
      writer.uint32(24).int32(message.errorCode);
    }
    if (message.lastSeenTime !== 0) {
      writer.uint32(32).uint64(message.lastSeenTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProvisioningState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProvisioningState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isProvisioned = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.errorCode = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.lastSeenTime = longToNumber(reader.uint64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ProvisioningState>, I>>(base?: I): ProvisioningState {
    return ProvisioningState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProvisioningState>, I>>(object: I): ProvisioningState {
    const message = createBaseProvisioningState();
    message.nodeId = object.nodeId ?? 0;
    message.isProvisioned = object.isProvisioned ?? false;
    message.errorCode = object.errorCode ?? 0;
    message.lastSeenTime = object.lastSeenTime ?? 0;
    return message;
  },
};

function createBaseLightState(): LightState {
  return {
    id: 0,
    brightness: 0,
    targetValue: 0,
    dimSpeedMsec: 0,
    lastModifiedTime: 0,
    activeAfterTime: 0,
    isTransitioning: false,
  };
}

export const LightState: MessageFns<LightState> = {
  encode(message: LightState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint32(message.id);
    }
    if (message.brightness !== 0) {
      writer.uint32(21).float(message.brightness);
    }
    if (message.targetValue !== 0) {
      writer.uint32(29).float(message.targetValue);
    }
    if (message.dimSpeedMsec !== 0) {
      writer.uint32(32).uint32(message.dimSpeedMsec);
    }
    if (message.lastModifiedTime !== 0) {
      writer.uint32(40).uint64(message.lastModifiedTime);
    }
    if (message.activeAfterTime !== 0) {
      writer.uint32(48).uint64(message.activeAfterTime);
    }
    if (message.isTransitioning !== false) {
      writer.uint32(56).bool(message.isTransitioning);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 21) {
            break;
          }

          message.brightness = reader.float();
          continue;
        }
        case 3: {
          if (tag !== 29) {
            break;
          }

          message.targetValue = reader.float();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.dimSpeedMsec = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.lastModifiedTime = longToNumber(reader.uint64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.activeAfterTime = longToNumber(reader.uint64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.isTransitioning = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightState>, I>>(base?: I): LightState {
    return LightState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightState>, I>>(object: I): LightState {
    const message = createBaseLightState();
    message.id = object.id ?? 0;
    message.brightness = object.brightness ?? 0;
    message.targetValue = object.targetValue ?? 0;
    message.dimSpeedMsec = object.dimSpeedMsec ?? 0;
    message.lastModifiedTime = object.lastModifiedTime ?? 0;
    message.activeAfterTime = object.activeAfterTime ?? 0;
    message.isTransitioning = object.isTransitioning ?? false;
    return message;
  },
};

function createBaseButtonState(): ButtonState {
  return { nodeId: 0, currentState: 0, lastModifiedTime: 0 };
}

export const ButtonState: MessageFns<ButtonState> = {
  encode(message: ButtonState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.currentState !== 0) {
      writer.uint32(16).int32(message.currentState);
    }
    if (message.lastModifiedTime !== 0) {
      writer.uint32(24).uint64(message.lastModifiedTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ButtonState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseButtonState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.currentState = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.lastModifiedTime = longToNumber(reader.uint64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ButtonState>, I>>(base?: I): ButtonState {
    return ButtonState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ButtonState>, I>>(object: I): ButtonState {
    const message = createBaseButtonState();
    message.nodeId = object.nodeId ?? 0;
    message.currentState = object.currentState ?? 0;
    message.lastModifiedTime = object.lastModifiedTime ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
