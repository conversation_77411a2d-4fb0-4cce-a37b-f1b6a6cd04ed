// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v4.25.3
// source: nanopb.proto

/* eslint-disable */
import { Binary<PERSON>eader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { FieldDescriptorProto_Label, FieldDescriptorProto_Type } from "./google/protobuf/descriptor";

export const protobufPackage = "";

export enum FieldType {
  /** FT_DEFAULT - Automatically decide field type, generate static field if possible. */
  FT_DEFAULT = 0,
  /** FT_CALLBACK - Always generate a callback field. */
  FT_CALLBACK = 1,
  /** FT_POINTER - Always generate a dynamically allocated field. */
  FT_POINTER = 4,
  /** FT_STATIC - Generate a static field or raise an exception if not possible. */
  FT_STATIC = 2,
  /** FT_IGNORE - Ignore the field completely. */
  FT_IGNORE = 3,
  /** FT_INLINE - Legacy option, use the separate 'fixed_length' option instead */
  FT_INLINE = 5,
  UNRECOGNIZED = -1,
}

export enum IntSize {
  /** IS_DEFAULT - Default, 32/64bit based on type in .proto */
  IS_DEFAULT = 0,
  IS_8 = 8,
  IS_16 = 16,
  IS_32 = 32,
  IS_64 = 64,
  UNRECOGNIZED = -1,
}

export enum TypenameMangling {
  /** M_NONE - Default, no typename mangling */
  M_NONE = 0,
  /** M_STRIP_PACKAGE - Strip current package name */
  M_STRIP_PACKAGE = 1,
  /** M_FLATTEN - Only use last path component */
  M_FLATTEN = 2,
  /** M_PACKAGE_INITIALS - Replace the package name by the initials */
  M_PACKAGE_INITIALS = 3,
  UNRECOGNIZED = -1,
}

export enum DescriptorSize {
  /** DS_AUTO - Select minimal size based on field type */
  DS_AUTO = 0,
  /** DS_1 - 1 word; up to 15 byte fields, no arrays */
  DS_1 = 1,
  /** DS_2 - 2 words; up to 4095 byte fields, 4095 entry arrays */
  DS_2 = 2,
  /** DS_4 - 4 words; up to 2^32-1 byte fields, 2^16-1 entry arrays */
  DS_4 = 4,
  /** DS_8 - 8 words; up to 2^32-1 entry arrays */
  DS_8 = 8,
  UNRECOGNIZED = -1,
}

/**
 * This is the inner options message, which basically defines options for
 * a field. When it is used in message or file scope, it applies to all
 * fields.
 */
export interface NanoPBOptions {
  /**
   * Allocated size for 'bytes' and 'string' fields.
   * For string fields, this should include the space for null terminator.
   */
  maxSize?:
    | number
    | undefined;
  /**
   * Maximum length for 'string' fields. Setting this is equivalent
   * to setting max_size to a value of length+1.
   */
  maxLength?:
    | number
    | undefined;
  /** Allocated number of entries in arrays ('repeated' fields) */
  maxCount?:
    | number
    | undefined;
  /**
   * Size of integer fields. Can save some memory if you don't need
   * full 32 bits for the value.
   */
  intSize?:
    | IntSize
    | undefined;
  /** Size for enum fields. Supported by C++11 and C23 standards. */
  enumIntsize?:
    | IntSize
    | undefined;
  /** Force type of field (callback or static allocation) */
  type?:
    | FieldType
    | undefined;
  /** Use long names for enums, i.e. EnumName_EnumValue. */
  longNames?:
    | boolean
    | undefined;
  /**
   * Add 'packed' attribute to generated structs.
   * Note: this cannot be used on CPUs that break on unaligned
   * accesses to variables.
   */
  packedStruct?:
    | boolean
    | undefined;
  /** Add 'packed' attribute to generated enums. */
  packedEnum?:
    | boolean
    | undefined;
  /** Skip this message */
  skipMessage?:
    | boolean
    | undefined;
  /** Generate oneof fields as normal optional fields instead of union. */
  noUnions?:
    | boolean
    | undefined;
  /** integer type tag for a message */
  msgid?:
    | number
    | undefined;
  /** decode oneof as anonymous union */
  anonymousOneof?:
    | boolean
    | undefined;
  /** Proto3 singular field does not generate a "has_" flag */
  proto3?:
    | boolean
    | undefined;
  /**
   * Force proto3 messages to have no "has_" flag.
   * This was default behavior until nanopb-0.4.0.
   */
  proto3SingularMsgs?:
    | boolean
    | undefined;
  /** Generate an enum->string mapping function (can take up lots of space). */
  enumToString?:
    | boolean
    | undefined;
  /** Generate validation methods for enums */
  enumValidate?:
    | boolean
    | undefined;
  /** Generate bytes arrays with fixed length */
  fixedLength?:
    | boolean
    | undefined;
  /** Generate repeated field with fixed count */
  fixedCount?:
    | boolean
    | undefined;
  /**
   * Generate message-level callback that is called before decoding submessages.
   * This can be used to set callback fields for submsgs inside oneofs.
   */
  submsgCallback?:
    | boolean
    | undefined;
  /**
   * Shorten or remove package names from type names.
   * This option applies only on the file level.
   */
  mangleNames?:
    | TypenameMangling
    | undefined;
  /** Data type for storage associated with callback fields. */
  callbackDatatype?:
    | string
    | undefined;
  /**
   * Callback function used for encoding and decoding.
   * Prior to nanopb-0.4.0, the callback was specified in per-field pb_callback_t
   * structure. This is still supported, but does not work inside e.g. oneof or pointer
   * fields. Instead, a new method allows specifying a per-message callback that
   * will be called for all callback fields in a message type.
   */
  callbackFunction?:
    | string
    | undefined;
  /**
   * Select the size of field descriptors. This option has to be defined
   * for the whole message, not per-field. Usually automatic selection is
   * ok, but if it results in compilation errors you can increase the field
   * size here.
   */
  descriptorsize?:
    | DescriptorSize
    | undefined;
  /** Set default value for has_ fields. */
  defaultHas?:
    | boolean
    | undefined;
  /** Extra files to include in generated `.pb.h` */
  include: string[];
  /**
   * Automatic includes to exclude from generated `.pb.h`
   * Same as nanopb_generator.py command line flag -x.
   */
  exclude: string[];
  /** Package name that applies only for nanopb. */
  package?:
    | string
    | undefined;
  /** Override type of the field in generated C code. Only to be used with related field types */
  typeOverride?:
    | FieldDescriptorProto_Type
    | undefined;
  /**
   * Override of the label of the field (see FieldDescriptorProto.Label). Can be used to create
   * fields which nanopb considers required in proto3, or whether nanopb treats the field as
   * optional/required/repeated.
   */
  labelOverride?:
    | FieldDescriptorProto_Label
    | undefined;
  /**
   * Due to historical reasons, nanopb orders fields in structs by their tag number
   * instead of the order in .proto. Set this to false to keep the .proto order.
   * The default value will probably change to false in nanopb-0.5.0.
   */
  sortByTag?:
    | boolean
    | undefined;
  /**
   * Set the FT_DEFAULT field conversion strategy.
   * A field that can become a static member of a c struct (e.g. int, bool, etc)
   * will be a a static field.
   * Fields with dynamic length are converted to either a pointer or a callback.
   */
  fallbackType?:
    | FieldType
    | undefined;
  /**
   * Override initializer used in generated MyMessage_init_zero and MyMessage_init_default macros
   * By default decided automatically based on field default value and datatype.
   */
  initializer?:
    | string
    | undefined;
  /**
   * Discard unused types that are automatically generated by protoc if they are not actually
   * needed. Currently this applies to map< > types when the field is ignored by options.
   */
  discardUnusedAutomaticTypes?:
    | boolean
    | undefined;
  /** Discard messages and fields marked with [deprecated = true] in the proto file. */
  discardDeprecated?: boolean | undefined;
}

function createBaseNanoPBOptions(): NanoPBOptions {
  return {
    maxSize: 0,
    maxLength: 0,
    maxCount: 0,
    intSize: 0,
    enumIntsize: 0,
    type: 0,
    longNames: true,
    packedStruct: false,
    packedEnum: false,
    skipMessage: false,
    noUnions: false,
    msgid: 0,
    anonymousOneof: false,
    proto3: false,
    proto3SingularMsgs: false,
    enumToString: false,
    enumValidate: false,
    fixedLength: false,
    fixedCount: false,
    submsgCallback: false,
    mangleNames: 0,
    callbackDatatype: "pb_callback_t",
    callbackFunction: "pb_default_field_callback",
    descriptorsize: 0,
    defaultHas: false,
    include: [],
    exclude: [],
    package: "",
    typeOverride: 1,
    labelOverride: 1,
    sortByTag: true,
    fallbackType: 1,
    initializer: "",
    discardUnusedAutomaticTypes: true,
    discardDeprecated: false,
  };
}

export const NanoPBOptions: MessageFns<NanoPBOptions> = {
  encode(message: NanoPBOptions, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.maxSize !== undefined && message.maxSize !== 0) {
      writer.uint32(8).int32(message.maxSize);
    }
    if (message.maxLength !== undefined && message.maxLength !== 0) {
      writer.uint32(112).int32(message.maxLength);
    }
    if (message.maxCount !== undefined && message.maxCount !== 0) {
      writer.uint32(16).int32(message.maxCount);
    }
    if (message.intSize !== undefined && message.intSize !== 0) {
      writer.uint32(56).int32(message.intSize);
    }
    if (message.enumIntsize !== undefined && message.enumIntsize !== 0) {
      writer.uint32(272).int32(message.enumIntsize);
    }
    if (message.type !== undefined && message.type !== 0) {
      writer.uint32(24).int32(message.type);
    }
    if (message.longNames !== undefined && message.longNames !== true) {
      writer.uint32(32).bool(message.longNames);
    }
    if (message.packedStruct !== undefined && message.packedStruct !== false) {
      writer.uint32(40).bool(message.packedStruct);
    }
    if (message.packedEnum !== undefined && message.packedEnum !== false) {
      writer.uint32(80).bool(message.packedEnum);
    }
    if (message.skipMessage !== undefined && message.skipMessage !== false) {
      writer.uint32(48).bool(message.skipMessage);
    }
    if (message.noUnions !== undefined && message.noUnions !== false) {
      writer.uint32(64).bool(message.noUnions);
    }
    if (message.msgid !== undefined && message.msgid !== 0) {
      writer.uint32(72).uint32(message.msgid);
    }
    if (message.anonymousOneof !== undefined && message.anonymousOneof !== false) {
      writer.uint32(88).bool(message.anonymousOneof);
    }
    if (message.proto3 !== undefined && message.proto3 !== false) {
      writer.uint32(96).bool(message.proto3);
    }
    if (message.proto3SingularMsgs !== undefined && message.proto3SingularMsgs !== false) {
      writer.uint32(168).bool(message.proto3SingularMsgs);
    }
    if (message.enumToString !== undefined && message.enumToString !== false) {
      writer.uint32(104).bool(message.enumToString);
    }
    if (message.enumValidate !== undefined && message.enumValidate !== false) {
      writer.uint32(256).bool(message.enumValidate);
    }
    if (message.fixedLength !== undefined && message.fixedLength !== false) {
      writer.uint32(120).bool(message.fixedLength);
    }
    if (message.fixedCount !== undefined && message.fixedCount !== false) {
      writer.uint32(128).bool(message.fixedCount);
    }
    if (message.submsgCallback !== undefined && message.submsgCallback !== false) {
      writer.uint32(176).bool(message.submsgCallback);
    }
    if (message.mangleNames !== undefined && message.mangleNames !== 0) {
      writer.uint32(136).int32(message.mangleNames);
    }
    if (message.callbackDatatype !== undefined && message.callbackDatatype !== "pb_callback_t") {
      writer.uint32(146).string(message.callbackDatatype);
    }
    if (message.callbackFunction !== undefined && message.callbackFunction !== "pb_default_field_callback") {
      writer.uint32(154).string(message.callbackFunction);
    }
    if (message.descriptorsize !== undefined && message.descriptorsize !== 0) {
      writer.uint32(160).int32(message.descriptorsize);
    }
    if (message.defaultHas !== undefined && message.defaultHas !== false) {
      writer.uint32(184).bool(message.defaultHas);
    }
    for (const v of message.include) {
      writer.uint32(194).string(v!);
    }
    for (const v of message.exclude) {
      writer.uint32(210).string(v!);
    }
    if (message.package !== undefined && message.package !== "") {
      writer.uint32(202).string(message.package);
    }
    if (message.typeOverride !== undefined && message.typeOverride !== 1) {
      writer.uint32(216).int32(message.typeOverride);
    }
    if (message.labelOverride !== undefined && message.labelOverride !== 1) {
      writer.uint32(248).int32(message.labelOverride);
    }
    if (message.sortByTag !== undefined && message.sortByTag !== true) {
      writer.uint32(224).bool(message.sortByTag);
    }
    if (message.fallbackType !== undefined && message.fallbackType !== 1) {
      writer.uint32(232).int32(message.fallbackType);
    }
    if (message.initializer !== undefined && message.initializer !== "") {
      writer.uint32(242).string(message.initializer);
    }
    if (message.discardUnusedAutomaticTypes !== undefined && message.discardUnusedAutomaticTypes !== true) {
      writer.uint32(264).bool(message.discardUnusedAutomaticTypes);
    }
    if (message.discardDeprecated !== undefined && message.discardDeprecated !== false) {
      writer.uint32(280).bool(message.discardDeprecated);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NanoPBOptions {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNanoPBOptions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.maxSize = reader.int32();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.maxLength = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.maxCount = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.intSize = reader.int32() as any;
          continue;
        }
        case 34: {
          if (tag !== 272) {
            break;
          }

          message.enumIntsize = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.longNames = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.packedStruct = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.packedEnum = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.skipMessage = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.noUnions = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.msgid = reader.uint32();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.anonymousOneof = reader.bool();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.proto3 = reader.bool();
          continue;
        }
        case 21: {
          if (tag !== 168) {
            break;
          }

          message.proto3SingularMsgs = reader.bool();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.enumToString = reader.bool();
          continue;
        }
        case 32: {
          if (tag !== 256) {
            break;
          }

          message.enumValidate = reader.bool();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.fixedLength = reader.bool();
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.fixedCount = reader.bool();
          continue;
        }
        case 22: {
          if (tag !== 176) {
            break;
          }

          message.submsgCallback = reader.bool();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.mangleNames = reader.int32() as any;
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.callbackDatatype = reader.string();
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.callbackFunction = reader.string();
          continue;
        }
        case 20: {
          if (tag !== 160) {
            break;
          }

          message.descriptorsize = reader.int32() as any;
          continue;
        }
        case 23: {
          if (tag !== 184) {
            break;
          }

          message.defaultHas = reader.bool();
          continue;
        }
        case 24: {
          if (tag !== 194) {
            break;
          }

          message.include.push(reader.string());
          continue;
        }
        case 26: {
          if (tag !== 210) {
            break;
          }

          message.exclude.push(reader.string());
          continue;
        }
        case 25: {
          if (tag !== 202) {
            break;
          }

          message.package = reader.string();
          continue;
        }
        case 27: {
          if (tag !== 216) {
            break;
          }

          message.typeOverride = reader.int32() as any;
          continue;
        }
        case 31: {
          if (tag !== 248) {
            break;
          }

          message.labelOverride = reader.int32() as any;
          continue;
        }
        case 28: {
          if (tag !== 224) {
            break;
          }

          message.sortByTag = reader.bool();
          continue;
        }
        case 29: {
          if (tag !== 232) {
            break;
          }

          message.fallbackType = reader.int32() as any;
          continue;
        }
        case 30: {
          if (tag !== 242) {
            break;
          }

          message.initializer = reader.string();
          continue;
        }
        case 33: {
          if (tag !== 264) {
            break;
          }

          message.discardUnusedAutomaticTypes = reader.bool();
          continue;
        }
        case 35: {
          if (tag !== 280) {
            break;
          }

          message.discardDeprecated = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<NanoPBOptions>, I>>(base?: I): NanoPBOptions {
    return NanoPBOptions.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NanoPBOptions>, I>>(object: I): NanoPBOptions {
    const message = createBaseNanoPBOptions();
    message.maxSize = object.maxSize ?? 0;
    message.maxLength = object.maxLength ?? 0;
    message.maxCount = object.maxCount ?? 0;
    message.intSize = object.intSize ?? 0;
    message.enumIntsize = object.enumIntsize ?? 0;
    message.type = object.type ?? 0;
    message.longNames = object.longNames ?? true;
    message.packedStruct = object.packedStruct ?? false;
    message.packedEnum = object.packedEnum ?? false;
    message.skipMessage = object.skipMessage ?? false;
    message.noUnions = object.noUnions ?? false;
    message.msgid = object.msgid ?? 0;
    message.anonymousOneof = object.anonymousOneof ?? false;
    message.proto3 = object.proto3 ?? false;
    message.proto3SingularMsgs = object.proto3SingularMsgs ?? false;
    message.enumToString = object.enumToString ?? false;
    message.enumValidate = object.enumValidate ?? false;
    message.fixedLength = object.fixedLength ?? false;
    message.fixedCount = object.fixedCount ?? false;
    message.submsgCallback = object.submsgCallback ?? false;
    message.mangleNames = object.mangleNames ?? 0;
    message.callbackDatatype = object.callbackDatatype ?? "pb_callback_t";
    message.callbackFunction = object.callbackFunction ?? "pb_default_field_callback";
    message.descriptorsize = object.descriptorsize ?? 0;
    message.defaultHas = object.defaultHas ?? false;
    message.include = object.include?.map((e) => e) || [];
    message.exclude = object.exclude?.map((e) => e) || [];
    message.package = object.package ?? "";
    message.typeOverride = object.typeOverride ?? 1;
    message.labelOverride = object.labelOverride ?? 1;
    message.sortByTag = object.sortByTag ?? true;
    message.fallbackType = object.fallbackType ?? 1;
    message.initializer = object.initializer ?? "";
    message.discardUnusedAutomaticTypes = object.discardUnusedAutomaticTypes ?? true;
    message.discardDeprecated = object.discardDeprecated ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
