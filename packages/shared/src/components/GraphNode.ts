import { Node } from "@xyflow/react";
import { match } from "ts-pattern";
import { randomId } from "../lib/randomId";
import { CommentNodeData } from "./CommentNode";
import {
  CanBusControllerAnchorNodeData,
  CanBusControllerContainerNodeData,
  createDefaultRelayConnectors,
  createDefaultThreePinPorts,
  createDefaultTwoPinPorts,
  createDefaultZeroToTenVoltPorts,
} from "./devices/CanBusController";
import {
  ControllerAnchorNodeData,
  ControllerContainerNodeData,
} from "./devices/Controller";
import {
  aDoorSensorAction,
  DoorSensorAnchorNodeData,
  DoorSensorContainerNodeData,
} from "./devices/DoorSensor";
import {
  aOutletDimmerButton,
  aOutletDimmerVia,
  OutletDimmerAnchorNodeData,
  OutletDimmerContainerNodeData,
} from "./devices/OutletDimmer";
import {
  aPresenceSensorAction,
  PresenceSensorAnchorNodeData,
  PresenceSensorContainerNodeData,
} from "./devices/PresenceSensor";
import {
  aServicePadAction,
  ServicePadAnchorNodeData,
  ServicePadContainerNodeData,
} from "./devices/ServicePad";
import {
  aSomoDimmerButton,
  aSomoDimmerVia,
  SomoDimmerAnchorNodeData,
  SomoDimmerContainerNodeData,
} from "./devices/SomoDimmer";
import {
  aSomoFanButton,
  aSomoFanVia,
  SomoFanAnchorNodeData,
  SomoFanContainerNodeData,
} from "./devices/SomoFan";
import {
  aSomoIrControllerButton,
  aSomoIrControllerVia,
  SomoIrControllerAnchorNodeData,
  SomoIrControllerContainerNodeData,
} from "./devices/SomoIrController";
import {
  aSomoShadesButton,
  aSomoShadesVia,
  SomoShadesAnchorNodeData,
  SomoShadesContainerNodeData,
} from "./devices/SomoShades";
import {
  aSomoSwitchVia,
  SomoSwitchAnchorNodeData,
  SomoSwitchContainerNodeData,
} from "./devices/SomoSwitch";
import {
  aSomoThermostatButton,
  aSomoThermostatVia,
  SomoThermostatAnchorNodeData,
  SomoThermostatContainerNodeData,
} from "./devices/SomoThermostat";
import {
  VirtualButtonAnchorNodeData,
  VirtualButtonContainerNodeData,
} from "./devices/VirtualButton";
import { ImageNodeData } from "./ImageNode";
import { SectionNodeData } from "./SectionNode";

/**
 * Map each Node `.type` to its related `.data` type.
 *
 * @example
 *   `Node<ImageNodeData, "image">` => `image: ImageNodeData`
 */
type NodesDataByType = {
  image: ImageNodeData;
  comment: CommentNodeData;
  section: SectionNodeData;
  virtualButtonAnchor: VirtualButtonAnchorNodeData;
  virtualButtonContainer: VirtualButtonContainerNodeData;
  controllerAnchor: ControllerAnchorNodeData;
  controllerContainer: ControllerContainerNodeData;
  canbusControllerAnchor: CanBusControllerAnchorNodeData;
  canbusControllerContainer: CanBusControllerContainerNodeData;
  somoSwitchAnchor: SomoSwitchAnchorNodeData;
  somoSwitchContainer: SomoSwitchContainerNodeData;
  somoDimmerAnchor: SomoDimmerAnchorNodeData;
  somoDimmerContainer: SomoDimmerContainerNodeData;
  outletDimmerAnchor: OutletDimmerAnchorNodeData;
  outletDimmerContainer: OutletDimmerContainerNodeData;
  presenceSensorAnchor: PresenceSensorAnchorNodeData;
  presenceSensorContainer: PresenceSensorContainerNodeData;
  doorSensorAnchor: DoorSensorAnchorNodeData;
  doorSensorContainer: DoorSensorContainerNodeData;
  somoThermostatAnchor: SomoThermostatAnchorNodeData;
  somoThermostatContainer: SomoThermostatContainerNodeData;
  somoIrControllerAnchor: SomoIrControllerAnchorNodeData;
  somoIrControllerContainer: SomoIrControllerContainerNodeData;
  somoShadesAnchor: SomoShadesAnchorNodeData;
  somoShadesContainer: SomoShadesContainerNodeData;
  somoFanAnchor: SomoFanAnchorNodeData;
  somoFanContainer: SomoFanContainerNodeData;
  servicePadAnchor: ServicePadAnchorNodeData;
  servicePadContainer: ServicePadContainerNodeData;
};

// Derive @xyflow/react's `Node` types based on the bindings above
export type GraphNodesByType = {
  [K in keyof NodesDataByType]: Node<NodesDataByType[K], K>;
};

export type GraphNode = GraphNodesByType[keyof GraphNodesByType];

/**
 * Helper function to filter nodes by type that gives you autocompletion +
 * type guard the filtered nodes.
 *
 * @example
 *   const nodes = filterNodes(nodes, "image");
 *   // ^ => Node<ImageNodeData, "image">[]
 */
export function filterNodes<Type extends keyof GraphNodesByType>(
  nodes: GraphNode[],
  type: Type,
): GraphNodesByType[Type][] {
  return nodes.filter((n): n is GraphNodesByType[Type] => n.type === type);
}

export function isAnchor(node: GraphNode) {
  // Do an exhaustive check so we *have* to update if we change the graph nodes
  return match(node.type)
    .with(
      "virtualButtonAnchor",
      "controllerAnchor",
      "canbusControllerAnchor",
      "somoSwitchAnchor",
      "somoDimmerAnchor",
      "outletDimmerAnchor",
      "presenceSensorAnchor",
      "doorSensorAnchor",
      "somoThermostatAnchor",
      "somoIrControllerAnchor",
      "somoShadesAnchor",
      "somoFanAnchor",
      "servicePadAnchor",
      () => true,
    )
    .with(
      "image",
      "section",
      "comment",
      "virtualButtonContainer",
      "controllerContainer",
      "canbusControllerContainer",
      "somoSwitchContainer",
      "somoDimmerContainer",
      "outletDimmerContainer",
      "presenceSensorContainer",
      "doorSensorContainer",
      "somoThermostatContainer",
      "somoIrControllerContainer",
      "somoShadesContainer",
      "somoFanContainer",
      "servicePadContainer",
      undefined,
      () => false,
    )
    .exhaustive();
}

// #region Generic helpers for @xyflow nodes

export function isManipulated(node: Node) {
  return node.selected || node.dragging;
}

export function isIdle(node: Node) {
  return !isManipulated(node);
}

export function isNodeOrParentSelected(allNodes: Node[], node: Node) {
  return node.selected || Boolean(findParent(allNodes, node)?.selected);
}

export function findParent(allNodes: Node[], node: Node) {
  if (!node.parentId) {
    return null;
  }

  return allNodes.find((n) => n.id === node.parentId);
}

// #endregion

// #region Factories

export function aSectionNode(
  params: Partial<GraphNodesByType["section"]>,
  data?: Partial<SectionNodeData>,
): GraphNodesByType["section"] {
  return {
    id: `section-node-${randomId()}`,
    type: "section",
    position: { x: 0, y: 0 },
    data: {
      title: "Section",
      devices: {},
      ...data,
    },
    ...params,
  };
}

export function aControllerContainerNode(
  params: Partial<GraphNodesByType["controllerContainer"]>,
  data?: Partial<ControllerContainerNodeData>,
): GraphNodesByType["controllerContainer"] {
  return {
    id: `controller-node-${randomId()}`,
    type: "controllerContainer",
    position: { x: 0, y: 0 },
    data: {
      title: "Controller Container",
      controllers: {},
      ...data,
    },
    ...params,
  };
}

export function aCanBusControllerContainerNode(
  params: Partial<GraphNodesByType["canbusControllerContainer"]>,
  data?: Partial<CanBusControllerContainerNodeData>,
): GraphNodesByType["canbusControllerContainer"] {
  return {
    id: `canbus-controller-node-${randomId()}`,
    type: "canbusControllerContainer",
    position: { x: 0, y: 0 },
    data: {
      title: "CanBus Controller Container",
      controllers: {},
      pirSensors: {},
      zeroToTenVoltDimmers: {},
      relayOutputs: {},
      threePinPorts: createDefaultThreePinPorts(),
      twoPinPorts: createDefaultTwoPinPorts(),
      zeroToTenVoltPorts: createDefaultZeroToTenVoltPorts(),
      relayConnectors: createDefaultRelayConnectors(),
      ...data,
    },
    ...params,
  };
}

export function aSomoSwitchContainerNode(
  params: Partial<GraphNodesByType["somoSwitchContainer"]>,
  data?: Partial<SomoSwitchContainerNodeData>,
): GraphNodesByType["somoSwitchContainer"] {
  return {
    id: `somo-switch-container-node-${randomId()}`,
    type: "somoSwitchContainer",
    position: { x: 0, y: 0 },
    data: {
      title: "Somo Switch Container",
      via1: aSomoSwitchVia({}),
      via2: aSomoSwitchVia({}),
      via3: aSomoSwitchVia({}),
      ...data,
    },
    ...params,
  };
}

export function aSomoDimmerContainerNode(
  params: Partial<GraphNodesByType["somoDimmerContainer"]>,
  data?: Partial<SomoDimmerContainerNodeData>,
): GraphNodesByType["somoDimmerContainer"] {
  return {
    id: `somo-dimmer-container-node-${randomId()}`,
    type: "somoDimmerContainer",
    position: { x: 0, y: 0 },
    data: {
      title: "Somo Dimmer Container",
      via: aSomoDimmerVia({}),
      viaUp: aSomoDimmerButton({}),
      viaDown: aSomoDimmerButton({}),
      dimSpeed: 0,
      dimmingCurve: {
        points: [],
        type: "linear",
      },
      ...data,
    },
    ...params,
  };
}

export function aOutletDimmerContainerNode(
  params: Partial<GraphNodesByType["outletDimmerContainer"]>,
  data?: Partial<OutletDimmerContainerNodeData>,
): GraphNodesByType["outletDimmerContainer"] {
  return {
    id: `outlet-dimmer-container-node-${randomId()}`,
    type: "outletDimmerContainer",
    position: { x: 0, y: 0 },
    data: {
      title: "Outlet Dimmer Container",
      via: aOutletDimmerVia({}),
      viaUp: aOutletDimmerButton({}),
      viaDown: aOutletDimmerButton({}),
      dimSpeed: 0,
      dimmingCurve: {
        points: [],
        type: "linear",
      },
      ...data,
    },
    ...params,
  };
}

export function aPresenceSensorContainerNode(
  params: Partial<GraphNodesByType["presenceSensorContainer"]>,
  data?: Partial<PresenceSensorContainerNodeData>,
): GraphNodesByType["presenceSensorContainer"] {
  return {
    id: `presence-sensor-container-node-${randomId()}`,
    type: "presenceSensorContainer",
    position: { x: 0, y: 0 },
    data: {
      title: "Presence Sensor Container",
      onActivate: aPresenceSensorAction({}),
      onDeactivate: aPresenceSensorAction({}),
      ...data,
    },
    ...params,
  };
}

export function aDoorSensorContainerNode(
  params: Partial<GraphNodesByType["doorSensorContainer"]>,
  data?: Partial<DoorSensorContainerNodeData>,
): GraphNodesByType["doorSensorContainer"] {
  return {
    id: `door-sensor-container-node-${randomId()}`,
    type: "doorSensorContainer",
    position: { x: 0, y: 0 },
    data: {
      title: "Door Sensor Container",
      onOpen: aDoorSensorAction({}),
      onClose: aDoorSensorAction({}),
      ...data,
    },
    ...params,
  };
}

export function aSomoThermostatContainerNode(
  params: Partial<GraphNodesByType["somoThermostatContainer"]>,
  data?: Partial<SomoThermostatContainerNodeData>,
): GraphNodesByType["somoThermostatContainer"] {
  return {
    id: `somo-thermostat-container-node-${randomId()}`,
    type: "somoThermostatContainer",
    position: { x: 0, y: 0 },
    data: {
      title: "Somo Thermostat Container",
      temperatureUnit: "C",
      minTemp: 41,
      maxTemp: 95,
      allowedModes: ["heat", "cool", "fan", "auto"],
      allowedFanSpeeds: ["low", "medium", "high", "auto"],
      via: aSomoThermostatVia({}),
      viaUp: aSomoThermostatButton({}),
      viaDown: aSomoThermostatButton({}),
      modeCool: aSomoThermostatButton({}),
      modeHeat: aSomoThermostatButton({}),
      modeAuto: aSomoThermostatButton({}),
      modeFan: aSomoThermostatButton({}),
      fanAuto: aSomoThermostatButton({}),
      fanLow: aSomoThermostatButton({}),
      fanMedium: aSomoThermostatButton({}),
      fanHigh: aSomoThermostatButton({}),
      stepSize: 1,
      ...data,
    },
    ...params,
  };
}

export function aSomoIrControllerContainerNode(
  params: Partial<GraphNodesByType["somoIrControllerContainer"]>,
  data?: Partial<SomoIrControllerContainerNodeData>,
): GraphNodesByType["somoIrControllerContainer"] {
  return {
    id: `somo-ir-controller-container-node-${randomId()}`,
    type: "somoIrControllerContainer",
    position: { x: 0, y: 0 },
    data: {
      title: "Somo IR HVAC Controller Container",
      temperatureUnit: "C",
      minTemp: 5,
      maxTemp: 35,
      allowedModes: ["heat", "cool", "fan", "auto"],
      allowedFanSpeeds: ["low", "medium", "high", "auto"],
      via: aSomoIrControllerVia({}),
      viaUp: aSomoIrControllerButton({}),
      viaDown: aSomoIrControllerButton({}),
      modeCool: aSomoIrControllerButton({}),
      modeHeat: aSomoIrControllerButton({}),
      modeAuto: aSomoIrControllerButton({}),
      modeFan: aSomoIrControllerButton({}),
      fanAuto: aSomoIrControllerButton({}),
      fanLow: aSomoIrControllerButton({}),
      fanMedium: aSomoIrControllerButton({}),
      fanHigh: aSomoIrControllerButton({}),
      stepSize: 1,
      ...data,
    },
    ...params,
  };
}

export function aSomoShadesContainerNode(
  params: Partial<GraphNodesByType["somoShadesContainer"]>,
  data?: Partial<SomoShadesContainerNodeData>,
): GraphNodesByType["somoShadesContainer"] {
  return {
    id: `somo-shades-container-node-${randomId()}`,
    type: "somoShadesContainer",
    position: { x: 0, y: 0 },
    data: {
      title: "Somo Shades Container",
      via: aSomoShadesVia({}),
      viaUp: aSomoShadesButton({}),
      viaDown: aSomoShadesButton({}),
      raceTime: 0,
      ...data,
    },
    ...params,
  };
}

export function aSomoFanContainerNode(
  params: Partial<GraphNodesByType["somoFanContainer"]>,
  data?: Partial<SomoFanContainerNodeData>,
): GraphNodesByType["somoFanContainer"] {
  return {
    id: `somo-fan-container-node-${randomId()}`,
    type: "somoFanContainer",
    position: { x: 0, y: 0 },
    data: {
      title: "Somo Fan Container",
      via: aSomoFanVia({}),
      viaLow: aSomoFanButton({}),
      viaMed: aSomoFanButton({}),
      viaHigh: aSomoFanButton({}),
      fanType: "onoff",
      ...data,
    },
    ...params,
  };
}

export function aServicePadContainerNode(
  params: Partial<GraphNodesByType["servicePadContainer"]>,
  data?: Partial<ServicePadContainerNodeData>,
): GraphNodesByType["servicePadContainer"] {
  return {
    id: `service-pad-container-node-${randomId()}`,
    type: "servicePadContainer",
    position: { x: 0, y: 0 },
    data: {
      title: "Service Pad Container",
      mode: "servicePad",
      makeUpRoomButton: aServicePadAction({}),
      doorbellButton: aServicePadAction({}),
      doNotDisturbButton: aServicePadAction({}),
      ...data,
    },
    ...params,
  };
}

// #endregion
