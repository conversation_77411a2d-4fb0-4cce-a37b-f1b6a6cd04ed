import { z } from "zod/v4";

/* eslint-disable @typescript-eslint/no-explicit-any */
export type JSONContent = {
  type?: string;
  attrs?: Record<string, any>;
  content?: JSONContent[];
  marks?: {
    type: string;
    attrs?: Record<string, any>;
    [key: string]: any;
  }[];
  text?: string;
  [key: string]: any;
};
/* eslint-enable @typescript-eslint/no-explicit-any */

// We don't have a Zod schema for tiptap's JSONContent, so go with any
const JSONContent = z.any().describe("@tiptap/react JSONContent");

export const CommentNodeReply = z.object({
  id: z.string(),
  text: JSONContent,
  authorId: z.string(),
  author: z.string(),
  authorAvatar: z.string().optional(),
  createdAt: z.number(),
});
export type CommentNodeReply = z.infer<typeof CommentNodeReply>;

export const CommentNodeData = z.object({
  componentParentId: z.string().optional(),
  text: JSONContent,
  authorId: z.string(),
  author: z.string(),
  authorAvatar: z.string().optional(),
  createdAt: z.number(),
  replies: z.array(CommentNodeReply),
});
export type CommentNodeData = z.infer<typeof CommentNodeData>;
