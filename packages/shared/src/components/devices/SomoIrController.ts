import { z } from "zod/v4";
import { DeviceIconKey } from "../DeviceIcons";
import { CurvePoint, CurveType } from "../DimmingCurve";
import { BaseDevice } from "./BaseDevice";
import { ThermostatControlSettings } from "./ThermostatControlSettings";

export const somoIrControllerViaIds = [
  "via",
  "viaUp",
  "viaDown",
  "modeCool",
  "modeHeat",
  "modeAuto",
  "modeFan",
  "fanAuto",
  "fanLow",
  "fanMedium",
  "fanHigh",
] as const;
export const SomoIrControllerViaId = z.enum(somoIrControllerViaIds);
export type SomoIrControllerViaId = z.infer<typeof SomoIrControllerViaId>;

export const SomoIrControllerVia = z.object({
  name: z.string(),
  icon: DeviceIconKey.optional(),
  hvacName: z.string(),
  hvacIcon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z
    .record(z.string(), ThermostatControlSettings)
    .refine((record) => Object.keys(record).length <= 1, {
      error: "Only one action is allowed.",
    }),
});
export type SomoIrControllerVia = z.infer<typeof SomoIrControllerVia>;

export function aSomoIrControllerVia(
  params: Partial<SomoIrControllerVia>,
): SomoIrControllerVia {
  return {
    name: "Somo IR Controller Via",
    showLabel: false,
    hvacName: "IR Controller",
    icon: undefined,
    hvacIcon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const SomoIrControllerButton = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z
    .record(z.string(), ThermostatControlSettings)
    .refine((record) => Object.keys(record).length <= 1, {
      error: "Only one action is allowed.",
    }),
});
export type SomoIrControllerButton = z.infer<typeof SomoIrControllerButton>;

export function aSomoIrControllerButton(
  params: Partial<SomoIrControllerButton>,
): SomoIrControllerButton {
  return {
    name: "Somo IR Controller Button",
    enabled: false,
    showLabel: false,
    icon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const SomoIrControllerContainerNodeData = z.object({
  title: z.string(),
  minTemp: z.number().describe("Default minimum temperature in degrees"),
  maxTemp: z.number().describe("Default maximum temperature in degrees"),
  temperatureUnit: z.enum(["C", "F"]),
  allowedModes: z.array(z.enum(["heat", "cool", "fan", "auto"])),
  allowedFanSpeeds: z.array(z.enum(["low", "medium", "high", "auto"])),
  via: SomoIrControllerVia,
  viaUp: SomoIrControllerButton,
  viaDown: SomoIrControllerButton,
  modeCool: SomoIrControllerButton,
  modeHeat: SomoIrControllerButton,
  modeAuto: SomoIrControllerButton,
  modeFan: SomoIrControllerButton,
  fanAuto: SomoIrControllerButton,
  fanLow: SomoIrControllerButton,
  fanMedium: SomoIrControllerButton,
  fanHigh: SomoIrControllerButton,
  stepSize: z
    .number()
    .prefault(1)
    .describe("Step size for setpoint adjustment"),
});
export type SomoIrControllerContainerNodeData = z.infer<
  typeof SomoIrControllerContainerNodeData
>;

export const SomoIrControllerAnchorNodeData = z.record(z.string(), z.unknown());
export type SomoIrControllerAnchorNodeData = z.infer<
  typeof SomoIrControllerAnchorNodeData
>;

export const SomoIrControllerDevice = BaseDevice.extend({
  type: z.literal("somoIrController"),
  nodeId: z.string(),
  viaId: SomoIrControllerViaId,
  dimmingCurve: z.array(CurvePoint).optional(),
  dimmingCurveType: CurveType.optional(),
  defaultDimmingSpeed: z.number().optional(),
});
export type SomoIrControllerDevice = z.infer<typeof SomoIrControllerDevice>;
