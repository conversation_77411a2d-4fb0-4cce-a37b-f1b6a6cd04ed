import { z } from "zod/v4";
import { DeviceIconKey } from "../DeviceIcons";
import { CurvePoint, CurveType } from "../DimmingCurve";
import { BaseDevice } from "./BaseDevice";
import { DeviceControlSettings } from "./DeviceControlSettings";

export const somoDimmerViaIds = ["via", "viaUp", "viaDown"] as const;
export const SomoDimmerViaId = z.enum(somoDimmerViaIds);
export type SomoDimmerViaId = z.infer<typeof SomoDimmerViaId>;

export const SomoDimmerVia = z.object({
  name: z.string(),
  icon: DeviceIconKey.optional(),
  lightName: z.string(),
  lightIcon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
});
export type SomoDimmerVia = z.infer<typeof SomoDimmerVia>;

export function aSomoDimmerVia(params: Partial<SomoDimmerVia>): SomoDimmerVia {
  return {
    name: "Somo Dimmer Via",
    showLabel: false,
    lightName: "Light",
    icon: undefined,
    lightIcon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const SomoDimmerButton = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
});
export type SomoDimmerButton = z.infer<typeof SomoDimmerButton>;

export function aSomoDimmerButton(
  params: Partial<SomoDimmerButton>,
): SomoDimmerButton {
  return {
    name: "Somo Dimmer Button",
    enabled: false,
    showLabel: false,
    icon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const SomoDimmerContainerNodeData = z.object({
  title: z.string(),
  via: SomoDimmerVia,
  viaUp: SomoDimmerButton,
  viaDown: SomoDimmerButton,
  dimSpeed: z.number().describe("Default dimming speed in seconds"),
  dimmingCurve: z.object({
    points: z.array(CurvePoint),
    type: CurveType,
  }),
});
export type SomoDimmerContainerNodeData = z.infer<
  typeof SomoDimmerContainerNodeData
>;

export const SomoDimmerAnchorNodeData = z.record(z.string(), z.unknown());
export type SomoDimmerAnchorNodeData = z.infer<typeof SomoDimmerAnchorNodeData>;

export const SomoDimmerDevice = BaseDevice.extend({
  type: z.literal("somoDimmer"),
  nodeId: z.string(),
  viaId: SomoDimmerViaId,
  dimmingCurve: z.array(CurvePoint).optional(),
  dimmingCurveType: CurveType.optional(),
  defaultDimmingSpeed: z.number().optional(),
});
export type SomoDimmerDevice = z.infer<typeof SomoDimmerDevice>;
