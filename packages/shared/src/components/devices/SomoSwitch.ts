import { z } from "zod/v4";
import { DeviceIconKey } from "../DeviceIcons";
import { BaseDevice } from "./BaseDevice";
import { DeviceControlSettings } from "./DeviceControlSettings";

export const somoSwitchViaIds = ["via1", "via2", "via3"] as const;
export const SomoSwitchViaId = z.enum(somoSwitchViaIds);
export type SomoSwitchViaId = z.infer<typeof SomoSwitchViaId>;

export const SomoSwitchVia = z.object({
  enabled: z.boolean(),
  hasLoad: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  lightName: z.string(),
  lightIcon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
});
export type SomoSwitchVia = z.infer<typeof SomoSwitchVia>;

export function aSomoSwitchVia(params: Partial<SomoSwitchVia>): SomoSwitchVia {
  return {
    name: "Somo Switch Via",
    enabled: false,
    hasLoad: false,
    showLabel: false,
    lightName: "Light",
    icon: undefined,
    lightIcon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const SomoSwitchContainerNodeData = z.object({
  title: z.string(),
  via1: SomoSwitchVia,
  via2: SomoSwitchVia,
  via3: SomoSwitchVia,
});
export type SomoSwitchContainerNodeData = z.infer<
  typeof SomoSwitchContainerNodeData
>;

export const SomoSwitchAnchorNodeData = z.record(z.string(), z.unknown());
export type SomoSwitchAnchorNodeData = z.infer<typeof SomoSwitchAnchorNodeData>;

export const SomoSwitchDevice = BaseDevice.extend({
  type: z.literal("somoSwitch"),
  nodeId: z.string(),
  viaId: SomoSwitchViaId,
});
export type SomoSwitchDevice = z.infer<typeof SomoSwitchDevice>;
