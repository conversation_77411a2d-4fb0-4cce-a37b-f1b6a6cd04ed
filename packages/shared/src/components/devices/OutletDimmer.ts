import { z } from "zod/v4";
import { DeviceIconKey } from "../DeviceIcons";
import { CurvePoint, CurveType } from "../DimmingCurve";
import { BaseDevice } from "./BaseDevice";
import { DeviceControlSettings } from "./DeviceControlSettings";

export const outletDimmerViaIds = ["via", "viaUp", "viaDown"] as const;
export const OutletDimmerViaId = z.enum(outletDimmerViaIds);
export type OutletDimmerViaId = z.infer<typeof OutletDimmerViaId>;

export const OutletDimmerVia = z.object({
  name: z.string(),
  icon: DeviceIconKey.optional(),
  lightName: z.string(),
  lightIcon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z
    .record(z.string(), DeviceControlSettings)
    .refine((record) => Object.keys(record).length <= 1, {
      error: "Only one action is allowed.",
    }),
});
export type OutletDimmerVia = z.infer<typeof OutletDimmerVia>;

export function aOutletDimmerVia(
  params: Partial<OutletDimmerVia>,
): OutletDimmerVia {
  return {
    name: "Outlet Dimmer Via",
    showLabel: false,
    lightName: "Light",
    icon: undefined,
    lightIcon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const OutletDimmerButton = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z
    .record(z.string(), DeviceControlSettings)
    .refine((record) => Object.keys(record).length <= 1, {
      error: "Only one action is allowed.",
    }),
});
export type OutletDimmerButton = z.infer<typeof OutletDimmerButton>;

export function aOutletDimmerButton(
  params: Partial<OutletDimmerButton>,
): OutletDimmerButton {
  return {
    name: "Outlet Dimmer Button",
    enabled: false,
    showLabel: false,
    icon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const OutletDimmerContainerNodeData = z.object({
  title: z.string(),
  via: OutletDimmerVia,
  viaUp: OutletDimmerButton,
  viaDown: OutletDimmerButton,
  dimSpeed: z.number().describe("Default dimming speed in seconds"),
  dimmingCurve: z.object({
    points: z.array(CurvePoint),
    type: CurveType,
  }),
});
export type OutletDimmerContainerNodeData = z.infer<
  typeof OutletDimmerContainerNodeData
>;

export const OutletDimmerAnchorNodeData = z.record(z.string(), z.unknown());
export type OutletDimmerAnchorNodeData = z.infer<
  typeof OutletDimmerAnchorNodeData
>;

export const OutletDimmerDevice = BaseDevice.extend({
  type: z.literal("outletDimmer"),
  nodeId: z.string(),
  viaId: OutletDimmerViaId,
  dimmingCurve: z.array(CurvePoint).optional(),
  dimmingCurveType: CurveType.optional(),
  defaultDimmingSpeed: z.number().optional(),
});
export type OutletDimmerDevice = z.infer<typeof OutletDimmerDevice>;
