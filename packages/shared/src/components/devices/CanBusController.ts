import { z } from "zod/v4";
import { randomId } from "../../lib/randomId";
import { <PERSON>ceIconKey } from "../DeviceIcons";
import { BaseDevice } from "./BaseDevice";
import { DeviceControlSettings } from "./DeviceControlSettings";

const BaseCanBusController = z.object({
  id: z.string(),
  label: z.string(),
  sortIndex: z.number(),
});

// Port definitions
export const ThreePinPort = z.object({
  id: z.string(),
  label: z.string(),
  connected: z.boolean().prefault(false),
  deviceType: z.enum(["toggle", "momentary", "none"]).prefault("none"),
  controllerId: z.string().optional(), // Links to a controller when connected
});
export type ThreePinPort = z.infer<typeof ThreePinPort>;

export const TwoPinPort = z.object({
  id: z.string(),
  label: z.string(),
  connected: z.boolean().prefault(false),
  deviceType: z.enum(["pir", "none"]).prefault("none"),
  sensorId: z.string().optional(), // Links to a PIR sensor when connected
});
export type TwoPinPort = z.infer<typeof TwoPinPort>;

export const ZeroToTenVoltPort = z.object({
  id: z.string(),
  label: z.string(),
  connected: z.boolean().prefault(false),
  deviceType: z.enum(["zeroToTenVoltDimmer", "none"]).prefault("none"),
  dimmerId: z.string().optional(), // Links to a dimmer when connected
});
export type ZeroToTenVoltPort = z.infer<typeof ZeroToTenVoltPort>;

export const RelayConnector = z.object({
  id: z.string(),
  label: z.string(),
  connected: z.boolean().prefault(false),
  deviceType: z
    .enum(["zeroToTenVoltDimmer", "relayOutput", "none"])
    .prefault("none"),
  dimmerId: z.string().optional(), // Links to a dimmer when connected
  relayOutputId: z.string().optional(), // Links to a relay output when connected
});
export type RelayConnector = z.infer<typeof RelayConnector>;

// PIR Sensor (behaves like presence sensor)
export const PirSensorViaId = z.enum(["onActivate", "onDeactivate"]);
export type PirSensorViaId = z.infer<typeof PirSensorViaId>;

export const PirSensor = z.object({
  id: z.string(),
  label: z.string(),
  portId: z.string(), // Which 2-pin port it's connected to
  onActivate: z.record(z.string(), DeviceControlSettings),
  onDeactivate: z.record(z.string(), DeviceControlSettings),
});
export type PirSensor = z.infer<typeof PirSensor>;

export const ZeroToTenVoltDimmer = z.object({
  id: z.string(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  portId: z.string(),
  dimmingType: z.enum(["sinking", "sourcing"]),
  sortIndex: z.number(),
  showLabel: z.boolean().prefault(true),
  useRelay: z.boolean().prefault(false),
  relayConnectorId: z.string().optional(), // Links to a relay connector when useRelay is true
  minBrightness: z.number().prefault(0),
  maxBrightness: z.number().prefault(100),
  defaultDimmingSpeed: z.number().prefault(0.2), // seconds
});
export type ZeroToTenVoltDimmer = z.infer<typeof ZeroToTenVoltDimmer>;

export const RelayOutput = z.object({
  id: z.string(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  relayConnectorId: z.string(), // Which relay connector it's connected to
  sortIndex: z.number(),
  showLabel: z.boolean().prefault(true),
});
export type RelayOutput = z.infer<typeof RelayOutput>;

export const ZeroToTenVoltDimmerDevice = BaseDevice.extend({
  type: z.literal("zeroToTenVoltDimmer"),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  nodeId: z.string(),
  viaId: z.string(),
  dimmingType: z.enum(["sinking", "sourcing"]),
  useRelay: z.boolean().prefault(false),
  relayConnectorId: z.string().optional(),
  minBrightness: z.number().prefault(0),
  maxBrightness: z.number().prefault(100),
  defaultDimmingSpeed: z.number().prefault(0.2),
});
export type ZeroToTenVoltDimmerDevice = z.infer<
  typeof ZeroToTenVoltDimmerDevice
>;

export const RelayOutputDevice = BaseDevice.extend({
  type: z.literal("relayOutput"),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  nodeId: z.string(),
  viaId: z.string(),
});
export type RelayOutputDevice = z.infer<typeof RelayOutputDevice>;

export const MomentaryCanBusControllerViaId = z.enum(["onUpClick", "onUpHold"]);
export type MomentaryCanBusControllerViaId = z.infer<
  typeof MomentaryCanBusControllerViaId
>;

export const MomentaryCanBusController = BaseCanBusController.extend({
  type: z.literal("momentary"),
  portId: z.string().optional(), // Which 3-pin port it's connected to
  onUpClick: z.record(z.string(), DeviceControlSettings),
  onUpHold: z.record(z.string(), DeviceControlSettings),
});
export type MomentaryCanBusController = z.infer<
  typeof MomentaryCanBusController
>;

export function aMomentaryCanBusController(
  params: Partial<MomentaryCanBusController>,
): MomentaryCanBusController {
  return {
    id: `momentary-canbus-controller-${randomId()}`,
    type: "momentary",
    label: "Momentary CanBus Controller",
    sortIndex: 0,
    onUpClick: {},
    onUpHold: {},
    ...params,
  };
}

export const MomentaryCanBusControllerDevice = BaseDevice.extend({
  type: z.literal("momentaryCanBusController"),
  nodeId: z.string(),
  viaId: MomentaryCanBusControllerViaId,
});
export type MomentaryCanBusControllerDevice = z.infer<
  typeof MomentaryCanBusControllerDevice
>;

export const ToggleCanBusControllerViaId = z.enum([
  "onUpClick",
  "onUpHold",
  "onDownClick",
  "onDownHold",
]);
export type ToggleCanBusControllerViaId = z.infer<
  typeof ToggleCanBusControllerViaId
>;

export const ToggleCanBusController = BaseCanBusController.extend({
  type: z.literal("toggle"),
  portId: z.string().optional(), // Which 3-pin port it's connected to
  onUpClick: z.record(z.string(), DeviceControlSettings),
  onUpHold: z.record(z.string(), DeviceControlSettings),
  onDownClick: z.record(z.string(), DeviceControlSettings),
  onDownHold: z.record(z.string(), DeviceControlSettings),
});
export type ToggleCanBusController = z.infer<typeof ToggleCanBusController>;

export const ToggleCanBusControllerDevice = BaseDevice.extend({
  type: z.literal("toggleCanBusController"),
  nodeId: z.string(),
  viaId: ToggleCanBusControllerViaId,
});
export type ToggleCanBusControllerDevice = z.infer<
  typeof ToggleCanBusControllerDevice
>;

// PIR Sensor Device (similar to presence sensor device)
export const PirSensorDevice = BaseDevice.extend({
  type: z.literal("pirSensor"),
  nodeId: z.string(),
  viaId: PirSensorViaId,
});
export type PirSensorDevice = z.infer<typeof PirSensorDevice>;

export const CanBusController = z.union([
  MomentaryCanBusController,
  ToggleCanBusController,
]);
export type CanBusController = z.infer<typeof CanBusController>;

export function getConnectorId(controller: CanBusController) {
  return parseInt(controller.portId?.split("-").pop() || "0");
}

export const CanBusControllerContainerNodeData = z.object({
  title: z.string(),
  controllers: z.record(z.string(), CanBusController),
  pirSensors: z.record(z.string(), PirSensor).prefault({}),
  zeroToTenVoltDimmers: z.record(z.string(), ZeroToTenVoltDimmer).prefault({}),
  relayOutputs: z.record(z.string(), RelayOutput).prefault({}),
  threePinPorts: z.record(z.string(), ThreePinPort).prefault({}),
  twoPinPorts: z.record(z.string(), TwoPinPort).prefault({}),
  zeroToTenVoltPorts: z.record(z.string(), ZeroToTenVoltPort).prefault({}),
  relayConnectors: z.record(z.string(), RelayConnector).prefault({}),
});
export type CanBusControllerContainerNodeData = z.infer<
  typeof CanBusControllerContainerNodeData
>;

export const CanBusControllerAnchorNodeData = z.record(z.string(), z.unknown());
export type CanBusControllerAnchorNodeData = z.infer<
  typeof CanBusControllerAnchorNodeData
>;

// Helper functions for creating default ports
export function createDefaultThreePinPorts(): Record<string, ThreePinPort> {
  const ports: Record<string, ThreePinPort> = {};
  for (let i = 1; i <= 4; i++) {
    const id = `3pin-port-${i}`;
    ports[id] = {
      id,
      label: `3-Pin Port ${i}`,
      connected: false,
      deviceType: "none",
    };
  }
  return ports;
}

export function createDefaultTwoPinPorts(): Record<string, TwoPinPort> {
  const ports: Record<string, TwoPinPort> = {};
  for (let i = 1; i <= 2; i++) {
    const id = `2pin-port-${i}`;
    ports[id] = {
      id,
      label: `2-Pin Port ${i}`,
      connected: false,
      deviceType: "none",
    };
  }
  return ports;
}

export function createDefaultZeroToTenVoltPorts(): Record<
  string,
  ZeroToTenVoltPort
> {
  const ports: Record<string, ZeroToTenVoltPort> = {};
  const id = `0-10v-port-1`;
  ports[id] = {
    id,
    label: `0-10V`,
    connected: false,
    deviceType: "none",
  };
  return ports;
}

export function createDefaultRelayConnectors(): Record<string, RelayConnector> {
  const connectors: Record<string, RelayConnector> = {};
  for (let i = 1; i <= 4; i++) {
    const id = `relay-connector-${i}`;
    connectors[id] = {
      id,
      label: `Output ${i}`,
      connected: false,
      deviceType: "none",
    };
  }
  return connectors;
}
