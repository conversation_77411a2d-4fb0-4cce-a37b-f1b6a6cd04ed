import { z } from "zod/v4";
import { randomId } from "../../lib/randomId";

export const ShadesControlSettings = z.object({
  id: z.string(),
  deviceId: z.string(),
  sortIndex: z.number(),
  onValue: z.number(),
  offValue: z.number(),
  raceTime: z.number().describe("Time to open/cloase the shades, in seconds"),
  targetValue: z.number(),
});
export type ShadesControlSettings = z.infer<typeof ShadesControlSettings>;

export function aShadesControlSettings(
  params: Partial<ShadesControlSettings>,
): ShadesControlSettings {
  return {
    id: `device-control-settings-${randomId()}`,
    deviceId: `device-id-${randomId()}`,
    raceTime: 0,
    sortIndex: 0,
    targetValue: 0,
    onValue: 0,
    offValue: 0,
    ...params,
  };
}
