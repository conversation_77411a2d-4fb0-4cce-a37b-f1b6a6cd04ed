import { z } from "zod/v4";
import { DeviceIconKey } from "../DeviceIcons";
import { BaseDevice } from "./BaseDevice";
import { ShadesControlSettings } from "./ShadesControlSettings";

export const somoShadesViaIds = ["via", "viaUp", "viaDown"] as const;
export const SomoShadesViaId = z.enum(somoShadesViaIds);
export type SomoShadesViaId = z.infer<typeof SomoShadesViaId>;

export const SomoShadesVia = z.object({
  name: z.string(),
  icon: DeviceIconKey.optional(),
  loadName: z.string(),
  loadIcon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), ShadesControlSettings),
});
export type SomoShadesVia = z.infer<typeof SomoShadesVia>;

export function aSomoShadesVia(params: Partial<SomoShadesVia>): SomoShadesVia {
  return {
    name: "Somo Shades Via",
    showLabel: false,
    loadName: "Shades",
    icon: undefined,
    loadIcon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const SomoShadesButton = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), ShadesControlSettings),
});
export type SomoShadesButton = z.infer<typeof SomoShadesButton>;

export function aSomoShadesButton(
  params: Partial<SomoShadesButton>,
): SomoShadesButton {
  return {
    name: "Somo Shades Button",
    enabled: false,
    showLabel: false,
    icon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const SomoShadesContainerNodeData = z.object({
  title: z.string(),
  via: SomoShadesVia,
  viaUp: SomoShadesButton,
  viaDown: SomoShadesButton,
  raceTime: z.number().describe("Default time to fully open/close in seconds"),
});
export type SomoShadesContainerNodeData = z.infer<
  typeof SomoShadesContainerNodeData
>;

export const SomoShadesAnchorNodeData = z.record(z.string(), z.unknown());
export type SomoShadesAnchorNodeData = z.infer<typeof SomoShadesAnchorNodeData>;

export const SomoShadesDevice = BaseDevice.extend({
  type: z.literal("somoShades"),
  nodeId: z.string(),
  viaId: SomoShadesViaId,
  defaultRacetime: z.number().optional(),
});
export type SomoShadesDevice = z.infer<typeof SomoShadesDevice>;
