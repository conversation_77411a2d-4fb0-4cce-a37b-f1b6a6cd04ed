import { z } from "zod/v4";
import { DeviceIconKey } from "../DeviceIcons";
import { BaseDevice } from "./BaseDevice";
import { DeviceControlSettings } from "./DeviceControlSettings";
import { ThermostatControlSettings } from "./ThermostatControlSettings";

export const UnifiedActionSettings = z.union([
  DeviceControlSettings,
  ThermostatControlSettings,
]);
export type UnifiedActionSettings = z.infer<typeof UnifiedActionSettings>;

export const doorSensorViaIds = ["onOpen", "onClose"] as const;
export const DoorSensorViaId = z.enum(doorSensorViaIds);
export type DoorSensorViaId = z.infer<typeof DoorSensorViaId>;

export const DoorSensorVia = z.object({
  name: z.string(),
  icon: DeviceIconKey.optional(),
  lightIcon: DeviceIconKey.optional(),
  lightName: z.string(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), UnifiedActionSettings),
});
export type DoorSensorVia = z.infer<typeof DoorSensorVia>;

export const DoorSensorAction = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  offDelay: z.number(),
  cancelOnActivityDuringDelay: z.boolean().prefault(false),
  onUpClick: z.record(z.string(), UnifiedActionSettings),
});
export type DoorSensorAction = z.infer<typeof DoorSensorAction>;

export function aDoorSensorAction(
  params: Partial<DoorSensorAction>,
): DoorSensorAction {
  return {
    enabled: false,
    showLabel: false,
    name: "Door Sensor Action",
    icon: undefined,
    offDelay: 0,
    cancelOnActivityDuringDelay: false,
    onUpClick: {},
    ...params,
  };
}

export const DoorSensorContainerNodeData = z.object({
  title: z.string(),
  onOpen: DoorSensorAction,
  onClose: DoorSensorAction,
});
export type DoorSensorContainerNodeData = z.infer<
  typeof DoorSensorContainerNodeData
>;

export const DoorSensorAnchorNodeData = z.record(z.string(), z.unknown());
export type DoorSensorAnchorNodeData = z.infer<typeof DoorSensorAnchorNodeData>;

export const DoorSensorDevice = BaseDevice.extend({
  type: z.literal("doorSensor"),
  nodeId: z.string(),
  viaId: DoorSensorViaId,
});
export type DoorSensorDevice = z.infer<typeof DoorSensorDevice>;
