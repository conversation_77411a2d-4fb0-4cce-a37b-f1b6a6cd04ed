import { z } from "zod/v4";
import { DeviceIconKey } from "../DeviceIcons";
import { BaseDevice } from "./BaseDevice";
import { FanControlSettings } from "./FanControlSettings";

export const somoFanViaIds = ["via", "viaLow", "viaMed", "viaHigh"] as const;
export const SomoFanViaId = z.enum(somoFanViaIds);
export type SomoFanViaId = z.infer<typeof SomoFanViaId>;

export const SomoFanVia = z.object({
  name: z.string(),
  icon: DeviceIconKey.optional(),
  loadName: z.string(),
  loadIcon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), FanControlSettings),
});
export type SomoFanVia = z.infer<typeof SomoFanVia>;

export function aSomoFanVia(params: Partial<SomoFanVia>): SomoFanVia {
  return {
    name: "Somo Fan Via",
    showLabel: false,
    loadName: "Fan",
    icon: undefined,
    loadIcon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const SomoFanButton = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), FanControlSettings),
});
export type SomoFanButton = z.infer<typeof SomoFanButton>;

export function aSomoFanButton(params: Partial<SomoFanButton>): SomoFanButton {
  return {
    name: "Somo Fan Button",
    enabled: true,
    showLabel: false,
    icon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const SomoFanContainerNodeData = z.object({
  title: z.string(),
  fanType: z.enum(["onoff", "3speed"]).describe("On/Off or 3-Speed"),
  via: SomoFanVia,
  viaLow: SomoFanButton,
  viaMed: SomoFanButton,
  viaHigh: SomoFanButton,
});
export type SomoFanContainerNodeData = z.infer<typeof SomoFanContainerNodeData>;

export const SomoFanAnchorNodeData = z.record(z.string(), z.unknown());
export type SomoFanAnchorNodeData = z.infer<typeof SomoFanAnchorNodeData>;

export const SomoFanDevice = BaseDevice.extend({
  type: z.literal("somoFan"),
  nodeId: z.string(),
  viaId: SomoFanViaId,
  defaultFanType: z.enum(["onoff", "3speed"]),
});
export type SomoFanDevice = z.infer<typeof SomoFanDevice>;
