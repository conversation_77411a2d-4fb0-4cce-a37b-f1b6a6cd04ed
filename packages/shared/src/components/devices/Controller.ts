import { z } from "zod/v4";
import { randomId } from "../../lib/randomId";
import { BaseDevice } from "./BaseDevice";
import { DeviceControlSettings } from "./DeviceControlSettings";

const BaseController = z.object({
  id: z.string(),
  label: z.string(),
  sortIndex: z.number(),
});

export const MomentaryControllerViaId = z.enum(["onUpClick", "onUpHold"]);
export type MomentaryControllerViaId = z.infer<typeof MomentaryControllerViaId>;

export const MomentaryController = BaseController.extend({
  type: z.literal("momentary"),
  channel: z.string(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
  onUpHold: z.record(z.string(), DeviceControlSettings),
});
export type MomentaryController = z.infer<typeof MomentaryController>;

export function aMomentaryController(
  params: Partial<MomentaryController>,
): MomentaryController {
  return {
    id: `momentary-controller-${randomId()}`,
    type: "momentary",
    label: "Momentary Controller",
    channel: `momentary-channel-${randomId()}`,
    sortIndex: 0,
    onUpClick: {},
    onUpHold: {},
    ...params,
  };
}

export const MomentaryControllerDevice = BaseDevice.extend({
  type: z.literal("momentaryController"),
  nodeId: z.string(),
  viaId: MomentaryControllerViaId,
});
export type MomentaryControllerDevice = z.infer<
  typeof MomentaryControllerDevice
>;

export const ToggleControllerViaId = z.enum([
  "onUpClick",
  "onUpHold",
  "onDownClick",
  "onDownHold",
]);
export type ToggleControllerViaId = z.infer<typeof ToggleControllerViaId>;

export const ToggleController = BaseController.extend({
  type: z.literal("toggle"),
  upChannel: z.string(),
  downChannel: z.string(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
  onUpHold: z.record(z.string(), DeviceControlSettings),
  onDownClick: z.record(z.string(), DeviceControlSettings),
  onDownHold: z.record(z.string(), DeviceControlSettings),
});
export type ToggleController = z.infer<typeof ToggleController>;

export const ToggleControllerDevice = BaseDevice.extend({
  type: z.literal("toggleController"),
  nodeId: z.string(),
  viaId: ToggleControllerViaId,
});
export type ToggleControllerDevice = z.infer<typeof ToggleControllerDevice>;

export const Controller = z.union([MomentaryController, ToggleController]);
export type Controller = z.infer<typeof Controller>;

export const ControllerContainerNodeData = z.object({
  title: z.string(),
  controllers: z.record(z.string(), Controller),
});
export type ControllerContainerNodeData = z.infer<
  typeof ControllerContainerNodeData
>;

export const ControllerAnchorNodeData = z.record(z.string(), z.unknown());
export type ControllerAnchorNodeData = z.infer<typeof ControllerAnchorNodeData>;
