import { z } from "zod/v4";
import { randomId } from "../../lib/randomId";

export const FanControlSettings = z.object({
  id: z.string(),
  deviceId: z.string(),
  sortIndex: z.number(),
  onValue: z.number(),
  offValue: z.number(),
  targetValue: z.number(),
});
export type FanControlSettings = z.infer<typeof FanControlSettings>;

export function aFanControlSettings(
  params: Partial<FanControlSettings>,
): FanControlSettings {
  return {
    id: `device-control-settings-${randomId()}`,
    deviceId: `device-id-${randomId()}`,
    sortIndex: 0,
    targetValue: 0,
    onValue: 0,
    offValue: 0,
    ...params,
  };
}
