# fly.toml app configuration file generated for somo-admin on 2025-01-15T12:22:22-08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'somo-admin'
primary_region = 'sjc'

# Used to build the app locally
# On GitHub, it will use the built Dockerfile image
[build]
  dockerfile = 'Dockerfile'

[build.args]
  VITE_ENVIRONMENT = 'production'
  VITE_API_URL = 'https://somo-server.fly.dev'
  VITE_CLERK_PUBLISHABLE_KEY = 'pk_live_Y2xlcmsuc29tby5haSQ'
  VITE_WS_URL = 'wss://somo-server.fly.dev'
  VITE_LAUNCH_DARKLY_CLIENT_SIDE_ID = '6863078aa2eb110946e2cbad'
  VITE_SENTRY_DSN = ''
  VITE_SENTRY_AUTH_TOKEN = ''

[http_service]
  internal_port = 5173
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
