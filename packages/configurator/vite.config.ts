/// <reference types='vitest' />
import { sentryVitePlugin } from "@sentry/vite-plugin";
import { TanStackRouterVite } from "@tanstack/router-plugin/vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { defineConfig } from "vite";

// https://vite.dev/config/
export default defineConfig({
  root: __dirname,
  cacheDir: "../../node_modules/.vite/packages/configurator",
  plugins: [
    TanStackRouterVite(),
    react(),
    sentryVitePlugin({
      org: "somo-ai",
      project: "configurator",
      authToken: process.env.VITE_SENTRY_AUTH_TOKEN,
      sourcemaps: {
        filesToDeleteAfterUpload: ["*.js.map"],
      },
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    // Enabled for Sentry to map errors to source code
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes("@xyflow/react")) {
            return "xyflow";
          }
          if (id.includes("@clerk/clerk-react")) {
            return "clerk";
          }
          if (id.includes("@radix-ui")) {
            return "radix-ui";
          }
          if (id.includes("node_modules")) {
            return "vendor";
          }
          if (id.includes("src/components/ui")) {
            return "components-ui";
          }
        },
      },
    },
  },
  // @ts-expect-error - Not sure why it complains, but we need to run tests
  test: {
    watch: false,
    globals: true,
    environment: "node",
    include: ["{src,tests}/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"],
    reporters: ["default"],
    coverage: {
      reportsDirectory: "./test-output/vitest/coverage",
      provider: "v8" as const,
    },
  },
});
