// TypeScript bindings for emscripten-generated code.  Automatically generated at compile time.
interface WasmModule {
}

type EmbindString = ArrayBuffer|Uint8Array|Uint8ClampedArray|Int8Array|string;
interface EmbindModule {
  setDebugConfiguration(): void;
  prettyPrintActiveConfiguration(): void;
  processAllLights(): void;
  setActiveConfigurationFromProtobuf(_0: EmbindString): boolean;
  handleCommand(_0: EmbindString, _1: number): void;
  getActiveConfiguration(): any;
  getDmxUniverse(): any;
}

export type MainModule = WasmModule & EmbindModule;
export default function MainModuleFactory (options?: unknown): Promise<MainModule>;
