import { Graph } from "@/components/graph/Graph";
import { WebSocketClient } from "@/lib/WebsocketClient";
import { createContext, useContext, useEffect, useRef, useState } from "react";
import { z } from "zod/v4";

const DEBUG_CONTROLLERS = false;

export const Controller = z.object({
  ip: z.string().optional().nullable(),
  qrCode: z.string(),
  name: z.string().optional().nullable(),
  orgId: z.string().nullable().optional(),
  roomId: z.string().nullable().optional(),
  currentVersion: z.string().nullable().optional(),
  nextVersion: z.string().nullable().optional(),
  lastSeen: z.preprocess(
    (val) =>
      typeof val == "string"
        ? new Date(val)
        : typeof val == "number"
          ? new Date(val)
          : val,
    z.date(),
  ),
  configuration: z
    .object({
      rf: z
        .object({
          channel: z.number().prefault(15),
          network: z.number().prefault(15),
        })
        .optional()
        .prefault({
          channel: 15,
          network: 15,
        }),
      wifi: z
        .object({
          ssid: z.string().prefault(""),
          password: z.string().prefault(""),
        })
        .optional()
        .prefault({
          ssid: "",
          password: "",
        }),
      somoSwitchIdToQRCodeMapping: z
        .array(
          z.object({
            id: z.string(),
            qrCode: z.string(),
          }),
        )
        .prefault([]),
      somoDimmerIdToQRCodeMapping: z
        .array(
          z.object({
            id: z.string(),
            qrCode: z.string(),
          }),
        )
        .prefault([]),
      wiredSwitchIdToQRCodeMapping: z
        .array(
          z.object({
            id: z.string(),
            qrCode: z.string(),
          }),
        )
        .prefault([]),
      canBusControllerIdToQRCodeMapping: z
        .array(
          z.object({
            id: z.string(),
            qrCode: z.string(),
          }),
        )
        .prefault([]),
      doorSensorIdToQRCodeMapping: z
        .array(
          z.object({
            id: z.string(),
            qrCode: z.string(),
          }),
        )
        .prefault([]),
    })
    .nullable()
    .optional(),
});
export type Controller = z.infer<typeof Controller>;

const DEBUG_CONTROLLER: Controller = {
  ip: "*************",
  qrCode: "00:00:00:00:00:00",
  name: "Debug Controller",
  orgId: "org_2rUqGL3pWBMpQVZlbZSrLmhGKrE",
  roomId: "cm73qg4ha0000mcbbnoysl2co",
  currentVersion: "000000",
  lastSeen: new Date(),
  configuration: {
    rf: {
      channel: 20,
      network: 151515,
    },
    wifi: {
      ssid: "ssid",
      password: "password",
    },
    somoSwitchIdToQRCodeMapping: [
      {
        id: "f1viw",
        qrCode: "1234567890",
      },
    ],
    somoDimmerIdToQRCodeMapping: [],
    wiredSwitchIdToQRCodeMapping: [],
    canBusControllerIdToQRCodeMapping: [],
    doorSensorIdToQRCodeMapping: [],
  },
};

interface ControllerContextType {
  controllers: Controller[];
  sendMessage: (message: object) => void;
  updateController: (
    qrCode: string,
    version: string,
    graph: Graph,
    configuration: Controller["configuration"],
    roomId: string,
  ) => void;
}
const ControllerContext = createContext<ControllerContextType | null>(null);

export function ControllerProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [controllers, setControllers] = useState<Controller[]>([]);
  const currentWs = useRef<WebSocketClient | null>(null);

  useEffect(() => {
    const ws = new WebSocketClient(
      `${import.meta.env.VITE_WS_URL}/ws/controller`,
    );
    currentWs.current = ws;

    const onMessage = (message: string) => {
      const data = JSON.parse(message);
      switch (data.action) {
        case "controllers": {
          const res = z.array(Controller).safeParse(data.controllers);
          if (!res.success) {
            console.error("Error parsing controllers", res.error);
          }
          const controllers = res.data ?? [];
          if (DEBUG_CONTROLLERS) {
            controllers.push(DEBUG_CONTROLLER);
          }
          setControllers(controllers);
          break;
        }
        default:
          break;
      }
    };
    ws.addListener("message", onMessage);

    return () => {
      currentWs.current = null;
      ws.removeListener("message", onMessage);
      ws.close();
    };
  }, []);

  const sendMessage = (message: object) => {
    if (!currentWs.current) {
      return;
    }
    currentWs.current.send(JSON.stringify(message));
  };

  const updateController = (
    qrCode: string,
    version: string,
    graph: Graph,
    configuration: Controller["configuration"],
    roomId: string,
  ) => {
    const controller = controllers.find((c) => c.qrCode === qrCode);
    if (!controller) {
      return;
    }

    setControllers((controllers) =>
      controllers.map((c) =>
        c.qrCode === qrCode ? { ...c, nextVersion: version } : c,
      ),
    );
    sendMessage({
      action: "update",
      qrCode,
      version: version.slice(-8),
      nodes: graph.nodes,
      edges: graph.edges,
      configuration,
      name: qrCode,
      roomId,
    });
  };

  return (
    <ControllerContext.Provider
      value={{
        controllers,
        sendMessage,
        updateController,
      }}
    >
      {children}
    </ControllerContext.Provider>
  );
}

export function useControllers() {
  const context = useContext(ControllerContext);
  if (!context) {
    throw new Error("useControllers must be used within a ControllerProvider");
  }
  return context;
}
