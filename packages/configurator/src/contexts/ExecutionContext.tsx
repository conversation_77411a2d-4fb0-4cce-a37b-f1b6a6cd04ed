import {
  DoorSensorViaId,
  MomentaryControllerViaId,
  OutletDimmerViaId,
  PresenceSensorViaId,
  ServicePadAction,
  ServicePadViaId,
  SomoDimmerViaId,
  SomoFanViaId,
  SomoIrControllerViaId,
  SomoShadesViaId,
  SomoSwitchViaId,
  SomoThermostatViaId,
  ToggleControllerViaId,
} from "@somo/shared";
import { createContext, useContext } from "react";
import { z } from "zod/v4";

export type ExecutionContext = {
  status:
    | { value: "online" }
    | { value: "offline" }
    | { value: "error"; error: Error };

  // Virtual button
  sendVirtualButtonCommand: (buttonId: string) => void;

  // Light
  isLightOn: (id: string) => boolean;
  getBrightness: (id: string) => number;

  // Controller
  isControllerOn: (containerId: string, controllerId: string) => boolean;
  sendMomentaryCommand: (
    containerId: string,
    controllerId: string,
    viaId: MomentaryControllerViaId,
  ) => void;
  sendToggleCommand: (
    containerId: string,
    controllerId: string,
    viaId: ToggleControllerViaId,
  ) => void;

  // Switch
  isSwitchOn: (switchId: string, viaId: SomoSwitchViaId) => boolean;
  sendSwitchCommand: (switchId: string, viaId: SomoSwitchViaId) => void;

  // Dimmer
  isDimmerOn: (dimmerId: string, viaId: SomoDimmerViaId) => boolean;
  sendDimmerCommand: (dimmerId: string, viaId: SomoDimmerViaId) => void;

  // Outlet Dimmer
  isOutletDimmerOn: (dimmerId: string, viaId: OutletDimmerViaId) => boolean;
  sendOutletDimmerCommand: (dimmerId: string, viaId: OutletDimmerViaId) => void;

  // Shades
  isShadesOpen: (shadesId: string, viaId: SomoShadesViaId) => boolean;
  getShadesProgress: (shadesId: string, viaId: SomoShadesViaId) => number;
  sendShadesCommand: (shadesId: string, viaId: SomoShadesViaId) => void;

  // Fan
  isFanOn: (id: string, viaId: SomoFanViaId) => boolean;
  getFanLevel: (fanId: string, viaId: SomoFanViaId) => number;
  sendFanCommand: (fanId: string, viaId: SomoFanViaId) => void;

  // Thermostat
  // TODO: refactor to expose methods instead of mutable state
  thermostatStates: {
    [deviceId: string]: ThermostatState;
  };
  getThermostatSetpoint: (
    thermostatId: string,
    viaId: SomoThermostatViaId,
  ) => ThermostatState["setpoint"];
  getThermostatMode: (
    thermostatId: string,
    viaId: SomoThermostatViaId,
  ) => ThermostatState["mode"];
  getThermostatFanSpeed: (
    thermostatId: string,
    viaId: SomoThermostatViaId,
  ) => ThermostatState["fanSpeed"];
  sendThermostatCommand: (
    command: "setpoint" | "mode" | "fanSpeed",
    thermostatId: string,
    viaId: SomoThermostatViaId,
    min: number,
    max: number,
    stepSize: number,
  ) => void;

  // Presence sensor
  sendPresenceSensorCommand: (
    sensorId: string,
    viaId: PresenceSensorViaId,
  ) => void;

  // Door Sensor
  sendDoorSensorCommand: (sensorId: string, viaId: DoorSensorViaId) => void;

  // PIR Sensor
  isPirSensorActive: (containerId: string, sensorId: string) => boolean;
  triggerPirSensor: (containerId: string, sensorId: string) => void;
  activatePirSensor: (containerId: string, sensorId: string) => void;
  deactivatePirSensor: (containerId: string, sensorId: string) => void;

  // Service Pad
  // TODO: refactor to expose methods instead of mutable state
  servicePadStatus:
    | { value: "doNotDisturb" }
    | { value: "makeUpRoom" }
    | { value: "none" };
  sendServicePadCommand: (
    sensorId: string,
    viaId: ServicePadViaId,
    action: ServicePadAction,
  ) => void;

  // IR Controller
  // TODO: refactor to expose methods instead of mutable state
  irControllerStates: {
    [deviceId: string]: IrControllerState;
  };
  getIrControllerSetpoint: (
    irControllerId: string,
    viaId: SomoIrControllerViaId,
  ) => IrControllerState["setpoint"];
  getIrControllerMode: (
    irControllerId: string,
    viaId: SomoIrControllerViaId,
  ) => IrControllerState["mode"];
  getIrControllerFanSpeed: (
    irControllerId: string,
    viaId: SomoIrControllerViaId,
  ) => IrControllerState["fanSpeed"];
  sendIrControllerCommand: (
    command: "setpoint" | "mode" | "fanSpeed",
    irControllerId: string,
    viaId: SomoIrControllerViaId,
    min: number,
    max: number,
    stepSize: number,
  ) => void;
};
const ExecutionContext = createContext<ExecutionContext | undefined>(undefined);

export function ExecutionContextProvider({
  children,
  value,
}: {
  children: React.ReactNode;
  value: ExecutionContext;
}) {
  return (
    <ExecutionContext.Provider value={value}>
      {children}
    </ExecutionContext.Provider>
  );
}

export function useExecutionContext() {
  const context = useContext(ExecutionContext);
  if (!context) {
    throw new Error(
      "useExecutionContext must be used within an ExecutionContextProvider",
    );
  }
  return context;
}

const allowedHvacModes = ["heat", "cool", "fan", "auto"] as const;
const allowedFanSpeeds = ["low", "medium", "high", "auto"] as const;

const ThermostatState = z.object({
  id: z.string(),
  setpoint: z.number(),
  on: z.boolean(),
  mode: z.enum(allowedHvacModes),
  fanSpeed: z.enum(allowedFanSpeeds),
});
export type ThermostatState = z.infer<typeof ThermostatState>;

const IrControllerState = z.object({
  id: z.string(),
  setpoint: z.number(),
  on: z.boolean(),
  mode: z.enum(allowedHvacModes),
  fanSpeed: z.enum(allowedFanSpeeds),
});
export type IrControllerState = z.infer<typeof IrControllerState>;
