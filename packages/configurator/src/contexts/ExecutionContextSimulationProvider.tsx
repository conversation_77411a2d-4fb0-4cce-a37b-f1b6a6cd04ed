import { Graph } from "@/components/graph/Graph";
import { LogEntry } from "@/components/graph/simulator/LogEntry";
import { Room } from "@/components/graph/useRoom";
import { useInterval } from "@/hooks/useInterval";
import { MainModule } from "@/types/basestation";
import {
  ActiveConfiguration,
  Commands,
  filterNodes,
  getConnectorId,
  GraphNode,
  serializeNodesForBasestation,
} from "@somo/shared";
import { useEffect, useMemo, useState } from "react";
import { match } from "ts-pattern";
import { MessageType } from "./BasestationEvents";
import { ExecutionContext } from "./ExecutionContext";

export function useSimulationExecutionContextProvider({
  basestation,
  room,
  graph,
  captureLog,
}: {
  basestation: MainModule;
  room: Room;
  graph: Graph;
  captureLog: (entry: LogEntry) => void;
}): ExecutionContext & { lastModificationTime: number } {
  const { nodes } = graph;

  const { data, lightsIds } = useMemo(
    () =>
      serializeNodesForBasestation({
        nodes,
        name: room?.name ?? "Room name",
        roomId: room?.id ?? "room-id",
        version: "000000",
        nodeQrMappings: nodes
          .filter((n) => n.type?.endsWith("Container"))
          .map((n) => ({ deviceId: n.id, qrCode: n.id })),
      }),
    [room?.id, nodes.length],
  );

  useEffect(() => {
    const success = basestation.setActiveConfigurationFromProtobuf(data);
    if (success) {
      console.log("✅ Successfully updated basestation configuration");
      basestation.prettyPrintActiveConfiguration();
    } else {
      console.error("Failed to update basestation configuration");
    }
  }, [data]);

  const [basestationState, setBasestationState] = useState({
    state: "",
    lastModifiedTime: Date.now(),
  });

  // Simulate the basestation's main loop
  // Update the last modification time to trigger a re-render if necessary
  const refreshFrequencyInHz = 20;
  useInterval(() => {
    basestation.processAllLights();
    const config = getActiveConfig(basestation);
    const newState = JSON.stringify(config);
    if (basestationState.state !== newState) {
      setBasestationState({ state: newState, lastModifiedTime: Date.now() });
    }
  }, 1000 / refreshFrequencyInHz);

  const isLightOn: ExecutionContext["isLightOn"] = (id) => {
    return getBrightness(id) > 0;
  };

  const getBrightness: ExecutionContext["getBrightness"] = (id) => {
    const lightId = lightsIds.find((l) => l.deviceId === id)?.basestationId;
    const activeConfig = getActiveConfig(basestation);
    const state = activeConfig.state?.lights.find((l) => l.id === lightId);
    if (!state) {
      console.warn("Can't find light state", { id, lightId, activeConfig });
      return 0;
    }

    return state.brightness;
  };

  const sendMomentaryCommand: ExecutionContext["sendMomentaryCommand"] = (
    containerId,
    controllerId,
    viaId,
  ) => {
    captureLog({
      timestamp: Date.now(),
      device: { type: "momentaryController", id: containerId },
      action: match(viaId)
        .with("onUpHold", () => "mouseDown" as const)
        .with("onUpClick", () => "mouseUp" as const)
        .exhaustive(),
      target: viaId,
    });

    const connectorId = findCanbusControllerConnectorId(
      nodes,
      containerId,
      controllerId,
    );
    if (!connectorId) {
      console.warn("Can't find connector ID to message the basestation");
      return;
    }

    const message = Commands.MomentaryButtonCommand.encode({
      connectorId,
      state: match(viaId)
        .with("onUpHold", () => Commands.MomentaryButtonCommand_State.Pressed)
        .with("onUpClick", () => Commands.MomentaryButtonCommand_State.Released)
        .exhaustive(),
    }).finish();

    sendCommandToDevices(
      basestation,
      containerId,
      MessageType.MESSAGE_MOMENTARY_BUTTON,
      message,
    );
  };

  const sendToggleCommand: ExecutionContext["sendToggleCommand"] = (
    containerId,
    controllerId,
    viaId,
  ) => {
    captureLog({
      timestamp: Date.now(),
      device: { type: "toggleController", id: containerId },
      action: match(viaId)
        .with("onUpHold", "onDownHold", () => "mouseDown" as const)
        .with("onUpClick", "onDownClick", () => "mouseUp" as const)
        .exhaustive(),
      target: viaId,
    });

    const connectorId = findCanbusControllerConnectorId(
      nodes,
      containerId,
      controllerId,
    );
    if (!connectorId) {
      console.warn("Can't find connector ID to message the basestation");
      return;
    }

    const message = Commands.ToggleButtonCommand.encode({
      connectorId,
      state: match(viaId)
        .with("onUpHold", () => Commands.ToggleButtonCommand_State.Up)
        .with("onDownHold", () => Commands.ToggleButtonCommand_State.Down)
        .with(
          "onUpClick",
          "onDownClick",
          () => Commands.ToggleButtonCommand_State.Released,
        )
        .exhaustive(),
    }).finish();

    sendCommandToDevices(
      basestation,
      containerId,
      MessageType.MESSAGE_TOGGLE_BUTTON,
      message,
    );
  };

  return {
    status: { value: "offline" },
    lastModificationTime: basestationState.lastModifiedTime,

    sendVirtualButtonCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "virtualButton", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    isLightOn,
    getBrightness,

    isControllerOn: () => false,
    sendMomentaryCommand,
    sendToggleCommand,

    isSwitchOn: () => false,
    sendSwitchCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "somoSwitch", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    isDimmerOn: () => false,
    sendDimmerCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "somoDimmer", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    isOutletDimmerOn: () => false,
    sendOutletDimmerCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "outletDimmer", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    sendPresenceSensorCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "presenceSensor", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    sendDoorSensorCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "doorSensor", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    isPirSensorActive: () => false,
    triggerPirSensor: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "pirSensor", id: "stub" },
        action: "emit",
        event: "PIR_ACTIVATED",
      });
    },
    activatePirSensor: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "pirSensor", id: "stub" },
        action: "emit",
        event: "PIR_ACTIVATED",
      });
    },
    deactivatePirSensor: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "pirSensor", id: "stub" },
        action: "emit",
        event: "PIR_DEACTIVATED",
      });
    },

    isShadesOpen: () => false,
    getShadesProgress: () => 0,
    sendShadesCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "somoShades", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    isFanOn: () => false,
    getFanLevel: () => 0,
    sendFanCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "somoFan", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    servicePadStatus: { value: "none" },
    sendServicePadCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "servicePad", id: "stub" },
        action: "servicePadStatus",
        status: "none",
      });
    },

    thermostatStates: {},
    getThermostatSetpoint: () => 0,
    getThermostatMode: () => "auto",
    getThermostatFanSpeed: () => "auto",
    sendThermostatCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "somoThermostat", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    irControllerStates: {},
    getIrControllerSetpoint: () => 0,
    getIrControllerMode: () => "auto",
    getIrControllerFanSpeed: () => "auto",
    sendIrControllerCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "somoIrController", id: "stub" },
        action: "click",
        target: "via",
      });
    },
  };
}

function findCanbusControllerConnectorId(
  nodes: GraphNode[],
  containerId: string,
  controllerId: string,
) {
  const container = filterNodes(nodes, "canbusControllerContainer").find(
    (n) => n.id === containerId,
  );
  if (!container) {
    console.warn("Can't find canbus controller container");
    return;
  }

  const controller = Object.values(container.data.controllers).find(
    (controller) => controller.id === controllerId,
  );
  if (!controller) {
    console.warn("Can't find controller");
    return;
  }

  return getConnectorId(controller);
}

function sendCommandToDevices(
  basestation: MainModule,
  containerId: string,
  messageType: number,
  message: Uint8Array,
) {
  const activeConfig = getActiveConfig(basestation);
  const nodes =
    activeConfig.config?.nodeQrMappings.filter(
      (i) => i.qrCode === containerId,
    ) ?? [];

  const command = encode([messageType, ...message]);
  nodes.forEach(({ nodeId }) => {
    basestation.handleCommand(command, nodeId);
  });
}

function encode(buffer: number[]) {
  const bytes = new Uint8Array(buffer);
  return Array.from(bytes)
    .map((b) => String.fromCharCode(b))
    .join("");
}

function getActiveConfig(basestation: MainModule) {
  const rawConfig = basestation.getActiveConfiguration();
  if (!rawConfig) {
    throw new Error("Can't get active configuration from basestation");
  }

  return ActiveConfiguration.decode(rawConfig);
}
