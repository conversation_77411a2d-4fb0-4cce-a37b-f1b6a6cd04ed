import { MainModule } from "@/types/basestation";
import { MessageType } from "@/contexts/BasestationEvents";
import { GraphNode } from "@somo/shared";
import { describe, it, expect, beforeEach, vi } from "vitest";
import { useSimulationExecutionContextProvider } from "./ExecutionContextSimulationProvider";
import { renderHook, act } from "@testing-library/react";

// Mock the WASM module creation
const mockBasestation: MainModule = {
  setActiveConfigurationFromProtobuf: vi.fn().mockReturnValue(true),
  getActiveConfiguration: vi.fn(),
  processAllLights: vi.fn(),
  handleCommand: vi.fn(),
  prettyPrintActiveConfiguration: vi.fn(),
  setDebugConfiguration: vi.fn(),
  getDmxUniverse: vi.fn(),
};

// Mock the global createBasestationModule function
global.window = {
  createBasestationModule: vi.fn().mockResolvedValue(mockBasestation),
} as any;

// Mock the useInterval hook
vi.mock("@/hooks/useInterval", () => ({
  useInterval: vi.fn((callback: () => void, delay: number) => {
    // For testing, we'll manually control when the interval callback is called
    (global as any).__intervalCallback = callback;
  }),
}));

describe("ExecutionContextSimulationProvider", () => {
  const mockRoom = {
    id: "test-room-id",
    name: "Test Room",
  };

  const mockCaptureLog = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset the mock basestation configuration
    mockBasestation.getActiveConfiguration.mockReturnValue(
      new Uint8Array([
        // Mock protobuf data representing initial state
        10, 20, // config field
        8, 1, // id: 1
        18, 6, 48, 48, 48, 48, 48, 48, // version: "000000"
        26, 0, // lights: []
        18, 10, // state field
        10, 0, // lights: []
        18, 0, // buttons: []
      ])
    );
  });

  describe("Basic Setup", () => {
    it("should initialize with empty graph", () => {
      const graph = { nodes: [], edges: [] };

      const { result } = renderHook(() =>
        useSimulationExecutionContextProvider({
          basestation: mockBasestation,
          room: mockRoom,
          graph,
          captureLog: mockCaptureLog,
        })
      );

      expect(result.current.status).toEqual({ value: "offline" });
      expect(mockBasestation.setActiveConfigurationFromProtobuf).toHaveBeenCalled();
    });

    it("should provision basestation with basic graph configuration", () => {
      // Create a basic graph with a canbus controller and light
      const nodes: GraphNode[] = [
        {
          id: "canbus-container-1",
          type: "canbusControllerContainer",
          position: { x: 0, y: 0 },
          data: {
            title: "CanBus Controller",
            controllers: {
              "toggle-controller-1": {
                id: "toggle-controller-1",
                label: "Toggle Button",
                sortIndex: 0,
                type: "toggle",
                portId: "3pin-port-1",
                onUpClick: {
                  "light-action-1": {
                    id: "light-action-1",
                    sortIndex: 0,
                    deviceId: "light-1",
                    dimSpeed: 0,
                    targetValue: 100,
                    onValue: 100,
                    offValue: 0,
                    type: "lighting",
                  },
                },
                onUpHold: {},
                onDownClick: {
                  "light-action-2": {
                    id: "light-action-2",
                    sortIndex: 0,
                    deviceId: "light-1",
                    dimSpeed: 0,
                    targetValue: 0,
                    onValue: 100,
                    offValue: 0,
                    type: "lighting",
                  },
                },
                onDownHold: {},
              },
            },
            relayConnectors: {},
            relayOutputs: {},
            zeroToTenVoltDimmers: {},
          },
        },
        {
          id: "section-1",
          type: "section",
          position: { x: 200, y: 0 },
          data: {
            title: "Test Section",
            devices: {
              "light-1": {
                id: "light-1",
                type: "dmx",
                channel: 1,
                universe: 1,
              },
            },
          },
        },
      ];

      const graph = { nodes, edges: [] };

      const { result } = renderHook(() =>
        useSimulationExecutionContextProvider({
          basestation: mockBasestation,
          room: mockRoom,
          graph,
          captureLog: mockCaptureLog,
        })
      );

      // Verify that the basestation was configured with the graph
      expect(mockBasestation.setActiveConfigurationFromProtobuf).toHaveBeenCalled();
      expect(result.current.status).toEqual({ value: "offline" });
    });
  });

  describe("Toggle Button Commands", () => {
    it("should send TOGGLE_BUTTON command and update state", async () => {
      // Setup graph with toggle controller and light
      const nodes: GraphNode[] = [
        {
          id: "canbus-container-1",
          type: "canbusControllerContainer",
          position: { x: 0, y: 0 },
          data: {
            title: "CanBus Controller",
            controllers: {
              "toggle-controller-1": {
                id: "toggle-controller-1",
                label: "Toggle Button",
                sortIndex: 0,
                type: "toggle",
                portId: "3pin-port-1",
                onUpClick: {
                  "light-action-1": {
                    id: "light-action-1",
                    sortIndex: 0,
                    deviceId: "light-1",
                    dimSpeed: 0,
                    targetValue: 100,
                    onValue: 100,
                    offValue: 0,
                    type: "lighting",
                  },
                },
                onUpHold: {},
                onDownClick: {
                  "light-action-2": {
                    id: "light-action-2",
                    sortIndex: 0,
                    deviceId: "light-1",
                    dimSpeed: 0,
                    targetValue: 0,
                    onValue: 100,
                    offValue: 0,
                    type: "lighting",
                  },
                },
                onDownHold: {},
              },
            },
            relayConnectors: {},
            relayOutputs: {},
            zeroToTenVoltDimmers: {},
          },
        },
        {
          id: "section-1",
          type: "section",
          position: { x: 200, y: 0 },
          data: {
            title: "Test Section",
            devices: {
              "light-1": {
                id: "light-1",
                type: "dmx",
                channel: 1,
                universe: 1,
              },
            },
          },
        },
      ];

      const graph = { nodes, edges: [] };

      const { result } = renderHook(() =>
        useSimulationExecutionContextProvider({
          basestation: mockBasestation,
          room: mockRoom,
          graph,
          captureLog: mockCaptureLog,
        })
      );

      // Send a toggle command (up click)
      act(() => {
        result.current.sendToggleCommand(
          "canbus-container-1",
          "toggle-controller-1",
          "onUpClick"
        );
      });

      // Verify that the command was sent to the basestation
      expect(mockBasestation.handleCommand).toHaveBeenCalledWith(
        expect.stringContaining(String.fromCharCode(MessageType.MESSAGE_TOGGLE_BUTTON)),
        expect.any(Number)
      );

      // Verify that the log was captured
      expect(mockCaptureLog).toHaveBeenCalledWith({
        timestamp: expect.any(Number),
        device: { type: "toggleController", id: "canbus-container-1" },
        action: "mouseUp",
        target: "onUpClick",
      });
    });

    it("should check light state after toggle command", async () => {
      // Mock the basestation to return light state with brightness > 0
      mockBasestation.getActiveConfiguration.mockReturnValue(
        new Uint8Array([
          // Mock protobuf data with light state
          10, 30, // config field
          8, 1, // id: 1
          18, 6, 48, 48, 48, 48, 48, 48, // version: "000000"
          26, 10, // lights field
          10, 8, // light entry
          8, 0, // id: 0 (basestation light id)
          16, 1, // channel: 1
          18, 20, // state field
          10, 10, // lights state
          10, 8, // light state entry
          8, 0, // id: 0
          21, 0, 0, 200, 66, // brightness: 100.0 (float32)
        ])
      );

      const nodes: GraphNode[] = [
        {
          id: "section-1",
          type: "section",
          position: { x: 200, y: 0 },
          data: {
            title: "Test Section",
            devices: {
              "light-1": {
                id: "light-1",
                type: "dmx",
                channel: 1,
                universe: 1,
              },
            },
          },
        },
      ];

      const graph = { nodes, edges: [] };

      const { result } = renderHook(() =>
        useSimulationExecutionContextProvider({
          basestation: mockBasestation,
          room: mockRoom,
          graph,
          captureLog: mockCaptureLog,
        })
      );

      // Simulate the basestation processing loop
      act(() => {
        (global as any).__intervalCallback?.();
      });

      // Check that the light is considered "on" (brightness > 0)
      const isLightOn = result.current.isLightOn("light-1");
      const brightness = result.current.getBrightness("light-1");

      // Note: These assertions depend on the mock protobuf data being properly decoded
      // In a real scenario, the ActiveConfiguration.decode would parse the protobuf
      expect(typeof isLightOn).toBe("boolean");
      expect(typeof brightness).toBe("number");
    });
  });
});
