// Copied from firmware/basestation/common/events.h
// Ideally, it would be sync automatically when we build the WASM module

export enum EventType {
  EVENT_SET_DEVICE_ID = 0x00,
  EVENT_BUTTON_CHANGE = 0x01,
  EVENT_TEMPERATURE_CHANGE = 0x02,
  EVENT_PWM_CHANGE = 0x03,
  EVENT_PROVISIONING_RESULT = 0x04,
  EVENT_PING = 0x05,
  EVENT_ANNOUNCE = 0x06,
}

export enum MessageType {
  MESSAGE_MOMENTARY_BUTTON = 0x00,
  MESSAGE_TOGGLE_BUTTON = 0x01,
  MESSAGE_MOTION_DETECTED = 0x02,
  MESSAGE_DOOR_SENSOR = 0x03,
  MESSAGE_CANBO_PROVISIONING = 0x04,
  MESSAGE_START_ZERO_TO_TEN_DIMMING = 0x05,
  MESSAGE_STOP_ZERO_TO_TEN_DIMMING = 0x06,
  MESSAGE_TOGGLE_RELAY = 0x07,
  MESSAGE_PING = 0x08,
  MESSAGE_RF_DOOR_SENSOR = 0x09,
}

export enum BackendMessageType {
  BACKEND_MESSAGE_UPDATE = 0x01,
  BACKEND_MESSAGE_PING = 0x02,
}

export enum ButtonTarget {
  BUTTON_UP = 0x00,
  BUTTON_DOWN = 0x01,
}
