import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { SomoBasestationController } from "@/lib/SomoBasestationController";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const FormSchema = z.object({
  qrCode: z.string().min(1, "QR code is required"),
});

type FormValues = z.infer<typeof FormSchema>;

interface UpdateQRModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdated: (qrCode: string) => void;
  controller: SomoBasestationController | null;
  isConnected: boolean;
  currentQRCode: string | null;
}

export function UpdateQRModal({
  open,
  onOpenChange,
  controller,
  isConnected,
  currentQRCode,
  onUpdated,
}: UpdateQRModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      qrCode: currentQRCode || "",
    },
  });

  const onSubmit = async (values: FormValues) => {
    if (!controller) {
      return;
    }

    if (!isConnected) {
      return;
    }

    setIsSubmitting(true);
    try {
      await controller.setQRCode(values.qrCode);

      // Wait a moment then perform handshake to refresh the QR code display
      setTimeout(async () => {
        try {
          const refreshResult = await controller.performHandshake();
          if (refreshResult && refreshResult.qr !== currentQRCode) {
            // QR code has changed, we need to handle any cache migration
            if (currentQRCode && refreshResult.qr) {
              // Migrate any cached form data from old QR to new QR
              const oldCacheKey = `form_cache_${currentQRCode}`;
              const newCacheKey = `form_cache_${refreshResult.qr}`;
              const cachedData = localStorage.getItem(oldCacheKey);
              if (cachedData) {
                localStorage.setItem(newCacheKey, cachedData);
                localStorage.removeItem(oldCacheKey);
              }
              onUpdated(refreshResult.qr);
            }
          }
        } catch (error) {
          console.error("Failed to refresh QR code:", error);
        }
      }, 500);

      onOpenChange(false);
    } catch (error) {
      console.error("Failed to update QR code:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update form when QR code changes
  useEffect(() => {
    form.setValue("qrCode", currentQRCode || "");
  }, [currentQRCode, form]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Update Base Station QR Code</DialogTitle>
          <DialogDescription>
            Enter a new QR code for this base station. This will be saved to the
            device's flash memory.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="qrCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>QR Code</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter QR code"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : "Save"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
