import { Device } from "@somo/shared";

export type LogEntry =
  | ClickLogEntry
  | MouseDownLogEntry
  | MouseUpLogEntry
  | StateLogEntry
  | BooleanStateLogEntry
  | EmitLogEntry
  | CancelLogEntry
  | ServicePadStatusLogEntry
  | DoorbellLogEntry;

type ClickLogEntry = BaseLogEntry & {
  action: "click";
  target: string;
};

type MouseDownLogEntry = BaseLogEntry & {
  action: "mouseDown";
  target: string;
};

type MouseUpLogEntry = BaseLogEntry & {
  action: "mouseUp";
  target: string;
};

type StateLogEntry = BaseLogEntry & {
  action: "set";
  target: string;
  value: string;
};

type BooleanStateLogEntry = BaseLogEntry & {
  action: "turned";
  state: "ON" | "OFF";
};

type EmitLogEntry = BaseLogEntry & {
  action: "emit";
  event: string;
};

type CancelLogEntry = BaseLogEntry & {
  action: "cancel";
  event: string;
};

type ServicePadStatusLogEntry = BaseLogEntry & {
  action: "servicePadStatus";
  status: "doNotDisturb" | "makeUpRoom" | "none";
};
type DoorbellLogEntry = BaseLogEntry & {
  action: "doorbell";
};

type BaseLogEntry = {
  timestamp: number;
  device:
    | {
        type: Exclude<Device["type"], "dmx">;
        id: string;
      }
    | { type: "dmx"; id: string; name: string };
};
