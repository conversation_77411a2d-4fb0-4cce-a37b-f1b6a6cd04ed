import {
  addEdge,
  applyEdgeChanges,
  applyNodeChanges,
  Edge,
  Node,
  OnConnect,
  OnEdgesChange,
  OnNodesChange,
  ReactFlow,
} from "@xyflow/react";
import { Allotment } from "allotment";
import { useCallback, useState } from "react";
import { PaneContainer } from "../ui/Layout";

// TODO: replace the <ReactFlow /> nodes and edges with our SLD implementation

// Type what we put in nodes' `data`
type NodeData = {
  label: string;
};

const initialNodes: Node<NodeData>[] = [
  { id: "n1", position: { x: 0, y: 0 }, data: { label: "Node 1" } },
  { id: "n2", position: { x: 0, y: 100 }, data: { label: "Node 2" } },
];
const initialEdges: Edge[] = [{ id: "n1-n2", source: "n1", target: "n2" }];

export function SldEditor() {
  const [nodes, setNodes] = useState(initialNodes);
  const [edges, setEdges] = useState(initialEdges);

  const onNodesChange: OnNodesChange<Node<NodeData>> = useCallback(
    (changes) => {
      setNodes((nodesSnapshot) => applyNodeChanges(changes, nodesSnapshot));
    },
    [],
  );
  const onEdgesChange: OnEdgesChange = useCallback(
    (changes) =>
      setEdges((edgesSnapshot) => applyEdgeChanges(changes, edgesSnapshot)),
    [],
  );
  const onConnect: OnConnect = useCallback(
    (params) => setEdges((edgesSnapshot) => addEdge(params, edgesSnapshot)),
    [],
  );

  return (
    <div className="w-full flex-1">
      <Allotment separator={false}>
        <Allotment.Pane className="absolute inset-0 flex flex-row py-2 px-1">
          <PaneContainer className="flex flex-col flex-grow">
            {/* TODO: this is where you can configure ReactFlow, see https://reactflow.dev/api-reference/react-flow */}
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              fitView
            />
          </PaneContainer>
        </Allotment.Pane>
      </Allotment>
    </div>
  );
}
