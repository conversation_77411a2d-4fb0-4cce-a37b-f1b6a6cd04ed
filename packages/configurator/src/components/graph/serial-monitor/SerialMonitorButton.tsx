import { But<PERSON> } from "@/components/ui/button";
import { PlugZap } from "lucide-react";
import { SerialPort } from "web-serial-polyfill";
import { useFlags } from "launchdarkly-react-client-sdk";

export function SerialMonitorButton({
  onClick,
}: {
  onClick?: (port: SerialPort) => void;
}) {
  const { rfSerialMonitor } = useFlags();

  if (!hasSerialSupport(navigator)) {
    return <></>;
  }

  if (!rfSerialMonitor) {
    return <></>;
  }

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={async () => {
        if (!hasSerialSupport(navigator)) {
          return;
        }
        const port = await navigator.serial.requestPort();
        onClick?.(port);
      }}
    >
      <PlugZap className="size-4" />
    </Button>
  );
}

type SerialNavigator = Navigator & {
  serial: { requestPort: () => Promise<SerialPort> };
};

function hasSerialSupport(navigator: Navigator): navigator is SerialNavigator {
  // We can't use zod to parse the navigator because we wouldn't be able to call
  // the requestPort function after it was parsed (Illegal Invocation).
  return (
    "serial" in navigator &&
    typeof navigator.serial === "object" &&
    navigator.serial !== null &&
    "requestPort" in navigator.serial &&
    typeof navigator.serial.requestPort === "function"
  );
}
