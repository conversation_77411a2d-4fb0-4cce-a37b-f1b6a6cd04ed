import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Toaster } from "@/components/ui/sonner";
import "web-serial-polyfill";
import { SerialPort } from "web-serial-polyfill";
import { Graph } from "../Graph";
import { SerialMonitor } from "./SerialMonitor";

export function SerialMonitorDialog({
  port,
  dialogOpen,
  setDialogOpen,
  graph,
  roomId,
}: {
  port?: SerialPort;
  dialogOpen: boolean;
  setDialogOpen: (open: boolean) => void;
  graph: Graph;
  roomId: string;
}) {
  if (!dialogOpen) {
    return <></>;
  }

  return (
    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <DialogContent className="w-[95%] h-[95%] min-w-[95%] min-h-[95%] flex flex-col">
        <Toaster />

        {port ? (
          <SerialMonitor port={port} graph={graph} roomId={roomId} />
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>No Port Connected</DialogTitle>
            </DialogHeader>

            <div className="flex flex-grow flex-col gap-4 border-t border-gray-200 -mx-6 -mb-6 rounded-b-md relative">
              <div className="absolute top-0 left-0 w-full h-full bg-gray-100" />
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
