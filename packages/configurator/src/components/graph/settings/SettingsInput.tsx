import { cn } from "@/lib/classNames";
import React, { useEffect, useRef } from "react";

export function SettingsInput({
  value,
  onEndEdit,
  className,
  inputClassName,
  id,
  label,
  disabled,
}: {
  value: string;
  onEndEdit: (value: string) => void;
  className?: string;
  inputClassName?: string;
  id?: string;
  label?: React.ReactNode;
  disabled?: boolean;
}) {
  const [internalValue, setValue] = React.useState(value);
  const savedValue = useRef(value);
  const [focused, setFocused] = React.useState(false);

  useEffect(() => {
    if (!focused && value !== internalValue) {
      setValue(value);
      savedValue.current = value;
    }
  }, [value, focused, internalValue]);

  useEffect(() => {
    return () => {
      if (internalValue !== savedValue.current) {
        onEndEdit?.(internalValue);
        savedValue.current = internalValue;
      }
    };
  }, [internalValue, onEndEdit]);

  const handleSaveChanged = () => {
    if (internalValue !== savedValue.current) {
      onEndEdit?.(internalValue);
      savedValue.current = internalValue;
    }
  };

  return (
    <div
      className={cn(
        "relative border border-input shadow-sm rounded-md flex flex-row items-stretch overflow-hidden h-10 w-full min-w-0",
        focused && "ring-1 ring-ring ring-blue-500",
        className,
      )}
    >
      {label && (
        <div className="flex flex-shrink-0 items-center justify-center text-xs px-3 bg-gray-50 border-r border-input text-gray-500 font-semibold">
          {label}
        </div>
      )}

      <input
        id={id}
        className={cn(
          "min-w-0 flex-shrink-0 bg-transparent px-3 py-1 transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 text-sm w-full",
          inputClassName,
        )}
        onFocus={() => setFocused(true)}
        type="text"
        value={internalValue}
        onChange={(e) => setValue(e.target.value)}
        onBlur={() => {
          setFocused(false);
          handleSaveChanged();
        }}
        disabled={disabled}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            e.currentTarget.blur();
          }
        }}
      />
    </div>
  );
}
