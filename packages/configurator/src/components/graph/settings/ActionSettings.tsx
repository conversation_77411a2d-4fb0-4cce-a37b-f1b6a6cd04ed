import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  type Device,
  DeviceControlSettings,
  filterNodes,
  GraphNodesByType,
  HistoricalDevice,
} from "@somo/shared";
import { Trash2Icon } from "lucide-react";
import React from "react";
import { match, P } from "ts-pattern";
import * as Y from "yjs";
import { useReactFlowContext } from "../ReactFlowContext";
import { SettingsRow } from "./SettingsGroup";
import { SettingsInput } from "./SettingsInput";

type SectionDeviceNode = Device & {
  sectionNode: GraphNodesByType["section"];
};

interface ActionSettingsProps {
  action: DeviceControlSettings;
  type: "toggle" | "momentary" | "pir";
  actionType: string;
  yControllerMap: Y.Map<any> | null;
  sectionDeviceNodes: SectionDeviceNode[];
  readOnly: boolean;
}

export const ActionSettings: React.FC<ActionSettingsProps> = ({
  type,
  action,
  actionType,
  yControllerMap,
  sectionDeviceNodes,
  readOnly,
}) => {
  const { nodes } = useReactFlowContext();
  return (
    <SettingsRow className="flex flex-col justify-between items-center gap-2 -mx-3">
      <div className="flex flex-row items-center gap-2 w-full">
        <Select
          onValueChange={(value) => {
            if (typeof value !== "string") {
              return;
            }
            const actionMap = yControllerMap?.get(actionType) as
              | Y.Map<any>
              | undefined;
            const map = actionMap?.get(action.id) as Y.Map<any> | undefined;
            if (map) {
              map.set("deviceId", value);
            }
          }}
          value={action.deviceId}
          disabled={readOnly}
        >
          <SelectTrigger className="h-10 min-h-10 w-full">
            <SelectValue
              placeholder="Select a section"
              className="w-full h-10 min-h-10"
            />
          </SelectTrigger>
          <SelectContent className="w-full">
            {sectionDeviceNodes?.map((device) => {
              return (
                match(device)
                  .with({ type: "dmx" }, (device) => {
                    const IconComponent = device.icon
                      ? DeviceIcons[device.icon]
                      : null;
                    return (
                      <SelectItem
                        key={device.id}
                        value={device.id}
                        className="flex flex-row w-full items-center "
                      >
                        <div className="flex flex-row items-center gap-1">
                          {IconComponent && (
                            <IconComponent className="size-4" />
                          )}
                          <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
                            <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
                              {device.sectionNode.data.title}
                            </div>
                            <div className="truncate mb-0 text-[10px] font-semibold">
                              {device.name}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })
                  .with({ type: P.optional(undefined) }, (device) => {
                    // ts-pattern struggles to exhaustively match a key that may be missing
                    // see https://github.com/gvergnaud/ts-pattern/issues/278
                    const historicalDevice = device as HistoricalDevice & {
                      sectionNode: GraphNodesByType["section"];
                    };
                    const IconComponent = historicalDevice.icon
                      ? DeviceIcons[historicalDevice.icon]
                      : null;
                    return (
                      <SelectItem
                        key={historicalDevice.id}
                        value={historicalDevice.id}
                        className="flex flex-row w-full items-center "
                      >
                        <div className="flex flex-row items-center gap-1">
                          {IconComponent && (
                            <IconComponent className="size-4" />
                          )}
                          <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
                            <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
                              {historicalDevice.sectionNode.data.title}
                            </div>
                            <div className="truncate mb-0 text-[10px] font-semibold">
                              {historicalDevice.name}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })
                  .with({ type: "somoSwitch" }, (device) => {
                    const switchNodes = filterNodes(
                      nodes,
                      "somoSwitchContainer",
                    );
                    const lights = [];
                    for (const switchNode of switchNodes) {
                      const via1 = switchNode.data.via1;
                      const via2 = switchNode.data.via2;
                      const via3 = switchNode.data.via3;
                      if (via1.hasLoad) {
                        lights.push({
                          nodeId: switchNode.id,
                          id: "via1",
                          ...via1,
                        });
                      }
                      if (via2.hasLoad) {
                        lights.push({
                          nodeId: switchNode.id,
                          id: "via2",
                          ...via2,
                        });
                      }
                      if (via3.hasLoad) {
                        lights.push({
                          nodeId: switchNode.id,
                          id: "via3",
                          ...via3,
                        });
                      }
                    }

                    const light = lights.find(
                      (light) =>
                        light.id === device.viaId &&
                        light.nodeId === device.nodeId,
                    );
                    if (!light) {
                      return null;
                    }
                    const IconComponent = light.lightIcon
                      ? DeviceIcons[light.lightIcon]
                      : null;
                    return (
                      <SelectItem
                        key={device.id}
                        value={device.id}
                        className="flex flex-row w-full items-center "
                      >
                        <div className="flex flex-row items-center gap-1">
                          {IconComponent && (
                            <IconComponent className="size-4" />
                          )}
                          <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
                            <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
                              {device.sectionNode.data.title}
                            </div>
                            <div className="truncate mb-0 text-[10px] font-semibold">
                              {light.lightName}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })
                  .with({ type: "somoDimmer" }, (device) => {
                    const dimmerNodes = filterNodes(
                      nodes,
                      "somoDimmerContainer",
                    );
                    const lights = dimmerNodes.map((dimmerNode) => ({
                      nodeId: dimmerNode.id,
                      ...dimmerNode.data.via,
                    }));

                    const light = lights.find(
                      (light) =>
                        device.viaId === "via" &&
                        light.nodeId === device.nodeId,
                    );
                    if (!light) {
                      return null;
                    }
                    const IconComponent = light.lightIcon
                      ? DeviceIcons[light.lightIcon]
                      : null;
                    return (
                      <SelectItem
                        key={device.id}
                        value={device.id}
                        className="flex flex-row w-full items-center "
                      >
                        <div className="flex flex-row items-center gap-1">
                          {IconComponent && (
                            <IconComponent className="size-4" />
                          )}
                          <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
                            <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
                              {device.sectionNode.data.title}
                            </div>
                            <div className="truncate mb-0 text-[10px] font-semibold">
                              {light.lightName}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })
                  .with({ type: "outletDimmer" }, (device) => {
                    const dimmerNodes = filterNodes(
                      nodes,
                      "outletDimmerContainer",
                    );
                    const lights = dimmerNodes.map((dimmerNode) => ({
                      nodeId: dimmerNode.id,
                      ...dimmerNode.data.via,
                    }));

                    const light = lights.find(
                      (light) =>
                        device.viaId === "via" &&
                        light.nodeId === device.nodeId,
                    );
                    if (!light) {
                      return null;
                    }
                    const IconComponent = light.lightIcon
                      ? DeviceIcons[light.lightIcon]
                      : null;
                    return (
                      <SelectItem
                        key={device.id}
                        value={device.id}
                        className="flex flex-row w-full items-center "
                      >
                        <div className="flex flex-row items-center gap-1">
                          {IconComponent && (
                            <IconComponent className="size-4" />
                          )}
                          <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
                            <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
                              {device.sectionNode.data.title}
                            </div>
                            <div className="truncate mb-0 text-[10px] font-semibold">
                              {light.lightName}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })
                  .with({ type: "somoShades" }, (device) => {
                    const shadesNodes = filterNodes(
                      nodes,
                      "somoShadesContainer",
                    );
                    const allShades = shadesNodes.map((shadesNode) => ({
                      nodeId: shadesNode.id,
                      ...shadesNode.data.via,
                    }));

                    const shades = allShades.find(
                      (light) =>
                        device.viaId === "via" &&
                        light.nodeId === device.nodeId,
                    );
                    if (!shades) {
                      return null;
                    }
                    const IconComponent = shades.loadIcon
                      ? DeviceIcons[shades.loadIcon]
                      : null;
                    return (
                      <SelectItem
                        key={device.id}
                        value={device.id}
                        className="flex flex-row w-full items-center "
                      >
                        <div className="flex flex-row items-center gap-1">
                          {IconComponent && (
                            <IconComponent className="size-4" />
                          )}
                          <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
                            <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
                              {device.sectionNode.data.title}
                            </div>
                            <div className="truncate mb-0 text-[10px] font-semibold">
                              {shades.loadName}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })
                  .with({ type: "somoFan" }, (device) => {
                    const fanNodes = filterNodes(nodes, "somoFanContainer");
                    const allFan = fanNodes.map((fanNode) => ({
                      nodeId: fanNode.id,
                      ...fanNode.data.via,
                    }));

                    const fan = allFan.find(
                      (light) =>
                        device.viaId === "via" &&
                        light.nodeId === device.nodeId,
                    );
                    if (!fan) {
                      return null;
                    }
                    const IconComponent = fan.loadIcon
                      ? DeviceIcons[fan.loadIcon]
                      : null;
                    return (
                      <SelectItem
                        key={device.id}
                        value={device.id}
                        className="flex flex-row w-full items-center "
                      >
                        <div className="flex flex-row items-center gap-1">
                          {IconComponent && (
                            <IconComponent className="size-4" />
                          )}
                          <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
                            <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
                              {device.sectionNode.data.title}
                            </div>
                            <div className="truncate mb-0 text-[10px] font-semibold">
                              {fan.loadName}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })
                  .with({ type: "zeroToTenVoltDimmer" }, (device) => {
                    const canBusControllerNodes = filterNodes(
                      nodes,
                      "canbusControllerContainer",
                    );
                    const allDimmers = [];
                    for (const canBusControllerNode of canBusControllerNodes) {
                      const dimmers = Object.values(
                        canBusControllerNode.data.zeroToTenVoltDimmers || {},
                      );
                      for (const dimmer of dimmers) {
                        allDimmers.push({
                          nodeId: canBusControllerNode.id,
                          ...dimmer,
                        });
                      }
                    }

                    const dimmer = allDimmers.find(
                      (d) =>
                        d.id === device.viaId && d.nodeId === device.nodeId,
                    );
                    if (!dimmer) {
                      return null;
                    }
                    const IconComponent = dimmer.icon
                      ? DeviceIcons[dimmer.icon]
                      : null;
                    return (
                      <SelectItem
                        key={device.id}
                        value={device.id}
                        className="flex flex-row w-full items-center "
                      >
                        <div className="flex flex-row items-center gap-1">
                          {IconComponent && (
                            <IconComponent className="size-4" />
                          )}
                          <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
                            <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
                              {device.sectionNode.data.title}
                            </div>
                            <div className="truncate mb-0 text-[10px] font-semibold">
                              {dimmer.name}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })
                  .with({ type: "relayOutput" }, (device) => {
                    // Find the actual relay output from the CanBus controller to get current settings
                    const canBusNodes = filterNodes(
                      nodes,
                      "canbusControllerContainer",
                    );
                    const relayOutput = canBusNodes
                      .flatMap((node) =>
                        Object.values(node.data.relayOutputs || {}).map(
                          (output) => ({
                            ...output,
                            nodeId: node.id,
                          }),
                        ),
                      )
                      .find(
                        (output) =>
                          device.viaId === output.id &&
                          output.nodeId === device.nodeId,
                      );

                    if (!relayOutput) {
                      return null;
                    }

                    const IconComponent = relayOutput.icon
                      ? DeviceIcons[relayOutput.icon]
                      : null;

                    return (
                      <SelectItem
                        key={device.id}
                        value={device.id}
                        className="flex flex-row w-full items-center "
                      >
                        <div className="flex flex-row items-center gap-1">
                          {IconComponent && (
                            <IconComponent className="size-4" />
                          )}
                          <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
                            <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
                              {device.sectionNode.data.title}
                            </div>
                            <div className="truncate mb-0 text-[10px] font-semibold">
                              {relayOutput.name}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })
                  // Trying to perform an exhaustive check results in a type error:
                  // `Type instantiation is excessively deep and possibly infinite`
                  // => Use `otherwise` instead.
                  .otherwise(() => null)
              );
            })}
          </SelectContent>
        </Select>
        {!readOnly && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="size-10 flex-shrink-0"
                onClick={() => {
                  const actionMap = yControllerMap?.get(actionType) as
                    | Y.Map<any>
                    | undefined;
                  actionMap?.delete(action.id);
                }}
              >
                <Trash2Icon className="size-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Delete</TooltipContent>
          </Tooltip>
        )}
      </div>
      <div className="flex flex-row items-center gap-2 w-full flex-wrap">
        <SettingsInput
          label="Dim Speed (sec)"
          value={`${action.dimSpeed ?? 0}`}
          disabled={readOnly}
          className="w-auto"
          inputClassName="w-[50px] text-center"
          onEndEdit={(value) => {
            try {
              const dimSpeed = parseFloat(value);
              if (isNaN(dimSpeed)) {
                return;
              }
              const actionMap = yControllerMap?.get(actionType) as
                | Y.Map<any>
                | undefined;
              const map = actionMap?.get(action.id) as Y.Map<any> | undefined;
              if (map) {
                map.set("dimSpeed", dimSpeed);
              }
            } catch (error) {
              console.error(error);
            }
          }}
        />

        {match(type)
          .with("toggle", () => (
            <SettingsInput
              label="Target Value"
              value={`${action.targetValue ?? 0}`}
              disabled={readOnly}
              className="w-auto"
              inputClassName="w-[50px] text-center"
              onEndEdit={(value) => {
                try {
                  const targetValue = parseFloat(value);
                  if (isNaN(targetValue)) {
                    return;
                  }
                  const actionMap = yControllerMap?.get(actionType) as
                    | Y.Map<any>
                    | undefined;
                  const map = actionMap?.get(action.id) as
                    | Y.Map<any>
                    | undefined;
                  if (map) {
                    map.set("targetValue", targetValue);
                  }
                } catch (error) {
                  console.error(error);
                }
              }}
            />
          ))
          .with("momentary", () => (
            <>
              <SettingsInput
                label="On Value"
                value={`${action.onValue ?? 0}`}
                disabled={readOnly}
                className="w-auto"
                inputClassName="w-[50px] text-center"
                onEndEdit={(value) => {
                  const onValue = parseFloat(value);
                  if (isNaN(onValue)) {
                    return;
                  }
                  const actionMap = yControllerMap?.get(actionType) as
                    | Y.Map<any>
                    | undefined;
                  const map = actionMap?.get(action.id) as
                    | Y.Map<any>
                    | undefined;
                  if (map) {
                    map.set("onValue", onValue);
                  }
                }}
              />
              <SettingsInput
                label="Off Value"
                value={`${action.offValue ?? 0}`}
                disabled={readOnly}
                className="w-auto"
                inputClassName="w-[50px] text-center"
                onEndEdit={(value) => {
                  const offValue = parseFloat(value);
                  if (isNaN(offValue)) {
                    return;
                  }
                  const actionMap = yControllerMap?.get(actionType) as
                    | Y.Map<any>
                    | undefined;
                  const map = actionMap?.get(action.id) as
                    | Y.Map<any>
                    | undefined;
                  if (map) {
                    map.set("offValue", offValue);
                  }
                }}
              />
            </>
          ))
          .with("pir", () => (
            <SettingsInput
              label="Target Value"
              value={`${action.targetValue ?? 0}`}
              disabled={readOnly}
              className="w-auto"
              inputClassName="w-[50px] text-center"
              onEndEdit={(value) => {
                try {
                  const targetValue = parseFloat(value);
                  if (isNaN(targetValue)) {
                    return;
                  }
                  const actionMap = yControllerMap?.get(actionType) as
                    | Y.Map<any>
                    | undefined;
                  const map = actionMap?.get(action.id) as
                    | Y.Map<any>
                    | undefined;
                  if (map) {
                    map.set("targetValue", targetValue);
                  }
                } catch (error) {
                  console.error(error);
                }
              }}
            />
          ))
          .exhaustive()}
      </div>
    </SettingsRow>
  );
};
