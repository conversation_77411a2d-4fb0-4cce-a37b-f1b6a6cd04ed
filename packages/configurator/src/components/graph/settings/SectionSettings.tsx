import { cleanupActionsForDevice } from "@/components/graph/utils/deviceUtils";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { TooltipDropdownMenu } from "@/components/ui/tooltip-dropdown-menu";
import { cn } from "@/lib/classNames";
import { objectToMap } from "@/lib/yjsUtils";
import {
  DmxDevice,
  DmxFixture,
  dmxFixtureTypes,
  filterNodes,
  GraphNodesByType,
  OutletDimmerDevice,
  randomId,
  RelayOutputDevice,
  SomoDimmerDevice,
  SomoFanDevice,
  SomoIrControllerDevice,
  SomoShadesDevice,
  SomoSwitchDevice,
  somoSwitchViaIds,
  SomoThermostatDevice,
  ZeroToTenVoltDimmerDevice,
} from "@somo/shared";
import { Position } from "@xyflow/react";
import {
  ArrowRightSquareIcon,
  ChevronDown,
  PlusIcon,
  Trash2,
  Trash2Icon,
} from "lucide-react";
import React, { useMemo } from "react";
import { match } from "ts-pattern";
import * as Y from "yjs";
import { getNodeData } from "../Graph";
import { useReactFlowContext } from "../ReactFlowContext";
import { DimmingCurve } from "./DimmingCurve";
import { LampPopover } from "./LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "./SettingsGroup";
import { SettingsInput } from "./SettingsInput";

function DMXDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: DmxDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { ydoc } = useReactFlowContext();

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(ydoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [ydoc, nodeId, device.id]);

  const yFixtureMap = useMemo(() => {
    const dataMap = getNodeData(ydoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
    const fixtureMap = deviceMap?.get("fixtures") as Y.Map<any> | null;
    return fixtureMap;
  }, [ydoc, nodeId, device.id]);

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(ydoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [ydoc, nodeId]);

  const sortedFixtures = useMemo(() => {
    const fixtures = Object.values(device.fixtures);
    return fixtures.sort((a, b) => a.sortIndex - b.sortIndex);
  }, [device.fixtures]);

  // Default dimming curve if not set
  const dimmingCurve = useMemo(() => {
    if (!device.dimmingCurve || !Array.isArray(device.dimmingCurve)) {
      return [
        { x: 0, y: 0 },
        { x: 1, y: 1 },
      ];
    }

    return device.dimmingCurve;
  }, [device.dimmingCurve]);

  // Default curve type if not set
  const dimmingCurveType = device.dimmingCurveType || "linear";

  return (
    <SettingsRow>
      <div
        className={cn(
          "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer hover:text-gray-400",
          !collapsed && "text-gray-800",
        )}
        onClick={() => {
          onCollapseChange(!collapsed);
        }}
      >
        <ChevronDown
          className={cn(
            "size-4 transition-transform duration-300 -ml-1",
            collapsed && "-rotate-90",
          )}
        />
        <span className="flex-grow">{device.name}</span>
      </div>
      {!collapsed && (
        <div className="flex flex-col gap-2">
          <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
            <LampPopover
              activeIconKey={device.icon}
              onIconClick={(iconKey) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("icon", iconKey);
              }}
              disabled={readOnly}
            />

            <SettingsInput
              className="text-sm font-normal h-10"
              value={device.name}
              disabled={readOnly}
              onEndEdit={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("name", value);
              }}
            />
            <div className="flex flex-row items-center gap-1 ml-3">
              <Checkbox
                id="showLabel"
                checked={device.showLabel}
                disabled={readOnly}
                onCheckedChange={(checked) => {
                  if (!yDevice) {
                    return;
                  }
                  yDevice.set("showLabel", checked);
                }}
              />
              <Label
                htmlFor="showLabel"
                className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate"
              >
                Show label
              </Label>
            </div>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (ydoc) {
                        cleanupActionsForDevice(ydoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </div>

          <SettingsRow className="flex flex-row items-center">
            <SettingsLabel
              htmlFor="anchorPosition"
              className="flex-grow w-full text-right mr-2"
            >
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10 w-[100px]">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>

          <div className="flex flex-row items-center justify-between gap-2 flex-wrap">
            <div className="flex flex-row items-center gap-1 h-10">
              <Checkbox
                id={`isDimmable-${device.id}`}
                checked={device.isDimmable}
                disabled={readOnly}
                onCheckedChange={(checked) => {
                  if (!yDevice) {
                    return;
                  }
                  yDevice.set("isDimmable", checked);
                }}
              />
              <Label
                htmlFor={`isDimmable-${device.id}`}
                className="text-gray-500 text-xs font-semibold"
              >
                Dimmable
              </Label>
            </div>

            {device.isDimmable && (
              <SettingsInput
                className="w-auto"
                inputClassName="w-[60px] text-center"
                label="Default dim speed (sec)"
                value={device.defaultDimmingSpeed.toString()}
                disabled={readOnly || !device.isDimmable}
                onEndEdit={(value) => {
                  if (readOnly || !device.isDimmable || !yDevice) {
                    return;
                  }
                  yDevice.set("defaultDimmingSpeed", parseFloat(value));
                }}
              />
            )}
          </div>

          {device.isDimmable && (
            <DimmingCurve
              points={dimmingCurve}
              curveType={dimmingCurveType}
              disabled={readOnly}
              onChange={(points) => {
                if (readOnly || !yDevice) {
                  return;
                }
                const yArray = new Y.Array<any>();
                let index = 0;
                for (const point of points) {
                  yArray.insert(index, [point]);
                  index++;
                }
                yDevice.set("dimmingCurve", yArray);
              }}
              onCurveTypeChange={(type) => {
                if (readOnly || !yDevice) {
                  return;
                }
                yDevice.set("dimmingCurveType", type);
              }}
              className="mt-2"
              height={180}
            />
          )}

          <div className="flex flex-row items-center gap-1 mt-2 mr-1">
            <SettingsLabel className="flex-grow">Fixtures</SettingsLabel>
            {!readOnly && (
              <Button
                variant="ghost"
                className="h-6 flex-shrink-0 text-[10px] px-1.5 text-gray-500"
                onClick={() => {
                  if (!yFixtureMap) {
                    return;
                  }
                  const fixture = {
                    id: randomId(),
                    sortIndex: sortedFixtures.length,
                    type: "Analog",
                    channel: 1,
                    minBrightness: 0,
                    maxBrightness: 100,
                    defaultDimmingSpeed: 0.19,
                  } satisfies DmxFixture;
                  yFixtureMap.set(fixture.id, objectToMap(fixture));
                }}
              >
                + Add Fixture
              </Button>
            )}
          </div>
          {sortedFixtures.length > 0 && (
            <div className="flex flex-col gap-6">
              {sortedFixtures.map((fixture) => (
                <div key={fixture.id} className="flex flex-col gap-2">
                  <div className="flex flex-row items-center gap-2">
                    <Select
                      value={fixture.type}
                      disabled={readOnly}
                      onValueChange={(value) => {
                        if (!yFixtureMap) {
                          return;
                        }
                        const yFixture = yFixtureMap.get(
                          fixture.id,
                        ) as Y.Map<any>;
                        if (!yFixture) {
                          return;
                        }
                        yFixture.set("type", value);
                      }}
                    >
                      <SelectTrigger className="h-10">
                        <SelectValue placeholder="Select fixture" />
                      </SelectTrigger>
                      <SelectContent>
                        {dmxFixtureTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <SettingsInput
                      className="w-auto flex-shrink-0"
                      inputClassName="w-[40px] text-center flex-shrink-0"
                      label="Channel"
                      value={fixture.channel.toString()}
                      disabled={readOnly}
                      onEndEdit={(value) => {
                        if (!yFixtureMap) {
                          return;
                        }
                        const yFixture = yFixtureMap.get(
                          fixture.id,
                        ) as Y.Map<any>;
                        if (!yFixture) {
                          return;
                        }
                        try {
                          const channel = parseInt(value);
                          if (isNaN(channel)) {
                            return;
                          }
                          yFixture.set("channel", channel);
                        } catch (e) {
                          console.error(e);
                        }
                      }}
                    />

                    {!readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="flex-shrink-0 h-10"
                            onClick={() => {
                              if (!yFixtureMap) {
                                return;
                              }
                              yFixtureMap.delete(fixture.id);
                            }}
                          >
                            <Trash2 className="size-4 flex-shrink-0" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Delete Fixture</TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                  <div className="flex flex-row items-center gap-2 flex-wrap">
                    <SettingsInput
                      className="w-auto flex-shrink-0"
                      inputClassName="w-[60px] text-center flex-shrink-0"
                      label="Min. brightness"
                      value={fixture.minBrightness.toString()}
                      disabled={readOnly}
                      onEndEdit={(value) => {
                        if (!yFixtureMap) {
                          return;
                        }
                        const yFixture = yFixtureMap.get(
                          fixture.id,
                        ) as Y.Map<any>;
                        if (!yFixture) {
                          return;
                        }
                        try {
                          const minBrightness = parseInt(value);
                          if (isNaN(minBrightness)) {
                            return;
                          }
                          yFixture.set("minBrightness", minBrightness);
                        } catch (e) {
                          console.error(e);
                        }
                      }}
                    />
                    <SettingsInput
                      className="w-auto flex-shrink-0"
                      inputClassName="w-[60px] text-center flex-shrink-0"
                      label="Max. brightness"
                      value={fixture.maxBrightness.toString()}
                      disabled={readOnly}
                      onEndEdit={(value) => {
                        if (!yFixtureMap) {
                          return;
                        }
                        const yFixture = yFixtureMap.get(
                          fixture.id,
                        ) as Y.Map<any>;
                        if (!yFixture) {
                          return;
                        }
                        try {
                          const maxBrightness = parseInt(value);
                          if (isNaN(maxBrightness)) {
                            return;
                          }
                          yFixture.set("maxBrightness", maxBrightness);
                        } catch (e) {
                          console.error(e);
                        }
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </SettingsRow>
  );
}

function SomoDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: SomoSwitchDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, ydoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(ydoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [ydoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(ydoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [ydoc, nodeId, device.id]);

  const light = useMemo(() => {
    const switchNodes = filterNodes(nodes, "somoSwitchContainer");
    const lights = [];
    for (const switchNode of switchNodes) {
      const via1 = switchNode.data.via1;
      const via2 = switchNode.data.via2;
      const via3 = switchNode.data.via3;
      if (via1.hasLoad) {
        lights.push({ nodeId: switchNode.id, id: "via1", ...via1 });
      }
      if (via2.hasLoad) {
        lights.push({ nodeId: switchNode.id, id: "via2", ...via2 });
      }
      if (via3.hasLoad) {
        lights.push({ nodeId: switchNode.id, id: "via3", ...via3 });
      }
    }

    return lights.find(
      (light) => light.id === device.viaId && light.nodeId === device.nodeId,
    );
  }, [nodes]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !light && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {light ? light.lightName : "Light missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from RF Switch)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!light) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === light.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (ydoc) {
                        cleanupActionsForDevice(ydoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function SomoDimmerDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: SomoDimmerDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, ydoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(ydoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [ydoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(ydoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [ydoc, nodeId, device.id]);

  const light = useMemo(() => {
    const dimmerNodes = filterNodes(nodes, "somoDimmerContainer");
    const lights = [];
    for (const dimmerNode of dimmerNodes) {
      const via = dimmerNode.data.via;
      lights.push({ nodeId: dimmerNode.id, id: "via", ...via });
    }

    return lights.find(
      (light) => light.id === device.viaId && light.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !light && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {light ? light.lightName : "Light missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from RF Dimmer)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!light) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === light.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function OutletDimmerDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: OutletDimmerDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, ydoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(ydoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [ydoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(ydoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [ydoc, nodeId, device.id]);

  const light = useMemo(() => {
    const dimmerNodes = filterNodes(nodes, "outletDimmerContainer");
    const lights = [];
    for (const dimmerNode of dimmerNodes) {
      const via = dimmerNode.data.via;
      lights.push({ nodeId: dimmerNode.id, id: "via", ...via });
    }

    return lights.find(
      (light) => light.id === device.viaId && light.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !light && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {light ? light.lightName : "Light missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from Outlet Dimmer)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!light) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === light.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (ydoc) {
                        cleanupActionsForDevice(ydoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function SomoShadesDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: SomoShadesDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, ydoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(ydoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [ydoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(ydoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [ydoc, nodeId, device.id]);

  const load = useMemo(() => {
    const shadesNodes = filterNodes(nodes, "somoShadesContainer");
    const lights = [];
    for (const shadesNode of shadesNodes) {
      const via = shadesNode.data.via;
      lights.push({ nodeId: shadesNode.id, id: "via", ...via });
    }

    return lights.find(
      (load) => load.id === device.viaId && load.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !load && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {load ? load.loadName : "Shades/Curtains missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from Somo Shades)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!load) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === load.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (ydoc) {
                        cleanupActionsForDevice(ydoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function SomoFanDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: SomoFanDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, ydoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(ydoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [ydoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(ydoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [ydoc, nodeId, device.id]);

  const load = useMemo(() => {
    const fanNodes = filterNodes(nodes, "somoFanContainer");
    const lights = [];
    for (const fanNode of fanNodes) {
      const via = fanNode.data.via;
      lights.push({ nodeId: fanNode.id, id: "via", ...via });
    }

    return lights.find(
      (load) => load.id === device.viaId && load.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !load && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {load ? load.loadName : "Fan/Curtains missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from Somo Fan)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!load) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === load.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (ydoc) {
                        cleanupActionsForDevice(ydoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function SomoThermostatDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: SomoThermostatDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, ydoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(ydoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [ydoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(ydoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [ydoc, nodeId, device.id]);

  const load = useMemo(() => {
    const thermostatNodes = filterNodes(nodes, "somoThermostatContainer");
    const lights = [];
    for (const thermostatNode of thermostatNodes) {
      const via = thermostatNode.data.via;
      lights.push({ nodeId: thermostatNode.id, id: "via", ...via });
    }

    return lights.find(
      (load) => load.id === device.viaId && load.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !load && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {load ? load.hvacName : "Thermostat missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from Thermostat)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!load) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === load.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (ydoc) {
                        cleanupActionsForDevice(ydoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function SomoIrControllerDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: SomoIrControllerDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, ydoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(ydoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [ydoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(ydoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [ydoc, nodeId, device.id]);

  const load = useMemo(() => {
    const irControllerNodes = filterNodes(nodes, "somoIrControllerContainer");
    const loads = [];
    for (const irControllerNode of irControllerNodes) {
      const via = irControllerNode.data.via;
      loads.push({ nodeId: irControllerNode.id, id: "via", ...via });
    }

    return loads.find(
      (load) => load.id === device.viaId && load.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !load && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {load ? load.hvacName : "IR Controller missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from IR HVAC Controller)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!load) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === load.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (ydoc) {
                        cleanupActionsForDevice(ydoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

/**
 * Generic function to add control settings or hvac actions to parent vias.
 *
 * @param options - Configuration options
 * @param options.ydoc - The Yjs document
 * @param options.parentNodeId - The ID of the parent node
 * @param options.viaKeys - The keys of the vias to add actions to
 * @param options.onUpClickKey - The event key to use (default: "onUpClick")
 * @param options.actionType - The type of action to add ("control" or "hvac")
 * @param options.makeControlSettings - Function to create control settings (for "control" actionType)
 * @param options.deviceId - The ID of the device (for "hvac" actionType)
 * @param options.hvacDefaults - Default values for hvac actions (for "hvac" actionType)
 */
function addActionsToParentVias({
  ydoc,
  parentNodeId,
  viaKeys,
  onUpClickKey = "onUpClick",
  actionType = "control",
  makeControlSettings,
  deviceId,
  thermostatDefaults = {
    setpoint: 22,
    mode: "auto" as const,
    fanSpeed: "auto" as const,
  },
}: {
  ydoc: Y.Doc;
  parentNodeId: string;
  viaKeys: string[];
  onUpClickKey?: string;
  actionType?: "control" | "hvac";
  makeControlSettings?: (
    viaKey: string,
    sortIndex: number,
  ) => { id: string; [key: string]: any };
  deviceId?: string;
  thermostatDefaults?: {
    setpoint: number;
    mode: "heat" | "cool" | "fan" | "auto";
    fanSpeed: "low" | "medium" | "high" | "auto";
  };
}) {
  if (!ydoc) {
    return;
  }

  const parentDataMap = getNodeData(ydoc, parentNodeId);
  if (!parentDataMap) {
    return;
  }

  viaKeys.forEach((viaKey, sortIndex) => {
    const viaMap = parentDataMap.get(viaKey) as Y.Map<any> | undefined;
    if (!viaMap) {
      return;
    }

    let onUpClickMap = viaMap.get(onUpClickKey) as Y.Map<any> | undefined;
    if (!onUpClickMap) {
      onUpClickMap = new Y.Map();
      viaMap.set(onUpClickKey, onUpClickMap);
    }

    if (actionType === "hvac") {
      if (!deviceId) {
        console.error("deviceId is required for hvac actions");
        return;
      }

      const thermostatAction = {
        id: randomId(),
        deviceId,
        sortIndex,
        setpoint: thermostatDefaults.setpoint,
        mode: thermostatDefaults.mode,
        fanSpeed: thermostatDefaults.fanSpeed,
      };

      onUpClickMap.set(thermostatAction.id, objectToMap(thermostatAction));
    } else {
      if (!makeControlSettings) {
        console.error("makeControlSettings is required for control actions");
        return;
      }

      const controlSettings = makeControlSettings(viaKey, sortIndex);
      onUpClickMap.set(controlSettings.id, objectToMap(controlSettings));
    }
  });
}

function ZeroToTenVoltDimmerDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: ZeroToTenVoltDimmerDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, ydoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(ydoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [ydoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(ydoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [ydoc, nodeId, device.id]);

  const dimmer = useMemo(() => {
    const canBusControllerNodes = filterNodes(
      nodes,
      "canbusControllerContainer",
    );
    const dimmers = [];
    for (const canBusControllerNode of canBusControllerNodes) {
      const zeroToTenVoltDimmers =
        canBusControllerNode.data.zeroToTenVoltDimmers || {};
      for (const dimmer of Object.values(zeroToTenVoltDimmers)) {
        dimmers.push({ nodeId: canBusControllerNode.id, ...dimmer });
      }
    }

    return dimmers.find(
      (dimmer) => dimmer.id === device.viaId && dimmer.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !dimmer && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {dimmer ? dimmer.name : "0-10V Dimmer missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from 0-10V Dimmer)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!dimmer) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === dimmer.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function RelayOutputDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: RelayOutputDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, ydoc } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    if (!ydoc) {
      return null;
    }
    const dataMap = getNodeData(ydoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    if (!devicesMap && dataMap) {
      const newDevicesMap = new Y.Map();
      dataMap?.set("devices", newDevicesMap);
      return newDevicesMap as Y.Map<any>;
    }
    return devicesMap as Y.Map<any>;
  }, [ydoc, nodeId]);

  const yDeviceMap = useMemo(() => {
    if (!yDevicesMap) {
      return null;
    }
    return yDevicesMap.get(device.id) as Y.Map<any> | null;
  }, [yDevicesMap, device.id]);

  const relayOutputNode = filterNodes(nodes, "canbusControllerContainer").find(
    (node) => node.id === device.nodeId,
  );

  const relayOutput = relayOutputNode?.data.relayOutputs?.[device.viaId];

  if (!relayOutput) {
    return (
      <div className="text-xs font-mono h-[40px] flex items-center justify-center px-2">
        Relay output missing
      </div>
    );
  }

  return (
    <div className="mb-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button
            className={cn(
              "flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900",
              collapsed && "text-gray-500",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            <ChevronDown
              className={cn(
                "size-4 transition-transform duration-300",
                collapsed && "-rotate-90",
              )}
            />
            {relayOutput.name}
          </button>
        </div>
        <div className="flex items-center gap-2">
          {!readOnly && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="size-5"
                  onClick={() => {
                    yDevicesMap?.delete(device.id);
                  }}
                >
                  <Trash2Icon className="size-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Remove Device</TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
      {!collapsed && (
        <div className="ml-6 mt-2 space-y-2">
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">
              ID: {device.id}
            </div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel>Show Label</SettingsLabel>
            <Checkbox
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                yDeviceMap?.set("showLabel", checked);
              }}
            />
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel>Anchor Position</SettingsLabel>
            <Select
              value={device.anchorPosition || "left"}
              disabled={readOnly}
              onValueChange={(value) => {
                yDeviceMap?.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="right">Right</SelectItem>
                <SelectItem value="top">Top</SelectItem>
                <SelectItem value="bottom">Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </div>
      )}
    </div>
  );
}

export function SectionSettings({
  node,
}: {
  node: GraphNodesByType["section"];
}) {
  const { reactFlowInstance, ydoc, nodes, readOnly } = useReactFlowContext();

  const [deviceCollapsedState, setDeviceCollapsedState] = React.useState<{
    [deviceId: string]: boolean;
  }>({});

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(ydoc, node.id);
    return dataMap?.get("devices") as Y.Map<any> | undefined;
  }, [ydoc, node.id]);

  const nodeDevices = Object.values(node.data.devices ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const allDevices = filterNodes(nodes, "section").flatMap((node) =>
    Object.values(node.data.devices ?? {}),
  );

  const switchNodes = filterNodes(nodes, "somoSwitchContainer");
  const switchDevices = allDevices.filter((d) => d.type === "somoSwitch");
  const availableSomoLights = switchNodes.flatMap((node) => {
    return somoSwitchViaIds
      .filter((viaId) => node.data[viaId].hasLoad)
      .filter(
        (viaId) =>
          !switchDevices.find((d) => d.nodeId === node.id && d.viaId === viaId),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: node.data[viaId].lightName,
      }));
  });

  const dimmerNodes = filterNodes(nodes, "somoDimmerContainer");
  const dimmerDevices = allDevices.filter((d) => d.type === "somoDimmer");
  const availableSomoDimmerLights = dimmerNodes.flatMap((node) => {
    // viaUp and viaDown should not be bound to a specific light
    return (["via"] as const)
      .filter(
        (viaId) =>
          !dimmerDevices.find((d) => d.nodeId === node.id && d.viaId === viaId),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: node.data[viaId].lightName,
      }));
  });

  const outletDimmerNodes = filterNodes(nodes, "outletDimmerContainer");
  const outletDimmerDevices = allDevices.filter(
    (d) => d.type === "outletDimmer",
  );
  const availableOutletDimmerLights = outletDimmerNodes.flatMap((node) => {
    // viaUp and viaDown should not be bound to a specific light
    return (["via"] as const)
      .filter(
        (viaId) =>
          !outletDimmerDevices.find(
            (d) => d.nodeId === node.id && d.viaId === viaId,
          ),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: node.data[viaId].lightName,
      }));
  });

  const shadesNodes = filterNodes(nodes, "somoShadesContainer");
  const shadesDevices = allDevices.filter((d) => d.type === "somoShades");
  const availableSomoShadesLights = shadesNodes.flatMap((node) => {
    // viaUp and viaDown should not be bound to a specific load
    return (["via"] as const)
      .filter(
        (viaId) =>
          !shadesDevices.find((d) => d.nodeId === node.id && d.viaId === viaId),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: node.data[viaId].loadName,
      }));
  });

  const fanNodes = filterNodes(nodes, "somoFanContainer");
  const fanDevices = allDevices.filter((d) => d.type === "somoFan");
  const availableSomoFanLights = fanNodes.flatMap((node) => {
    // viaUp and viaDown should not be bound to a specific load
    return (["via"] as const)
      .filter(
        (viaId) =>
          !fanDevices.find((d) => d.nodeId === node.id && d.viaId === viaId),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: node.data[viaId].loadName,
      }));
  });

  const thermostatNodes = filterNodes(nodes, "somoThermostatContainer");
  const thermostatDevices = allDevices.filter(
    (d) => d.type === "somoThermostat",
  );
  const availableSomoThermostats = thermostatNodes.flatMap((node) => {
    return (["via"] as const)
      .filter(
        (viaId) =>
          !thermostatDevices.find(
            (d) => d.nodeId === node.id && d.viaId === viaId,
          ),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: node.data[viaId].hvacName,
      }));
  });

  const irControllerNodes = filterNodes(nodes, "somoIrControllerContainer");
  const irControllerDevices = allDevices.filter(
    (d) => d.type === "somoIrController",
  );
  const availableSomoIrControllers = irControllerNodes.flatMap((node) => {
    return (["via"] as const)
      .filter(
        (viaId) =>
          !irControllerDevices.find(
            (d) => d.nodeId === node.id && d.viaId === viaId,
          ),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: node.data[viaId].hvacName,
      }));
  });

  const canBusControllerNodes = filterNodes(nodes, "canbusControllerContainer");
  const zeroToTenVoltDimmerDevices = allDevices.filter(
    (d) => d.type === "zeroToTenVoltDimmer",
  );
  const availableZeroToTenVoltDimmers = canBusControllerNodes.flatMap(
    (node) => {
      return Object.values(node.data.zeroToTenVoltDimmers || {})
        .filter(
          (dimmer) =>
            !zeroToTenVoltDimmerDevices.find(
              (d) => d.nodeId === node.id && d.viaId === dimmer.id,
            ),
        )
        .map((dimmer) => ({
          id: dimmer.id,
          nodeId: node.id,
          name: dimmer.name,
          dimmingType: dimmer.dimmingType,
          useRelay: dimmer.useRelay,
          minBrightness: dimmer.minBrightness,
          maxBrightness: dimmer.maxBrightness,
          defaultDimmingSpeed: dimmer.defaultDimmingSpeed,
        }));
    },
  );

  const relayOutputDevices = allDevices.filter((d) => d.type === "relayOutput");
  const availableRelayOutputs = canBusControllerNodes.flatMap((node) => {
    return Object.values(node.data.relayOutputs || {})
      .filter(
        (relayOutput) =>
          !relayOutputDevices.find(
            (d) => d.nodeId === node.id && d.viaId === relayOutput.id,
          ),
      )
      .map((relayOutput) => ({
        label: `${node.data.title} - ${relayOutput.name}`,
        value: `${node.id}:${relayOutput.id}`,
      }));
  });

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={node.data.title}
              disabled={readOnly}
              onEndEdit={(value) => {
                reactFlowInstance?.updateNodeData(node.id, {
                  title: value,
                });
              }}
            />
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="direction">Direction</SettingsLabel>
            <Select
              value={node.data.direction ?? "left"}
              disabled={readOnly}
              onValueChange={(value) => {
                reactFlowInstance?.updateNodeData(node.id, {
                  direction: value,
                });
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select direction" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="right">Right</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
      <SettingsGroup>
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2">
          <span>Devices</span>

          {!readOnly && (
            <Tooltip>
              <TooltipTrigger asChild>
                <TooltipDropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="size-6 data-[state=open]:bg-gray-100"
                    >
                      <PlusIcon className="size-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="mr-5">
                    <DropdownMenuLabel>Add Device</DropdownMenuLabel>
                    <DropdownMenuItem
                      onClick={() => {
                        if (!yDevicesMap) {
                          return;
                        }
                        const index = Object.keys(yDevicesMap.entries).length;

                        // Create default dimming curve
                        const dimmingCurveArray = new Y.Array<any>();
                        dimmingCurveArray.insert(0, [
                          { x: 0, y: 0 },
                          { x: 0.5, y: 0.5 },
                          { x: 1, y: 1 },
                        ]);

                        const device: DmxDevice = {
                          id: randomId(),
                          type: "dmx",
                          name: "DMX Device",
                          icon: "ceilingLamp",
                          sortIndex: index,
                          fixtures: {},
                          isDimmable: true,
                          defaultDimmingSpeed: 0.2,
                          showLabel: true,
                          dimmingCurveType: "linear",
                        };

                        const deviceMap = objectToMap(device);
                        if (deviceMap) {
                          deviceMap.set("dimmingCurve", dimmingCurveArray);
                          yDevicesMap.set(device.id, deviceMap);
                        }
                      }}
                    >
                      DMX Device
                    </DropdownMenuItem>
                    {availableSomoLights.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>RF Switch Lights</DropdownMenuLabel>
                        {availableSomoLights.map((light) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: SomoSwitchDevice = {
                                  id: randomId(),
                                  type: "somoSwitch",
                                  viaId: light.id,
                                  nodeId: light.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                };
                                yDevicesMap.set(device.id, objectToMap(device));
                              }}
                              key={`${light.nodeId}-${light.id}`}
                            >
                              {light.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableSomoDimmerLights.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>RF Dimmer Lights</DropdownMenuLabel>
                        {availableSomoDimmerLights.map((light) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: SomoDimmerDevice = {
                                  id: randomId(),
                                  type: "somoDimmer",
                                  viaId: light.id as SomoDimmerDevice["viaId"],
                                  nodeId: light.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                };
                                yDevicesMap.set(device.id, objectToMap(device));
                              }}
                              key={`${light.nodeId}-${light.id}`}
                            >
                              {light.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableOutletDimmerLights.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>
                          Outlet Dimmer Lights
                        </DropdownMenuLabel>
                        {availableOutletDimmerLights.map((light) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: OutletDimmerDevice = {
                                  id: randomId(),
                                  type: "outletDimmer",
                                  viaId:
                                    light.id as OutletDimmerDevice["viaId"],
                                  nodeId: light.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                };
                                yDevicesMap.set(device.id, objectToMap(device));

                                if (!ydoc) {
                                  return;
                                }
                                addActionsToParentVias({
                                  ydoc,
                                  parentNodeId: light.nodeId,
                                  viaKeys: ["via", "viaUp", "viaDown"],
                                  onUpClickKey: "onUpClick",
                                  actionType: "control",
                                  makeControlSettings: (
                                    _viaKey,
                                    sortIndex,
                                  ) => ({
                                    id: `device-control-settings-${randomId()}`,
                                    deviceId: device.id,
                                    sortIndex,
                                    onValue: 100,
                                    offValue: 0,
                                    dimSpeed: device.defaultDimmingSpeed ?? 0.2,
                                    targetValue: 100,
                                  }),
                                });
                              }}
                              key={`${light.nodeId}-${light.id}`}
                            >
                              {light.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableSomoShadesLights.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Somo Shades</DropdownMenuLabel>
                        {availableSomoShadesLights.map((light) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: SomoShadesDevice = {
                                  id: randomId(),
                                  type: "somoShades",
                                  viaId: light.id as SomoShadesDevice["viaId"],
                                  nodeId: light.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                };
                                yDevicesMap.set(device.id, objectToMap(device));
                              }}
                              key={`${light.nodeId}-${light.id}`}
                            >
                              {light.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableSomoFanLights.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Somo Fan</DropdownMenuLabel>
                        {availableSomoFanLights.map((light) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: SomoFanDevice = {
                                  id: randomId(),
                                  type: "somoFan",
                                  viaId: light.id as SomoFanDevice["viaId"],
                                  nodeId: light.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                  defaultFanType: "onoff",
                                };
                                yDevicesMap.set(device.id, objectToMap(device));
                              }}
                              key={`${light.nodeId}-${light.id}`}
                            >
                              {light.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableSomoThermostats.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Thermostat</DropdownMenuLabel>
                        {availableSomoThermostats.map((thermostat) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: SomoThermostatDevice = {
                                  id: randomId(),
                                  type: "somoThermostat",
                                  viaId:
                                    thermostat.id as SomoThermostatDevice["viaId"],
                                  nodeId: thermostat.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                };
                                yDevicesMap.set(device.id, objectToMap(device));

                                if (!ydoc) {
                                  return;
                                }

                                // Add thermostat actions to the parent vias
                                addActionsToParentVias({
                                  ydoc,
                                  parentNodeId: thermostat.nodeId,
                                  viaKeys: [
                                    "viaUp",
                                    "viaDown",
                                    "modeCool",
                                    "modeHeat",
                                    "modeAuto",
                                    "modeFan",
                                    "fanAuto",
                                    "fanLow",
                                    "fanMedium",
                                    "fanHigh",
                                  ],
                                  actionType: "hvac",
                                  deviceId: device.id,
                                });
                              }}
                              key={`${thermostat.nodeId}-${thermostat.id}`}
                            >
                              {thermostat.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableSomoIrControllers.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>
                          IR HVAC Controller
                        </DropdownMenuLabel>
                        {availableSomoIrControllers.map((irController) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: SomoIrControllerDevice = {
                                  id: randomId(),
                                  type: "somoIrController",
                                  viaId:
                                    irController.id as SomoIrControllerDevice["viaId"],
                                  nodeId: irController.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                };
                                yDevicesMap.set(device.id, objectToMap(device));

                                if (!ydoc) {
                                  return;
                                }

                                // Add thermostat actions to the parent vias
                                addActionsToParentVias({
                                  ydoc,
                                  parentNodeId: irController.nodeId,
                                  viaKeys: [
                                    "viaUp",
                                    "viaDown",
                                    "modeCool",
                                    "modeHeat",
                                    "modeAuto",
                                    "modeFan",
                                    "fanAuto",
                                    "fanLow",
                                    "fanMedium",
                                    "fanHigh",
                                  ],
                                  actionType: "hvac",
                                  deviceId: device.id,
                                });
                              }}
                              key={`${irController.nodeId}-${irController.id}`}
                            >
                              {irController.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableZeroToTenVoltDimmers.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>0-10V Dimmers</DropdownMenuLabel>
                        {availableZeroToTenVoltDimmers.map((dimmer) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: ZeroToTenVoltDimmerDevice = {
                                  id: randomId(),
                                  type: "zeroToTenVoltDimmer",
                                  viaId: dimmer.id,
                                  nodeId: dimmer.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                  name: dimmer.name,
                                  dimmingType: dimmer.dimmingType,
                                  useRelay: dimmer.useRelay || false,
                                  minBrightness: dimmer.minBrightness || 0,
                                  maxBrightness: dimmer.maxBrightness || 100,
                                  defaultDimmingSpeed:
                                    dimmer.defaultDimmingSpeed || 0.2,
                                };
                                yDevicesMap.set(device.id, objectToMap(device));
                              }}
                              key={`${dimmer.nodeId}-${dimmer.id}`}
                            >
                              {dimmer.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableRelayOutputs.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Relay Outputs</DropdownMenuLabel>
                        {availableRelayOutputs.map((relayOutput) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                // Parse the value to get nodeId and relayOutputId
                                const [nodeId, relayOutputId] =
                                  relayOutput.value.split(":");

                                // Find the actual relay output from the node data
                                const canBusNode = canBusControllerNodes.find(
                                  (n) => n.id === nodeId,
                                );
                                const actualRelayOutput =
                                  canBusNode?.data.relayOutputs?.[
                                    relayOutputId
                                  ];

                                if (!actualRelayOutput) {
                                  return;
                                }

                                const newDevice: RelayOutputDevice = {
                                  id: randomId(),
                                  type: "relayOutput",
                                  name: actualRelayOutput.name,
                                  icon: actualRelayOutput.icon,
                                  nodeId: nodeId,
                                  viaId: actualRelayOutput.id,
                                  anchorPosition: "left",
                                  showLabel: true,
                                  sortIndex: Array.from(yDevicesMap.entries())
                                    .length,
                                };
                                yDevicesMap.set(
                                  newDevice.id,
                                  objectToMap(newDevice),
                                );
                              }}
                              key={relayOutput.value}
                            >
                              {relayOutput.label}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                  </DropdownMenuContent>
                </TooltipDropdownMenu>
              </TooltipTrigger>
              <TooltipContent>Add Device</TooltipContent>
            </Tooltip>
          )}
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {nodeDevices.map((device) =>
            match(device)
              .with({ type: "dmx" }, (device) => (
                <DMXDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "somoSwitch" }, (device) => (
                <SomoDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "somoDimmer" }, (device) => (
                <SomoDimmerDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "outletDimmer" }, (device) => (
                <OutletDimmerDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "somoShades" }, (device) => (
                <SomoShadesDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "somoFan" }, (device) => (
                <SomoFanDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "somoThermostat" }, (device) => (
                <SomoThermostatDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "somoIrController" }, (device) => (
                <SomoIrControllerDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "zeroToTenVoltDimmer" }, (device) => (
                <ZeroToTenVoltDimmerDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "relayOutput" }, (device) => (
                <RelayOutputDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              // Trying to perform an exhaustive check results in a type error:
              // `Type instantiation is excessively deep and possibly infinite`
              // => Use `otherwise` instead.
              .otherwise(() => null),
          )}
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}
