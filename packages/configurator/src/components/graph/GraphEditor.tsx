import { CanBusControllerAnchorNode } from "@/components/devices/canbus-controller/CanBusControllerAnchorNode";
import { CanBusControllerContainerNode } from "@/components/devices/canbus-controller/CanBusControllerContainerNode";
import { CanBusControllerSettings } from "@/components/devices/canbus-controller/CanBusControllerSettings";
import { ControllerAnchorNode } from "@/components/devices/controller/ControllerAnchorNode";
import { ControllerContainerNode } from "@/components/devices/controller/ControllerContainerNode";
import { ControllerSettings } from "@/components/devices/controller/ControllerSettings";
import { DoorSensorAnchorNode } from "@/components/devices/door-sensor/DoorSensorAnchorNode";
import { DoorSensorContainerNode } from "@/components/devices/door-sensor/DoorSensorContainerNode";
import { DoorSensorSettings } from "@/components/devices/door-sensor/DoorSensorSettings";
import { OutletDimmerAnchorNode } from "@/components/devices/outlet-dimmer/OutletDimmerAnchorNode";
import { OutletDimmerContainerNode } from "@/components/devices/outlet-dimmer/OutletDimmerContainerNode";
import { OutletDimmerSettings } from "@/components/devices/outlet-dimmer/OutletDimmerSettings";
import { PresenceSensorAnchorNode } from "@/components/devices/presence-sensor/PresenceSensorAnchorNode";
import { PresenceSensorContainerNode } from "@/components/devices/presence-sensor/PresenceSensorContainerNode";
import { PresenceSensorSettings } from "@/components/devices/presence-sensor/PresenceSensorSettings";
import { ServicePadAnchorNode } from "@/components/devices/service-pad/ServicePadAnchorNode";
import { ServicePadContainerNode } from "@/components/devices/service-pad/ServicePadContainerNode";
import { ServicePadSettings } from "@/components/devices/service-pad/ServicePadSettings";
import { SomoDimmerAnchorNode } from "@/components/devices/somo-dimmer/SomoDimmerAnchorNode";
import { SomoDimmerContainerNode } from "@/components/devices/somo-dimmer/SomoDimmerContainerNode";
import { SomoDimmerSettings } from "@/components/devices/somo-dimmer/SomoDimmerSettings";
import { SomoFanAnchorNode } from "@/components/devices/somo-fan/SomoFanAnchorNode";
import { SomoFanContainerNode } from "@/components/devices/somo-fan/SomoFanContainerNode";
import { SomoFanSettings } from "@/components/devices/somo-fan/SomoFanSettings";
import { SomoIrControllerAnchorNode } from "@/components/devices/somo-ir-controller/SomoIrControllerAnchorNode";
import { SomoIrControllerContainerNode } from "@/components/devices/somo-ir-controller/SomoIrControllerContainerNode";
import { SomoIrControllerSettings } from "@/components/devices/somo-ir-controller/SomoIrControllerSettings";
import { SomoShadesAnchorNode } from "@/components/devices/somo-shades/SomoShadesAnchorNode";
import { SomoShadesContainerNode } from "@/components/devices/somo-shades/SomoShadesContainerNode";
import { SomoShadesSettings } from "@/components/devices/somo-shades/SomoShadesSettings";
import { SomoSwitchAnchorNode } from "@/components/devices/somo-switch/SomoSwitchAnchorNode";
import { SomoSwitchContainerNode } from "@/components/devices/somo-switch/SomoSwitchContainerNode";
import { SomoSwitchSettings } from "@/components/devices/somo-switch/SomoSwitchSettings";
import { SomoThermostatAnchorNode } from "@/components/devices/somo-thermostat/SomoThermostatAnchorNode";
import { SomoThermostatContainerNode } from "@/components/devices/somo-thermostat/SomoThermostatContainerNode";
import { SomoThermostatSettings } from "@/components/devices/somo-thermostat/SomoThermostatSettings";
import { VirtualButtonAnchorNode } from "@/components/devices/virtual-button/VirtualButtonAnchorNode";
import { VirtualButtonContainerNode } from "@/components/devices/virtual-button/VirtualButtonContainerNode";
import { VirtualButtonSettings } from "@/components/devices/virtual-button/VirtualButtonSettings";
import { PaneContainer } from "@/components/ui/Layout";
import { useMeasure } from "@/hooks/useMeasure";
import { HocuspocusProvider } from "@hocuspocus/provider";
import { GraphNode, isNodeOrParentSelected } from "@somo/shared";
import {
  ConnectionLineType,
  Edge,
  EdgeChange,
  NodeChange,
  ReactFlow,
  ReactFlowInstance,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { Allotment } from "allotment";
import "allotment/dist/style.css";
import { useMemo, useRef, useState } from "react";
import { match } from "ts-pattern";
import * as Y from "yjs";
import { ConnectedControllers } from "./ConnectedControllers";
import {
  DocumentGraphCollaborationCursors,
  LiveUser,
} from "./DocumentGraphCollaborationCursors";
import { Graph } from "./Graph";
import { GraphContextMenu } from "./GraphContextMenu";
import {
  handleConnect,
  handleEdgesChange,
  handleNodesChange,
} from "./graphHandlers";
import { NodeList } from "./NodeList";
import { CommentNode } from "./nodes/CommentNode";
import { ImageNode } from "./nodes/ImageNode";
import { SectionNode } from "./nodes/SectionNode";
import { useReactFlowContext } from "./ReactFlowContext";
import { CommentSettings } from "./settings/CommentSettings";
import { ImageSettings } from "./settings/ImageSettings";
import { SectionSettings } from "./settings/SectionSettings";
import { useVisibleNodesAndEdges } from "./useVisibleNodesAndEdges";

const edgeTypes = {};
const nodeTypes = {
  comment: CommentNode,
  image: ImageNode,
  section: SectionNode,
  virtualButtonAnchor: VirtualButtonAnchorNode,
  virtualButtonContainer: VirtualButtonContainerNode,
  controllerAnchor: ControllerAnchorNode,
  controllerContainer: ControllerContainerNode,
  canbusControllerAnchor: CanBusControllerAnchorNode,
  canbusControllerContainer: CanBusControllerContainerNode,
  somoSwitchAnchor: SomoSwitchAnchorNode,
  somoSwitchContainer: SomoSwitchContainerNode,
  somoDimmerAnchor: SomoDimmerAnchorNode,
  somoDimmerContainer: SomoDimmerContainerNode,
  outletDimmerAnchor: OutletDimmerAnchorNode,
  outletDimmerContainer: OutletDimmerContainerNode,
  somoThermostatAnchor: SomoThermostatAnchorNode,
  somoThermostatContainer: SomoThermostatContainerNode,
  somoIrControllerAnchor: SomoIrControllerAnchorNode,
  somoIrControllerContainer: SomoIrControllerContainerNode,
  presenceSensorAnchor: PresenceSensorAnchorNode,
  presenceSensorContainer: PresenceSensorContainerNode,
  doorSensorAnchor: DoorSensorAnchorNode,
  doorSensorContainer: DoorSensorContainerNode,
  somoShadesAnchor: SomoShadesAnchorNode,
  somoShadesContainer: SomoShadesContainerNode,
  somoFanAnchor: SomoFanAnchorNode,
  somoFanContainer: SomoFanContainerNode,
  servicePadAnchor: ServicePadAnchorNode,
  servicePadContainer: ServicePadContainerNode,
};

export function GraphEditor({
  provider,
  reactFlowInstance,
  graph,
  liveUsers,
  trackedLiveUser,
  ydoc,
  setNodes,
  setEdges,
  orgId,
  controllerId,
}: {
  provider: HocuspocusProvider;
  reactFlowInstance: React.MutableRefObject<ReactFlowInstance<GraphNode> | null>;
  graph: Graph;
  liveUsers: LiveUser[];
  trackedLiveUser: LiveUser | undefined;
  ydoc: Y.Doc;
  setNodes: React.Dispatch<React.SetStateAction<GraphNode[]>>;
  setEdges: React.Dispatch<React.SetStateAction<Edge[]>>;
  orgId: string;
  controllerId: string;
}) {
  const { nodes, edges } = graph;

  // Sort nodes to ensure parents come before children
  const sortedNodes = [...nodes].sort((a, b) => {
    if (b.parentId === a.id) {
      return -1;
    }
    if (a.parentId === b.id) {
      return 1;
    }
    if (a.parentId && !b.parentId) {
      return 1;
    }
    if (!a.parentId && b.parentId) {
      return -1;
    }

    return (a.zIndex ?? 0) - (b.zIndex ?? 0);
  });

  const { visibleNodes, visibleEdges } = useVisibleNodesAndEdges({
    nodes: sortedNodes,
    edges,
  });

  const { readOnly } = useReactFlowContext();

  const processedVisibleNodes = useMemo(() => {
    if (!readOnly) {
      return visibleNodes;
    }
    return visibleNodes.map((node) => ({
      ...node,
      draggable: node.type === "comment",
    }));
  }, [visibleNodes, readOnly]);

  const selectedNodes = processedVisibleNodes.filter((node) =>
    isNodeOrParentSelected(processedVisibleNodes, node),
  );

  const [currentContextMenuPosition, setCurrentContextMenuPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [leftDrawerWidth, setLeftDrawerWidth] = useState<number | undefined>(
    200,
  );
  const [rightDrawerWidth, setRightDrawerWidth] = useState<number | undefined>(
    200,
  );
  const [centerDrawerWidth, setCenterDrawerWidth] = useState<
    number | undefined
  >(undefined);

  const graphContainerRef = useRef<HTMLDivElement>(null);

  const updateCursor = (event?: React.MouseEvent) => {
    if (!provider.awareness) {
      return;
    }
    if (!reactFlowInstance.current) {
      return;
    }

    const oldCursor = provider.awareness.getLocalState()?.["somo-cursor"] ?? {};
    const position = event
      ? reactFlowInstance.current.screenToFlowPosition(
          {
            x: event.clientX,
            y: event.clientY,
          },
          { snapToGrid: false },
        )
      : oldCursor.position;

    provider.awareness.setLocalStateField("somo-cursor", {
      ...oldCursor,
      ...position,
      dragging: false,
      viewport: reactFlowInstance.current.getViewport(),
      timestamp: Date.now(),
    });
  };

  const [containerRef, containerDimensions] = useMeasure();
  const enableTouchGestures = containerDimensions.width < 1000;
  const showLeftPanel = !enableTouchGestures;

  return (
    <div className="w-full flex-1" ref={containerRef}>
      <Allotment
        separator={false}
        onDragEnd={(sizes) => {
          if (showLeftPanel) {
            setLeftDrawerWidth(sizes[0]);
            setCenterDrawerWidth(sizes[1]);
            setRightDrawerWidth(sizes[2]);
          } else {
            setCenterDrawerWidth(sizes[0]);
            setRightDrawerWidth(sizes[1]);
          }
        }}
      >
        {showLeftPanel && (
          <Allotment.Pane
            maxSize={500}
            minSize={200}
            className="absolute inset-0 flex flex-row py-2 pr-1 pl-2"
            preferredSize={leftDrawerWidth}
          >
            <NodeList visibleNodes={processedVisibleNodes} nodes={nodes} />
          </Allotment.Pane>
        )}
        <Allotment.Pane
          minSize={showLeftPanel ? 500 : 300}
          className="absolute inset-0 flex flex-row py-2 px-1"
          preferredSize={centerDrawerWidth}
        >
          <PaneContainer
            ref={graphContainerRef}
            className="flex flex-col flex-grow"
          >
            <GraphContextMenu
              orgId={orgId}
              controllerId={controllerId}
              reactFlowInstance={reactFlowInstance.current}
              currentContextMenuPosition={currentContextMenuPosition}
              setCurrentContextMenuPosition={setCurrentContextMenuPosition}
              selectedNodes={selectedNodes}
            >
              {/* Show a helpful message if we have nothing to render */}
              {processedVisibleNodes.length === 0 && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-gray-500 text-md">
                    Right-click here to start adding items
                  </div>
                </div>
              )}

              <ReactFlow
                onMoveEnd={() => updateCursor()}
                onMove={() => updateCursor()}
                onMouseMove={updateCursor}
                onNodeDragStart={updateCursor}
                onNodeDrag={updateCursor}
                onNodeDragStop={updateCursor}
                onSelectionChange={({ nodes, edges }) => {
                  if (!provider.awareness) {
                    return;
                  }

                  // don't broadcast selection changes if we are tracking a user
                  if (trackedLiveUser) {
                    return;
                  }

                  const oldCursor =
                    provider.awareness.getLocalState()?.["somo-cursor"] ?? {};

                  provider.awareness.setLocalStateField("somo-cursor", {
                    ...oldCursor,
                    selectedNodes: nodes.map(({ id }) => id),
                    selectedEdges: edges.map(({ id }) => id),
                  });
                }}
                onViewportChange={(viewport) => {
                  if (!provider.awareness) {
                    return;
                  }

                  const oldCursor =
                    provider.awareness.getLocalState()?.["somo-cursor"] ?? {};

                  provider.awareness.setLocalStateField("somo-cursor", {
                    ...oldCursor,
                    viewport,
                  });
                }}
                elevateEdgesOnSelect={false}
                elevateNodesOnSelect={false}
                nodes={processedVisibleNodes}
                edges={visibleEdges}
                edgeTypes={edgeTypes}
                nodeTypes={nodeTypes}
                proOptions={{ hideAttribution: true }}
                fitView
                className="touch-flow"
                onInit={(instance) => {
                  reactFlowInstance.current = instance;
                }}
                connectionLineStyle={{
                  stroke: "#4D9FC9",
                  strokeWidth: 2,
                }}
                connectionLineType={ConnectionLineType.SimpleBezier}
                panOnDrag={enableTouchGestures || [1]}
                selectionOnDrag={!enableTouchGestures}
                panOnScroll
                multiSelectionKeyCode="Shift"
                selectNodesOnDrag={true}
                nodesDraggable={true}
                onConnect={(connection) => {
                  handleConnect(connection, graph, ydoc);
                }}
                onEdgesChange={(changes: EdgeChange<Edge>[]) =>
                  handleEdgesChange(changes, graph, setEdges, ydoc)
                }
                onNodesChange={(changes: NodeChange<GraphNode>[]) =>
                  handleNodesChange(changes, graph, setNodes, ydoc)
                }
              >
                <DocumentGraphCollaborationCursors
                  liveUsers={liveUsers}
                  graphContainerRef={graphContainerRef}
                  reactFlowInstance={reactFlowInstance.current}
                />
              </ReactFlow>
            </GraphContextMenu>
          </PaneContainer>
        </Allotment.Pane>
        <Allotment.Pane
          maxSize={showLeftPanel ? 800 : 500}
          minSize={300}
          className="absolute inset-0 flex flex-row py-2 pr-2 pl-1"
          preferredSize={rightDrawerWidth}
        >
          <PaneContainer className="flex flex-col flex-shrink-0 divide-y-8 divide-gray-300 dark:divide-gray-800">
            {selectedNodes.map((node) => {
              return match(node)
                .with({ type: "section" }, (node) => (
                  <SectionSettings key={node.id} node={node} />
                ))
                .with({ type: "image" }, (node) => (
                  <ImageSettings key={node.id} node={node} />
                ))
                .with({ type: "comment" }, (node) => (
                  <CommentSettings key={node.id} node={node} />
                ))
                .with({ type: "virtualButtonContainer" }, (node) => (
                  <VirtualButtonSettings key={node.id} node={node} />
                ))
                .with({ type: "controllerContainer" }, (node) => (
                  <ControllerSettings key={node.id} node={node} />
                ))
                .with({ type: "canbusControllerContainer" }, (node) => (
                  <CanBusControllerSettings key={node.id} node={node} />
                ))
                .with({ type: "somoSwitchContainer" }, (node) => (
                  <SomoSwitchSettings key={node.id} node={node} />
                ))
                .with({ type: "somoDimmerContainer" }, (node) => (
                  <SomoDimmerSettings key={node.id} node={node} />
                ))
                .with({ type: "outletDimmerContainer" }, (node) => (
                  <OutletDimmerSettings key={node.id} node={node} />
                ))
                .with({ type: "presenceSensorContainer" }, (node) => (
                  <PresenceSensorSettings key={node.id} node={node} />
                ))
                .with({ type: "doorSensorContainer" }, (node) => (
                  <DoorSensorSettings key={node.id} node={node} />
                ))
                .with({ type: "somoThermostatContainer" }, (node) => (
                  <SomoThermostatSettings key={node.id} node={node} />
                ))
                .with({ type: "somoIrControllerContainer" }, (node) => (
                  <SomoIrControllerSettings key={node.id} node={node} />
                ))
                .with({ type: "somoShadesContainer" }, (node) => (
                  <SomoShadesSettings key={node.id} node={node} />
                ))
                .with({ type: "somoFanContainer" }, (node) => (
                  <SomoFanSettings key={node.id} node={node} />
                ))
                .with({ type: "servicePadContainer" }, (node) => (
                  <ServicePadSettings key={node.id} node={node} />
                ))
                .with(
                  { type: "virtualButtonAnchor" },
                  { type: "controllerAnchor" },
                  { type: "canbusControllerAnchor" },
                  { type: "somoSwitchAnchor" },
                  { type: "somoDimmerAnchor" },
                  { type: "outletDimmerAnchor" },
                  { type: "presenceSensorAnchor" },
                  { type: "doorSensorAnchor" },
                  { type: "somoThermostatAnchor" },
                  { type: "somoIrControllerAnchor" },
                  { type: "somoShadesAnchor" },
                  { type: "somoFanAnchor" },
                  { type: "servicePadAnchor" },
                  () => null,
                )
                .exhaustive();
            })}
            {selectedNodes.length === 0 && (
              <ConnectedControllers
                orgId={orgId}
                roomId={controllerId}
                graph={graph}
              />
            )}
          </PaneContainer>
        </Allotment.Pane>
      </Allotment>
    </div>
  );
}
