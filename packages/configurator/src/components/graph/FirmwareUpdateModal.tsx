import { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Upload, CheckCircle, XCircle, Loader2 } from "lucide-react";
import { UseBasestationSerialReturn } from "@/hooks/useBasestationSerial";

interface FirmwareUpdateModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  basestationSerial: UseBasestationSerialReturn;
}

type UpdateState = "idle" | "uploading" | "success" | "error";

export function FirmwareUpdateModal({
  open,
  onOpenChange,
  basestationSerial,
}: FirmwareUpdateModalProps) {
  const [updateState, setUpdateState] = useState<UpdateState>("idle");
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Reset state when modal opens
  useEffect(() => {
    if (open) {
      setUpdateState("idle");
      setProgress(0);
      setError(null);
      setSelectedFile(null);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  }, [open]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.name.endsWith(".bin")) {
        setSelectedFile(file);
        setError(null);
      } else {
        setError("Please select a .bin firmware file");
        setSelectedFile(null);
      }
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setError("Please select a firmware file");
      return;
    }

    setUpdateState("uploading");
    setProgress(0);
    setError(null);

    try {
      // If we're already in bootloader mode, skip entering bootloader
      if (!basestationSerial.isBootloader) {
        // Step 1: Enter bootloader mode
        await basestationSerial.enterBootloader();

        // Step 2: Wait for device to disconnect and re-enumerate
        // The device will disconnect when entering bootloader mode
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      // Step 3: Upload firmware with progress tracking
      await basestationSerial.uploadFirmware(selectedFile, (percent) => {
        setProgress(percent);
      });

      setUpdateState("success");

      // The uploadFirmware function handles reconnection
    } catch (err) {
      console.error("Firmware update failed:", err);
      setError(err instanceof Error ? err.message : "Firmware update failed");
      setUpdateState("error");
    }
  };

  const handleClose = () => {
    if (updateState !== "uploading") {
      setUpdateState("idle");
      setProgress(0);
      setError(null);
      setSelectedFile(null);
      onOpenChange(false);
    }
  };

  const getProgressMessage = () => {
    if (progress < 20) {
      return "Erasing flash memory...";
    }
    if (progress < 80) {
      return "Writing firmware...";
    }
    if (progress < 100) {
      return "Verifying firmware...";
    }
    return "Update complete!";
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {basestationSerial.isBootloader
              ? "Bootloader Detected - Upload Firmware"
              : "Update Basestation Firmware"}
          </DialogTitle>
          <DialogDescription>
            {basestationSerial.isBootloader
              ? "The device is in bootloader mode. Select a firmware file to upload."
              : "Upload a new firmware file to update your basestation. The device will restart automatically after the update."}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {updateState === "idle" && (
            <>
              <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-6">
                <Upload className="mb-4 h-12 w-12 text-gray-400" />
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".bin"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  variant="outline"
                >
                  Choose Firmware File
                </Button>
                {selectedFile && (
                  <p className="mt-2 text-sm text-gray-600">
                    Selected: {selectedFile.name}
                  </p>
                )}
              </div>

              {error && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
                <Button onClick={handleUpload} disabled={!selectedFile}>
                  Start Update
                </Button>
              </div>
            </>
          )}

          {updateState === "uploading" && (
            <>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>{getProgressMessage()}</span>
                  <span>{Math.floor(progress)}%</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>

              <Alert>
                <Loader2 className="h-4 w-4 animate-spin" />
                <AlertDescription>
                  Please do not disconnect the device or close this window.
                </AlertDescription>
              </Alert>
            </>
          )}

          {updateState === "success" && (
            <>
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription>
                  Firmware update completed successfully! The device will
                  restart automatically.
                </AlertDescription>
              </Alert>

              <div className="flex justify-end">
                <Button onClick={handleClose}>Close</Button>
              </div>
            </>
          )}

          {updateState === "error" && (
            <>
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={handleClose}>
                  Close
                </Button>
                <Button
                  onClick={() => {
                    setUpdateState("idle");
                    setError(null);
                    setProgress(0);
                    setSelectedFile(null);
                    // Reset the file input
                    if (fileInputRef.current) {
                      fileInputRef.current.value = "";
                    }
                  }}
                >
                  Try Again
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
