import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Cpu, HardDrive, Clock, Activity } from "lucide-react";

export interface SystemStats {
  activeTasks: number;
  configuredHeap: {
    bytes: number;
    kb: number;
  };
  tasks: Array<{
    name: string;
    core: string;
    priority: number;
    cpuPercent: number;
    stackFreeWords: number;
    freeBytes: number;
    freeKb: number;
    hasEstimateWarning: boolean;
  }>;
  memoryUsage: {
    totalStackAllocated: { bytes: number; kb: number };
    totalStackUsed: { bytes: number; kb: number };
    stackUtilization: number;
  };
  cpuUsage: {
    totalCpuAccounted: number;
    systemUptime: number;
  };
}

interface SystemStatsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  stats: SystemStats | null;
  loading: boolean;
  error: string | null;
}

export function SystemStatsModal({
  open,
  onOpenChange,
  stats,
  loading,
  error,
}: SystemStatsModalProps) {
  const formatBytes = (bytes: number) => {
    if (bytes >= 1024) {
      return `${bytes.toLocaleString()}B (${(bytes / 1024).toFixed(1)}KB)`;
    }
    return `${bytes.toLocaleString()}B`;
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const getCpuColor = (cpuPercent: number) => {
    if (cpuPercent > 50) {
      return "text-red-600";
    }
    if (cpuPercent > 20) {
      return "text-yellow-600";
    }
    return "text-green-600";
  };

  const getStackColor = (freeKb: number) => {
    if (freeKb < 1) {
      return "text-red-600";
    }
    if (freeKb < 5) {
      return "text-yellow-600";
    }
    return "text-green-600";
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Activity className="size-5" />
            System Statistics
          </DialogTitle>
          <DialogDescription>
            Real-time system performance and memory usage
          </DialogDescription>
        </DialogHeader>

        {loading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-sm text-gray-600">Loading stats...</span>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="text-sm text-red-800">
              <strong>Error:</strong> {error}
            </div>
          </div>
        )}

        {stats && (
          <div className="space-y-6">
            {/* Overview Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Active Tasks
                  </CardTitle>
                  <Cpu className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.activeTasks}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    System Heap
                  </CardTitle>
                  <HardDrive className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {stats.configuredHeap.kb}KB
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {stats.configuredHeap.bytes.toLocaleString()} bytes
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    CPU Usage
                  </CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {stats.cpuUsage.totalCpuAccounted.toFixed(1)}%
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Uptime</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-lg font-bold">
                    {formatUptime(stats.cpuUsage.systemUptime)}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Memory Usage Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Memory Usage Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <div className="text-sm font-medium text-gray-600">
                      Total Stack Allocated
                    </div>
                    <div className="text-lg font-semibold">
                      {formatBytes(stats.memoryUsage.totalStackAllocated.bytes)}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-600">
                      Total Stack Used
                    </div>
                    <div className="text-lg font-semibold">
                      {formatBytes(stats.memoryUsage.totalStackUsed.bytes)}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-600">
                      Stack Utilization
                    </div>
                    <div className="text-lg font-semibold">
                      {stats.memoryUsage.stackUtilization.toFixed(1)}%
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tasks Table */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Task Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2 font-medium text-sm">
                          Task Name
                        </th>
                        <th className="text-left p-2 font-medium text-sm">
                          Core
                        </th>
                        <th className="text-left p-2 font-medium text-sm">
                          Priority
                        </th>
                        <th className="text-left p-2 font-medium text-sm">
                          CPU %
                        </th>
                        <th className="text-left p-2 font-medium text-sm">
                          Stack Free
                        </th>
                        <th className="text-left p-2 font-medium text-sm">
                          Free Memory
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {stats.tasks.map((task, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="p-2 font-medium">
                            {task.name}
                            {task.hasEstimateWarning && (
                              <span
                                className="ml-1 text-orange-500"
                                title="Stack size estimate may be incorrect"
                              >
                                *
                              </span>
                            )}
                          </td>
                          <td className="p-2">
                            <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">
                              {task.core}
                            </span>
                          </td>
                          <td className="p-2">{task.priority}</td>
                          <td className="p-2">
                            <span
                              className={`font-semibold ${getCpuColor(task.cpuPercent)}`}
                            >
                              {task.cpuPercent.toFixed(1)}%
                            </span>
                          </td>
                          <td className="p-2 font-mono text-sm">
                            {task.stackFreeWords.toLocaleString()} words
                          </td>
                          <td className="p-2">
                            <span
                              className={`font-semibold ${getStackColor(task.freeKb)}`}
                            >
                              {formatBytes(task.freeBytes)}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* Notes */}
            <div className="text-xs text-gray-500 space-y-1 bg-gray-50 p-3 rounded-lg">
              <div>
                <strong>Notes:</strong>
              </div>
              <div>• Stack free shows remaining unused stack space</div>
              <div>
                • Lower values indicate more stack usage (closer to overflow)
              </div>
              <div>• Core affinity: 0=Core0, 1=Core1, Both=Either core</div>
              <div>• * = Stack size estimate may be incorrect</div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
