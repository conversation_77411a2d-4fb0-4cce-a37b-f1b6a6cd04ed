import {
  RelayOutputButton,
  RelayOutputButtonSimulator,
} from "@/components/devices/canbus-controller/RelayOutputButton";
import {
  ZeroToTenVoltDimmerButton,
  ZeroToTenVoltDimmerButtonSimulator,
} from "@/components/devices/canbus-controller/ZeroToTenVoltDimmerButton";
import {
  DmxButton,
  DmxButtonSimulator,
} from "@/components/devices/dmx/DmxButton";
import {
  OutletDimmerButton,
  OutletDimmerButtonSimulator,
} from "@/components/devices/outlet-dimmer/OutletDimmerButton";
import {
  SomoDimmerButton,
  SomoDimmerButtonSimulator,
} from "@/components/devices/somo-dimmer/SomoDimmerButton";
import {
  SomoFanButton,
  SomoFanButtonSimulator,
} from "@/components/devices/somo-fan/SomoFanButton";
import {
  SomoIrControllerButton,
  SomoIrControllerButtonSimulator,
} from "@/components/devices/somo-ir-controller/SomoIrControllerButton";
import {
  SomoShad<PERSON><PERSON><PERSON>on,
  SomoShadesButtonSimulator,
} from "@/components/devices/somo-shades/SomoShadesButton";
import {
  SomoSwitchButton,
  SomoSwitchButtonSimulator,
} from "@/components/devices/somo-switch/SomoSwitchButton";
import {
  SomoThermostatButton,
  SomoThermostatButtonSimulator,
} from "@/components/devices/somo-thermostat/SomoThermostatButton";
import { cn } from "@/lib/classNames";
import { Device } from "@somo/shared";
import { NodeProps, NodeResizer, useUpdateNodeInternals } from "@xyflow/react";
import equal from "fast-deep-equal/react";
import React, { useEffect, useMemo } from "react";
import { match } from "ts-pattern";
import { z } from "zod/v4";

export const SectionNode = React.memo(RegularNode, equal);
export const SectionNodeSimulator = React.memo(SimulatorNode, equal);

const Direction = z.enum(["left", "right"]);
type Direction = z.infer<typeof Direction>;

export const SectionNodeData = z.object({
  title: z.string(),
  direction: Direction.optional(),
  devices: z.record(z.string(), Device),
});
export type SectionNodeData = z.infer<typeof SectionNodeData>;

interface Props extends NodeProps {
  data: SectionNodeData;
  type: "section";
}

function RegularNode(props: Props) {
  return (
    <BaseNode
      {...props}
      renderDevice={(device, direction) =>
        match(device)
          .with({ type: "dmx" }, (device) => (
            <DmxButton key={device.id} device={device} direction={direction} />
          ))
          .with({ type: "somoSwitch" }, (device) => (
            <SomoSwitchButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoDimmer" }, (device) => (
            <SomoDimmerButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "outletDimmer" }, (device) => (
            <OutletDimmerButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoShades" }, (device) => (
            <SomoShadesButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoFan" }, (device) => (
            <SomoFanButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoThermostat" }, (device) => (
            <SomoThermostatButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoIrController" }, (device) => (
            <SomoIrControllerButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "zeroToTenVoltDimmer" }, (device) => (
            <ZeroToTenVoltDimmerButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "relayOutput" }, (device) => (
            <RelayOutputButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          // Trying to perform an exhaustive check results in a type error:
          // `Type instantiation is excessively deep and possibly infinite`
          // => Use `otherwise` instead.
          .otherwise(() => null)
      }
    />
  );
}

function SimulatorNode(props: Props) {
  return (
    <BaseNode
      {...props}
      className="nodrag"
      renderDevice={(device, direction) =>
        match(device)
          .with({ type: "dmx" }, (device) => (
            <DmxButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoSwitch" }, (device) => (
            <SomoSwitchButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoDimmer" }, (device) => (
            <SomoDimmerButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "outletDimmer" }, (device) => (
            <OutletDimmerButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoShades" }, (device) => (
            <SomoShadesButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoFan" }, (device) => (
            <SomoFanButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoThermostat" }, (device) => (
            <SomoThermostatButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoIrController" }, (device) => (
            <SomoIrControllerButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "zeroToTenVoltDimmer" }, (device) => (
            <ZeroToTenVoltDimmerButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "relayOutput" }, (device) => (
            <RelayOutputButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          // Trying to perform an exhaustive check results in a type error:
          // `Type instantiation is excessively deep and possibly infinite`
          // => Use `otherwise` instead.
          .otherwise(() => null)
      }
    />
  );
}

function BaseNode({
  selected,
  draggable,
  id,
  data,
  dragging,
  renderDevice,
  className = "",
}: Props & {
  renderDevice: (device: Device, direction: Direction) => React.ReactNode;
  className?: string;
}) {
  const direction = data.direction ?? "right";

  const updateNodeInternals = useUpdateNodeInternals();

  useEffect(() => {
    const timeout = setTimeout(() => {
      updateNodeInternals(id);
    }, 1);
    return () => clearTimeout(timeout);
  }, [updateNodeInternals, id]);

  const sortedDevices = useMemo(() => {
    return Object.values(data.devices ?? {}).sort(
      (a, b) => a.sortIndex - b.sortIndex,
    );
  }, [data.devices]);

  useEffect(() => {
    updateNodeInternals(id);
  }, [sortedDevices, updateNodeInternals, id, selected, dragging, direction]);

  return (
    <>
      {selected && draggable && <NodeResizer minWidth={60} minHeight={200} />}
      <div className={cn("absolute inset-[4px]", className)}>
        <div
          className={cn(
            "overflow-visible z-0",
            "absolute inset-0 pointer-events-none",
            direction === "right" && "border-r border-gray-400 items-end",
            direction === "left" && "border-l border-gray-400",
          )}
        >
          <div
            className={cn(
              "px-2 text-sm font-medium text-[#919191]",
              direction === "right" && "text-right w-full",
            )}
          >
            {data.title}
          </div>
          <div
            className={cn(
              "w-full flex flex-row flex-wrap px-2 mt-2 gap-2",
              direction === "right" && "justify-end ",
            )}
          >
            {sortedDevices.map((device) => renderDevice(device, direction))}
          </div>
        </div>
      </div>
    </>
  );
}
