import { EditorComponent } from "@/components/EditorComponent";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/classNames";
import { useUser } from "@clerk/clerk-react";
import { type JSONContent } from "@tiptap/react";
import { NodeProps } from "@xyflow/react";
import { formatDistanceToNow } from "date-fns";
import equal from "fast-deep-equal/react";
import React, { useMemo } from "react";
import * as Y from "yjs";
import { z } from "zod/v4";
import { getNodeData } from "../Graph";
import { useReactFlowContext } from "../ReactFlowContext";

// We don't have a Zod schema for tiptap's JSONContent, so go with any
const JSONContent = z.any().describe("@tiptap/react JSONContent");

export const CommentNodeReply = z.object({
  id: z.string(),
  text: JSONContent,
  authorId: z.string(),
  author: z.string(),
  authorAvatar: z.string().optional(),
  createdAt: z.number(),
});
export type CommentNodeReply = z.infer<typeof CommentNodeReply>;

export const CommentNodeData = z.object({
  componentParentId: z.string().optional(),
  text: JSONContent,
  authorId: z.string(),
  author: z.string(),
  authorAvatar: z.string().optional(),
  createdAt: z.number(),
  replies: z.array(CommentNodeReply),
});
export type CommentNodeData = z.infer<typeof CommentNodeData>;

const getInitials = (name: string) => {
  const nameParts = name.split(" ");
  const firstName = nameParts[0];
  const lastInitial = nameParts[nameParts.length - 1][0];
  return `${firstName[0]}${lastInitial}`.toUpperCase();
};

export interface CommentNodeProps extends NodeProps {
  data: CommentNodeData;
  type: "comment";
}

function InternalCommentNode({
  data,
  selected,
  id,
  dragging,
}: CommentNodeProps) {
  const { ydoc } = useReactFlowContext();
  const { user } = useUser();

  const commentTextFragment = useMemo(() => {
    const dataMap = getNodeData(ydoc, id);
    const textFragment = dataMap?.get("text") as Y.XmlFragment | undefined;
    if (dataMap && !textFragment) {
      const newTextFragment = new Y.XmlFragment();
      dataMap.set("text", newTextFragment);
      return newTextFragment;
    }
    return textFragment;
  }, [ydoc, id]);

  return (
    <div className="flex size-10 items-end justify-start relative">
      <div
        className={cn(
          "absolute size-10 bg-gray-100 rounded-tl-[20px] rounded-br-[20px] rounded-tr-[20px] shadow-md transition-all duration-100 p-[2px] border border-white",
          "hover:w-[280px] hover:h-auto hover:p-[6px] group",
          (dragging || selected) && "w-[280px] h-auto p-[6px] group",
          selected &&
            "outline outline-blue-500 outline-offset-[2px] outline-1 ",
        )}
      >
        <div className="flex flex-row">
          <Avatar className="size-9">
            <AvatarFallback className="text-sm">
              {getInitials(data.author)}
            </AvatarFallback>
            <AvatarImage src={data.authorAvatar} className="rounded-full" />
          </Avatar>
          <div
            className={cn(
              "flex-col ml-2 hidden group-hover:flex",
              (dragging || selected) && "flex",
            )}
          >
            <div className="flex flex-row">
              <div className="text-sm font-semibold truncate">
                {data.author}
              </div>
              <div className="text-sm text-gray-500 ml-2 flex-shrink-0">
                {formatDistanceToNow(data.createdAt, {
                  addSuffix: true,
                })}
              </div>
            </div>

            {commentTextFragment && (
              <EditorComponent
                fragment={commentTextFragment}
                className="text-sm nodrag cursor-text"
                readOnly={data.authorId !== user?.id || dragging || !selected}
                placeholder="Write a comment..."
              />
            )}

            <div className="text-sm text-gray-500">
              {data?.replies?.length === 1
                ? "1 reply"
                : data?.replies?.length === 0
                  ? "No replies"
                  : `${data?.replies?.length} replies`}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export const CommentNode = React.memo(InternalCommentNode, equal);
