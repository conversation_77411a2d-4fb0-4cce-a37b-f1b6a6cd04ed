import { GraphNode } from "@somo/shared";
import { Edge } from "@xyflow/react";
import * as Y from "yjs";
import { z } from "zod/v4";

export type Graph = {
  nodes: GraphNode[];
  edges: Edge[];
};

const YMap = z.instanceof(Y.Map);
const YMapChild = YMap.or(z.undefined());

function getGraph(yDoc: Y.Doc | null) {
  return yDoc ? YMap.parse(yDoc.getMap("graph")) : undefined;
}

export function getNodes(yDoc: Y.Doc | null) {
  return YMapChild.parse(getGraph(yDoc)?.get("nodes"));
}

export function getNode(yDoc: Y.Doc | null, nodeId: string) {
  return YMapChild.parse(getNodes(yDoc)?.get(nodeId));
}

export function getNodeData(yDoc: Y.Doc | null, nodeId: string) {
  return YMapChild.parse(getNode(yDoc, nodeId)?.get("data"));
}
