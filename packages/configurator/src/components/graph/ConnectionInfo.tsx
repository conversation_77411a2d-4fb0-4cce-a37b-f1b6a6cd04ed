import { useBasestationSerial } from "@/hooks/useBasestationSerial";
import { getConnectionData } from "@/lib/connectionStorage";
import { useEffect, useState } from "react";

export function ConnectionInfo() {
  const { isConnected, qrCode, firmwareVersion } = useBasestationSerial();
  const [connectionData, setConnectionData] =
    useState<ReturnType<typeof getConnectionData>>(null);

  useEffect(() => {
    if (qrCode) {
      const data = getConnectionData(qrCode);
      setConnectionData(data);
    } else {
      setConnectionData(null);
    }
  }, [qrCode]);

  if (!isConnected || !qrCode) {
    return null;
  }

  return (
    <div className="p-4 border rounded-md bg-gray-50">
      <h3 className="font-semibold mb-2">Connection Info</h3>
      <div className="space-y-1 text-sm">
        <div>
          <span className="font-medium">QR Code:</span> {qrCode}
        </div>
        <div>
          <span className="font-medium">Firmware:</span>{" "}
          {firmwareVersion || "Unknown"}
        </div>
        {connectionData && (
          <>
            {connectionData.ssid && (
              <div>
                <span className="font-medium">WiFi SSID:</span>{" "}
                {connectionData.ssid}
              </div>
            )}
            {connectionData.rfConfig && (
              <div>
                <span className="font-medium">RF Config:</span> Channel{" "}
                {connectionData.rfConfig.channel}, Network{" "}
                {connectionData.rfConfig.network}
              </div>
            )}
            <div>
              <span className="font-medium">Last Connected:</span>{" "}
              {new Date(connectionData.lastConnected).toLocaleString()}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
