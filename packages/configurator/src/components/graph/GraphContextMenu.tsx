import { ControllerIcon } from "@/components/devices/controller/ControllerIcon";
import { DoorSensorIcon } from "@/components/devices/door-sensor/DoorSensorIcon";
import { OutletDimmerIcon } from "@/components/devices/outlet-dimmer/OutletDimmerIcon";
import { PresenceSensorIcon } from "@/components/devices/presence-sensor/PresenceSensorIcon";
import { ServicePadIcon } from "@/components/devices/service-pad/ServicePadIcon";
import { SomoDimmerIcon } from "@/components/devices/somo-dimmer/SomoDimmerIcon";
import { SomoFanIcon } from "@/components/devices/somo-fan/SomoFanIcon";
import { SomoIrControllerIcon } from "@/components/devices/somo-ir-controller/SomoIrControllerIcon";
import { SomoShadesIcon } from "@/components/devices/somo-shades/SomoShadesIcon";
import { SomoSwitchIcon } from "@/components/devices/somo-switch/SomoSwitchIcon";
import { SomoThermostatIcon } from "@/components/devices/somo-thermostat/SomoThermostatIcon";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { useUser } from "@clerk/clerk-react";
import { DmxDevice, GraphNode, GraphNodesByType, randomId } from "@somo/shared";
import { Edge, ReactFlowInstance } from "@xyflow/react";
import { useFlags } from "launchdarkly-react-client-sdk";
import {
  ArrowDownFromLine,
  ArrowUpFromLine,
  ImageIcon,
  LayoutPanelTop,
  MessageSquareIcon,
  PowerIcon,
  Trash2,
} from "lucide-react";
import React from "react";
import { CanBusControllerIcon } from "../devices/canbus-controller/CanBusControllerIcon";
import { useRoom } from "./useRoom";
import { deleteSelectedNodes } from "./utils/deleteNodes";

type GraphContextMenuProps = {
  orgId: string;
  controllerId: string;
  reactFlowInstance: ReactFlowInstance<GraphNode> | null;
  currentContextMenuPosition: { x: number; y: number } | null;
  setCurrentContextMenuPosition: React.Dispatch<
    React.SetStateAction<{ x: number; y: number } | null>
  >;
  selectedNodes: GraphNode[];
  children: React.ReactNode;
};

export const GraphContextMenu: React.FC<GraphContextMenuProps> = ({
  orgId,
  controllerId,
  reactFlowInstance,
  currentContextMenuPosition,
  setCurrentContextMenuPosition,
  selectedNodes,
  children,
}) => {
  const { user } = useUser();
  const { data: room } = useRoom({ orgId, controllerId });
  const flags = useFlags();
  const {
    rfSwitch,
    virtualButton,
    wiredSwitch,
    canbo,
    rfDimmer,
    outletDimmer,
    presenceSensor,
    doorSensor,
    thermostat,
    irController,
    shades,
    fan,
    servicePad,
  } = flags;

  return (
    <ContextMenu modal={true}>
      <ContextMenuTrigger
        className="absolute inset-0"
        disabled={false}
        onContextMenu={(event) => {
          setCurrentContextMenuPosition({
            x: event.clientX,
            y: event.clientY,
          });
        }}
      >
        {children}
      </ContextMenuTrigger>
      <ContextMenuContent>
        {!room?.readOnly && (
          <>
            <MenuItem
              label="Background"
              Icon={ImageIcon}
              reactFlowInstance={reactFlowInstance}
              currentContextMenuPosition={currentContextMenuPosition}
              addNodes={({ center }) => [
                {
                  id: randomId(),
                  type: "image",
                  data: { opacity: 0.3, imageUrl: "/<EMAIL>" },
                  position: center,
                  width: 598,
                  height: 452,
                  selected: true,
                },
              ]}
            />
            <MenuItem
              label="Section"
              Icon={LayoutPanelTop}
              reactFlowInstance={reactFlowInstance}
              currentContextMenuPosition={currentContextMenuPosition}
              addNodes={({ center }) => {
                center.y = center.y - 300 + 5;
                center.x = center.x - 300 + 5;

                const device: DmxDevice = {
                  id: randomId(),
                  type: "dmx",
                  name: "Device",
                  icon: "ceilingLamp",
                  sortIndex: 0,
                  fixtures: {},
                  defaultDimmingSpeed: 0.2,
                  isDimmable: true,
                  showLabel: true,
                };

                return [
                  {
                    id: randomId(),
                    type: "section",
                    data: {
                      title: "Section",
                      direction: "left",
                      devices: { [device.id]: device },
                    },
                    position: center,
                    width: 300,
                    height: 300,
                    selected: true,
                  },
                ];
              }}
            />
            <ContextMenuSeparator />
            {virtualButton && (
              <MenuItem
                label="Virtual Button"
                Icon={PowerIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  const anchorNode: GraphNodesByType["virtualButtonAnchor"] = {
                    id: randomId(),
                    type: "virtualButtonAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                  };

                  const containerNode: GraphNodesByType["virtualButtonContainer"] =
                    {
                      id: randomId(),
                      type: "virtualButtonContainer",
                      data: {
                        title: "Virtual Button",
                        via: {
                          icon: undefined,
                          lightIcon: undefined,
                          name: "Button",
                          lightName: "Light",
                          showLabel: true,
                          enabled: true,
                          onUpClick: {},
                        },
                      },
                      position: {
                        x: -(40 - (anchorNode.width ?? 0)) / 2,
                        y: -80,
                      },
                      width: 40,
                      height: 40,
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {wiredSwitch && (
              <MenuItem
                label="Wired Switch"
                Icon={ControllerIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  const anchorNode: GraphNodesByType["controllerAnchor"] = {
                    id: randomId(),
                    type: "controllerAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                  };

                  const containerNode: GraphNodesByType["controllerContainer"] =
                    {
                      id: randomId(),
                      type: "controllerContainer",
                      data: {
                        title: "Wired Switch",
                        controllers: {},
                      },
                      position: {
                        x: -(40 - (anchorNode.width ?? 0)) / 2,
                        y: -80,
                      },
                      width: 40,
                      height: 40,
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {canbo && (
              <MenuItem
                label="CanBus Controller"
                Icon={CanBusControllerIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  const anchorNode: GraphNodesByType["canbusControllerAnchor"] =
                    {
                      id: randomId(),
                      type: "canbusControllerAnchor",
                      data: {},
                      position: center,
                      width: 16,
                      height: 16,
                      selected: true,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                    };

                  const containerNode: GraphNodesByType["canbusControllerContainer"] =
                    {
                      id: randomId(),
                      type: "canbusControllerContainer",
                      data: {
                        title: "CanBus Controller",
                        controllers: {},
                        pirSensors: {},
                        zeroToTenVoltDimmers: {},
                        relayOutputs: {},
                        threePinPorts: {},
                        twoPinPorts: {},
                        zeroToTenVoltPorts: {},
                        relayConnectors: {},
                      },
                      position: {
                        x: -(40 - (anchorNode.width ?? 0)) / 2,
                        y: -80,
                      },
                      width: 40,
                      height: 40,
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {rfSwitch && (
              <MenuItem
                label="RF Switch"
                Icon={SomoSwitchIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["somoSwitchAnchor"] = {
                    id: randomId(),
                    type: "somoSwitchAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["somoSwitchContainer"] =
                    {
                      id: randomId(),
                      type: "somoSwitchContainer",
                      data: {
                        title: "RF Switch",
                        via1: {
                          icon: undefined,
                          lightIcon: undefined,
                          name: "Button 1",
                          lightName: "Light 1",
                          hasLoad: false,
                          showLabel: true,
                          enabled: true,
                          onUpClick: {},
                        },
                        via2: {
                          icon: undefined,
                          lightIcon: undefined,
                          name: "Button 2",
                          lightName: "Light 2",
                          hasLoad: false,
                          showLabel: true,
                          enabled: true,
                          onUpClick: {},
                        },
                        via3: {
                          icon: undefined,
                          lightIcon: undefined,
                          name: "Button 3",
                          lightName: "Light 3",
                          hasLoad: false,
                          showLabel: true,
                          enabled: true,
                          onUpClick: {},
                        },
                      },
                      position: {
                        x: -(60 - (anchorNode.width ?? 0)) / 2,
                        y: -140,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {rfDimmer && (
              <MenuItem
                label="RF Dimmer"
                Icon={SomoDimmerIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["somoDimmerAnchor"] = {
                    id: randomId(),
                    type: "somoDimmerAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["somoDimmerContainer"] =
                    {
                      id: randomId(),
                      type: "somoDimmerContainer",
                      data: {
                        title: "Dimmer",
                        via: {
                          icon: undefined,
                          lightIcon: undefined,
                          name: "Dimmable Button",
                          lightName: "Dimmable Light",
                          showLabel: true,
                          onUpClick: {},
                        },
                        dimSpeed: 1.0,
                        dimmingCurve: {
                          type: "linear",
                          points: [
                            { x: 0, y: 0 },
                            { x: 1, y: 1 },
                          ],
                        },
                        viaDown: {
                          icon: undefined,
                          enabled: false,
                          name: "Dimmer Down",
                          onUpClick: {},
                          showLabel: true,
                        },
                        viaUp: {
                          icon: undefined,
                          enabled: false,
                          name: "Dimmer Up",
                          onUpClick: {},
                          showLabel: true,
                        },
                      },
                      position: {
                        x: -(75 - (anchorNode.width ?? 0)) / 2,
                        y: -90,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {outletDimmer && (
              <MenuItem
                label="Outlet Dimmer"
                Icon={OutletDimmerIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["outletDimmerAnchor"] = {
                    id: randomId(),
                    type: "outletDimmerAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["outletDimmerContainer"] =
                    {
                      id: randomId(),
                      type: "outletDimmerContainer",
                      data: {
                        title: "Outlet Dimmer",
                        via: {
                          icon: undefined,
                          lightIcon: undefined,
                          name: "Last/Off",
                          lightName: "Dimmable Outlet Light",
                          showLabel: true,
                          onUpClick: {},
                        },
                        dimSpeed: 1.0,
                        dimmingCurve: {
                          type: "linear",
                          points: [
                            { x: 0, y: 0 },
                            { x: 1, y: 1 },
                          ],
                        },
                        viaDown: {
                          icon: undefined,
                          enabled: true,
                          name: "Down",
                          onUpClick: {},
                          showLabel: true,
                        },
                        viaUp: {
                          icon: undefined,
                          enabled: true,
                          name: "Up",
                          onUpClick: {},
                          showLabel: true,
                        },
                      },
                      position: {
                        x: -(75 - (anchorNode.width ?? 0)) / 2,
                        y: -90,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {presenceSensor && (
              <MenuItem
                label="Presence Sensor"
                Icon={PresenceSensorIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["presenceSensorAnchor"] = {
                    id: randomId(),
                    type: "presenceSensorAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["presenceSensorContainer"] =
                    {
                      id: randomId(),
                      type: "presenceSensorContainer",
                      data: {
                        title: "Presence Sensor",
                        onDeactivate: {
                          icon: undefined,
                          enabled: false,
                          name: "On Deactivate",
                          onUpClick: {},
                          showLabel: true,
                          offDelay: 0.0,
                          cancelOnActivityDuringDelay: false,
                        },
                        onActivate: {
                          icon: undefined,
                          enabled: true,
                          name: "On Activate",
                          onUpClick: {},
                          showLabel: true,
                          offDelay: 0.0,
                          cancelOnActivityDuringDelay: false,
                        },
                      },
                      position: {
                        x: -(75 - (anchorNode.width ?? 0)) / 2,
                        y: -90,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {doorSensor && (
              <MenuItem
                label="Door Sensor"
                Icon={DoorSensorIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["doorSensorAnchor"] = {
                    id: randomId(),
                    type: "doorSensorAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["doorSensorContainer"] =
                    {
                      id: randomId(),
                      type: "doorSensorContainer",
                      data: {
                        title: "Door Sensor",
                        onClose: {
                          icon: undefined,
                          enabled: false,
                          name: "On Close",
                          onUpClick: {},
                          showLabel: true,
                          offDelay: 0.0,
                          cancelOnActivityDuringDelay: false,
                        },
                        onOpen: {
                          icon: undefined,
                          enabled: true,
                          name: "On Open",
                          onUpClick: {},
                          showLabel: true,
                          offDelay: 0.0,
                          cancelOnActivityDuringDelay: false,
                        },
                      },
                      position: {
                        x: -(75 - (anchorNode.width ?? 0)) / 2,
                        y: -90,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {thermostat && (
              <MenuItem
                label="Thermostat"
                Icon={SomoThermostatIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["somoThermostatAnchor"] = {
                    id: randomId(),
                    type: "somoThermostatAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["somoThermostatContainer"] =
                    {
                      id: randomId(),
                      type: "somoThermostatContainer",
                      data: {
                        title: "Thermostat",
                        temperatureUnit: "C",
                        minTemp: 5,
                        maxTemp: 35,
                        allowedModes: ["heat", "cool", "fan", "auto"],
                        allowedFanSpeeds: ["low", "medium", "high", "auto"],
                        stepSize: 1,
                        via: {
                          icon: undefined,
                          hvacIcon: undefined,
                          name: "Via",
                          hvacName: "Thermostat",
                          showLabel: true,
                          onUpClick: {},
                        },
                        viaDown: {
                          icon: undefined,
                          enabled: true,
                          name: "Lower Setpoint",
                          onUpClick: {},
                          showLabel: true,
                        },
                        viaUp: {
                          icon: undefined,
                          enabled: true,
                          name: "Increase Setpoint",
                          onUpClick: {},
                          showLabel: true,
                        },
                        modeCool: {
                          icon: undefined,
                          enabled: true,
                          name: "Cool",
                          onUpClick: {},
                          showLabel: true,
                        },
                        modeHeat: {
                          icon: undefined,
                          enabled: true,
                          name: "Heat",
                          onUpClick: {},
                          showLabel: true,
                        },
                        modeAuto: {
                          icon: undefined,
                          enabled: true,
                          name: "Auto",
                          onUpClick: {},
                          showLabel: true,
                        },
                        modeFan: {
                          icon: undefined,
                          enabled: true,
                          name: "Fan",
                          onUpClick: {},
                          showLabel: true,
                        },
                        fanAuto: {
                          icon: undefined,
                          enabled: true,
                          name: "Fan Auto",
                          onUpClick: {},
                          showLabel: true,
                        },
                        fanLow: {
                          icon: undefined,
                          enabled: true,
                          name: "Fan Low",
                          onUpClick: {},
                          showLabel: true,
                        },
                        fanMedium: {
                          icon: undefined,
                          enabled: true,
                          name: "Fan Medium",
                          onUpClick: {},
                          showLabel: true,
                        },
                        fanHigh: {
                          icon: undefined,
                          enabled: true,
                          name: "Fan High",
                          onUpClick: {},
                          showLabel: true,
                        },
                      },
                      position: {
                        x: -(75 - (anchorNode.width ?? 0)) / 2,
                        y: -90,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {irController && (
              <MenuItem
                label="IR HVAC Controller"
                Icon={SomoIrControllerIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["somoIrControllerAnchor"] =
                    {
                      id: randomId(),
                      type: "somoIrControllerAnchor",
                      data: {},
                      position: center,
                      width: 16,
                      height: 16,
                      selected: true,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  const containerNode: GraphNodesByType["somoIrControllerContainer"] =
                    {
                      id: randomId(),
                      type: "somoIrControllerContainer",
                      data: {
                        title: "IR HVAC Controller",
                        temperatureUnit: "C",
                        minTemp: 5,
                        maxTemp: 35,
                        allowedModes: ["heat", "cool", "fan", "auto"],
                        allowedFanSpeeds: ["low", "medium", "high", "auto"],
                        stepSize: 1,
                        via: {
                          name: "IR Controller",
                          icon: undefined,
                          hvacName: "IR Controller",
                          hvacIcon: undefined,
                          showLabel: true,
                          onUpClick: {},
                        },
                        viaUp: {
                          enabled: true,
                          name: "Temp Up",
                          icon: undefined,
                          showLabel: true,
                          onUpClick: {},
                        },
                        viaDown: {
                          enabled: true,
                          name: "Temp Down",
                          icon: undefined,
                          showLabel: true,
                          onUpClick: {},
                        },
                        modeCool: {
                          enabled: true,
                          name: "Cool",
                          icon: undefined,
                          showLabel: true,
                          onUpClick: {},
                        },
                        modeHeat: {
                          enabled: true,
                          name: "Heat",
                          icon: undefined,
                          showLabel: true,
                          onUpClick: {},
                        },
                        modeAuto: {
                          enabled: true,
                          name: "Auto",
                          icon: undefined,
                          showLabel: true,
                          onUpClick: {},
                        },
                        modeFan: {
                          enabled: true,
                          name: "Fan",
                          icon: undefined,
                          showLabel: true,
                          onUpClick: {},
                        },
                        fanAuto: {
                          enabled: true,
                          name: "Fan Auto",
                          icon: undefined,
                          showLabel: true,
                          onUpClick: {},
                        },
                        fanLow: {
                          enabled: true,
                          name: "Fan Low",
                          icon: undefined,
                          showLabel: true,
                          onUpClick: {},
                        },
                        fanMedium: {
                          enabled: true,
                          name: "Fan Medium",
                          icon: undefined,
                          showLabel: true,
                          onUpClick: {},
                        },
                        fanHigh: {
                          enabled: true,
                          name: "Fan High",
                          icon: undefined,
                          showLabel: true,
                          onUpClick: {},
                        },
                      },
                      position: {
                        x: -(75 - (anchorNode.width ?? 0)) / 2,
                        y: -90,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {shades && (
              <MenuItem
                label="Somo Shades"
                Icon={SomoShadesIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["somoShadesAnchor"] = {
                    id: randomId(),
                    type: "somoShadesAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["somoShadesContainer"] =
                    {
                      id: randomId(),
                      type: "somoShadesContainer",
                      data: {
                        title: "Shades",
                        via: {
                          icon: undefined,
                          loadIcon: undefined,
                          name: "Shades",
                          loadName: "Shades",
                          showLabel: true,
                          onUpClick: {},
                        },
                        raceTime: 30,
                        viaDown: {
                          icon: undefined,
                          enabled: true,
                          name: "Shades Close",
                          onUpClick: {},
                          showLabel: true,
                        },
                        viaUp: {
                          icon: undefined,
                          enabled: true,
                          name: "Shades Open",
                          onUpClick: {},
                          showLabel: true,
                        },
                      },
                      position: {
                        x: -(75 - (anchorNode.width ?? 0)) / 2,
                        y: -90,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {fan && (
              <MenuItem
                label="Somo Fan"
                Icon={SomoFanIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["somoFanAnchor"] = {
                    id: randomId(),
                    type: "somoFanAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["somoFanContainer"] = {
                    id: randomId(),
                    type: "somoFanContainer",
                    data: {
                      title: "Fan",
                      via: {
                        icon: undefined,
                        loadIcon: undefined,
                        name: "Fan",
                        loadName: "Fan",
                        showLabel: true,
                        onUpClick: {},
                      },
                      fanType: "onoff",
                      viaLow: {
                        icon: undefined,
                        enabled: true,
                        name: "Fan Low",
                        onUpClick: {},
                        showLabel: true,
                      },
                      viaMed: {
                        icon: undefined,
                        enabled: true,
                        name: "Fan Medium",
                        onUpClick: {},
                        showLabel: true,
                      },
                      viaHigh: {
                        icon: undefined,
                        enabled: true,
                        name: "Fan High",
                        onUpClick: {},
                        showLabel: true,
                      },
                    },
                    position: {
                      x: -(75 - (anchorNode.width ?? 0)) / 2,
                      y: -90,
                    },
                    selected: false,
                    parentId: anchorNode.id,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {servicePad && (
              <MenuItem
                label="Service Pad"
                Icon={ServicePadIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["servicePadAnchor"] = {
                    id: randomId(),
                    type: "servicePadAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["servicePadContainer"] =
                    {
                      id: randomId(),
                      type: "servicePadContainer",
                      data: {
                        title: "Service Pad",
                        mode: "servicePad",
                        makeUpRoomButton: {
                          icon: "brush",
                          enabled: true,
                          name: "Make Up Room",
                          showLabel: true,
                        },
                        doorbellButton: {
                          icon: "bell",
                          enabled: true,
                          name: "Doorbell",
                          showLabel: true,
                        },
                        doNotDisturbButton: {
                          icon: "doNotDisturb",
                          enabled: true,
                          name: "Do Not Disturb",
                          showLabel: true,
                        },
                      },
                      position: {
                        x: -(75 - (anchorNode.width ?? 0)) / 2,
                        y: -90,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            <ContextMenuSeparator />
          </>
        )}
        <MenuItem
          label="Comment"
          Icon={MessageSquareIcon}
          reactFlowInstance={reactFlowInstance}
          currentContextMenuPosition={currentContextMenuPosition}
          addNodes={({ reactFlowInstance, center }) => {
            if (!user) {
              throw new Error("user not signed in");
            }

            center.y = center.y - 8;
            center.x = center.x - 8;

            let zIndex =
              Math.max(
                ...reactFlowInstance.getNodes().map((node) => node.zIndex ?? 0),
              ) + 1;

            const commentNode: GraphNodesByType["comment"] = {
              id: randomId(),
              type: "comment",
              data: {
                authorId: user.id,
                author: user.fullName ?? "",
                authorAvatar: user.imageUrl,
                replies: [],
                createdAt: Date.now(),
                text: {},
              },
              position: center,
              width: 16,
              height: 16,
              selected: true,
              selectable: true,
              draggable: true,
              deletable: true,
              zIndex: zIndex++,
            };

            return [commentNode];
          }}
        />
        {selectedNodes.length > 0 && (
          <>
            <ContextMenuSeparator />
            {!room?.readOnly && (
              <>
                <ContextMenuItem
                  onClick={() => {
                    if (!reactFlowInstance) {
                      return;
                    }
                    const nodes = reactFlowInstance.getNodes() ?? [];
                    const maxZIndex = Math.max(
                      ...nodes.map((node) => node.zIndex ?? 0),
                    );
                    let i = 1;
                    for (const node of selectedNodes) {
                      const zIndex = maxZIndex + i++;
                      reactFlowInstance.updateNode(node.id, {
                        zIndex,
                        ...node,
                      });
                    }
                  }}
                >
                  <ArrowUpFromLine className="size-4 mr-2" />
                  <span>Bring to front</span>
                </ContextMenuItem>
                <ContextMenuItem
                  onClick={() => {
                    if (!reactFlowInstance) {
                      return;
                    }
                    const nodes = reactFlowInstance.getNodes() ?? [];
                    const minZIndex = Math.min(
                      ...nodes.map((node) => node.zIndex ?? 0),
                    );
                    let i = 1;
                    for (const node of selectedNodes) {
                      const zIndex = minZIndex - i++;
                      reactFlowInstance.updateNode(node.id, {
                        ...node,
                        zIndex,
                      });
                    }
                  }}
                >
                  <ArrowDownFromLine className="size-4 mr-2" />
                  <span>Send to back</span>
                </ContextMenuItem>

                <ContextMenuSeparator />
              </>
            )}
            <ContextMenuItem
              onClick={() => {
                if (!reactFlowInstance) {
                  return;
                }

                deleteSelectedNodes(
                  reactFlowInstance,
                  selectedNodes,
                  room?.readOnly ?? false,
                );
              }}
            >
              <Trash2 className="size-4 mr-2" />
              <span>Delete</span>
            </ContextMenuItem>
          </>
        )}
      </ContextMenuContent>
    </ContextMenu>
  );
};

type Position = { x: number; y: number };

function MenuItem({
  reactFlowInstance,
  currentContextMenuPosition,
  addNodes,
  Icon,
  label,
}: {
  reactFlowInstance: ReactFlowInstance<GraphNode> | null;
  currentContextMenuPosition: Position | null;
  addNodes: (params: {
    reactFlowInstance: ReactFlowInstance<GraphNode>;
    center: Position;
  }) => GraphNode[] | { nodes: GraphNode[]; edges?: Edge[] };
  Icon: React.ComponentType<React.ComponentProps<"svg">>;
  label: string;
}) {
  return (
    <ContextMenuItem
      onClick={() => {
        if (!reactFlowInstance) {
          throw new Error("reactFlowInstance is not set");
        }
        if (!currentContextMenuPosition) {
          throw new Error("currentContextMenuPosition is not set");
        }

        const center = reactFlowInstance.screenToFlowPosition(
          currentContextMenuPosition,
        ) ?? { x: 0, y: 0 };

        const nodesOrNodesAndEdges = addNodes({ reactFlowInstance, center });
        const nodes = Array.isArray(nodesOrNodesAndEdges)
          ? nodesOrNodesAndEdges
          : nodesOrNodesAndEdges.nodes;
        const edges = Array.isArray(nodesOrNodesAndEdges)
          ? undefined
          : nodesOrNodesAndEdges.edges;

        reactFlowInstance.addNodes(nodes);
        if (edges) {
          reactFlowInstance.addEdges(edges);
        }
      }}
    >
      <Icon className="size-4 mr-2" />
      <span>{label}</span>
    </ContextMenuItem>
  );
}
