import { useAuth } from "@clerk/clerk-react";
import { useQuery } from "@tanstack/react-query";
import { z } from "zod/v4";

const BASE_URL = import.meta.env.VITE_API_URL;

export const Room = z.object({
  id: z.string(),
  name: z.string(),
  readOnly: z.boolean(),
});
export type Room = z.infer<typeof Room>;

export function useRoom({
  controllerId,
  orgId,
}: {
  controllerId: string;
  orgId: string;
}) {
  const { getToken } = useAuth();

  return useQuery({
    queryKey: ["room", orgId, controllerId],
    queryFn: async () => {
      const token = await getToken({ template: "Configurator" });

      const url = `${BASE_URL}/org/${orgId}/rooms/${controllerId}`;
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const roomJson = await response.json();

      return Room.parse(roomJson);
    },
  });
}
