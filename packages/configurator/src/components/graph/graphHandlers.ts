import { addAction } from "@/components/devices/addAction";
import { objectToMap } from "@/lib/yjsUtils";
import {
  CanBusControllerAnchorNodeData,
  CanBusControllerContainerNodeData,
  ControllerAnchorNodeData,
  ControllerContainerNodeData,
  DeviceControlSettings,
  DoorSensorAnchorNodeData,
  DoorSensorContainerNodeData,
  DoorSensorViaId,
  GraphNode,
  OutletDimmerAnchorNodeData,
  OutletDimmerContainerNodeData,
  PresenceSensorAnchorNodeData,
  PresenceSensorContainerNodeData,
  PresenceSensorViaId,
  ServicePadAnchorNodeData,
  ServicePadContainerNodeData,
  SomoDimmerAnchorNodeData,
  SomoDimmerContainerNodeData,
  SomoDimmerViaId,
  SomoFanAnchorNodeData,
  SomoFanContainerNodeData,
  SomoFanViaId,
  SomoIrControllerAnchorNodeData,
  SomoIrControllerContainerNodeData,
  SomoShadesAnchorNodeData,
  SomoShadesContainerNodeData,
  SomoShadesViaId,
  SomoSwitchAnchorNodeData,
  SomoSwitchContainerNodeData,
  SomoSwitchViaId,
  SomoThermostatAnchorNodeData,
  SomoThermostatContainerNodeData,
  SomoThermostatViaId,
  VirtualButtonAnchorNodeData,
  VirtualButtonContainerNodeData,
  VirtualButtonViaId,
} from "@somo/shared";
import {
  applyEdgeChanges,
  applyNodeChanges,
  Connection,
  Edge,
  EdgeChange,
  NodeChange,
} from "@xyflow/react";
import deepEqual from "fast-deep-equal/react";
import { match } from "ts-pattern";
import * as Y from "yjs";
import { z } from "zod/v4";
import { generateSyntheticEdges } from "./generateSyntheticEdges";
import { getNodeData, Graph } from "./Graph";
import { CommentNodeData } from "./nodes/CommentNode";
import { ImageNodeData } from "./nodes/ImageNode";
import { SectionNodeData } from "./nodes/SectionNode";
import {
  actionAlreadyExistsForDevice,
  getDeviceDimmingSpeed,
  isDeviceConnectionAllowed,
} from "./utils/deviceUtils";

export function handleConnect(
  connection: Connection,
  { nodes, edges }: Graph,
  ydoc: Y.Doc,
) {
  const { sourceHandle, targetHandle } = connection;
  if (!sourceHandle) {
    return;
  }
  if (!targetHandle) {
    return;
  }

  // dont add an edge if there's already one
  const existingEdge = edges.find(
    (edge) =>
      edge.source === connection.source &&
      edge.sourceHandle === sourceHandle &&
      edge.target === connection.target &&
      edge.targetHandle === targetHandle,
  );
  if (existingEdge) {
    return;
  }

  const targetNode = nodes.find((node) => node.id === connection.target);
  if (targetNode?.type !== "section") {
    return;
  }

  const sourceNode = nodes.find((node) => node.id === connection.source);
  if (!sourceNode) {
    return;
  }

  const targetSectionDevice = targetNode.data.devices[targetHandle];
  if (!targetSectionDevice) {
    console.warn("targetSectionDevice not found");
    return;
  }

  if (!isDeviceConnectionAllowed(sourceNode.type, targetSectionDevice.type)) {
    console.warn(
      `Connection not allowed between ${sourceNode.type} and ${targetSectionDevice.type}`,
    );
    return;
  }

  const sectionDevices = Object.values(targetNode.data.devices);
  const dimSpeed = getDeviceDimmingSpeed(
    targetSectionDevice.id,
    sectionDevices,
    nodes,
    0.2,
  );
  const defaultDimSpeed = getDeviceDimmingSpeed(
    targetSectionDevice.id,
    sectionDevices,
    nodes,
    0.1,
  );

  match(sourceNode)
    .with({ type: "virtualButtonContainer" }, (sourceNode) => {
      const viaIdResult = VirtualButtonViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }
      const viaId = viaIdResult.data;

      // Get the Y.Map for the via
      const dataMap = getNodeData(ydoc, sourceNode.id);
      const viaMap = dataMap?.get(viaId) as Y.Map<any> | undefined;
      if (!viaMap) {
        console.warn(`Via map not found for ${viaId}`);
        return;
      }

      if (actionAlreadyExistsForDevice(viaMap, targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      addAction(viaMap, {
        device: "virtualButton",
        key: "onUpClick",
        params: {
          deviceId: targetSectionDevice.id,
          dimSpeed: dimSpeed,
          targetValue: 100,
          onValue: 100,
          offValue: 0,
          type: "lighting",
        },
      });
    })
    .with({ type: "controllerContainer" }, (sourceNode) => {
      const sourceController = sourceNode.data.controllers[sourceHandle];

      // Get the Y.Map for the controller
      const dataMap = getNodeData(ydoc, sourceNode.id);
      const controllersMap = dataMap?.get("controllers") as
        | Y.Map<any>
        | undefined;
      if (!controllersMap) {
        console.warn(`Controllers map not found`);
        return;
      }

      const controllerKeyMap = controllersMap.get(sourceController.id) as
        | Y.Map<any>
        | undefined;
      if (!controllerKeyMap) {
        console.warn(`Controller key map not found for ${sourceController.id}`);
        return;
      }

      if (
        actionAlreadyExistsForDevice(controllerKeyMap, targetSectionDevice.id)
      ) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      if (sourceController.type === "toggle") {
        addAction(controllerKeyMap, {
          device: "toggleController",
          key: "onUpClick",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: dimSpeed,
            targetValue: 100,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });
        addAction(controllerKeyMap, {
          device: "toggleController",
          key: "onUpHold",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: defaultDimSpeed,
            targetValue: 100,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });
        addAction(controllerKeyMap, {
          device: "toggleController",
          key: "onDownClick",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: dimSpeed,
            targetValue: 0,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });
        addAction(controllerKeyMap, {
          device: "toggleController",
          key: "onDownHold",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: defaultDimSpeed,
            targetValue: 0,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });
      } else if (sourceController.type === "momentary") {
        addAction(controllerKeyMap, {
          device: "momentaryController",
          key: "onUpClick",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: dimSpeed,
            targetValue: 100,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });
        addAction(controllerKeyMap, {
          device: "momentaryController",
          key: "onUpHold",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: defaultDimSpeed,
            targetValue: 100,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });
      }
    })
    .with({ type: "canbusControllerContainer" }, (sourceNode) => {
      // Check if this is a PIR sensor handle
      const pirSensor = Object.values(sourceNode.data.pirSensors || {}).find(
        (sensor) => sensor.id === sourceHandle,
      );

      if (pirSensor) {
        // Get the Y.Map for the PIR sensor
        const dataMap = getNodeData(ydoc, sourceNode.id);
        const pirSensorsMap = dataMap?.get("pirSensors") as
          | Y.Map<any>
          | undefined;
        if (!pirSensorsMap) {
          console.warn(`PIR sensors map not found`);
          return;
        }

        const pirSensorMap = pirSensorsMap.get(pirSensor.id) as
          | Y.Map<any>
          | undefined;
        if (!pirSensorMap) {
          console.warn(`PIR sensor map not found for ${pirSensor.id}`);
          return;
        }

        if (
          actionAlreadyExistsForDevice(pirSensorMap, targetSectionDevice.id)
        ) {
          console.warn(`Action for ${targetSectionDevice.id} already exists`);
          return;
        }

        // Handle onActivate
        let onActivateMap = pirSensorMap.get("onActivate") as
          | Y.Map<any>
          | undefined;
        if (!onActivateMap) {
          onActivateMap = new Y.Map();
          pirSensorMap.set("onActivate", onActivateMap);
        }

        addAction(pirSensorMap, {
          device: "pirSensor",
          key: "onActivate",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: dimSpeed,
            targetValue: 100,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });

        // Handle onDeactivate
        let onDeactivateMap = pirSensorMap.get("onDeactivate") as
          | Y.Map<any>
          | undefined;
        if (!onDeactivateMap) {
          onDeactivateMap = new Y.Map();
          pirSensorMap.set("onDeactivate", onDeactivateMap);
        }

        addAction(pirSensorMap, {
          device: "pirSensor",
          key: "onDeactivate",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: dimSpeed,
            targetValue: 0,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });
        return;
      }

      // Regular controller handling
      const sourceController = sourceNode.data.controllers[sourceHandle];
      if (!sourceController) {
        console.warn(`Controller not found for handle ${sourceHandle}`);
        return;
      }

      // Get the Y.Map for the controller
      const dataMap = getNodeData(ydoc, sourceNode.id);
      const controllersMap = dataMap?.get("controllers") as
        | Y.Map<any>
        | undefined;
      if (!controllersMap) {
        console.warn(`Controllers map not found`);
        return;
      }

      const controllerKeyMap = controllersMap.get(sourceController.id) as
        | Y.Map<any>
        | undefined;
      if (!controllerKeyMap) {
        console.warn(`Controller key map not found for ${sourceController.id}`);
        return;
      }

      if (
        actionAlreadyExistsForDevice(controllerKeyMap, targetSectionDevice.id)
      ) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      if (sourceController.type === "toggle") {
        addAction(controllerKeyMap, {
          device: "toggleCanBusController",
          key: "onUpClick",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: dimSpeed,
            targetValue: 100,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });
        addAction(controllerKeyMap, {
          device: "toggleCanBusController",
          key: "onUpHold",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: getDeviceDimmingSpeed(
              targetSectionDevice.id,
              sectionDevices,
              nodes,
              1,
            ),
            targetValue: 100,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });
        addAction(controllerKeyMap, {
          device: "toggleCanBusController",
          key: "onDownClick",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: dimSpeed,
            targetValue: 0,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });

        addAction(controllerKeyMap, {
          device: "toggleCanBusController",
          key: "onDownHold",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: getDeviceDimmingSpeed(
              targetSectionDevice.id,
              sectionDevices,
              nodes,
              1,
            ),
            targetValue: 0,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });
      } else if (sourceController.type === "momentary") {
        addAction(controllerKeyMap, {
          device: "momentaryCanBusController",
          key: "onUpClick",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: dimSpeed,
            targetValue: 100,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });
        addAction(controllerKeyMap, {
          device: "momentaryCanBusController",
          key: "onUpHold",
          params: {
            deviceId: targetSectionDevice.id,
            dimSpeed: defaultDimSpeed,
            targetValue: 100,
            onValue: 100,
            offValue: 0,
            type: "lighting",
          },
        });
      }
    })
    .with({ type: "somoSwitchContainer" }, (sourceNode) => {
      const viaIdResult = SomoSwitchViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }
      const viaId = viaIdResult.data;

      // Get the Y.Map for the via
      const dataMap = getNodeData(ydoc, sourceNode.id);
      const viaMap = dataMap?.get(viaId) as Y.Map<any> | undefined;
      if (!viaMap) {
        console.warn(`Via map not found for ${viaId}`);
        return;
      }

      if (actionAlreadyExistsForDevice(viaMap, targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      addAction(viaMap, {
        device: "somoSwitch",
        key: "onUpClick",
        params: {
          deviceId: targetSectionDevice.id,
          dimSpeed: dimSpeed,
          targetValue: 100,
          onValue: 100,
          offValue: 0,
          type: "lighting",
        },
      });
    })
    .with({ type: "somoDimmerContainer" }, (sourceNode) => {
      const viaIdResult = SomoDimmerViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }
      const viaId = viaIdResult.data;

      // Get the Y.Map for the via
      const dataMap = getNodeData(ydoc, sourceNode.id);
      const viaMap = dataMap?.get(viaId) as Y.Map<any> | undefined;
      if (!viaMap) {
        console.warn(`Via map not found for ${viaId}`);
        return;
      }

      if (actionAlreadyExistsForDevice(viaMap, targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      addAction(viaMap, {
        device: "somoDimmer",
        key: "onUpClick",
        params: {
          deviceId: targetSectionDevice.id,
          dimSpeed: dimSpeed,
          targetValue: 100,
          onValue: 100,
          offValue: 0,
          type: "lighting",
        },
      });
    })
    .with({ type: "presenceSensorContainer" }, (sourceNode) => {
      const viaIdResult = PresenceSensorViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }
      const viaId = viaIdResult.data;

      // Get the Y.Map for the via
      const dataMap = getNodeData(ydoc, sourceNode.id);
      const viaMap = dataMap?.get(viaId) as Y.Map<any> | undefined;
      if (!viaMap) {
        console.warn(`Via map not found for ${viaId}`);
        return;
      }

      if (actionAlreadyExistsForDevice(viaMap, targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      addAction(viaMap, {
        device: "presenceSensor",
        key: "onUpClick",
        params: {
          deviceId: targetSectionDevice.id,
          dimSpeed: dimSpeed,
          targetValue: 100,
          onValue: 100,
          offValue: 0,
          type: "lighting",
        },
      });
    })
    .with({ type: "doorSensorContainer" }, (sourceNode) => {
      const viaIdResult = DoorSensorViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }
      const viaId = viaIdResult.data;

      // Get the Y.Map for the via
      const dataMap = getNodeData(ydoc, sourceNode.id);
      const viaMap = dataMap?.get(viaId) as Y.Map<any> | undefined;
      if (!viaMap) {
        console.warn(`Via map not found for ${viaId}`);
        return;
      }

      if (actionAlreadyExistsForDevice(viaMap, targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      if (
        targetSectionDevice.type === "somoThermostat" ||
        targetSectionDevice.type === "somoIrController"
      ) {
        addAction(viaMap, {
          device: targetSectionDevice.type,
          key: "onUpClick",
          params: {
            deviceId: targetSectionDevice.id,
            setpoint: 22,
            mode: "auto",
            fanSpeed: "auto",
            type: "thermostat",
          },
        });
        return;
      }

      addAction(viaMap, {
        device: "doorSensor",
        key: "onUpClick",
        params: {
          deviceId: targetSectionDevice.id,
          dimSpeed: dimSpeed,
          targetValue: 100,
          onValue: 100,
          offValue: 0,
          type: "lighting",
        } as DeviceControlSettings,
      });
    })
    .with({ type: "somoShadesContainer" }, (sourceNode) => {
      const viaIdResult = SomoShadesViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }
      const viaId = viaIdResult.data;

      // Get the Y.Map for the via
      const dataMap = getNodeData(ydoc, sourceNode.id);
      const viaMap = dataMap?.get(viaId) as Y.Map<any> | undefined;
      if (!viaMap) {
        console.warn(`Via map not found for ${viaId}`);
        return;
      }

      if (actionAlreadyExistsForDevice(viaMap, targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      addAction(viaMap, {
        device: "somoShades",
        key: "onUpClick",
        params: {
          deviceId: targetSectionDevice.id,
          raceTime: sourceNode.data.raceTime || 30,
          targetValue: 100,
          onValue: 100,
          offValue: 0,
        },
      });
    })
    .with({ type: "somoFanContainer" }, (sourceNode) => {
      const viaIdResult = SomoFanViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }
      const viaId = viaIdResult.data;

      // Get the Y.Map for the via
      const dataMap = getNodeData(ydoc, sourceNode.id);
      const viaMap = dataMap?.get(viaId) as Y.Map<any> | undefined;
      if (!viaMap) {
        console.warn(`Via map not found for ${viaId}`);
        return;
      }

      if (actionAlreadyExistsForDevice(viaMap, targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      addAction(viaMap, {
        device: "somoFan",
        key: "onUpClick",
        params: {
          deviceId: targetSectionDevice.id,
          targetValue: 100,
          onValue: 100,
          offValue: 0,
        },
      });
    })
    .with({ type: "somoThermostatContainer" }, (sourceNode) => {
      const viaIdResult = SomoThermostatViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }
      const viaId = viaIdResult.data;

      // Get the Y.Map for the via
      const dataMap = getNodeData(ydoc, sourceNode.id);
      const viaMap = dataMap?.get(viaId) as Y.Map<any> | undefined;
      if (!viaMap) {
        console.warn(`Via map not found for ${viaId}`);
        return;
      }

      if (actionAlreadyExistsForDevice(viaMap, targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      addAction(viaMap, {
        device: "somoThermostat",
        key: "onUpClick",
        params: {
          deviceId: targetSectionDevice.id,
          setpoint: 22,
          mode: "auto",
          fanSpeed: "auto",
          type: "thermostat",
        },
      });
    })
    // Trying to perform an exhaustive check results in a type error:
    // `Type instantiation is excessively deep and possibly infinite`
    // => Use `otherwise` instead.
    .otherwise(() => null);
}

export function handleEdgesChange(
  changes: EdgeChange<Edge>[],
  { nodes, edges }: Graph,
  setEdges: (edges: Edge[]) => void,
  ydoc: Y.Doc,
) {
  const syntheticEdges = generateSyntheticEdges(nodes);
  const newEdges = applyEdgeChanges(changes, edges);
  setEdges(newEdges);

  // update the ydoc
  ydoc.transact(() => {
    const graph = ydoc.getMap("graph");
    const yEdges = graph.get("edges") as Y.Map<any>;
    for (const change of changes) {
      // skip synthetic edges
      if (
        syntheticEdges.some(
          (edge) =>
            edge.id === (change.type === "add" ? change.item.id : change.id),
        )
      ) {
        continue;
      }

      match(change)
        .with({ type: "add" }, (change) => {
          const newEdge = objectToMap({
            id: change.item.id,
            source: change.item.source,
            sourceHandle: change.item.sourceHandle,
            target: change.item.target,
            targetHandle: change.item.targetHandle,
            label: change.item.label,
            type: change.item.type,
            data: change.item.data,
          });
          yEdges.set(change.item.id, newEdge);
        })
        .with({ type: "remove" }, (change) => {
          yEdges.delete(change.id);
        })
        .with({ type: "replace" }, (change) => {
          const edge = yEdges.get(change.id) as Y.Map<any> | undefined;
          if (!edge) {
            return;
          }

          edge.set("id", change.item.id);
          edge.set("source", change.item.source);
          edge.set("sourceHandle", change.item.sourceHandle);
          edge.set("target", change.item.target);
          edge.set("targetHandle", change.item.targetHandle);
          edge.set("label", change.item.label);
          edge.set("type", change.item.type);

          if (change.item.data) {
            let data = edge.get("data") as Y.Map<any>;
            if (!data) {
              data = new Y.Map();
              edge.set("data", data);
            }
            for (const [key, value] of Object.entries(change.item.data)) {
              if (
                data.get(key) instanceof Y.XmlFragment ||
                data.get(key) instanceof Y.Array ||
                data.get(key) instanceof Y.Map
              ) {
                continue;
              }
              data.set(key, value);
            }
          }
        })
        .with({ type: "select" }, () => {
          // Do nothing
        })
        .exhaustive();
    }
  });
}

export function handleNodesChange(
  changes: NodeChange<GraphNode>[],
  { nodes }: Graph,
  setNodes: (nodes: GraphNode[]) => void,
  ydoc: Y.Doc,
) {
  let newNodes = applyNodeChanges(changes, nodes);
  const hasAddChange = changes.some((change) => change.type === "add");
  if (hasAddChange) {
    newNodes = newNodes.map((node) => {
      const includedInAddChange = changes.some(
        (change) => change.type === "add" && change.item.id === node.id,
      );
      if (!includedInAddChange) {
        return { ...node, selected: false };
      }
      return node;
    });
  }

  // Only sync to Yjs if there are non-selection changes
  const hasNonSelectionChanges = changes.some(
    (change) => change.type !== "select",
  );

  if (hasNonSelectionChanges) {
    // Preserve selection state by not syncing selection to Yjs
    const nodesForYjs = newNodes.map((node) => ({
      ...node,
      selected: false, // Don't sync selection state to Yjs
    }));

    const nodesArray = ydoc.getArray("nodes"); // Get the Yjs array that stores nodes
    nodesArray.delete(0, nodesArray.length); // Clear ALL nodes from the Yjs array
    nodesArray.push(nodesForYjs); // Add the current local nodes to Yjs
  }

  let hightestZIndex = newNodes.reduce((max, node) => {
    return Math.max(max, node.zIndex ?? 0);
  }, 0);

  // sort nodes by zIndex
  newNodes.sort((a, b) => (a.zIndex ?? 0) - (b.zIndex ?? 0));

  // Remove nodes with invalid data
  const invalidNodes = newNodes.filter((node) => {
    const DataSchema = match(node.type)
      .with("image", () => ImageNodeData)
      .with("section", () => SectionNodeData)
      .with("comment", () => CommentNodeData)
      .with("virtualButtonContainer", () => VirtualButtonContainerNodeData)
      .with("virtualButtonAnchor", () => VirtualButtonAnchorNodeData)
      .with("controllerContainer", () => ControllerContainerNodeData)
      .with("controllerAnchor", () => ControllerAnchorNodeData)
      .with(
        "canbusControllerContainer",
        () => CanBusControllerContainerNodeData,
      )
      .with("canbusControllerAnchor", () => CanBusControllerAnchorNodeData)
      .with("somoDimmerContainer", () => SomoDimmerContainerNodeData)
      .with("somoDimmerAnchor", () => SomoDimmerAnchorNodeData)
      .with("outletDimmerContainer", () => OutletDimmerContainerNodeData)
      .with("outletDimmerAnchor", () => OutletDimmerAnchorNodeData)
      .with("somoSwitchContainer", () => SomoSwitchContainerNodeData)
      .with("somoSwitchAnchor", () => SomoSwitchAnchorNodeData)
      .with("presenceSensorContainer", () => PresenceSensorContainerNodeData)
      .with("presenceSensorAnchor", () => PresenceSensorAnchorNodeData)
      .with("doorSensorContainer", () => DoorSensorContainerNodeData)
      .with("doorSensorAnchor", () => DoorSensorAnchorNodeData)
      .with("somoThermostatContainer", () => SomoThermostatContainerNodeData)
      .with("somoThermostatAnchor", () => SomoThermostatAnchorNodeData)
      .with(
        "somoIrControllerContainer",
        () => SomoIrControllerContainerNodeData,
      )
      .with("somoIrControllerAnchor", () => SomoIrControllerAnchorNodeData)
      .with("somoShadesContainer", () => SomoShadesContainerNodeData)
      .with("somoShadesAnchor", () => SomoShadesAnchorNodeData)
      .with("somoFanContainer", () => SomoFanContainerNodeData)
      .with("somoFanAnchor", () => SomoFanAnchorNodeData)
      .with("servicePadContainer", () => ServicePadContainerNodeData)
      .with("servicePadAnchor", () => ServicePadAnchorNodeData)
      .with(undefined, () => z.record(z.string(), z.unknown()))
      .exhaustive();

    return !DataSchema.safeParse(node.data).success;
  });
  invalidNodes.forEach((node) => {
    console.warn("Node with invalid data found. Removing it.", { node });
    changes.push({ type: "remove", id: node.id });
  });

  setNodes(
    newNodes.filter((n) => {
      // find deleted changes
      // go through all delete changes:
      for (const change of changes) {
        if (change.type === "remove") {
          // see if the parentId is the same than the removed node
          if (change.id === n.parentId) {
            return false;
          }
        }
      }
      return true;
    }),
  );

  // update the ydoc
  ydoc.transact(() => {
    const graph = ydoc.getMap("graph");
    const yNodes = graph.get("nodes") as Y.Map<any>;
    for (const change of changes) {
      match(change)
        .with({ type: "dimensions" }, (change) => {
          const node = yNodes.get(change.id) as Y.Map<number> | undefined;
          if (!node) {
            console.warn(`Can't find node ${change.id} for ${change.type}`);
            return;
          }
          if (change.dimensions) {
            node.set("width", change.dimensions.width);
            node.set("height", change.dimensions.height);
          }
        })
        .with({ type: "position" }, (change) => {
          const node = yNodes.get(change.id) as Y.Map<any> | undefined;
          if (!node) {
            console.warn(`Can't find node ${change.id} for ${change.type}`);
            return;
          }
          if (change.position) {
            const position = node.get("position") as Y.Map<number> | undefined;
            if (!position) {
              return;
            }
            position.set("x", change.position.x);
            position.set("y", change.position.y);
          }
        })
        .with({ type: "add" }, (change) => {
          const newNode = objectToMap({
            id: change.item.id,
            type: change.item.type,
            width: change.item.width,
            height: change.item.height,
            position: change.item.position,
            zIndex: hightestZIndex++,
            parentId: change.item.parentId,
            expandParent: change.item.expandParent,
            extent: change.item.extent,
            selectable: change.item.selectable,
            draggable: change.item.draggable,
            deletable: change.item.deletable,
            connectable: change.item.connectable,
          });

          if (!newNode) {
            throw new Error("Could not create new node");
          }
          const yData = new Y.Map();
          for (const [key, value] of Object.entries(change.item.data)) {
            if (
              typeof value === "string" ||
              typeof value === "number" ||
              typeof value === "boolean"
            ) {
              yData.set(key, value);
            }
          }

          // handle custom node types
          match(change.item)
            .with({ type: "section" }, (item) => {
              const devices = new Y.Map();
              if (item.data.devices) {
                for (const [deviceId, device] of Object.entries(
                  item.data.devices,
                )) {
                  devices.set(deviceId, objectToMap(device));
                }
              }
              yData.set("devices", devices);

              yData.set("title", item.data.title);
              yData.set("direction", item.data.direction);
            })
            .with({ type: "virtualButtonContainer" }, (item) => {
              yData.set("title", item.data.title);
              yData.set("via", objectToMap(item.data.via));
            })
            .with({ type: "controllerContainer" }, (item) => {
              yData.set("title", item.data.title);
              yData.set("controllers", objectToMap(item.data.controllers));
            })
            .with({ type: "canbusControllerContainer" }, (item) => {
              yData.set("title", item.data.title);
              yData.set("controllers", objectToMap(item.data.controllers));
            })
            .with({ type: "somoSwitchContainer" }, (item) => {
              yData.set("via1", objectToMap(item.data.via1));
              yData.set("via2", objectToMap(item.data.via2));
              yData.set("via3", objectToMap(item.data.via3));
            })
            .with({ type: "somoDimmerContainer" }, (item) => {
              yData.set("via", objectToMap(item.data.via));
              yData.set("viaUp", objectToMap(item.data.viaUp));
              yData.set("viaDown", objectToMap(item.data.viaDown));

              const yDimmingCurve = new Y.Map();
              yDimmingCurve.set("type", item.data.dimmingCurve.type);
              const pointsArr = new Y.Array();
              pointsArr.push(item.data.dimmingCurve.points);
              yDimmingCurve.set("points", pointsArr);
              yData.set("dimmingCurve", yDimmingCurve);
            })
            .with({ type: "outletDimmerContainer" }, (item) => {
              yData.set("via", objectToMap(item.data.via));
              yData.set("viaUp", objectToMap(item.data.viaUp));
              yData.set("viaDown", objectToMap(item.data.viaDown));

              const yDimmingCurve = new Y.Map();
              yDimmingCurve.set("type", item.data.dimmingCurve.type);
              const pointsArr = new Y.Array();
              pointsArr.push(item.data.dimmingCurve.points);
              yDimmingCurve.set("points", pointsArr);
              yData.set("dimmingCurve", yDimmingCurve);
            })
            .with({ type: "presenceSensorContainer" }, (item) => {
              yData.set("title", item.data.title);
              yData.set("onActivate", objectToMap(item.data.onActivate));
              yData.set("onDeactivate", objectToMap(item.data.onDeactivate));
            })
            .with({ type: "doorSensorContainer" }, (item) => {
              yData.set("title", item.data.title);
              yData.set("onOpen", objectToMap(item.data.onOpen));
              yData.set("onClose", objectToMap(item.data.onClose));
            })
            .with({ type: "somoThermostatContainer" }, (item) => {
              yData.set("title", item.data.title);
              yData.set("minTemp", item.data.minTemp);
              yData.set("maxTemp", item.data.maxTemp);
              yData.set("temperatureUnit", item.data.temperatureUnit);
              yData.set("via", objectToMap(item.data.via));
              yData.set("viaUp", objectToMap(item.data.viaUp));
              yData.set("viaDown", objectToMap(item.data.viaDown));
              yData.set("modeCool", objectToMap(item.data.modeCool));
              yData.set("modeHeat", objectToMap(item.data.modeHeat));
              yData.set("modeAuto", objectToMap(item.data.modeAuto));
              yData.set("modeFan", objectToMap(item.data.modeFan));
              yData.set("fanAuto", objectToMap(item.data.fanAuto));
              yData.set("fanLow", objectToMap(item.data.fanLow));
              yData.set("fanMedium", objectToMap(item.data.fanMedium));
              yData.set("fanHigh", objectToMap(item.data.fanHigh));

              const speedsArr = new Y.Array();
              speedsArr.push(item.data.allowedFanSpeeds);
              yData.set("allowedFanSpeeds", speedsArr);

              const modesArr = new Y.Array();
              modesArr.push(item.data.allowedModes);
              yData.set("allowedModes", modesArr);
            })
            .with({ type: "somoIrControllerContainer" }, (item) => {
              yData.set("title", item.data.title);
              yData.set("minTemp", item.data.minTemp);
              yData.set("maxTemp", item.data.maxTemp);
              yData.set("temperatureUnit", item.data.temperatureUnit);
              yData.set("via", objectToMap(item.data.via));
              yData.set("viaUp", objectToMap(item.data.viaUp));
              yData.set("viaDown", objectToMap(item.data.viaDown));
              yData.set("modeCool", objectToMap(item.data.modeCool));
              yData.set("modeHeat", objectToMap(item.data.modeHeat));
              yData.set("modeAuto", objectToMap(item.data.modeAuto));
              yData.set("modeFan", objectToMap(item.data.modeFan));
              yData.set("fanAuto", objectToMap(item.data.fanAuto));
              yData.set("fanLow", objectToMap(item.data.fanLow));
              yData.set("fanMedium", objectToMap(item.data.fanMedium));
              yData.set("fanHigh", objectToMap(item.data.fanHigh));

              const speedsArr = new Y.Array();
              speedsArr.push(item.data.allowedFanSpeeds);
              yData.set("allowedFanSpeeds", speedsArr);

              const modesArr = new Y.Array();
              modesArr.push(item.data.allowedModes);
              yData.set("allowedModes", modesArr);
            })
            .with({ type: "somoShadesContainer" }, (item) => {
              yData.set("via", objectToMap(item.data.via));
              yData.set("viaUp", objectToMap(item.data.viaUp));
              yData.set("viaDown", objectToMap(item.data.viaDown));
            })
            .with({ type: "somoFanContainer" }, (item) => {
              yData.set("via", objectToMap(item.data.via));
              yData.set("viaLow", objectToMap(item.data.viaLow));
              yData.set("viaMed", objectToMap(item.data.viaMed));
              yData.set("viaHigh", objectToMap(item.data.viaHigh));
            })
            .with({ type: "servicePadContainer" }, (item) => {
              yData.set("title", item.data.title);
              yData.set(
                "makeUpRoomButton",
                objectToMap(item.data.makeUpRoomButton),
              );
              yData.set(
                "doorbellButton",
                objectToMap(item.data.doorbellButton),
              );
              yData.set(
                "doNotDisturbButton",
                objectToMap(item.data.doNotDisturbButton),
              );
            })
            .with({ type: "comment" }, () => {
              yData.set("replies", new Y.Array());
              yData.set("text", new Y.XmlFragment());
            })
            .with(
              { type: "image" },
              { type: "virtualButtonAnchor" },
              { type: "controllerAnchor" },
              { type: "canbusControllerAnchor" },
              { type: "somoSwitchAnchor" },
              { type: "somoDimmerAnchor" },
              { type: "outletDimmerAnchor" },
              { type: "presenceSensorAnchor" },
              { type: "doorSensorAnchor" },
              { type: "somoThermostatAnchor" },
              { type: "somoIrControllerAnchor" },
              { type: "somoShadesAnchor" },
              { type: "somoFanAnchor" },
              { type: "servicePadAnchor" },
              () => {
                //Do nothing
              },
            )
            .exhaustive();

          newNode.set("data", yData);
          yNodes.set(change.item.id, newNode);
        })
        .with({ type: "remove" }, (change) => {
          const node = yNodes.get(change.id) as Y.Map<any> | undefined;
          if (!node) {
            console.warn(`Can't find node ${change.id} for ${change.type}`);
            return;
          }

          // remove all children with the same parentId
          for (const child of yNodes.values()) {
            if (child.get("parentId") === change.id) {
              yNodes.delete(child.get("id"));
            }
          }

          const json = node.toJSON();
          yNodes.delete(change.id);
          if (json.parentId) {
            yNodes.delete(json.parentId);
          }
        })
        .with({ type: "replace" }, (change) => {
          const foundNode = nodes.find((n) => n.id === change.item.id);
          if (!foundNode) {
            console.log("node not found", change.item.id);
            return;
          }
          const foundNoteChangeSet = {
            id: foundNode.id,
            type: foundNode.type,
            width: foundNode.width,
            height: foundNode.height,
            position: foundNode.position,
            data: foundNode.data,
            zIndex: foundNode.zIndex,
            parentId: foundNode.parentId,
            expandParent: foundNode.expandParent,
            extent: foundNode.extent,
            selectable: foundNode.selectable,
            draggable: foundNode.draggable,
            deletable: foundNode.deletable,
            connectable: foundNode.connectable,
          };
          const changeSet = {
            id: change.item.id,
            type: change.item.type,
            width: change.item.width,
            height: change.item.height,
            position: change.item.position,
            data: change.item.data,
            zIndex: change.item.zIndex,
            parentId: change.item.parentId,
            expandParent: change.item.expandParent,
            extent: change.item.extent,
            selectable: change.item.selectable,
            draggable: change.item.draggable,
            deletable: change.item.deletable,
            connectable: change.item.connectable,
          };
          if (deepEqual(foundNoteChangeSet, changeSet)) {
            return;
          }
          const node = yNodes.get(change.id) as Y.Map<any> | undefined;
          if (!node) {
            console.warn(`Can't find node ${change.id} for ${change.type}`);
            return;
          }
          node.set("id", change.item.id);
          node.set("type", change.item.type);
          node.set("width", change.item.width);
          node.set("height", change.item.height);
          node.set("draggable", change.item.draggable);
          node.set("selectable", change.item.selectable);
          node.set("connectable", change.item.connectable);
          node.set("deletable", change.item.deletable);
          node.set("zIndex", change.item.zIndex);
          node.set("parentId", change.item.parentId);
          node.set("expandParent", change.item.expandParent);
          node.set("extent", change.item.extent);
          let data = node.get("data") as Y.Map<any>;
          if (!data) {
            data = new Y.Map();
            node.set("data", data);
          }
          for (const [key, value] of Object.entries(change.item.data)) {
            // check if it's a yjs type (we handle those somewhere else)
            if (
              data.get(key) instanceof Y.XmlFragment ||
              data.get(key) instanceof Y.Map ||
              data.get(key) instanceof Y.Array
            ) {
              continue;
            }
            data.set(key, value);
          }
          const position = node.get("position") as Y.Map<number>;
          if (!position) {
            return;
          }
          position.set("x", change.item.position.x);
          position.set("y", change.item.position.y);
        })
        .with({ type: "select" }, () => {
          // do nothing
        })
        .exhaustive();
    }
  });
}
