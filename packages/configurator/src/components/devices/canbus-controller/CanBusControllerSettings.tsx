import { getNodeData } from "@/components/graph/Graph";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { ActionSettings } from "@/components/graph/settings/ActionSettings";
import { LampPopover } from "@/components/graph/settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import {
  getDeviceDimmingSpeed,
  isDeviceConnectionAllowed,
} from "@/components/graph/utils/deviceUtils";
import { DeviceIconKey } from "@/components/icons/DeviceIcons";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { TooltipDropdownMenu } from "@/components/ui/tooltip-dropdown-menu";
import { cn } from "@/lib/classNames";
import { objectToMap } from "@/lib/yjsUtils";
import {
  CanBusControllerContainerNodeData,
  createDefaultRelayConnectors,
  createDefaultThreePinPorts,
  createDefaultTwoPinPorts,
  createDefaultZeroToTenVoltPorts,
  filterNodes,
  GraphNodesByType,
  MomentaryCanBusController,
  randomId,
  ToggleCanBusController,
} from "@somo/shared";
import { ChevronDown, PlusIcon, Trash2Icon } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { match } from "ts-pattern";
import * as Y from "yjs";
import { addAction } from "../addAction";

function ConnectedControllerSettings({
  controller,
  yControllersMap,
  reactFlowInstance,
  readOnly,
}: {
  controller: CanBusControllerContainerNodeData["controllers"][0];
  yControllersMap: Y.Map<any> | null;
  reactFlowInstance: any;
  readOnly: boolean;
}) {
  const yControllerMap = useMemo(() => {
    if (!yControllersMap) {
      return null;
    }
    return yControllersMap.get(controller.id) as Y.Map<any> | null;
  }, [yControllersMap, controller.id]);

  const { nodes } = useReactFlowContext();

  const sectionDeviceNodes = useMemo(() => {
    const usedNodeIds: string[] = [];
    if (controller.type === "toggle") {
      usedNodeIds.push(
        ...Object.values(controller.onUpClick ?? {}).map(
          (action) => action.deviceId,
        ),
        ...Object.values(controller.onUpHold ?? {}).map(
          (action) => action.deviceId,
        ),
        ...Object.values(controller.onDownClick ?? {}).map(
          (action) => action.deviceId,
        ),
        ...Object.values(controller.onDownHold ?? {}).map(
          (action) => action.deviceId,
        ),
      );
    } else if (controller.type === "momentary") {
      usedNodeIds.push(
        ...Object.values(controller.onUpClick ?? {}).map(
          (action) => action.deviceId,
        ),
        ...Object.values(controller.onUpHold ?? {}).map(
          (action) => action.deviceId,
        ),
      );
    }
    const foundSectionNodes = filterNodes(nodes ?? [], "section").filter(
      (node) => !usedNodeIds.includes(node.id),
    );
    const foundDevices = foundSectionNodes
      .flatMap((node) =>
        Object.values(node.data.devices ?? {}).map((device) => ({
          ...device,
          sectionNode: node,
        })),
      )
      .filter((device) =>
        isDeviceConnectionAllowed("canbusControllerContainer", device.type),
      );

    return foundDevices;
  }, [reactFlowInstance, controller, nodes]);

  const sortedOnUpClickActions = useMemo(() => {
    const actions = Object.values(controller.onUpClick ?? {});
    return actions.sort((a, b) => a.sortIndex - b.sortIndex);
  }, [controller]);

  const sortedOnUpHoldActions = useMemo(() => {
    const actions = Object.values(controller.onUpHold ?? {});
    return actions.sort((a, b) => a.sortIndex - b.sortIndex);
  }, [controller]);

  const sortedOnDownClickActions = useMemo(() => {
    if (controller.type !== "toggle") {
      return [];
    }
    const actions = Object.values(controller.onDownClick ?? {});
    return actions.sort((a, b) => a.sortIndex - b.sortIndex);
  }, [controller]);

  const sortedOnDownHoldActions = useMemo(() => {
    if (controller.type !== "toggle") {
      return [];
    }
    const actions = Object.values(controller.onDownHold ?? {});
    return actions.sort((a, b) => a.sortIndex - b.sortIndex);
  }, [controller]);

  // Calculate unused devices per action type
  const getUnusedDevicesForActionType = (actionType: string) => {
    const actionsForType = match(actionType)
      .with("onUpClick", () => sortedOnUpClickActions)
      .with("onUpHold", () => sortedOnUpHoldActions)
      .with("onDownClick", () => sortedOnDownClickActions)
      .with("onDownHold", () => sortedOnDownHoldActions)
      .otherwise(() => []);

    return sectionDeviceNodes.filter((device) => {
      const isUsed = actionsForType.some(
        (action) => action.deviceId === device.id,
      );
      return !isUsed;
    });
  };

  // Helper to get the first unused device id for a given action type
  const getFirstUnusedDeviceIdForActionType = (actionType: string) => {
    const unused = getUnusedDevicesForActionType(actionType);
    return unused.length > 0 ? unused[0].id : undefined;
  };

  return (
    <div className="ml-4 space-y-2 border-l-2 border-gray-200 pl-4">
      <SettingsInput
        label="Device Name"
        value={controller.label}
        onEndEdit={(value) => {
          if (typeof value !== "string") {
            return;
          }
          yControllerMap?.set("label", value);
        }}
        disabled={readOnly}
      />

      {controller.type === "toggle" && (
        <>
          <div className="flex flex-row justify-between items-center gap-2 -mb-2">
            <SettingsLabel>On up click</SettingsLabel>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-5"
                    onClick={() => {
                      const firstDeviceId =
                        getFirstUnusedDeviceIdForActionType("onUpClick");
                      if (!yControllerMap || !firstDeviceId) {
                        return;
                      }

                      const dimSpeed = getDeviceDimmingSpeed(
                        firstDeviceId,
                        sectionDeviceNodes,
                        nodes,
                      );

                      addAction(yControllerMap, {
                        device: "toggleCanBusController",
                        key: "onUpClick",
                        params: {
                          deviceId: firstDeviceId,
                          dimSpeed: dimSpeed,
                          targetValue: 100,
                          onValue: 100,
                          offValue: 0,
                          type: "lighting",
                        },
                      });
                    }}
                  >
                    <PlusIcon className="size-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add action</TooltipContent>
              </Tooltip>
            )}
          </div>
          {sortedOnUpClickActions.length === 0 && (
            <div className="text-gray-500 text-xs">No actions defined.</div>
          )}
          {sortedOnUpClickActions.map((action) => (
            <ActionSettings
              key={action.id}
              action={action}
              actionType="onUpClick"
              yControllerMap={yControllerMap}
              sectionDeviceNodes={sectionDeviceNodes}
              type="toggle"
              readOnly={readOnly}
            />
          ))}

          <div className="flex flex-row justify-between items-center gap-2 -mb-2">
            <SettingsLabel>On up hold</SettingsLabel>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-5"
                    onClick={() => {
                      const firstDeviceId =
                        getFirstUnusedDeviceIdForActionType("onUpHold");
                      if (!yControllerMap || !firstDeviceId) {
                        return;
                      }

                      const dimSpeed = getDeviceDimmingSpeed(
                        firstDeviceId,
                        sectionDeviceNodes,
                        nodes,
                      );

                      addAction(yControllerMap, {
                        device: "toggleCanBusController",
                        key: "onUpHold",
                        params: {
                          deviceId: firstDeviceId,
                          dimSpeed,
                          targetValue: 100,
                          onValue: 100,
                          offValue: 0,
                          type: "lighting",
                        },
                      });
                    }}
                  >
                    <PlusIcon className="size-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add action</TooltipContent>
              </Tooltip>
            )}
          </div>
          {sortedOnUpHoldActions.length === 0 && (
            <div className="text-gray-500 text-xs">No actions defined.</div>
          )}
          {sortedOnUpHoldActions.map((action) => (
            <ActionSettings
              key={action.id}
              action={action}
              actionType="onUpHold"
              yControllerMap={yControllerMap}
              sectionDeviceNodes={sectionDeviceNodes}
              type="toggle"
              readOnly={readOnly}
            />
          ))}

          <div className="flex flex-row justify-between items-center gap-2 -mb-2">
            <SettingsLabel>On down click</SettingsLabel>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-5"
                    onClick={() => {
                      const firstDeviceId =
                        getFirstUnusedDeviceIdForActionType("onDownClick");
                      if (!yControllerMap || !firstDeviceId) {
                        return;
                      }
                      const dimSpeed = getDeviceDimmingSpeed(
                        firstDeviceId,
                        sectionDeviceNodes,
                        nodes,
                      );

                      addAction(yControllerMap, {
                        device: "toggleCanBusController",
                        key: "onDownClick",
                        params: {
                          deviceId: firstDeviceId,
                          dimSpeed,
                          targetValue: 0,
                          onValue: 100,
                          offValue: 0,
                          type: "lighting",
                        },
                      });
                    }}
                  >
                    <PlusIcon className="size-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add action</TooltipContent>
              </Tooltip>
            )}
          </div>
          {sortedOnDownClickActions.length === 0 && (
            <div className="text-gray-500 text-xs">No actions defined.</div>
          )}
          {sortedOnDownClickActions.map((action) => (
            <ActionSettings
              key={action.id}
              action={action}
              actionType="onDownClick"
              yControllerMap={yControllerMap}
              sectionDeviceNodes={sectionDeviceNodes}
              type="toggle"
              readOnly={readOnly}
            />
          ))}

          <div className="flex flex-row justify-between items-center gap-2 -mb-2">
            <SettingsLabel>On down hold</SettingsLabel>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-5"
                    onClick={() => {
                      const firstDeviceId =
                        getFirstUnusedDeviceIdForActionType("onDownHold");
                      if (!yControllerMap || !firstDeviceId) {
                        return;
                      }
                      const dimSpeed = getDeviceDimmingSpeed(
                        firstDeviceId,
                        sectionDeviceNodes,
                        nodes,
                      );

                      addAction(yControllerMap, {
                        device: "toggleCanBusController",
                        key: "onDownHold",
                        params: {
                          deviceId: firstDeviceId,
                          dimSpeed,
                          targetValue: 0,
                          onValue: 100,
                          offValue: 0,
                          type: "lighting",
                        },
                      });
                    }}
                  >
                    <PlusIcon className="size-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add action</TooltipContent>
              </Tooltip>
            )}
          </div>
          {sortedOnDownHoldActions.length === 0 && (
            <div className="text-gray-500 text-xs">No actions defined.</div>
          )}
          {sortedOnDownHoldActions.map((action) => (
            <ActionSettings
              key={action.id}
              action={action}
              actionType="onDownHold"
              yControllerMap={yControllerMap}
              sectionDeviceNodes={sectionDeviceNodes}
              type="toggle"
              readOnly={readOnly}
            />
          ))}
        </>
      )}

      {controller.type === "momentary" && (
        <>
          <div className="flex flex-row justify-between items-center gap-2 -mb-2">
            <SettingsLabel>On click</SettingsLabel>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-5"
                    onClick={() => {
                      const firstDeviceId =
                        getFirstUnusedDeviceIdForActionType("onUpClick");
                      if (!yControllerMap || !firstDeviceId) {
                        return;
                      }
                      const dimSpeed = getDeviceDimmingSpeed(
                        firstDeviceId,
                        sectionDeviceNodes,
                        nodes,
                      );

                      addAction(yControllerMap, {
                        device: "momentaryCanBusController",
                        key: "onUpClick",
                        params: {
                          deviceId: firstDeviceId,
                          dimSpeed,
                          targetValue: 100,
                          onValue: 100,
                          offValue: 0,
                          type: "lighting",
                        },
                      });
                    }}
                  >
                    <PlusIcon className="size-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add action</TooltipContent>
              </Tooltip>
            )}
          </div>
          {sortedOnUpClickActions.length === 0 && (
            <div className="text-gray-500 text-xs">No actions defined.</div>
          )}
          {sortedOnUpClickActions.map((action) => (
            <ActionSettings
              key={action.id}
              action={action}
              actionType="onUpClick"
              yControllerMap={yControllerMap}
              sectionDeviceNodes={sectionDeviceNodes}
              type="momentary"
              readOnly={readOnly}
            />
          ))}

          <div className="flex flex-row justify-between items-center gap-2 -mb-2">
            <SettingsLabel>On hold</SettingsLabel>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-5"
                    onClick={() => {
                      const firstDeviceId =
                        getFirstUnusedDeviceIdForActionType("onUpHold");
                      if (!yControllerMap || !firstDeviceId) {
                        return;
                      }
                      const dimSpeed = getDeviceDimmingSpeed(
                        firstDeviceId,
                        sectionDeviceNodes,
                        nodes,
                      );
                      addAction(yControllerMap, {
                        device: "momentaryCanBusController",
                        key: "onUpHold",
                        params: {
                          deviceId: firstDeviceId,
                          dimSpeed,
                          targetValue: 100,
                          onValue: 100,
                          offValue: 0,
                          type: "lighting",
                        },
                      });
                    }}
                  >
                    <PlusIcon className="size-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add action</TooltipContent>
              </Tooltip>
            )}
          </div>
          {sortedOnUpHoldActions.length === 0 && (
            <div className="text-gray-500 text-xs">No actions defined.</div>
          )}
          {sortedOnUpHoldActions.map((action) => (
            <ActionSettings
              key={action.id}
              action={action}
              actionType="onUpHold"
              yControllerMap={yControllerMap}
              sectionDeviceNodes={sectionDeviceNodes}
              type="momentary"
              readOnly={readOnly}
            />
          ))}
        </>
      )}
    </div>
  );
}

function ConnectedPirSensorSettings({
  sensor,
  nodeId,
  ydoc,
  reactFlowInstance,
  readOnly,
}: {
  sensor: any;
  nodeId: string;
  ydoc: any;
  reactFlowInstance: any;
  readOnly: boolean;
}) {
  const ySensorMap = useMemo(() => {
    const yDataMap = getNodeData(ydoc, nodeId);
    const pirSensorsMap = yDataMap?.get("pirSensors") as Y.Map<any>;
    return pirSensorsMap?.get(sensor.id) as Y.Map<any> | null;
  }, [ydoc, nodeId, sensor.id]);

  const { nodes } = useReactFlowContext();

  const sectionDeviceNodes = useMemo(() => {
    const usedNodeIds: string[] = [];
    usedNodeIds.push(
      ...Object.values(sensor.onActivate ?? {}).map(
        (action: any) => action.deviceId,
      ),
      ...Object.values(sensor.onDeactivate ?? {}).map(
        (action: any) => action.deviceId,
      ),
    );
    const foundSectionNodes = filterNodes(nodes ?? [], "section").filter(
      (node) => !usedNodeIds.includes(node.id),
    );
    const foundDevices = foundSectionNodes
      .flatMap((node) =>
        Object.values(node.data.devices ?? {}).map((device) => ({
          ...device,
          sectionNode: node,
        })),
      )
      .filter((device) =>
        isDeviceConnectionAllowed("canbusControllerContainer", device.type),
      );

    return foundDevices;
  }, [reactFlowInstance, sensor, nodes]);

  const sortedOnActivateActions = useMemo(() => {
    const actions = Object.values(sensor.onActivate ?? {}) as any[];
    return actions.sort((a: any, b: any) => a.sortIndex - b.sortIndex);
  }, [sensor]);

  const sortedOnDeactivateActions = useMemo(() => {
    const actions = Object.values(sensor.onDeactivate ?? {}) as any[];
    return actions.sort((a: any, b: any) => a.sortIndex - b.sortIndex);
  }, [sensor]);

  // Calculate unused devices per action type for PIR sensors
  const getUnusedDevicesForActionType = (actionType: string) => {
    const actionsForType = match(actionType)
      .with("onActivate", () => sortedOnActivateActions)
      .with("onDeactivate", () => sortedOnDeactivateActions)
      .otherwise(() => []);

    return sectionDeviceNodes.filter((device) => {
      const isUsed = actionsForType.some(
        (action: any) => action.deviceId === device.id,
      );
      return !isUsed;
    });
  };

  // Helper to get the first unused device id for a given action type
  const getFirstUnusedDeviceIdForActionType = (actionType: string) => {
    const unused = getUnusedDevicesForActionType(actionType);
    return unused.length > 0 ? unused[0].id : undefined;
  };

  return (
    <div className="ml-4 space-y-2 border-l-2 border-gray-200 pl-4">
      <SettingsInput
        label="Device Name"
        value={sensor.label}
        onEndEdit={(value) => {
          if (typeof value !== "string") {
            return;
          }
          ySensorMap?.set("label", value);
        }}
        disabled={readOnly}
      />

      <div className="flex flex-row justify-between items-center gap-2 -mb-2">
        <SettingsLabel>On activate</SettingsLabel>
        {!readOnly && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="size-5"
                onClick={() => {
                  const firstDeviceId =
                    getFirstUnusedDeviceIdForActionType("onActivate");
                  if (!ySensorMap || !firstDeviceId) {
                    return;
                  }
                  const dimSpeed = getDeviceDimmingSpeed(
                    firstDeviceId,
                    sectionDeviceNodes,
                    nodes,
                  );
                  addAction(ySensorMap, {
                    device: "pirSensor",
                    key: "onActivate",
                    params: {
                      deviceId: firstDeviceId,
                      dimSpeed,
                      targetValue: 100,
                      onValue: 100,
                      offValue: 0,
                      type: "lighting",
                    },
                  });
                }}
              >
                <PlusIcon className="size-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Add action</TooltipContent>
          </Tooltip>
        )}
      </div>
      {sortedOnActivateActions.length === 0 && (
        <div className="text-gray-500 text-xs">No actions defined.</div>
      )}
      {sortedOnActivateActions.map((action: any) => (
        <ActionSettings
          key={action.id}
          action={action}
          actionType="onActivate"
          yControllerMap={ySensorMap}
          sectionDeviceNodes={sectionDeviceNodes}
          type="pir"
          readOnly={readOnly}
        />
      ))}

      <div className="flex flex-row justify-between items-center gap-2 -mb-2">
        <SettingsLabel>On deactivate</SettingsLabel>
        {!readOnly && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="size-5"
                onClick={() => {
                  const firstDeviceId =
                    getFirstUnusedDeviceIdForActionType("onDeactivate");
                  if (!ySensorMap || !firstDeviceId) {
                    return;
                  }
                  const dimSpeed = getDeviceDimmingSpeed(
                    firstDeviceId,
                    sectionDeviceNodes,
                    nodes,
                  );
                  addAction(ySensorMap, {
                    device: "pirSensor",
                    key: "onDeactivate",
                    params: {
                      deviceId: firstDeviceId,
                      dimSpeed,
                      targetValue: 0,
                      onValue: 100,
                      offValue: 0,
                      type: "lighting",
                    },
                  });
                }}
              >
                <PlusIcon className="size-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Add action</TooltipContent>
          </Tooltip>
        )}
      </div>
      {sortedOnDeactivateActions.length === 0 && (
        <div className="text-gray-500 text-xs">No actions defined.</div>
      )}
      {sortedOnDeactivateActions.map((action: any) => (
        <ActionSettings
          key={action.id}
          action={action}
          actionType="onDeactivate"
          yControllerMap={ySensorMap}
          sectionDeviceNodes={sectionDeviceNodes}
          type="pir"
          readOnly={readOnly}
        />
      ))}
    </div>
  );
}

function ConnectedZeroToTenVoltDimmerSettings({
  dimmer,
  nodeId,
  ydoc,
  readOnly,
}: {
  dimmer: any;
  nodeId: string;
  ydoc: any;
  readOnly: boolean;
}) {
  const { nodes } = useReactFlowContext();

  const yDimmerMap = useMemo(() => {
    const yDataMap = getNodeData(ydoc, nodeId);
    const dimmersMap = yDataMap?.get("zeroToTenVoltDimmers") as Y.Map<any>;
    return dimmersMap?.get(dimmer.id) as Y.Map<any> | null;
  }, [ydoc, nodeId, dimmer.id]);

  const yRelayConnectorsMap = useMemo(() => {
    const yDataMap = getNodeData(ydoc, nodeId);
    return yDataMap?.get("relayConnectors") as Y.Map<any> | undefined;
  }, [ydoc, nodeId]);

  const currentNode = useMemo(() => {
    return nodes.find((node) => node.id === nodeId);
  }, [nodes, nodeId]);

  const availableRelayConnectors = useMemo(() => {
    if (
      !yRelayConnectorsMap ||
      !currentNode ||
      currentNode.type !== "canbusControllerContainer"
    ) {
      return [];
    }

    const nodeData = currentNode.data;
    const relayConnectors = nodeData.relayConnectors || {};
    const relayOutputs = nodeData.relayOutputs || {};
    const connectors: any[] = [];

    Object.values(relayConnectors).forEach((connector: any) => {
      // Check if this connector is occupied by a relay output
      const isOccupiedByRelayOutput = Object.values(relayOutputs).some(
        (output: any) => output.relayConnectorId === connector.id,
      );

      // A connector is available if:
      // 1. It's not connected to anything AND not occupied by a relay output, OR
      // 2. It's already connected to this specific dimmer
      const isAvailable =
        (connector.deviceType === "none" &&
          !connector.connected &&
          !isOccupiedByRelayOutput) ||
        (connector.deviceType === "zeroToTenVoltDimmer" &&
          connector.dimmerId === dimmer.id);

      if (isAvailable) {
        connectors.push(connector);
      }
    });

    return connectors;
  }, [yRelayConnectorsMap, currentNode, dimmer.id]);

  const hasAvailableRelayConnectors = availableRelayConnectors.length > 0;
  const canUseRelay = hasAvailableRelayConnectors || dimmer.useRelay;

  const handleRelayToggle = (checked: boolean) => {
    if (readOnly) {
      return;
    }

    if (checked && hasAvailableRelayConnectors) {
      // Assign first available relay connector
      const firstAvailable = availableRelayConnectors[0];
      yDimmerMap?.set("useRelay", true);
      yDimmerMap?.set("relayConnectorId", firstAvailable.id);

      // Update relay connector status
      const relayConnectorMap = yRelayConnectorsMap?.get(firstAvailable.id);
      if (relayConnectorMap) {
        relayConnectorMap.set("connected", true);
        relayConnectorMap.set("deviceType", "zeroToTenVoltDimmer");
        relayConnectorMap.set("dimmerId", dimmer.id);
      }
    } else if (!checked && dimmer.relayConnectorId) {
      // Release the assigned relay connector
      yDimmerMap?.set("useRelay", false);
      yDimmerMap?.delete("relayConnectorId");

      const relayConnectorMap = yRelayConnectorsMap?.get(
        dimmer.relayConnectorId,
      );
      if (relayConnectorMap) {
        relayConnectorMap.set("connected", false);
        relayConnectorMap.set("deviceType", "none");
        relayConnectorMap.delete("dimmerId");
      }
    }
  };

  return (
    <div className="ml-4 space-y-2 border-l-2 border-gray-200 pl-4">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
        <LampPopover
          activeIconKey={dimmer.icon}
          onIconClick={(iconKey: DeviceIconKey | undefined) => {
            yDimmerMap?.set("icon", iconKey);
          }}
          disabled={readOnly}
        />
        <SettingsInput
          className="text-sm font-normal h-10"
          value={dimmer.name}
          onEndEdit={(value) => {
            if (typeof value !== "string") {
              return;
            }
            yDimmerMap?.set("name", value);
          }}
          disabled={readOnly}
        />
      </div>

      <SettingsRow>
        <SettingsLabel>Dimming Type</SettingsLabel>
        <select
          value={dimmer.dimmingType}
          onChange={(e) => {
            if (readOnly) {
              return;
            }
            yDimmerMap?.set("dimmingType", e.target.value);
          }}
          disabled={readOnly}
          className="px-2 py-1 border rounded text-sm"
        >
          <option value="sinking">Sinking</option>
          <option value="sourcing">Sourcing</option>
        </select>
      </SettingsRow>

      <SettingsRow>
        <div className="flex items-center gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-2">
                <Checkbox
                  id={`relay-${dimmer.id}`}
                  checked={dimmer.useRelay || false}
                  disabled={readOnly || !canUseRelay}
                  onCheckedChange={(checked) =>
                    handleRelayToggle(checked as boolean)
                  }
                />
                <Label
                  htmlFor={`relay-${dimmer.id}`}
                  className="text-gray-500 text-xs font-semibold"
                >
                  Use Relay
                </Label>
              </div>
            </TooltipTrigger>
            {!canUseRelay && (
              <TooltipContent>
                No relay connectors available. All 4 relay connectors are
                currently in use.
              </TooltipContent>
            )}
          </Tooltip>
        </div>
      </SettingsRow>

      <div className="flex flex-row items-center gap-2 flex-wrap">
        <SettingsInput
          className="w-auto flex-shrink-0"
          inputClassName="w-[60px] text-center flex-shrink-0"
          label="Min. brightness"
          value={(dimmer.minBrightness ?? 0).toString()}
          disabled={readOnly}
          onEndEdit={(value) => {
            if (readOnly) {
              return;
            }
            const minBrightness = parseInt(value);
            if (isNaN(minBrightness)) {
              return;
            }
            yDimmerMap?.set("minBrightness", minBrightness);
          }}
        />
        <SettingsInput
          className="w-auto flex-shrink-0"
          inputClassName="w-[60px] text-center flex-shrink-0"
          label="Max. brightness"
          value={(dimmer.maxBrightness ?? 100).toString()}
          disabled={readOnly}
          onEndEdit={(value) => {
            if (readOnly) {
              return;
            }
            const maxBrightness = parseInt(value);
            if (isNaN(maxBrightness)) {
              return;
            }
            yDimmerMap?.set("maxBrightness", maxBrightness);
          }}
        />
      </div>

      <SettingsInput
        className="w-auto"
        inputClassName="w-[60px] text-center"
        label="Default dim speed (sec)"
        value={(dimmer.defaultDimmingSpeed ?? 0.2).toString()}
        disabled={readOnly}
        onEndEdit={(value) => {
          if (readOnly) {
            return;
          }
          const dimSpeed = parseFloat(value);
          if (isNaN(dimSpeed)) {
            return;
          }
          yDimmerMap?.set("defaultDimmingSpeed", dimSpeed);
        }}
      />
    </div>
  );
}

function ConnectedRelayOutputSettings({
  connectedDevice,
  yDataMap,
  readOnly,
}: {
  connectedDevice: { id: string; name: string; icon?: string };
  yDataMap: Y.Map<any>;
  readOnly?: boolean;
}) {
  const devicesMap = yDataMap?.get("relayOutputs") as Y.Map<any>;
  const yDeviceMap = devicesMap?.get(connectedDevice.id) as Y.Map<any>;

  return (
    <div className="ml-4 space-y-2 border-l-2 border-gray-200 pl-4">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
        <LampPopover
          activeIconKey={connectedDevice.icon as DeviceIconKey}
          onIconClick={(iconKey: DeviceIconKey | undefined) => {
            yDeviceMap?.set("icon", iconKey);
          }}
          disabled={readOnly}
        />
        <SettingsInput
          className="text-sm font-normal h-10"
          value={connectedDevice.name}
          onEndEdit={(value) => {
            if (typeof value !== "string") {
              return;
            }
            yDeviceMap?.set("name", value);
          }}
          disabled={readOnly}
        />
      </div>
    </div>
  );
}

export function CanBusControllerSettings({
  node,
}: {
  node: GraphNodesByType["canbusControllerContainer"];
}) {
  const { reactFlowInstance, ydoc, readOnly } = useReactFlowContext();

  // Add port collapsed state
  const [portCollapsedState, setPortCollapsedState] = useState<{
    [portId: string]: boolean;
  }>({});

  const yControllersMap = useMemo(() => {
    if (!ydoc) {
      return null;
    }
    const dataMap = getNodeData(ydoc, node.id);
    const controllersMap = dataMap?.get("controllers") as
      | Y.Map<any>
      | undefined;
    if (!controllersMap && dataMap) {
      const newControllersMap = new Y.Map();
      dataMap?.set("controllers", newControllersMap);
      return newControllersMap as Y.Map<any>;
    }
    return controllersMap as Y.Map<any>;
  }, [ydoc, node.id]);

  // Initialize ports if they don't exist
  useEffect(() => {
    const yDataMap = getNodeData(ydoc, node.id);
    if (yDataMap) {
      // Use helper functions from shared package
      if (!yDataMap.has("threePinPorts")) {
        yDataMap.set(
          "threePinPorts",
          objectToMap(createDefaultThreePinPorts()),
        );
      }
      if (!yDataMap.has("twoPinPorts")) {
        yDataMap.set("twoPinPorts", objectToMap(createDefaultTwoPinPorts()));
      }
      if (!yDataMap.has("zeroToTenVoltPorts")) {
        yDataMap.set(
          "zeroToTenVoltPorts",
          objectToMap(createDefaultZeroToTenVoltPorts()),
        );
      }
      if (!yDataMap.has("relayConnectors")) {
        yDataMap.set(
          "relayConnectors",
          objectToMap(createDefaultRelayConnectors()),
        );
      }
      if (!yDataMap.has("pirSensors")) {
        yDataMap.set("pirSensors", new Y.Map());
      }
      if (!yDataMap.has("zeroToTenVoltDimmers")) {
        yDataMap.set("zeroToTenVoltDimmers", new Y.Map());
      }
      if (!yDataMap.has("relayOutputs")) {
        yDataMap.set("relayOutputs", new Y.Map());
      }
    }
  }, [ydoc, node.id]);

  const [sortedControllers, setSortableControllers] = useState<
    (MomentaryCanBusController | ToggleCanBusController)[]
  >([]);

  useEffect(() => {
    // get all objects from node.data.controllers
    const controllers = Object.values(node.data.controllers ?? {});
    // sort devices by sortIndex
    controllers.sort((a, b) => a.sortIndex - b.sortIndex);
    setSortableControllers(controllers);
  }, [node.data.controllers]);

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={node.data.title}
              onEndEdit={(value) => {
                if (typeof value !== "string") {
                  return;
                }
                reactFlowInstance?.updateNodeData(node.id, {
                  title: value,
                });
              }}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader>
          <span>3-Pin Ports</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {Object.values(node.data.threePinPorts || {}).map((port) => {
            const connectedController = Object.values(
              node.data.controllers || {},
            ).find((controller) => controller.portId === port.id);

            return (
              <SettingsRow key={port.id} className="flex flex-col gap-2">
                <div
                  className={cn(
                    "flex flex-row items-center justify-between cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded",
                    connectedController && "cursor-pointer",
                  )}
                  onClick={() => {
                    if (connectedController) {
                      setPortCollapsedState((prev) => ({
                        ...prev,
                        [port.id]: !prev[port.id],
                      }));
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    {connectedController && (
                      <ChevronDown
                        className={cn(
                          "size-4 transition-transform duration-300 text-gray-500",
                          portCollapsedState[port.id] && "-rotate-90",
                        )}
                      />
                    )}
                    <div className="flex flex-col">
                      <div className="text-sm font-medium">{port.label}</div>
                      <div className="text-xs text-gray-500">
                        {connectedController
                          ? `${connectedController.type === "toggle" ? "Toggle" : "Momentary"}: ${connectedController.label}`
                          : "Not connected"}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "size-3 rounded-full",
                        connectedController ? "bg-green-400" : "bg-gray-400",
                      )}
                    />
                    {!connectedController && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TooltipDropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="size-5"
                              >
                                <PlusIcon className="size-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="mr-5">
                              <DropdownMenuLabel>
                                Add device to this port
                              </DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => {
                                  if (yControllersMap) {
                                    const index = sortedControllers.length;
                                    const controller: CanBusControllerContainerNodeData["controllers"][0] =
                                      {
                                        id: randomId(),
                                        label: "Toggle",
                                        sortIndex: index,
                                        type: "toggle",
                                        portId: port.id,
                                        onUpClick: {},
                                        onUpHold: {},
                                        onDownClick: {},
                                        onDownHold: {},
                                      };
                                    yControllersMap.set(
                                      controller.id,
                                      objectToMap(controller),
                                    );
                                    // Update port status
                                    const yDataMap = getNodeData(ydoc, node.id);
                                    const threePinPorts = yDataMap?.get(
                                      "threePinPorts",
                                    ) as Y.Map<any> | undefined;
                                    if (threePinPorts) {
                                      const portMap = threePinPorts.get(
                                        port.id,
                                      ) as Y.Map<any>;
                                      if (portMap) {
                                        portMap.set("connected", true);
                                        portMap.set("deviceType", "toggle");
                                        portMap.set(
                                          "controllerId",
                                          controller.id,
                                        );
                                      }
                                    }
                                  }
                                }}
                              >
                                Toggle Button
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  if (yControllersMap) {
                                    const index = sortedControllers.length;
                                    const controller: CanBusControllerContainerNodeData["controllers"][0] =
                                      {
                                        id: randomId(),
                                        label: "Momentary",
                                        sortIndex: index,
                                        type: "momentary",
                                        portId: port.id,
                                        onUpClick: {},
                                        onUpHold: {},
                                      };
                                    yControllersMap.set(
                                      controller.id,
                                      objectToMap(controller),
                                    );
                                    // Update port status
                                    const yDataMap = getNodeData(ydoc, node.id);
                                    const threePinPorts = yDataMap?.get(
                                      "threePinPorts",
                                    ) as Y.Map<any> | undefined;
                                    if (threePinPorts) {
                                      const portMap = threePinPorts.get(
                                        port.id,
                                      ) as Y.Map<any>;
                                      if (portMap) {
                                        portMap.set("connected", true);
                                        portMap.set("deviceType", "momentary");
                                        portMap.set(
                                          "controllerId",
                                          controller.id,
                                        );
                                      }
                                    }
                                  }
                                }}
                              >
                                Momentary Button
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </TooltipDropdownMenu>
                        </TooltipTrigger>
                        <TooltipContent>Add device</TooltipContent>
                      </Tooltip>
                    )}
                    {connectedController && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent collapse/expand when clicking remove
                              // Remove controller
                              yControllersMap?.delete(connectedController.id);

                              const yDataMap = getNodeData(ydoc, node.id);
                              const threePinPorts = yDataMap?.get(
                                "threePinPorts",
                              ) as Y.Map<any>;
                              const portMap = threePinPorts?.get(
                                port.id,
                              ) as Y.Map<any>;
                              if (portMap) {
                                portMap.set("connected", false);
                                portMap.set("deviceType", "none");
                                portMap.delete("controllerId");
                              }
                            }}
                          >
                            <Trash2Icon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Remove Device</TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </div>

                {connectedController &&
                  !portCollapsedState[port.id] &&
                  yControllersMap && (
                    <ConnectedControllerSettings
                      controller={connectedController}
                      yControllersMap={yControllersMap}
                      reactFlowInstance={reactFlowInstance}
                      readOnly={readOnly}
                    />
                  )}
              </SettingsRow>
            );
          })}
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader>
          <span>2-Pin Ports</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {Object.values(node.data.twoPinPorts || {}).map((port) => {
            const connectedSensor = Object.values(
              node.data.pirSensors || {},
            ).find((sensor) => sensor.portId === port.id);

            return (
              <SettingsRow key={port.id} className="flex flex-col gap-2">
                <div
                  className={cn(
                    "flex flex-row items-center justify-between cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded",
                    connectedSensor && "cursor-pointer",
                  )}
                  onClick={() => {
                    if (connectedSensor) {
                      setPortCollapsedState((prev) => ({
                        ...prev,
                        [port.id]: !prev[port.id],
                      }));
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    {connectedSensor && (
                      <ChevronDown
                        className={cn(
                          "size-4 transition-transform duration-300 text-gray-500",
                          portCollapsedState[port.id] && "-rotate-90",
                        )}
                      />
                    )}
                    <div className="flex flex-col">
                      <div className="text-sm font-medium">{port.label}</div>
                      <div className="text-xs text-gray-500">
                        {connectedSensor
                          ? `PIR: ${connectedSensor.label}`
                          : "Not connected"}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "size-3 rounded-full",
                        connectedSensor ? "bg-green-400" : "bg-gray-400",
                      )}
                    />
                    {!connectedSensor && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TooltipDropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="size-5"
                              >
                                <PlusIcon className="size-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="mr-5">
                              <DropdownMenuLabel>
                                Add device to this port
                              </DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => {
                                  const yDataMap = getNodeData(ydoc, node.id);
                                  if (!yDataMap) {
                                    return;
                                  }
                                  // Get or create PIR sensors map
                                  let yPirSensorsMap = yDataMap.get(
                                    "pirSensors",
                                  ) as Y.Map<any> | undefined;
                                  if (!yPirSensorsMap) {
                                    yPirSensorsMap = new Y.Map();
                                    yDataMap.set("pirSensors", yPirSensorsMap);
                                  }
                                  // Create new PIR sensor
                                  const newSensorId = `pir-sensor-${randomId()}`;
                                  const sensorMap = new Y.Map();
                                  sensorMap.set("id", newSensorId);
                                  sensorMap.set("label", `PIR`);
                                  sensorMap.set("portId", port.id);
                                  sensorMap.set("onActivate", new Y.Map());
                                  sensorMap.set("onDeactivate", new Y.Map());
                                  yPirSensorsMap.set(newSensorId, sensorMap);
                                  // Update port status
                                  const yTwoPinPortsMap = yDataMap.get(
                                    "twoPinPorts",
                                  ) as Y.Map<any> | undefined;
                                  if (yTwoPinPortsMap) {
                                    const yPortMap = yTwoPinPortsMap.get(
                                      port.id,
                                    ) as Y.Map<any> | undefined;
                                    if (yPortMap) {
                                      yPortMap.set("connected", true);
                                      yPortMap.set("deviceType", "pir");
                                      yPortMap.set("sensorId", newSensorId);
                                    }
                                  }
                                }}
                              >
                                PIR Sensor
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </TooltipDropdownMenu>
                        </TooltipTrigger>
                        <TooltipContent>Add device</TooltipContent>
                      </Tooltip>
                    )}
                    {connectedSensor && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Remove sensor
                              const yDataMap = getNodeData(ydoc, node.id);
                              if (!yDataMap) {
                                return;
                              }
                              const yPirSensorsMap = yDataMap.get(
                                "pirSensors",
                              ) as Y.Map<any> | undefined;
                              if (yPirSensorsMap) {
                                yPirSensorsMap.delete(connectedSensor.id);
                              }
                              const yTwoPinPortsMap = yDataMap.get(
                                "twoPinPorts",
                              ) as Y.Map<any> | undefined;
                              if (yTwoPinPortsMap) {
                                const yPortMap = yTwoPinPortsMap.get(
                                  port.id,
                                ) as Y.Map<any> | undefined;
                                if (yPortMap) {
                                  yPortMap.set("connected", false);
                                  yPortMap.set("deviceType", "none");
                                  yPortMap.delete("sensorId");
                                }
                              }
                            }}
                          >
                            <Trash2Icon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Remove Device</TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </div>

                {connectedSensor && !portCollapsedState[port.id] && (
                  <ConnectedPirSensorSettings
                    sensor={connectedSensor}
                    nodeId={node.id}
                    ydoc={ydoc}
                    reactFlowInstance={reactFlowInstance}
                    readOnly={readOnly}
                  />
                )}
              </SettingsRow>
            );
          })}
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader>
          <span>0-10V Ports</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {Object.values(node.data.zeroToTenVoltPorts || {}).map((port) => {
            const connectedDimmer = Object.values(
              node.data.zeroToTenVoltDimmers || {},
            ).find((dimmer) => dimmer.portId === port.id);

            return (
              <SettingsRow key={port.id} className="flex flex-col gap-2">
                <div
                  className={cn(
                    "flex flex-row items-center justify-between cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded",
                    connectedDimmer && "cursor-pointer",
                  )}
                  onClick={() => {
                    if (connectedDimmer) {
                      setPortCollapsedState((prev) => ({
                        ...prev,
                        [port.id]: !prev[port.id],
                      }));
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    {connectedDimmer && (
                      <ChevronDown
                        className={cn(
                          "size-4 transition-transform duration-300 text-gray-500",
                          portCollapsedState[port.id] && "-rotate-90",
                        )}
                      />
                    )}
                    <div className="flex flex-col">
                      <div className="text-sm font-medium">{port.label}</div>
                      <div className="text-xs text-gray-500">
                        {connectedDimmer
                          ? `0-10V Dimmer: ${connectedDimmer.name}`
                          : "Not connected"}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "size-3 rounded-full",
                        connectedDimmer ? "bg-green-400" : "bg-gray-400",
                      )}
                    />
                    {!connectedDimmer && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={() => {
                              const yDataMap = getNodeData(ydoc, node.id);
                              if (!yDataMap) {
                                return;
                              }
                              // Get or create 0-10V dimmers map
                              let yDimmersMap = yDataMap.get(
                                "zeroToTenVoltDimmers",
                              ) as Y.Map<any> | undefined;
                              if (!yDimmersMap) {
                                yDimmersMap = new Y.Map();
                                yDataMap.set(
                                  "zeroToTenVoltDimmers",
                                  yDimmersMap,
                                );
                              }
                              // Create new 0-10V dimmer
                              const newDimmerId = `0-10v-dimmer-${randomId()}`;
                              const dimmerMap = new Y.Map();
                              dimmerMap.set("id", newDimmerId);
                              dimmerMap.set("name", "0-10V Dimmer");
                              dimmerMap.set("portId", port.id);
                              dimmerMap.set("dimmingType", "sinking");
                              dimmerMap.set("sortIndex", 0);
                              dimmerMap.set("showLabel", true);
                              dimmerMap.set("icon", "ceilingLamp");
                              dimmerMap.set("useRelay", false);
                              dimmerMap.set("minBrightness", 0);
                              dimmerMap.set("maxBrightness", 100);
                              dimmerMap.set("defaultDimmingSpeed", 0.2);
                              yDimmersMap.set(newDimmerId, dimmerMap);
                              // Update port status
                              const yZeroToTenVoltPortsMap = yDataMap.get(
                                "zeroToTenVoltPorts",
                              ) as Y.Map<any> | undefined;
                              if (yZeroToTenVoltPortsMap) {
                                const yPortMap = yZeroToTenVoltPortsMap.get(
                                  port.id,
                                ) as Y.Map<any> | undefined;
                                if (yPortMap) {
                                  yPortMap.set("connected", true);
                                  yPortMap.set(
                                    "deviceType",
                                    "zeroToTenVoltDimmer",
                                  );
                                  yPortMap.set("dimmerId", newDimmerId);
                                }
                              }
                            }}
                          >
                            <PlusIcon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Add 0-10V Dimmer</TooltipContent>
                      </Tooltip>
                    )}
                    {connectedDimmer && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Release relay connector if assigned
                              if (connectedDimmer.relayConnectorId) {
                                const yDataMap = getNodeData(ydoc, node.id);
                                const relayConnectorsMap = yDataMap?.get(
                                  "relayConnectors",
                                ) as Y.Map<any>;
                                const relayConnectorMap =
                                  relayConnectorsMap?.get(
                                    connectedDimmer.relayConnectorId,
                                  ) as Y.Map<any>;
                                if (relayConnectorMap) {
                                  relayConnectorMap.set("connected", false);
                                  relayConnectorMap.set("deviceType", "none");
                                  relayConnectorMap.delete("dimmerId");
                                }
                              }
                              // Remove dimmer
                              const yDataMap = getNodeData(ydoc, node.id);
                              if (!yDataMap) {
                                return;
                              }
                              const yDimmersMap = yDataMap.get(
                                "zeroToTenVoltDimmers",
                              ) as Y.Map<any> | undefined;
                              if (yDimmersMap) {
                                yDimmersMap.delete(connectedDimmer.id);
                              }
                              const yZeroToTenVoltPortsMap = yDataMap.get(
                                "zeroToTenVoltPorts",
                              ) as Y.Map<any> | undefined;
                              if (yZeroToTenVoltPortsMap) {
                                const yPortMap = yZeroToTenVoltPortsMap.get(
                                  port.id,
                                ) as Y.Map<any> | undefined;
                                if (yPortMap) {
                                  yPortMap.set("connected", false);
                                  yPortMap.set("deviceType", "none");
                                  yPortMap.delete("dimmerId");
                                }
                              }
                            }}
                          >
                            <Trash2Icon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Remove Device</TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </div>

                {connectedDimmer && !portCollapsedState[port.id] && (
                  <ConnectedZeroToTenVoltDimmerSettings
                    dimmer={connectedDimmer}
                    nodeId={node.id}
                    ydoc={ydoc}
                    readOnly={readOnly}
                  />
                )}
              </SettingsRow>
            );
          })}
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader>
          <span>Output Ports</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {Object.values(node.data.relayConnectors || {}).map((connector) => {
            const connectedDevice = Object.values(
              node.data.relayOutputs || {},
            ).find((device) => device.relayConnectorId === connector.id);

            const connectedDimmer = Object.values(
              node.data.zeroToTenVoltDimmers || {},
            ).find((dimmer) => dimmer.relayConnectorId === connector.id);

            return (
              <SettingsRow key={connector.id} className="flex flex-col gap-2">
                <div
                  className={cn(
                    "flex flex-row items-center justify-between cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded",
                    (connectedDevice || connectedDimmer) && "cursor-pointer",
                  )}
                  onClick={() => {
                    if (connectedDevice || connectedDimmer) {
                      setPortCollapsedState((prev) => ({
                        ...prev,
                        [connector.id]: !prev[connector.id],
                      }));
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    {(connectedDevice || connectedDimmer) && (
                      <ChevronDown
                        className={cn(
                          "size-4 transition-transform duration-300 text-gray-500",
                          portCollapsedState[connector.id] && "-rotate-90",
                        )}
                      />
                    )}
                    <div className="flex flex-col">
                      <div className="text-sm font-medium">
                        {connector.label}
                      </div>
                      <div className="text-xs text-gray-500">
                        {connectedDevice
                          ? `Relay Output: ${connectedDevice.name}`
                          : connectedDimmer
                            ? `Used by 0-10V Dimmer: ${connectedDimmer.name}`
                            : "Not connected"}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "size-3 rounded-full",
                        connectedDevice || connectedDimmer
                          ? "bg-green-400"
                          : "bg-gray-400",
                      )}
                    />
                    {!connectedDevice && !connectedDimmer && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={() => {
                              const yDataMap = getNodeData(ydoc, node.id);
                              if (!yDataMap) {
                                return;
                              }
                              // Get or create relay outputs map
                              let yRelayOutputsMap = yDataMap.get(
                                "relayOutputs",
                              ) as Y.Map<any> | undefined;
                              if (!yRelayOutputsMap) {
                                yRelayOutputsMap = new Y.Map();
                                yDataMap.set("relayOutputs", yRelayOutputsMap);
                              }
                              // Create new relay output
                              const newDeviceId = `relay-output-${randomId()}`;
                              const deviceMap = new Y.Map();
                              deviceMap.set("id", newDeviceId);
                              deviceMap.set("name", "Relay Output");
                              deviceMap.set("relayConnectorId", connector.id);
                              deviceMap.set("sortIndex", 0);
                              deviceMap.set("showLabel", true);
                              deviceMap.set("icon", "ceilingLamp");
                              yRelayOutputsMap.set(newDeviceId, deviceMap);
                              // Update connector status
                              const yRelayConnectorsMap = yDataMap.get(
                                "relayConnectors",
                              ) as Y.Map<any> | undefined;
                              if (yRelayConnectorsMap) {
                                const yConnectorMap = yRelayConnectorsMap.get(
                                  connector.id,
                                ) as Y.Map<any> | undefined;
                                if (yConnectorMap) {
                                  yConnectorMap.set("connected", true);
                                  yConnectorMap.set(
                                    "deviceType",
                                    "relayOutput",
                                  );
                                  yConnectorMap.set(
                                    "relayOutputId",
                                    newDeviceId,
                                  );
                                }
                              }
                            }}
                          >
                            <PlusIcon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Add Relay Output</TooltipContent>
                      </Tooltip>
                    )}
                    {connectedDevice && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Remove device
                              const yDataMap = getNodeData(ydoc, node.id);
                              if (!yDataMap) {
                                return;
                              }
                              const yRelayOutputsMap = yDataMap.get(
                                "relayOutputs",
                              ) as Y.Map<any> | undefined;
                              if (yRelayOutputsMap) {
                                yRelayOutputsMap.delete(connectedDevice.id);
                              }
                              const yRelayConnectorsMap = yDataMap.get(
                                "relayConnectors",
                              ) as Y.Map<any> | undefined;
                              if (yRelayConnectorsMap) {
                                const yConnectorMap = yRelayConnectorsMap.get(
                                  connector.id,
                                ) as Y.Map<any> | undefined;
                                if (yConnectorMap) {
                                  yConnectorMap.set("connected", false);
                                  yConnectorMap.set("deviceType", "none");
                                  yConnectorMap.delete("relayOutputId");
                                }
                              }
                            }}
                          >
                            <Trash2Icon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Remove Device</TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </div>

                {connectedDevice && !portCollapsedState[connector.id] && (
                  <ConnectedRelayOutputSettings
                    connectedDevice={connectedDevice}
                    yDataMap={getNodeData(ydoc, node.id)!}
                    readOnly={readOnly}
                  />
                )}
              </SettingsRow>
            );
          })}
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}
