import { DeviceIconKey } from "@somo/shared";
import { z } from "zod/v4";
import { Bell } from "./Bell";
import { Brush } from "./Brush";
import { Fan } from "./Fan";
import { FloorLamp } from "./FloorLamp";
import { Lamp } from "./Lamp";
import { LampLeft } from "./LampLeft";
import { LampRight } from "./LampRight";
import { Shade } from "./Shade";
import { SideLamp } from "./SideLamp";
import { Stop } from "./Stop";
import { TV } from "./TV";

export { DeviceIconKey };
type DeviceIconKeyType = z.infer<typeof DeviceIconKey>;
export type DeviceIconKey = DeviceIconKeyType;

export const DeviceIcons = {
  ceilingLamp: Lamp,
  wallLamp: SideLamp,
  floorLamp: FloorLamp,
  lampLeft: LampLeft,
  lampRight: LampRight,
  shade: Shade,
  tv: TV,
  fan: Fan,
  doNotDisturb: Stop,
  bell: Bell,
  brush: Brush,
} as const;
