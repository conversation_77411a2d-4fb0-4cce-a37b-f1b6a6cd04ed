import {
  getConnectionData,
  saveConnectionData,
  updateConnectionData,
} from "@/lib/connectionStorage";
import { Picoboot3USB } from "@/lib/Picoboot3USB";
import { SomoBasestationController } from "@/lib/SomoBasestationController";
import { useCallback, useEffect, useRef, useState } from "react";
import { SerialPort } from "web-serial-polyfill";

export interface UseBasestationSerialReturn {
  isConnected: boolean;
  isConnecting: boolean;
  isSupported: boolean;
  port: SerialPort | null;
  controller: SomoBasestationController | null;
  bootloader: Picoboot3USB | null;
  qrCode: string | null;
  firmwareVersion: string | null;
  lastError: string | null;
  isBootloader: boolean;
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  sendConfigurationUpdate: (params: {
    roomId: string;
    version: string;
    nodes: any[];
    edges: any[];
    configuration: {
      rf?: { channel: number; network: number };
      wifi?: { ssid: string; password: string };
      somoSwitchIdToQRCodeMapping?: { id: string; qrCode: string }[];
      somoDimmerIdToQRCodeMapping?: { id: string; qrCode: string }[];
      wiredSwitchIdToQRCodeMapping?: { id: string; qrCode: string }[];
      canBusControllerIdToQRCodeMapping?: { id: string; qrCode: string }[];
    };
    qrCode: string;
  }) => Promise<void>;
  sendReboot: () => Promise<void>;
  sendStatus: () => Promise<void>;
  setQrCode: (qrCode: string) => void;
  enterBootloader: () => Promise<void>;
  uploadFirmware: (
    file: File,
    onProgress?: (percent: number) => void,
  ) => Promise<void>;
}

export function useBasestationSerial(): UseBasestationSerialReturn {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [port, setPort] = useState<SerialPort | null>(null);
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [firmwareVersion, setFirmwareVersion] = useState<string | null>(null);
  const [lastError, setLastError] = useState<string | null>(null);
  const [isBootloader, setIsBootloader] = useState(false);
  const controllerRef = useRef<SomoBasestationController | null>(null);
  const bootloaderRef = useRef<Picoboot3USB | null>(null);

  const isSupported = SomoBasestationController.isSupported();

  const connect = useCallback(async () => {
    if (!isSupported) {
      throw new Error("Web Serial API is not supported");
    }

    // Set connecting state and clear any previous errors
    setIsConnecting(true);
    setLastError(null);

    // Clean up any existing connections first
    if (controllerRef.current) {
      try {
        await controllerRef.current.close();
      } catch (e) {
        // Ignore errors during cleanup
      }
      controllerRef.current = null;
      setPort(null);
    }

    if (bootloaderRef.current) {
      try {
        await bootloaderRef.current.disconnect();
      } catch (e) {
        // Ignore errors during cleanup
      }
      bootloaderRef.current = null;
    }

    try {
      // Always request a new port from the user to ensure they select an active device
      const selectedPort = await SomoBasestationController.requestPort();

      if (!selectedPort) {
        setIsConnecting(false);
        return;
      }

      // Test if the device is in bootloader mode
      let isInBootloaderMode = false;
      const bootloader = new Picoboot3USB();
      try {
        await bootloader.connect(selectedPort);
        await bootloader.activate();

        const version = await bootloader.getVersion();
        console.log("[useBasestationSerial] Bootloader version:", version);
        isInBootloaderMode = true;

        // Keep the bootloader instance connected
        bootloaderRef.current = bootloader;
        setIsBootloader(true);
        setPort(selectedPort);
        setIsConnected(true);
        setFirmwareVersion(`Bootloader v${version}`);

        // Set up progress listener for firmware updates
        bootloader.on("progress", (progress, message) => {
          console.log(`[bootloader] ${message} (${progress}%)`);
        });

        bootloader.on("error", (error) => {
          console.error("[bootloader] Error:", error);
          setLastError(error.message);
        });

        setIsConnecting(false);
        return; // Exit early - we're in bootloader mode
      } catch (error) {
        console.warn("[useBasestationSerial] Not in bootloader mode:", error);
        // If it fails, the device is likely in normal mode
        await bootloader.disconnect();
      }
      // Normal mode - create controller
      console.log("[useBasestationSerial] Entering normal basestation mode");
      const controller = new SomoBasestationController(selectedPort);

      // Set up event listeners
      controller.on("connected", async () => {
        console.log("[useBasestationSerial] Connected to basestation");
        controllerRef.current = controller;
        setPort(selectedPort);
        setIsBootloader(false);

        // Give the device time to be ready after connection
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Perform normal handshake
        try {
          const handshakeResult = await controller.performHandshake();

          if (handshakeResult) {
            const oldQrCode = qrCode;
            setQrCode(handshakeResult.qr);
            setFirmwareVersion(handshakeResult.version);

            // Save/update connection data in local storage
            if (handshakeResult.qr) {
              const existingData = getConnectionData(handshakeResult.qr);
              if (existingData) {
                // Update existing data with new firmware version
                updateConnectionData(handshakeResult.qr, {
                  firmwareVersion: handshakeResult.version,
                });
              } else {
                // Create new connection data
                saveConnectionData({
                  qrCode: handshakeResult.qr,
                  firmwareVersion: handshakeResult.version,
                  lastConnected: new Date().toISOString(),
                });
              }

              // If QR code changed, migrate any existing connection data
              if (oldQrCode && oldQrCode !== handshakeResult.qr) {
                const oldData = getConnectionData(oldQrCode);
                if (oldData) {
                  // Migrate the old data to the new QR code
                  saveConnectionData({
                    ...oldData,
                    qrCode: handshakeResult.qr,
                    firmwareVersion: handshakeResult.version,
                    lastConnected: new Date().toISOString(),
                  });
                  // Clear old connection data
                  localStorage.removeItem(
                    `basestation_connection:${oldQrCode}`,
                  );
                }
              }
            }

            setIsConnected(true);
            setIsConnecting(false);
            console.log(
              "[useBasestationSerial] Handshake complete:",
              handshakeResult,
            );
          } else {
            console.error("[useBasestationSerial] Handshake failed");
            setIsConnected(false);
            setIsConnecting(false);
            // Disconnect on handshake failure
            await controller.close();
            controllerRef.current = null;
            setPort(null);
          }
        } catch (error) {
          console.error("[useBasestationSerial] Handshake error:", error);
          setIsConnected(false);
          setIsConnecting(false);
          setLastError(
            error instanceof Error ? error.message : "Handshake failed",
          );
          await controller.close();
          controllerRef.current = null;
          setPort(null);
        }
      });

      controller.on("disconnected", () => {
        setIsConnected(false);
        setQrCode(null);
        setFirmwareVersion(null);
        controllerRef.current = null;
        setPort(null);
        console.log("[useBasestationSerial] Disconnected from basestation");
      });

      controller.on("error", (error) => {
        console.error("[useBasestationSerial] Error:", error);
        setIsConnected(false);
        setQrCode(null);
        setFirmwareVersion(null);
        setLastError(error?.message || "Unknown error");
        // Clean up on error
        controllerRef.current = null;
        setPort(null);
      });
    } catch (error) {
      console.error("[useBasestationSerial] Connection error:", error);
      setIsConnected(false);
      setIsConnecting(false);
      setLastError(
        error instanceof Error ? error.message : "Connection failed",
      );
      throw error;
    }
  }, [isSupported, qrCode]);

  const disconnect = useCallback(async () => {
    if (controllerRef.current) {
      await controllerRef.current.close();
      controllerRef.current = null;
    }

    if (bootloaderRef.current) {
      await bootloaderRef.current.disconnect();
      bootloaderRef.current = null;
    }

    setPort(null);
    setIsConnected(false);
    setQrCode(null);
    setFirmwareVersion(null);
    setLastError(null);
    setIsBootloader(false);
  }, []);

  const sendConfigurationUpdate = useCallback(
    async (
      params: Parameters<
        SomoBasestationController["sendConfigurationUpdate"]
      >[0],
    ) => {
      if (!controllerRef.current || !isConnected) {
        throw new Error("Not connected to basestation");
      }

      // Send configuration to basestation
      await controllerRef.current.sendConfigurationUpdate(params);

      // Update local storage with the configuration data
      if (qrCode) {
        updateConnectionData(qrCode, {
          ssid: params.configuration.wifi?.ssid,
          password: params.configuration.wifi?.password,
          rfConfig: params.configuration.rf,
        });
      }
    },
    [isConnected, qrCode],
  );

  const sendReboot = useCallback(async () => {
    if (!controllerRef.current || !isConnected) {
      throw new Error("Not connected to basestation");
    }
    await controllerRef.current.sendReboot();
  }, [isConnected]);

  const sendStatus = useCallback(async () => {
    if (!controllerRef.current || !isConnected) {
      throw new Error("Not connected to basestation");
    }
    await controllerRef.current.sendStatus();
  }, [isConnected]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (controllerRef.current) {
        controllerRef.current.close();
      }
      if (bootloaderRef.current) {
        bootloaderRef.current.disconnect();
      }
    };
  }, []);

  const enterBootloader = useCallback(async () => {
    if (!controllerRef.current) {
      throw new Error("Not connected to basestation");
    }
    try {
      await controllerRef.current.sendCommand("ENTER_BOOTLOADER");
    } catch (error) {
      // If the command fails, it might be because the device disconnected
      // This is expected when entering bootloader mode
      console.log("[enterBootloader] Command sent, device may have rebooted");
    }
  }, []);

  const uploadFirmware = useCallback(
    async (file: File, onProgress?: (percent: number) => void) => {
      if (!isSupported) {
        throw new Error("Web Serial API is not supported");
      }

      if (!bootloaderRef.current) {
        throw new Error("No bootloader instance available");
      }

      console.log("[uploadFirmware] Starting firmware upload");

      try {
        // Read the firmware file
        const arrayBuffer = await file.arrayBuffer();
        const firmwareData = new Uint8Array(arrayBuffer);

        // Set up progress handler
        const progressHandler = (progress: number, message: string) => {
          console.log(`[uploadFirmware] ${message} (${progress}%)`);
          if (onProgress) {
            onProgress(progress);
          }
        };

        bootloaderRef.current.on("progress", progressHandler);

        // Flash the firmware using Picoboot3USB
        await bootloaderRef.current.flashFirmware(firmwareData);

        console.log("[uploadFirmware] Upload complete, launching application");

        // Tell bootloader to launch the application
        await bootloaderRef.current.goToApp();

        // Clean up progress handler
        bootloaderRef.current.off("progress", progressHandler);

        // Clear bootloader state after upload
        await bootloaderRef.current.disconnect();
        bootloaderRef.current = null;
        setIsBootloader(false);
        setPort(null);
        setIsConnected(false);

        console.log("[uploadFirmware] Upload complete, device will restart");
      } catch (error) {
        console.error("[uploadFirmware] Upload failed:", error);
        throw error;
      }
    },
    [isSupported],
  );

  return {
    isConnected,
    isConnecting,
    isSupported,
    port,
    controller: controllerRef.current,
    bootloader: bootloaderRef.current,
    qrCode,
    firmwareVersion,
    lastError,
    isBootloader,
    connect,
    disconnect,
    sendConfigurationUpdate,
    sendReboot,
    sendStatus,
    setQrCode,
    enterBootloader,
    uploadFirmware,
  };
}
