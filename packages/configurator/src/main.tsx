import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/clerk-react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createRouter, RouterProvider } from "@tanstack/react-router";
import { <PERSON><PERSON><PERSON>ider } from "launchdarkly-react-client-sdk";
import ReactDOM from "react-dom/client";
import { TooltipProvider } from "./components/ui/tooltip";
import "./index.css";
import { initSentry } from "./lib/sentry";
import { routeTree } from "./routeTree.gen";

initSentry();

const queryClient = new QueryClient();
const router = createRouter({ routeTree });

// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;
if (!PUBLISHABLE_KEY) {
  throw new Error("Missing Publishable Key");
}

const LAUNCH_DARKLY_CLIENT_SIDE_ID = import.meta.env
  .VITE_LAUNCH_DARKLY_CLIENT_SIDE_ID;

// Render the app
const rootElement = document.getElementById("root");
if (!rootElement) {
  throw new Error("Root element could not be found");
}

if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <TooltipProvider>
      <LDProvider clientSideID={LAUNCH_DARKLY_CLIENT_SIDE_ID}>
        <ClerkProvider publishableKey={PUBLISHABLE_KEY} afterSignOutUrl="/">
          <QueryClientProvider client={queryClient}>
            <RouterProvider router={router} />
          </QueryClientProvider>
        </ClerkProvider>
      </LDProvider>
    </TooltipProvider>,
  );
}
