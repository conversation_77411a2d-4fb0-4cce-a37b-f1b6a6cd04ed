import { SerialPort } from "web-serial-polyfill";
import { EventEmitter } from "./EventEmitter";
import { SerialPortClient } from "./SerialPortClient";

interface Picoboot3USBEvents {
  on(
    event: "progress",
    listener: (progress: number, message: string) => void,
  ): this;
  on(event: "error", listener: (error: Error) => void): this;

  off(
    event: "progress",
    listener: (progress: number, message: string) => void,
  ): this;
  off(event: "error", listener: (error: Error) => void): this;

  addListener(
    event: "progress",
    listener: (progress: number, message: string) => void,
  ): this;
  addListener(event: "error", listener: (error: Error) => void): this;

  removeListener(
    event: "progress",
    listener: (progress: number, message: string) => void,
  ): this;
  removeListener(event: "error", listener: (error: Error) => void): this;

  removeAllListeners(event?: "progress" | "error"): this;
}

export class Picoboot3USB extends EventEmitter implements Picoboot3USBEvents {
  // Command codes
  private static readonly USB_CMD_ACTIVATE = 0xa5;
  private static readonly USB_CMD_VERSION = 0x02;
  private static readonly USB_CMD_READ = 0x10;
  private static readonly USB_CMD_PROGRAM = 0x20;
  private static readonly USB_CMD_ERASE = 0x30;
  private static readonly USB_CMD_GO_TO_APP = 0x40;
  private static readonly USB_CMD_FLASH_SIZE = 0x50;
  private static readonly USB_CMD_NODE_ID = 0x60;

  // Response codes
  private static readonly USB_RESP_ACK = 0x06;
  private static readonly USB_RESP_NACK = 0x15;
  private static readonly USB_RESP_DATA = 0x80;

  // Activation key
  private static readonly ACTIVATION_KEY = new Uint8Array([
    0x70, 0x62, 0x74, 0x33,
  ]); // "pbt3"

  // Default timeout for read operations (2 seconds)
  private static readonly DEFAULT_READ_TIMEOUT = 1000;

  private client: SerialPortClient | null = null;
  private activated = false;
  private readTimeout: number = Picoboot3USB.DEFAULT_READ_TIMEOUT;

  constructor(readTimeout: number = Picoboot3USB.DEFAULT_READ_TIMEOUT) {
    super();
    this.readTimeout = readTimeout;
  }

  /**
   * Set the read timeout for operations
   */
  setReadTimeout(timeout: number): void {
    this.readTimeout = timeout;
  }

  /**
   * Find Pico devices in bootloader mode
   */
  static async findPicoDevices(): Promise<SerialPort[]> {
    if (!("serial" in navigator)) {
      throw new Error("Web Serial API not supported");
    }

    const ports = await navigator.serial.getPorts();
    const picoDevices: SerialPort[] = [];

    for (const port of ports) {
      const info = await port.getInfo();
      // Check for Pico USB vendor/product ID (2e8a:000a)
      if (info.usbVendorId === 0x2e8a && info.usbProductId === 0x000a) {
        picoDevices.push(port);
      }
    }

    return picoDevices;
  }

  /**
   * Request a new Pico device from the user
   */
  static async requestPicoDevice(): Promise<SerialPort> {
    if (!("serial" in navigator)) {
      throw new Error("Web Serial API not supported");
    }

    // Request port with Pico filter
    const port = await navigator.serial.requestPort({
      filters: [{ usbVendorId: 0x2e8a }],
    });

    return port;
  }

  /**
   * Connect to a Pico device
   */
  async connect(port: SerialPort): Promise<void> {
    if (this.client) {
      await this.disconnect();
    }

    // Create SerialPortClient in raw mode for binary communication
    this.client = new SerialPortClient(
      port,
      115200, // baudRate
      "none", // parity
      1, // stopBits
      "none", // flowControl
      8, // dataBits
      "raw", // lineMode - important for binary data
    );

    // Wait for connection
    await new Promise<void>((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error("Connection timeout"));
      }, 5000);

      this.client!.once("connected", () => {
        clearTimeout(timeout);
        resolve();
      });

      this.client!.once("error", (err) => {
        clearTimeout(timeout);
        reject(err);
      });
    });

    // Allow USB to stabilize
    await new Promise((resolve) => setTimeout(resolve, 500));
  }

  /**
   * Disconnect from the device
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.disconnect();
      this.client = null;
      this.activated = false;
    }
  }

  /**
   * Send a command to the bootloader
   */
  private async sendCommand(
    cmd: number,
    data: Uint8Array = new Uint8Array(),
  ): Promise<void> {
    if (!this.client) {
      throw new Error("Not connected");
    }

    // Create packet: cmd (1 byte) + length (4 bytes, big-endian) + data
    const packet = new Uint8Array(5 + data.length);
    packet[0] = cmd;

    // Pack length as big-endian
    const view = new DataView(packet.buffer);
    view.setUint32(1, data.length, false); // false = big-endian

    // Copy data
    if (data.length > 0) {
      packet.set(data, 5);
    }

    await this.client.writeBinary(packet);
  }

  /**
   * Read response from bootloader
   */
  private async readResponse(): Promise<{ cmd: number; data: Uint8Array }> {
    if (!this.client) {
      throw new Error("Not connected");
    }

    try {
      // Read response header (5 bytes: cmd + length)
      const header = await this.client.readBytes(5, this.readTimeout);

      const respCmd = header[0];
      const view = new DataView(
        header.buffer,
        header.byteOffset,
        header.byteLength,
      );
      const respLen = view.getUint32(1, false); // false = big-endian

      // Read response data
      let data = new Uint8Array();
      if (respLen > 0) {
        data = await this.client.readBytes(respLen, this.readTimeout);
      }

      return { cmd: respCmd, data };
    } catch (error) {
      if (error instanceof Error && error.message.includes("Read timeout")) {
        throw new Error(`Bootloader read timeout: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Activate the bootloader interface
   */
  async activate(): Promise<void> {
    this.emit("progress", 0, "Activating bootloader...");

    await this.sendCommand(
      Picoboot3USB.USB_CMD_ACTIVATE,
      Picoboot3USB.ACTIVATION_KEY,
    );

    const response = await this.readResponse();

    if (
      response.cmd === Picoboot3USB.USB_RESP_ACK &&
      this.arrayEquals(response.data, Picoboot3USB.ACTIVATION_KEY)
    ) {
      this.activated = true;
      this.emit("progress", 10, "Bootloader activated");
    } else {
      throw new Error("Failed to activate bootloader");
    }
  }

  /**
   * Get bootloader version
   */
  async getVersion(): Promise<string> {
    if (!this.activated) {
      throw new Error("Bootloader not activated");
    }

    await this.sendCommand(Picoboot3USB.USB_CMD_VERSION);
    const response = await this.readResponse();

    if (
      response.cmd === Picoboot3USB.USB_RESP_DATA &&
      response.data.length === 3
    ) {
      return `${response.data[0]}.${response.data[1]}.${response.data[2]}`;
    } else {
      throw new Error("Failed to get version");
    }
  }

  /**
   * Get CAN node ID
   */
  async getNodeId(): Promise<number> {
    if (!this.activated) {
      throw new Error("Bootloader not activated");
    }

    await this.sendCommand(Picoboot3USB.USB_CMD_NODE_ID);
    const response = await this.readResponse();

    if (
      response.cmd === Picoboot3USB.USB_RESP_DATA &&
      response.data.length === 4
    ) {
      const view = new DataView(
        response.data.buffer,
        response.data.byteOffset,
        response.data.byteLength,
      );
      return view.getUint32(0, true); // true = little-endian
    } else {
      throw new Error("Failed to get node ID");
    }
  }

  /**
   * Get flash size
   */
  async getFlashSize(): Promise<number> {
    if (!this.activated) {
      throw new Error("Bootloader not activated");
    }

    await this.sendCommand(Picoboot3USB.USB_CMD_FLASH_SIZE);
    const response = await this.readResponse();

    if (
      response.cmd === Picoboot3USB.USB_RESP_DATA &&
      response.data.length === 4
    ) {
      const view = new DataView(
        response.data.buffer,
        response.data.byteOffset,
        response.data.byteLength,
      );
      return view.getUint32(0, false); // false = big-endian
    } else {
      throw new Error("Failed to get flash size");
    }
  }

  /**
   * Erase a flash sector
   */
  async eraseSector(sector: number): Promise<void> {
    if (!this.activated) {
      throw new Error("Bootloader not activated");
    }

    const data = new Uint8Array(2);
    const view = new DataView(data.buffer);
    view.setUint16(0, sector, false); // false = big-endian

    await this.sendCommand(Picoboot3USB.USB_CMD_ERASE, data);
    const response = await this.readResponse();

    if (response.cmd !== Picoboot3USB.USB_RESP_ACK) {
      throw new Error(`Failed to erase sector ${sector}`);
    }
  }

  /**
   * Program a flash page
   */
  async programPage(address: number, data: Uint8Array): Promise<void> {
    if (!this.activated) {
      throw new Error("Bootloader not activated");
    }

    // Pad data to page size (256 bytes)
    let pageData = data;
    if (data.length < 256) {
      pageData = new Uint8Array(256);
      pageData.set(data);
      pageData.fill(0xff, data.length);
    } else if (data.length > 256) {
      pageData = data.slice(0, 256);
    }

    // Create command data: address (4 bytes) + length (2 bytes) + data
    const cmdData = new Uint8Array(6 + pageData.length);
    const view = new DataView(cmdData.buffer);
    view.setUint32(0, address, false); // false = big-endian
    view.setUint16(4, pageData.length, false); // false = big-endian
    cmdData.set(pageData, 6);

    await this.sendCommand(Picoboot3USB.USB_CMD_PROGRAM, cmdData);
    const response = await this.readResponse();

    if (response.cmd !== Picoboot3USB.USB_RESP_ACK) {
      throw new Error(
        `Failed to program at 0x${address.toString(16).padStart(8, "0")}`,
      );
    }
  }

  /**
   * Read flash memory
   */
  async readFlash(address: number, length: number): Promise<Uint8Array> {
    if (!this.activated) {
      throw new Error("Bootloader not activated");
    }

    const data = new Uint8Array(6);
    const view = new DataView(data.buffer);
    view.setUint32(0, address, false); // false = big-endian
    view.setUint16(4, length, false); // false = big-endian

    await this.sendCommand(Picoboot3USB.USB_CMD_READ, data);
    const response = await this.readResponse();

    if (response.cmd === Picoboot3USB.USB_RESP_DATA) {
      return response.data;
    } else {
      throw new Error(
        `Failed to read flash at 0x${address.toString(16).padStart(8, "0")}`,
      );
    }
  }

  /**
   * Exit bootloader and start application
   */
  async goToApp(): Promise<void> {
    if (!this.activated) {
      throw new Error("Bootloader not activated");
    }

    this.emit("progress", 100, "Starting application...");
    await this.sendCommand(Picoboot3USB.USB_CMD_GO_TO_APP);

    try {
      const response = await this.readResponse();
      if (response.cmd === Picoboot3USB.USB_RESP_ACK) {
        this.emit("progress", 100, "Application started");
      }
    } catch {
      // Connection may close when jumping to app
    }
  }

  /**
   * Flash a firmware file
   */
  async flashFirmware(
    firmwareData: Uint8Array,
    offset = 0x8000,
  ): Promise<void> {
    const firmwareSize = firmwareData.length;
    this.emit("progress", 0, `Firmware size: ${firmwareSize} bytes`);

    // Calculate sectors to erase
    const sectorSize = 4096;
    const startSector = Math.floor(offset / sectorSize);
    const numSectors = Math.ceil(firmwareSize / sectorSize);

    // Erase sectors
    this.emit("progress", 5, `Erasing ${numSectors} sectors...`);
    for (let i = 0; i < numSectors; i++) {
      const sector = startSector + i;
      await this.eraseSector(sector);
      const progress = 5 + ((i + 1) * 15) / numSectors;
      this.emit(
        "progress",
        progress,
        `Erasing: ${Math.round(((i + 1) * 100) / numSectors)}%`,
      );
    }

    // Program pages
    const pageSize = 256;
    const numPages = Math.ceil(firmwareSize / pageSize);
    this.emit("progress", 20, `Programming ${numPages} pages...`);

    for (let i = 0; i < numPages; i++) {
      const pageOffset = i * pageSize;
      const pageAddr = offset + pageOffset;
      const pageData = firmwareData.slice(pageOffset, pageOffset + pageSize);

      await this.programPage(pageAddr, pageData);

      const progress = 20 + ((i + 1) * 60) / numPages;
      this.emit(
        "progress",
        progress,
        `Programming: ${Math.round(((i + 1) * 100) / numPages)}%`,
      );
    }

    // Verify
    this.emit("progress", 80, "Verifying...");
    const chunkSize = 4096;
    for (let i = 0; i < firmwareSize; i += chunkSize) {
      const readSize = Math.min(chunkSize, firmwareSize - i);
      const readData = await this.readFlash(offset + i, readSize);
      const expectedData = firmwareData.slice(i, i + readSize);

      if (!this.arrayEquals(readData, expectedData)) {
        throw new Error(
          `Verification failed at offset 0x${i.toString(16).padStart(8, "0")}`,
        );
      }

      const progress = 80 + ((i + readSize) * 15) / firmwareSize;
      this.emit(
        "progress",
        progress,
        `Verifying: ${Math.round(((i + readSize) * 100) / firmwareSize)}%`,
      );
    }

    this.emit("progress", 100, "Flash complete!");
  }

  /**
   * Helper to compare Uint8Arrays
   */
  private arrayEquals(a: Uint8Array, b: Uint8Array): boolean {
    if (a.length !== b.length) {
      return false;
    }
    for (let i = 0; i < a.length; i++) {
      if (a[i] !== b[i]) {
        return false;
      }
    }
    return true;
  }
}
