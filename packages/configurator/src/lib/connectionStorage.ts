/**
 * Connection data stored in local storage, keyed by QR code
 */
export interface ConnectionData {
  ssid?: string;
  password?: string;
  rfConfig?: {
    channel: number;
    network: number;
  };
  qrCode: string;
  firmwareVersion?: string;
  lastConnected: string; // ISO date string
}

const CONNECTION_STORAGE_PREFIX = "basestation_connection:";

/**
 * Get connection data for a specific QR code
 */
export function getConnectionData(qrCode: string): ConnectionData | null {
  if (!qrCode) {
    return null;
  }

  const key = `${CONNECTION_STORAGE_PREFIX}${qrCode}`;
  const stored = localStorage.getItem(key);

  if (!stored) {
    return null;
  }

  try {
    return JSON.parse(stored) as ConnectionData;
  } catch (error) {
    console.error("Failed to parse connection data:", error);
    return null;
  }
}

/**
 * Save connection data for a specific QR code
 */
export function saveConnectionData(data: ConnectionData): void {
  if (!data.qrCode) {
    console.error("Cannot save connection data without QR code");
    return;
  }

  const key = `${CONNECTION_STORAGE_PREFIX}${data.qrCode}`;
  const toStore = {
    ...data,
    lastConnected: new Date().toISOString(),
  };

  localStorage.setItem(key, JSON.stringify(toStore));
}

/**
 * Update connection data for a specific QR code
 */
export function updateConnectionData(
  qrCode: string,
  updates: Partial<Omit<ConnectionData, "qrCode">>,
): void {
  const existing = getConnectionData(qrCode);
  const updated: ConnectionData = {
    ...existing,
    ...updates,
    qrCode,
    lastConnected: new Date().toISOString(),
  };

  saveConnectionData(updated);
}

/**
 * Get all saved connection data
 */
export function getAllConnectionData(): ConnectionData[] {
  const connections: ConnectionData[] = [];

  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key?.startsWith(CONNECTION_STORAGE_PREFIX)) {
      const data = localStorage.getItem(key);
      if (data) {
        try {
          connections.push(JSON.parse(data) as ConnectionData);
        } catch (error) {
          console.error("Failed to parse connection data:", error);
        }
      }
    }
  }

  return connections.sort(
    (a, b) =>
      new Date(b.lastConnected).getTime() - new Date(a.lastConnected).getTime(),
  );
}

/**
 * Clear connection data for a specific QR code
 */
export function clearConnectionData(qrCode: string): void {
  const key = `${CONNECTION_STORAGE_PREFIX}${qrCode}`;
  localStorage.removeItem(key);
}
