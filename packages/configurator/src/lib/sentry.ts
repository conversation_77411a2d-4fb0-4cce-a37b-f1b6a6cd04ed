import * as Sentry from "@sentry/react";

export function initSentry() {
  const environment = import.meta.env.VITE_ENVIRONMENT || "development";
  const dsn = import.meta.env.VITE_SENTRY_DSN;

  Sentry.init({
    dsn,
    environment,
    sendDefaultPii: true,
    // Only set true when installing/debugging Sentry
    debug: false,

    // Sampling
    tracesSampleRate: environment === "production" ? 0.1 : 1.0,
    replaysSessionSampleRate: environment === "production" ? 0.1 : 1.0,
    replaysOnErrorSampleRate: 1.0,

    integrations: [
      Sentry.captureConsoleIntegration({
        levels: ["error", "warn", "debug"],
      }),
      Sentry.browserTracingIntegration(),
      Sentry.replayIntegration({
        maskAllText: false,
        blockAllMedia: false,
      }),
    ],

    beforeSend(event) {
      // We can use this function to filter out noisy events
      return event;
    },
  });
}

// Helpers (so we don't import Sentry directly from everywhere)
export const captureException: typeof Sentry.captureException = (...params) => {
  return Sentry.captureException(...params);
};

export const captureMessage = (
  message: string,
  captureContext?: Exclude<Parameters<typeof Sentry.captureMessage>[1], string>,
) => {
  // Enforce "debug" since we filter out the "info" logs to avoid capturing
  // all the console.log from the frontend (even in development).
  return Sentry.captureMessage(message, { ...captureContext, level: "debug" });
};
