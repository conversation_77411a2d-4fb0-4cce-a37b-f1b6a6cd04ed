import Semaphore from "semaphore-promise";
import { SerialPort } from "web-serial-polyfill";
import * as z from "zod/v4";
import { EventEmitter } from "./EventEmitter";
import { SerialPortClient } from "./SerialPortClient";

export const RfResponse = z.object({
  data: z.array(z.number()),
  rssi: z.number(),
  duration: z.number().optional(),
});
export type RfResponse = z.infer<typeof RfResponse>;

export class RfController extends EventEmitter {
  private serialPort: SerialPortClient;
  private initialized = false;
  private responseTimeout = 5000; // 5 seconds default timeout
  private waitingForResponse = false;
  private debug = false;
  private lastRssi = -1;
  private semaphore = new Semaphore(1);
  private currentChannel: number;
  private currentNetwork: number;

  constructor(
    port: SerialPort,
    debug: boolean,
    initialChannel: number,
    initialNetwork: number,
  ) {
    super();
    this.serialPort = new SerialPortClient(port);
    this.currentChannel = initialChannel;
    this.currentNetwork = initialNetwork;
    this.debug = debug;
    this.setupEventListeners();
  }

  private setupEventListeners() {
    this.serialPort.on("connected", async () => {
      console.log("[rf] Connected to RF controller");
      await this.serialPort.write(
        `Channel|${this.currentChannel},${this.currentNetwork}\r\n`,
      );
      this.emit("connected");
    });

    this.serialPort.on("data", (data: string) => this.handleData(data));
    this.serialPort.on("error", (error: Error) => this.emit("error", error));
  }

  private handleData(line: string) {
    if (this.debug) {
      console.log("[rf|data] ", line);
    }

    if (line.includes("RSSI:")) {
      const rssiMatch = line.match(/RSSI:\s*(-?\d+)/);
      if (rssiMatch) {
        const rssiValue = parseInt(rssiMatch[1], 10);
        this.lastRssi = rssiValue;
      }
      this.emit("serialData", line);
    } else if (line.startsWith("[ RF4463 Rx ] Packet: ")) {
      const packetData = line.substring("[ RF4463 Tx ] Packet: ".length);
      const asciiNumbers = packetData
        .split("")
        .map((char) => char.charCodeAt(0));
      if (this.waitingForResponse) {
        this.emit("rfResponse", { data: asciiNumbers, rssi: this.lastRssi });
        this.setWaitingForResponse(false);
      } else {
        this.emit("rfIncoming", { data: asciiNumbers, rssi: this.lastRssi });
      }
      this.emit(
        "serialData",
        "[ RF4463 Rx ] Packet: " + JSON.stringify(asciiNumbers),
      );
    } else if (line.startsWith("[ RF4463 Tx ] Packet: ")) {
      const packetData = line.substring("[ RF4463 Tx ] Packet: ".length);
      const asciiNumbers = packetData
        .split("")
        .map((char) => char.charCodeAt(0));
      if (this.debug) {
        console.log("[rf|outgoing] ", asciiNumbers);
      }
      this.emit("rfOutgoing", { data: asciiNumbers });
      this.emit(
        "serialData",
        "[ RF4463 Tx ] Packet: " + JSON.stringify(asciiNumbers),
      );
    } else {
      this.emit("serialData", line);
    }

    const match = line.trim().match(this.logLineRegex);
    if (match) {
      const [_, level, timestamp, component, content] = match;
      const logData = {
        level,
        timestamp,
        component: component.trim(),
        content: content.trim(),
      };

      if (this.isInitializationMessage(logData)) {
        this.initialized = true;
        this.emit("initialized");
      }

      this.emit("log", logData);
    }
  }

  private readonly logLineRegex =
    /^([VIDEW])\s*\((\d{2}:\d{2}:\d{2}\.\d{3})\)\s*\[\s*([^\]]+)\s*\]\s*:\s*(.+)$/;

  private isInitializationMessage(logData: any) {
    return (
      logData.component === "RF4463_Handler" &&
      logData.content ===
        "**********************[ RF_HOT_SWAP* ]*********************"
    );
  }

  private sleep(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async changeChannel(channel: number, network: number) {
    if (channel === this.currentChannel && network === this.currentNetwork) {
      return;
    }

    this.currentChannel = channel;
    this.currentNetwork = network;

    const command = `Channel|${channel},${network}\r\n`;

    await this.sendRfRawCommand(command, {
      waitForResponse: false,
      timeout: 2000,
      retries: 3,
    });

    await this.sleep(100);
  }

  async discoverDevice(qrCode: string) {
    const command = `Discover|${qrCode}\r\n`;
    return this.sendRfRawCommand(command, {
      waitForResponse: true,
      timeout: 2000,
      retries: 3,
    });
  }

  async adoptDevice(
    qrCode: string,
    node: number,
    channel: number,
    network: number,
  ) {
    const opMode = 0;
    const command = `Adopt|${qrCode}|${node}|${opMode}|${channel}|${network}\r\n`;
    return this.sendRfRawCommand(command, {
      waitForResponse: true,
      timeout: 2000,
      retries: 3,
    });
  }

  async removeDevice(
    qrCode: string,
    node: number,
    channel: number,
    network: number,
  ) {
    const command = `Remove|${qrCode}|${node}|${channel}|${network}\r\n`;
    return this.sendRfRawCommand(command, {
      waitForResponse: true,
      timeout: 2000,
      retries: 3,
    });
  }

  async sendRfCommand(
    source: number,
    destination: number,
    table: number,
    command: number,
    options: {
      data?: string;
      waitForResponse?: boolean;
      timeout?: number;
      retries?: number;
    } = {
      waitForResponse: true,
      timeout: 1000,
      retries: 3,
    },
  ) {
    const data = options.data;
    console.log("[rf|send] ", source, destination, table, command, data);
    const rfCommand = this.buildRfCommand(
      source,
      destination,
      table,
      command,
      data,
    );
    return this.sendRfRawCommand(rfCommand, options);
  }

  private async sendRfRawCommand(
    rfCommand: string,
    options: {
      waitForResponse?: boolean;
      timeout?: number;
      retries?: number;
    } = {
      waitForResponse: true,
      timeout: 1000,
      retries: 3,
    },
  ): Promise<{ data: number[]; rssi: number; duration?: number } | void> {
    if (!this.initialized) {
      console.log("[rf] RF Controller not initialized");
      throw new Error("RF Controller not initialized");
    }
    const releaseLock = await this.semaphore.acquire();
    const now = Date.now();
    console.log(
      `[rf|send]  "${rfCommand.toString().replace(/[\r\n\f]/g, "")}"`,
    );
    const retries = options.retries ?? 3;

    let lastError: Error | null = null;
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        if (options.waitForResponse) {
          this.setWaitingForResponse(true);
        }
        await this.serialPort.write(rfCommand);

        if (!options.waitForResponse) {
          console.log("[rf|sent] RF Command sent");
          return;
        }

        return await new Promise<RfResponse>((resolve, reject) => {
          const timeoutMs = options.timeout || this.responseTimeout;
          const timeoutId = setTimeout(() => {
            console.log("[rf|timeout] RF Command timeout");
            this.removeListener("rfResponse", responseHandler);

            releaseLock();
            reject(new Error("RF Command timeout"));
          }, timeoutMs);

          const responseHandler = async (response: any) => {
            clearTimeout(timeoutId);
            this.removeListener("rfResponse", responseHandler);
            await this.sleep(10);

            const parsedResponse = RfResponse.parse(response);
            const dx = Date.now() - now;
            releaseLock();
            resolve({ ...parsedResponse, duration: dx });
          };

          this.once("rfResponse", responseHandler);
        });
      } catch (error) {
        lastError = error as Error;
        if (attempt < retries) {
          // Wait a bit before retrying (exponential backoff)
          const backoffMs = Math.min(100 * Math.pow(2, attempt), 1000);
          await this.sleep(backoffMs);
          continue;
        }
        throw new Error(
          `RF Command failed after ${retries + 1} attempts. Last error: ${
            lastError.message
          }`,
        );
      } finally {
        releaseLock();
      }
    }
  }

  private buildRfCommand(
    source: number,
    destination: number,
    table: number,
    command: number,
    data?: string,
  ): string {
    return `RF|${source}|${destination}|${table}|${command}${
      data ? `|${data}` : ""
    }\r\n`;
  }

  async waitForInitialization(timeout = 30000): Promise<void> {
    if (this.initialized) {
      return;
    }

    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error("Initialization timeout"));
      }, timeout);

      this.once("initialized", () => {
        clearTimeout(timeoutId);
        resolve();
      });
    });
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  async close(): Promise<void> {
    this.initialized = false;
    await this.serialPort.disconnect();
  }

  setWaitingForResponse(waiting: boolean) {
    this.waitingForResponse = waiting;
  }
}

export const RfBroadcast = 255;

// Export the RF tables and commands as constants
export const RfTables = {
  TABLE_RF_TEST: 1,
  TABLE_RF_SETUP: 2,
  TABLE_RF_OTA: 3,
  TABLE_RF_GLOBAL: 4,
  TABLE_RF_SWITCH: 5,
  TABLE_RF_DIMMER: 6,
  TABLE_RF_OUTLET: 7,
  TABLE_RF_REED: 8,
  TABLE_RF_PIR: 9,
  TABLE_RF_THERMOSTAT: 10,
  TABLE_RF_BREAKER: 11,
  TABLE_RF_IRDA: 12,
  TABLE_RF_SERVICE_PAD: 13,
  TABLE_RF_BLINDS: 14,
  TABLE_RF_KEYPAD: 15,
  TABLE_RF_KEYLOCK: 16,
} as const;

export const RfSwitchTable = {
  RF_SWITCH_ON: 0,
  RF_SWITCH_OFF: 1,
  RF_SWITCH_VIA1_ON: 2,
  RF_SWITCH_VIA1_OFF: 3,
  RF_SWITCH_VIA2_ON: 4,
  RF_SWITCH_VIA2_OFF: 5,
  RF_SWITCH_VIA3_ON: 6,
  RF_SWITCH_VIA3_OFF: 7,
  RF_SWITCH_SET_1_GANG: 8,
  RF_SWITCH_SET_2_GANG: 9,
  RF_SWITCH_SET_3_GANG: 10,
  RF_SWITCH_SLEEP: 11,
  RF_SWITCH_WAKE: 12,
  RF_SWITCH_HAPTIC_ENABLE: 13,
  RF_SWITCH_HAPTIC_DISABLE: 14,
  RF_SWITCH_TOUCH_ENABLE: 15,
  RF_SWITCH_TOUCH_DISABLE: 16,
  RF_SWITCH_GET_CONSUMPTION: 17,
  RF_SWITCH_LOAD_UPDATE: 18,
  RF_SWITCH_ACTIVE_CONSUMPTION: 19,
  RF_SWITCH_HIGH_SENSITIVITY: 20,
  RF_SWITCH_LOW_SENSITIVITY: 21,
  RF_SWITCH_TABLE: 22,
} as const;

export const RfSetupTable = {
  /**********************************************
   * Device administration commands
   **********************************************/
  RF_SETUP_DISCOVER: 0,
  RF_SETUP_FIND_BY_QR: 1,
  RF_SETUP_REGISTER: 2,
  RF_SETUP_REMOVE: 3,
  RF_SETUP_HARD_RESET_REQUEST: 4,
  /**********************************************
   * Reserved for recovery purposes
   **********************************************/
  RF_SETUP_OVERRIDE_ENABLE: 5,
  RF_SETUP_OVERRIDE_DISABLE: 6,
  RF_SETUP_FTY_RESTORE: 7,
  RF_SETUP_FTY_RECONFIGURE: 8,
  RF_SETUP_FTY_DISCOVER: 9,
  /**********************************************
   *
   **********************************************/
  RF_SETUP_SET_ZONES: 10, // 5th Nov 2021
  RF_SETUP_SET_RC_MODE: 11, // ++
  RF_SETUP_SET_RELATION: 12, // ++
  RF_SETUP_REMOVE_RELATION: 13, // ++
  RF_SETUP_TABLE: 14,
} as const;

export const RfTestCommands = {
  RF_TEST_DEVICE_PING: 0,
  RF_TEST_STATUS: 1,
  /**********************************************
   * Reserved for factory purposes
   **********************************************/
  RF_TEST_QR: 2,
  RF_TEST_OK: 3,
  RF_TEST_STAGE: 4,
  RF_TEST_LOOP: 5,
  /**********************************************
   *
   **********************************************/
  RF_TEST_CHANNEL: 6,
  /**********************************************
   *
   **********************************************/
  RF_TEST_MESSAGE: 7,
  /**********************************************
   *
   **********************************************/
  RF_TEST_INFO: 8,
  RF_TEST_TABLE: 9,
} as const;

export const RfDimmerCommands = {
  RF_DIMMER_ON: 0,
  RF_DIMMER_OFF: 1,
  RF_DIMMER_LEVEL_1: 2,
  RF_DIMMER_LEVEL_2: 3,
  RF_DIMMER_LEVEL_3: 4,
  RF_DIMMER_LEVEL_4: 5,
  RF_DIMMER_LEVEL_5: 6,
  RF_DIMMER_GET_ACK: 7,
  RF_DIMMER_TOUCH_ENABLE: 8,
  RF_DIMMER_TOUCH_DISABLE: 9,
  RF_DIMMER_SLEEP: 10,
  RF_DIMMER_WAKE: 11,
  RF_DIMMER_RECALIBRATE_SUPPLY: 12,
  RF_DIMMER_IS_ONLINE: 13,
  RF_DIMMER_GET_CONSUMPTION: 14,
  RF_DIMMER_CALIBRATE_OFFSET: 15,
  RF_DIMMER_SET_OFFSET: 16,
  RF_DIMMER_TABLE: 17,
} as const;
