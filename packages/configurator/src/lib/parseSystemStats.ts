import { SystemStats } from "@/components/graph/SystemStatsModal";
import { z } from "zod";

// Zod schemas for JSON validation
const TaskSchema = z.object({
  name: z.string(),
  core: z.string(),
  priority: z.number(),
  cpuPercent: z.number(),
  stackFreeWords: z.number(),
  freeBytes: z.number(),
  freeKb: z.number(),
  hasEstimateWarning: z.boolean(),
});

const HeapInfoSchema = z.object({
  bytes: z.number(),
  kb: z.number(),
});

const MemoryUsageSchema = z.object({
  totalStackAllocated: HeapInfoSchema,
  totalStackUsed: HeapInfoSchema,
  stackUtilization: z.number(),
});

const CpuUsageSchema = z.object({
  totalCpuAccounted: z.number(),
  systemUptime: z.number(),
});

const SystemStatsJSONSchema = z.object({
  activeTasks: z.number(),
  configuredHeap: HeapInfoSchema,
  tasks: z.array(TaskSchema),
  memoryUsage: MemoryUsageSchema,
  cpuUsage: CpuUsageSchema,
});

// Export Zod schema for external use
export { SystemStatsJSONSchema };

// Type inference from Zod schema
export type SystemStatsJSON = z.infer<typeof SystemStatsJSONSchema>;

export function parseSystemStats(rawOutput: string): SystemStats | null {
  try {
    // Check if the output is JSON format
    const trimmedOutput = rawOutput.trim();
    if (trimmedOutput.startsWith("{") && trimmedOutput.endsWith("}")) {
      try {
        const jsonData = JSON.parse(trimmedOutput);

        // Validate using Zod schema
        const validatedData = SystemStatsJSONSchema.parse(jsonData);
        return validatedData;
      } catch (error) {
        console.error("Failed to parse system statistics JSON:", error);
        if (error instanceof SyntaxError) {
          console.error("JSON syntax error - invalid JSON format");
        } else if (error instanceof z.ZodError) {
          console.error("Zod validation error:", error.issues);
        }
        // Fall back to text parsing
      }
    }

    // If output is empty or doesn't contain stats, return null
    if (!rawOutput || !rawOutput.includes("=== System RAM Usage ===")) {
      return null;
    }

    const lines = rawOutput
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    let activeTasks = 0;
    const configuredHeap = { bytes: 0, kb: 0 };
    const tasks: SystemStats["tasks"] = [];
    const memoryUsage = {
      totalStackAllocated: { bytes: 0, kb: 0 },
      totalStackUsed: { bytes: 0, kb: 0 },
      stackUtilization: 0,
    };
    const cpuUsage = {
      totalCpuAccounted: 0,
      systemUptime: 0,
    };

    let currentSection = "";
    let taskHeaderFound = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Parse active tasks
      if (line.startsWith("Active Tasks:")) {
        const match = line.match(/Active Tasks:\s*(\d+)/);
        if (match) {
          activeTasks = parseInt(match[1]);
        }
      }

      // Parse configured heap
      if (line.startsWith("Configured System Heap:")) {
        const match = line.match(
          /Configured System Heap:\s*(\d+)\s*bytes\s*\(([\d.]+)\s*KB\)/,
        );
        if (match) {
          configuredHeap.bytes = parseInt(match[1]);
          configuredHeap.kb = parseFloat(match[2]);
        }
      }

      // Find task information section
      if (line.includes("Task Information:")) {
        currentSection = "tasks";
        taskHeaderFound = false;
        continue;
      }

      // Skip header and separator lines in task section
      if (currentSection === "tasks") {
        if (
          line.includes("Task Name") &&
          line.includes("Core") &&
          (line.includes("CPU%") || line.includes("CPU%%"))
        ) {
          taskHeaderFound = true;
          continue;
        }
        if (line.includes("----") || line.includes("===")) {
          continue;
        }

        // Parse task data
        if (
          taskHeaderFound &&
          line.length > 0 &&
          !line.includes("Memory Usage Summary") &&
          !line.includes("CPU Usage Summary")
        ) {
          // Try to match the task line format: TaskName Core Prio CPU% StackFree FreeBytes
          // Example: Tmr Svc              0      7      0.0      990          3960B (3.9KB)
          // Handle task names with spaces by using a more flexible pattern
          const taskMatch = line.match(
            /^(.+?)\s+(\d+)\s+(\d+)\s+([\d.]+)\s+(\d+)\s+(\d+)B\s*\(([\d.]+)KB\)(\*?)$/,
          );
          if (taskMatch) {
            tasks.push({
              name: taskMatch[1].trim(),
              core: taskMatch[2],
              priority: parseInt(taskMatch[3]),
              cpuPercent: parseFloat(taskMatch[4]),
              stackFreeWords: parseInt(taskMatch[5]),
              freeBytes: parseInt(taskMatch[6]),
              freeKb: parseFloat(taskMatch[7]),
              hasEstimateWarning: taskMatch[8] === "*",
            });
          }
        }
      }

      // Parse memory usage summary
      if (line.includes("Memory Usage Summary:")) {
        currentSection = "memory";
        continue;
      }

      if (currentSection === "memory") {
        if (line.startsWith("Total Stack Allocated:")) {
          const match = line.match(
            /Total Stack Allocated:\s*(\d+)\s*bytes\s*\(([\d.]+)\s*KB\)/,
          );
          if (match) {
            memoryUsage.totalStackAllocated.bytes = parseInt(match[1]);
            memoryUsage.totalStackAllocated.kb = parseFloat(match[2]);
          }
        }
        if (line.startsWith("Total Stack Used:")) {
          const match = line.match(
            /Total Stack Used:\s*(\d+)\s*bytes\s*\(([\d.]+)\s*KB\)/,
          );
          if (match) {
            memoryUsage.totalStackUsed.bytes = parseInt(match[1]);
            memoryUsage.totalStackUsed.kb = parseFloat(match[2]);
          }
        }
        if (line.startsWith("Stack Utilization:")) {
          const match = line.match(/Stack Utilization:\s*([\d.]+)%/);
          if (match) {
            memoryUsage.stackUtilization = parseFloat(match[1]);
          }
        }
      }

      // Parse CPU usage summary
      if (line.includes("CPU Usage Summary:")) {
        currentSection = "cpu";
        continue;
      }

      if (currentSection === "cpu") {
        if (line.startsWith("Total CPU Accounted:")) {
          const match = line.match(/Total CPU Accounted:\s*([\d.]+)%/);
          if (match) {
            cpuUsage.totalCpuAccounted = parseFloat(match[1]);
          }
        }
        if (line.startsWith("System Uptime:")) {
          const match = line.match(/System Uptime:\s*([\d.]+)\s*seconds/);
          if (match) {
            cpuUsage.systemUptime = parseFloat(match[1]);
          }
        }
      }
    }

    return {
      activeTasks,
      configuredHeap,
      tasks,
      memoryUsage,
      cpuUsage,
    };
  } catch (error) {
    console.error("Error parsing system stats:", error);
    return null;
  }
}
