import { SerialPort } from "web-serial-polyfill";
import { EventEmitter } from "./EventEmitter";

interface SerialPortClientEvents {
  on(event: "connected", listener: () => void): this;
  on(event: "data", listener: (data?: Buffer) => void): this;
  on(event: "error", listener: (err?: Error) => void): this;

  off(event: "connected", listener: () => void): this;
  off(event: "data", listener: (data?: Buffer) => void): this;
  off(event: "error", listener: (err?: Error) => void): this;

  addListener(event: "connected", listener: () => void): this;
  addListener(event: "data", listener: (data?: Buffer) => void): this;
  addListener(event: "error", listener: (err?: Error) => void): this;

  removeListener(event: "connected", listener: () => void): this;
  removeListener(event: "data", listener: (data?: <PERSON>uffer) => void): this;
  removeListener(event: "error", listener: (err?: Error) => void): this;

  removeAllListeners(event?: "connected" | "data" | "error"): this;
}

export class SerialPortClient
  extends EventEmitter
  implements SerialPortClientEvents
{
  private port: SerialPort;
  private baudRate: number;
  private parity: "none" | "even" | "odd";
  private stopBits: 1 | 2;
  private flowControl: "none" | "xon-xoff" | "rts-cts";
  private dataBits: number;
  private _isOpen = false;
  private reader: ReadableStreamDefaultReader<Uint8Array> | null = null;
  private isDisconnecting = false;
  private lineMode: "normal" | "raw";

  /**
   * Buffer for accumulating bytes in raw mode for readBytes()
   */
  private rawBuffer: Uint8Array = new Uint8Array(0);
  /**
   * Queue of pending readBytes requests
   */
  private readBytesQueue: Array<{
    len: number;
    resolve: (data: Uint8Array) => void;
    reject: (err: any) => void;
  }> = [];

  constructor(
    port: SerialPort,
    baudRate = 115200,
    parity: "none" | "even" | "odd" = "none",
    stopBits: 1 | 2 = 1,
    flowControl: "none" | "xon-xoff" | "rts-cts" = "none",
    dataBits = 8,
    lineMode: "normal" | "raw" = "normal",
  ) {
    super();
    this.port = port;
    this.baudRate = baudRate;
    this.parity = parity;
    this.stopBits = stopBits;
    this.flowControl = flowControl;
    this.dataBits = dataBits;
    this.lineMode = lineMode;
    this.connect();
  }

  private async connect() {
    try {
      console.log("[serial] Attempting to open port with settings:", {
        baudRate: this.baudRate,
        parity: this.parity,
        stopBits: this.stopBits,
        flowControl: this.flowControl,
        dataBits: this.dataBits,
      });

      await this.port.open({
        baudRate: this.baudRate,
        parity: this.parity,
        stopBits: this.stopBits,
        flowControl: this.flowControl,
        dataBits: this.dataBits,
      });

      console.log("[serial] Port opened successfully");

      // Set up the data reading
      if (!this.port.readable) {
        throw new Error("Port is not readable");
      }
      this._isOpen = true;
      this.emit("connected");

      this.reader = this.port.readable.getReader();

      const decoder = new TextDecoder();

      // Start reading in a loop
      let buffer = "";
      while (true) {
        try {
          if (!this.reader) {
            throw new Error("Reader is not initialized");
          }
          const { value, done } = await this.reader.read();
          if (done) {
            break;
          }

          // RAW MODE: handle readBytes() and emit data
          if (this.lineMode === "raw") {
            if (value) {
              // If there are pending readBytes requests, fulfill them first
              if (this.readBytesQueue.length > 0) {
                let incoming = value;
                // If we have leftover from previous reads, prepend
                if (this.rawBuffer.length > 0) {
                  const merged = new Uint8Array(
                    this.rawBuffer.length + incoming.length,
                  );
                  merged.set(this.rawBuffer, 0);
                  merged.set(incoming, this.rawBuffer.length);
                  incoming = merged;
                  this.rawBuffer = new Uint8Array(0);
                }
                let offset = 0;
                while (
                  this.readBytesQueue.length > 0 &&
                  offset < incoming.length
                ) {
                  const req = this.readBytesQueue[0];
                  if (incoming.length - offset >= req.len) {
                    // Enough data to fulfill this request
                    const chunk = incoming.slice(offset, offset + req.len);
                    req.resolve(chunk);
                    offset += req.len;
                    this.readBytesQueue.shift();
                  } else {
                    // Not enough data, buffer the rest
                    this.rawBuffer = incoming.slice(offset);
                    break;
                  }
                }
                // If any data remains after all requests, buffer it
                if (offset < incoming.length) {
                  this.rawBuffer = incoming.slice(offset);
                }
              } else {
                // No pending requests, just buffer for emit
                this.emit("data", value);
              }
            }
            continue;
          }

          // NORMAL MODE: line-based
          buffer += decoder.decode(value);
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";
          for (const line of lines) {
            if (line) {
              this.emit("data", line);
            }
          }
        } catch (error) {
          if (this.isDisconnecting) {
            break;
          }
          console.error("Error reading from serial port:", error);
          if (
            error instanceof DOMException &&
            error.message?.includes("device has been lost")
          ) {
            const deviceLostError = new Error(
              "The device has been lost. Please reconnect the device and try again.",
            );
            this.emit("error", deviceLostError);
          } else {
            this.emit("error", error);
          }
          await this.disconnect();
          break;
        }
      }
    } catch (error) {
      console.error("[serial] Error connecting to serial port:", error);

      // Provide more detailed error messages
      let errorMessage = "Failed to connect to serial port";
      if (error instanceof DOMException) {
        if (error.name === "NetworkError") {
          errorMessage =
            "Failed to open serial port. The port may be in use by another application.";
        } else if (error.name === "SecurityError") {
          errorMessage =
            "Permission denied. Please ensure the site is served over HTTPS or localhost.";
        } else if (error.name === "NotFoundError") {
          errorMessage =
            "Serial port not found. The device may have been disconnected.";
        } else if (error.message?.includes("device has been lost")) {
          errorMessage =
            "The device has been lost. Please reconnect the device and try again.";
        } else {
          errorMessage = `Serial port error (${error.name}): ${error.message}`;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      const detailedError = new Error(errorMessage);
      this.emit("error", detailedError);
      this._isOpen = false;
      // Also emit disconnected event to ensure UI updates correctly
      this.emit("disconnected");
    }
  }

  /**
   * Reads exactly len bytes from the serial port in raw mode.
   * Returns a Promise that resolves with a Uint8Array of length len.
   * If not in raw mode, rejects.
   * @param len - Number of bytes to read
   * @param timeout - Optional timeout in milliseconds (default: 2000ms)
   */
  public readBytes(len: number, timeout = 2000): Promise<Uint8Array> {
    if (this.lineMode !== "raw") {
      return Promise.reject(
        new Error("readBytes is only available in raw mode"),
      );
    }
    if (!this._isOpen) {
      return Promise.reject(new Error("Port is not open"));
    }
    // If we already have enough in the buffer, resolve immediately
    if (this.rawBuffer.length >= len) {
      const chunk = this.rawBuffer.slice(0, len);
      this.rawBuffer = this.rawBuffer.slice(len);
      return Promise.resolve(chunk);
    }
    // Otherwise, queue the request with timeout
    return new Promise<Uint8Array>((resolve, reject) => {
      let timeoutId: NodeJS.Timeout | undefined;

      const request = {
        len,
        resolve: (data: Uint8Array) => {
          if (timeoutId) {
            clearTimeout(timeoutId);
          }
          resolve(data);
        },
        reject: (err: any) => {
          if (timeoutId) {
            clearTimeout(timeoutId);
          }
          reject(err);
        },
      };

      // Set up timeout
      if (timeout > 0) {
        timeoutId = setTimeout(() => {
          // Remove this request from the queue
          const index = this.readBytesQueue.indexOf(request);
          if (index !== -1) {
            this.readBytesQueue.splice(index, 1);
          }
          reject(
            new Error(
              `Read timeout: expected ${len} bytes within ${timeout}ms`,
            ),
          );
        }, timeout);
      }

      this.readBytesQueue.push(request);
    });
  }

  async disconnect() {
    if (this.isDisconnecting) {
      return;
    }
    this.isDisconnecting = true;
    try {
      if (this.reader) {
        try {
          await this.reader.cancel();
        } catch (_error) {
          // Ignore errors during cancel
        }
        this.reader = null;
      }
      console.log("[serial] Disconnecting");
      try {
        await this.port.close();
      } catch (error) {
        // Ignore errors during close
        console.log("[serial] Error during close:", error);
      }
    } finally {
      this._isOpen = false;
      this.emit("disconnected");
      console.log("[serial] Disconnected");
      this.isDisconnecting = false;
    }
  }

  async releaseStreams() {
    // Release the reader without closing the port
    if (this.reader) {
      try {
        await this.reader.cancel();
        this.reader.releaseLock();
      } catch (_error) {
        // Ignore errors
      }
      this.reader = null;
    }
    this._isOpen = false;
    console.log("[serial] Released streams but kept port open");
  }

  public async write(data: string): Promise<void> {
    if (!this.port.writable) {
      throw new Error("Port is not writable");
    }
    const writer = this.port.writable.getWriter();
    await writer.write(new TextEncoder().encode(data));
    writer.releaseLock();
  }

  public async writeBinary(data: Uint8Array): Promise<void> {
    if (!this.port.writable) {
      throw new Error("Port is not writable");
    }
    const writer = this.port.writable.getWriter();
    await writer.write(data);
    writer.releaseLock();
  }

  get isOpen(): boolean {
    return this._isOpen;
  }

  getPort(): SerialPort {
    return this.port;
  }
}
