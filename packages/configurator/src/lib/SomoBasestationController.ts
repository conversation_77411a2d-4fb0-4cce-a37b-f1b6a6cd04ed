import {
  BasestationUpdateMessage,
  getActiveConfigurationForBasestation,
} from "@somo/shared";
import { SerialPort } from "web-serial-polyfill";
import { EventEmitter } from "./EventEmitter";
import { SerialPortClient } from "./SerialPortClient";

// Add Web Serial API type declarations
declare global {
  interface Navigator {
    serial: {
      getPorts(): Promise<SerialPort[]>;
      requestPort(options?: {
        filters?: Array<{ usbVendorId?: number }>;
      }): Promise<SerialPort>;
    };
  }
}

interface BasestationControllerEvents {
  on(event: "connected", listener: () => void): this;
  on(event: "disconnected", listener: () => void): this;
  on(event: "response", listener: (response: string) => void): this;
  on(event: "error", listener: (err?: Error) => void): this;
  on(event: "serialData", listener: (data: string) => void): this;

  off(event: "connected", listener: () => void): this;
  off(event: "disconnected", listener: () => void): this;
  off(event: "response", listener: (response: string) => void): this;
  off(event: "error", listener: (err?: Error) => void): this;
  off(event: "serialData", listener: (data: string) => void): this;

  addListener(event: "connected", listener: () => void): this;
  addListener(event: "disconnected", listener: () => void): this;
  addListener(event: "response", listener: (response: string) => void): this;
  addListener(event: "error", listener: (err?: Error) => void): this;
  addListener(event: "serialData", listener: (data: string) => void): this;

  removeListener(event: "connected", listener: () => void): this;
  removeListener(event: "disconnected", listener: () => void): this;
  removeListener(event: "response", listener: (response: string) => void): this;
  removeListener(event: "error", listener: (err?: Error) => void): this;
  removeListener(event: "serialData", listener: (data: string) => void): this;

  removeAllListeners(
    event?: "connected" | "disconnected" | "response" | "error" | "serialData",
  ): this;
}

export class SomoBasestationController
  extends EventEmitter
  implements BasestationControllerEvents
{
  private serialPort: SerialPortClient;

  constructor(port: SerialPort) {
    super();
    this.serialPort = new SerialPortClient(port);
    this.setupEventListeners();
  }

  private setupEventListeners() {
    this.serialPort.on("connected", () => {
      console.log("[basestation] Connected to Somo Basestation");
      this.emit("connected");
    });

    this.serialPort.on("disconnected", () => {
      console.log("[basestation] Disconnected from Somo Basestation");
      this.emit("disconnected");
    });

    this.serialPort.on("data", (data: string) => {
      this.handleData(data);
    });

    this.serialPort.on("error", (error: Error) => {
      console.error("[basestation|error]", error);
      this.emit("error", error);
    });
  }

  private handleData(line: string) {
    // Log all serial output to developer console
    console.log("[basestation|serial]", line);

    // Emit all serial data for monitoring
    this.emit("serialData", line);

    // Parse specific responses
    if (line.includes("[SERIAL]")) {
      this.emit("response", line);
    }
  }

  /**
   * Check if Web Serial API is supported
   */
  static isSupported(): boolean {
    return "serial" in navigator;
  }

  /**
   * Get list of available serial ports that match Pico devices
   */
  static async getAvailablePorts(): Promise<SerialPort[]> {
    if (!SomoBasestationController.isSupported()) {
      throw new Error("Web Serial API is not supported in this browser");
    }

    const ports = await navigator.serial.getPorts();
    // Filter for Somo Basestation devices based on vendor/product IDs
    return ports.filter((port: SerialPort) => {
      const info = port.getInfo();
      // Raspberry Pi Foundation vendor ID is 0x2E8A (used by Somo Basestation)
      return info.usbVendorId === 0x2e8a || !info.usbVendorId;
    });
  }

  /**
   * Request a new serial port from the user
   */
  static async requestPort(): Promise<SerialPort | null> {
    if (!SomoBasestationController.isSupported()) {
      throw new Error("Web Serial API is not supported in this browser");
    }

    try {
      // Request port with filters for Raspberry Pi Pico
      const port = await navigator.serial.requestPort({
        filters: [
          { usbVendorId: 0x2e8a }, // Raspberry Pi vendor ID
        ],
      });
      return port;
    } catch (error) {
      if (error instanceof DOMException && error.name === "NotFoundError") {
        // User cancelled the prompt
        return null;
      }
      throw error;
    }
  }

  /**
   * Send a raw command to the basestation
   */
  async sendCommand(command: string): Promise<void> {
    if (!command.endsWith("\n")) {
      command += "\n";
    }
    await this.serialPort.write(command);
  }

  /**
   * Send configuration update to the basestation
   */
  async sendConfigurationUpdate(params: {
    roomId: string;
    version: string;
    nodes: any[];
    edges: any[];
    configuration: {
      rf?: { channel: number; network: number };
      wifi?: { ssid: string; password: string };
      somoSwitchIdToQRCodeMapping?: { id: string; qrCode: string }[];
      somoDimmerIdToQRCodeMapping?: { id: string; qrCode: string }[];
      wiredSwitchIdToQRCodeMapping?: { id: string; qrCode: string }[];
      canBusControllerIdToQRCodeMapping?: { id: string; qrCode: string }[];
      doorSensorIdToQRCodeMapping?: { id: string; qrCode: string }[];
    };
    qrCode: string;
  }): Promise<void> {
    console.log("[basestation] Sending configuration update");

    // Convert configuration QR mappings to nodeQrMappings format
    const nodeQrMappings = [
      ...(params.configuration.somoSwitchIdToQRCodeMapping || []),
      ...(params.configuration.somoDimmerIdToQRCodeMapping || []),
      ...(params.configuration.wiredSwitchIdToQRCodeMapping || []),
      ...(params.configuration.canBusControllerIdToQRCodeMapping || []),
      ...(params.configuration.doorSensorIdToQRCodeMapping || []),
    ].map((mapping) => ({
      deviceId: mapping.id,
      qrCode: mapping.qrCode,
    }));

    // Get the active configuration
    const { activeConfiguration } = getActiveConfigurationForBasestation({
      name: params.qrCode,
      roomId: params.roomId,
      version: params.version,
      nodes: params.nodes,
      nodeQrMappings,
      rfConfig: params.configuration.rf,
      wifiConfig: params.configuration.wifi,
    });

    // Encode the configuration
    const data = BasestationUpdateMessage.encode({
      qrCode: params.qrCode,
      config: activeConfiguration.config,
    }).finish();

    const dataWithAction = new Uint8Array(data.length);
    dataWithAction.set(data, 0);

    // Use base64 encoding to avoid newline issues in binary data
    const base64Data = btoa(String.fromCharCode(...dataWithAction));
    console.log("[basestation] Base64 data:", base64Data);
    const command = `FLASH_CONFIG|${base64Data}`;

    console.log(
      `[basestation] Sending command: FLASH_CONFIG|<${dataWithAction.length} bytes as base64>`,
    );

    // Send as text command - base64 is safe for line-based processing
    await this.sendCommand(command);

    // Wait a bit for the response
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        console.log("[basestation] Configuration update sent (timeout)");
        resolve();
      }, 2000);

      const responseHandler = (response: string) => {
        if (
          response.includes("Configuration sent to event queue") ||
          response.includes("Failed to send configuration")
        ) {
          clearTimeout(timeout);
          this.removeListener("response", responseHandler);
          console.log("[basestation] Configuration update response:", response);
          resolve();
        }
      };

      this.on("response", responseHandler);
    });
  }

  /**
   * Send reboot command to the basestation
   */
  async sendReboot(): Promise<void> {
    console.log("[basestation] Sending reboot command");
    await this.sendCommand("reboot");
  }

  /**
   * Send status command to the basestation
   */
  async sendStatus(): Promise<void> {
    console.log("[basestation] Sending status command");
    await this.sendCommand("status");
  }

  /**
   * Send help command to the basestation
   */
  async sendHelp(): Promise<void> {
    console.log("[basestation] Sending help command");
    await this.sendCommand("help");
  }

  /**
   * Perform handshake with basestation and get QR code and firmware version
   */
  async performHandshake(): Promise<{ qr: string; version: string } | null> {
    console.log("[basestation] Performing handshake");

    // Try handshake with retry logic
    for (let attempt = 1; attempt <= 2; attempt++) {
      const result = await this.attemptHandshake();
      if (result) {
        return result;
      }
      if (attempt === 1) {
        console.log("[basestation] Handshake failed, retrying...");
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    }

    return null;
  }

  private async attemptHandshake(): Promise<{
    qr: string;
    version: string;
  } | null> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        console.log("[basestation] Handshake timeout");
        resolve(null);
      }, 3000); // 3 second timeout per attempt

      const responseHandler = (data: string) => {
        // Look for text response from GET_QR command: <qrcode>|<version>
        // Ignore log messages that start with "["
        if (!data.startsWith("[") && data.includes("|")) {
          const parts = data.split("|");
          if (parts.length === 2) {
            const qr = parts[0]; // QR code (may be empty string)
            const version = parts[1].trim(); // Version (trim whitespace)

            clearTimeout(timeout);
            this.removeListener("serialData", responseHandler);
            console.log("[basestation] Handshake successful:", { qr, version });
            resolve({ qr, version });
          }
        }
      };

      this.on("serialData", responseHandler);

      // Only send command if port is still writable
      if (this.serialPort.isOpen) {
        this.sendCommand("GET_QR").catch(() => {
          // Ignore errors if port was closed
          clearTimeout(timeout);
          resolve(null);
        });
      } else {
        clearTimeout(timeout);
        resolve(null);
      }
    });
  }

  /**
   * Set QR code on the basestation
   */
  async setQRCode(qrCode: string): Promise<void> {
    console.log("[basestation] Setting QR code:", qrCode);
    await this.sendCommand(`SET_QR|${qrCode}`);
  }

  /**
   * Close the connection
   */
  async close(): Promise<void> {
    await this.serialPort.disconnect();
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.serialPort.isOpen;
  }

  /**
   * Send raw binary data (for bootloader communication)
   */
  async sendBinary(data: Uint8Array): Promise<void> {
    await this.serialPort.writeBinary(data);
  }

  /**
   * Get direct access to serial port for bootloader
   */
  getSerialPort(): SerialPort {
    return this.serialPort.getPort();
  }

  /**
   * Release serial streams without closing port (for bootloader mode)
   */
  async releaseStreams(): Promise<void> {
    await this.serialPort.releaseStreams();
  }
}
