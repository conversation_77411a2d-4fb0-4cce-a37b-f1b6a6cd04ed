import { useUpdateRoom } from "@/components/graph/useUpdateRoom";
import { Logo } from "@/components/icons/Logo";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useClickAway } from "@/hooks/useClickAway";
import { useToast } from "@/hooks/useToast";
import { useAuth, UserButton, useUser } from "@clerk/clerk-react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { createFileRoute, Link, useRouter } from "@tanstack/react-router";
import { Copy, Loader2, Lock, Plus, Settings2 } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { z } from "zod/v4";

const Room = z.object({
  id: z.string(),
  name: z.string(),
  createdAt: z.number(),
  updatedAt: z.number(),
  readOnly: z.boolean(),
});
type Room = z.infer<typeof Room>;

export const Route = createFileRoute("/_auth/org/$orgId/_authenticatedOrg/")({
  component: RouteComponent,
});

function RouteComponent() {
  const { getToken } = useAuth();
  const { user } = useUser();
  const { orgId } = Route.useParams();
  const { toast } = useToast();
  const org = useMemo(() => {
    return user?.organizationMemberships.find(
      (m) => m.organization.id === orgId,
    );
  }, [user, orgId]);

  const {
    isError,
    error,
    data: rooms,
    isLoading: roomsLoading,
    refetch: refetchRooms,
  } = useQuery({
    queryKey: ["rooms", orgId],
    queryFn: async () => {
      const token = await getToken({ template: "Configurator" });
      const response = await fetch(
        import.meta.env.VITE_API_URL + "/org/" + orgId + "/rooms",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorCode = response.status;
        const errorMessage =
          errorData.message || response.statusText || "Failed to fetch rooms";
        throw new Error(`Error ${errorCode}: ${errorMessage}`);
      }

      const roomJson = await response.json();
      const rooms = z.array(Room).parse(roomJson);
      return rooms;
    },
  });

  useEffect(() => {
    if (isError && error) {
      toast({
        variant: "destructive",
        title: "Error fetching rooms",
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
      });
    }
  }, [isError, error, toast]);

  const { mutateAsync: createRoom, isPending: creatingRoom } = useMutation({
    mutationFn: async (name: string) => {
      const token = await getToken({ template: "Configurator" });
      const response = await fetch(
        import.meta.env.VITE_API_URL + "/org/" + orgId + "/rooms",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ name }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorCode = response.status;
        const errorMessage =
          errorData.message || response.statusText || "Failed to create room";
        throw new Error(`Error ${errorCode}: ${errorMessage}`);
      }

      const roomJson = await response.json();
      const room = Room.parse(roomJson);
      return room;
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: "Error creating room",
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
      });
    },
  });

  const { updateRoom: updateRoomMutation, isUpdating: updatingRoom } =
    useUpdateRoom({
      orgId,
      onSuccess: () => {
        refetchRooms();
      },
      onError: (error) => {
        toast({
          variant: "destructive",
          title: "Error updating room",
          description:
            error instanceof Error ? error.message : "Unknown error occurred",
        });
      },
    });

  const { mutateAsync: deleteRoom, isPending: deletingRoom } = useMutation({
    mutationFn: async ({ roomId }: { roomId: string }) => {
      const token = await getToken({ template: "Configurator" });
      const response = await fetch(
        import.meta.env.VITE_API_URL + "/org/" + orgId + "/rooms/" + roomId,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorCode = response.status;
        const errorMessage =
          errorData.message || response.statusText || "Failed to delete room";
        throw new Error(`Error ${errorCode}: ${errorMessage}`);
      }
    },
    onSuccess: () => {
      refetchRooms();
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: "Error deleting room",
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
      });
    },
  });

  const [open, setOpen] = useState(false);
  const [openSettings, setOpenSettings] = useState<{
    [roomId: string]: boolean;
  }>({});

  const [copyDialogOpen, setCopyDialogOpen] = useState(false);
  const [roomToCopy, setRoomToCopy] = useState<Room | null>(null);
  const router = useRouter();

  const { mutateAsync: copyRoom, isPending: copyingRoom } = useMutation({
    mutationFn: async ({
      name,
      sourceRoomId,
    }: {
      name: string;
      sourceRoomId: string;
    }) => {
      const token = await getToken({ template: "Configurator" });
      const response = await fetch(
        import.meta.env.VITE_API_URL + "/org/" + orgId + "/rooms/copy",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ name, sourceRoomId }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorCode = response.status;
        const errorMessage =
          errorData.message || response.statusText || "Failed to copy room";
        throw new Error(`Error ${errorCode}: ${errorMessage}`);
      }

      const roomJson = await response.json();
      return Room.parse(roomJson);
    },
    onSuccess: () => {
      refetchRooms();
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: "Error copying room",
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
      });
    },
  });

  return (
    <div className="absolute inset-0 flex flex-col items-center justify-start mt-10 overflow-y-auto">
      <div className="flex flex-row items-center gap-10 mb-10">
        <Link to="/">
          <Logo className="w-10 h-10" />
        </Link>
        <div className="w-[1px] h-full bg-gray-200" />
        <img src={org?.organization.imageUrl} className="h-[80px] w-auto" />
      </div>
      <div className="bg-white rounded-lg p-4 max-w-[400px] border border-gray-200 shadow-md w-full">
        <div className="flex flex-row items-center justify-between gap-2 w-full">
          <div className="text-sm font-semibold text-gray-500">
            Room Configurations
          </div>
          <div className="flex flex-row items-center gap-2">
            <div className="text-sm text-gray-500 truncate">
              {user?.fullName}
            </div>
            <UserButton />
          </div>
        </div>

        <div className="flex flex-col gap-2 mt-4 relative">
          {rooms?.map((room) => {
            return (
              <div
                key={room.id}
                className="relative flex flex-row items-center justify-between gap-2"
              >
                <Link
                  to="/org/$orgId/controller/$controllerId"
                  params={{
                    orgId,
                    controllerId: room.id,
                  }}
                  className="flex flex-row w-full px-4 py-2 bg-white rounded shadow hover:bg-gray-50 border border-gray-200"
                >
                  <div className="flex flex-col items-start flex-grow">
                    <div className="text-sm font-regular flex items-center gap-2">
                      {room.name}
                      {room.readOnly && (
                        <Lock className="size-3 text-gray-400" />
                      )}
                    </div>
                    <div className="text-xs text-gray-400">
                      {new Date(room.updatedAt).toLocaleString()}
                    </div>
                  </div>
                </Link>
                {/* Edit */}
                <div className="flex flex-row items-center justify-center">
                  <Dialog
                    open={openSettings[room.id]}
                    onOpenChange={(open) => {
                      setOpenSettings((prev) => ({
                        ...prev,
                        [room.id]: open,
                      }));
                    }}
                    key={room.id}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setOpenSettings((prev) => ({
                            ...prev,
                            [room.id]: true,
                          }));
                        }}
                        className="w-10 h-10 flex items-center justify-center flex-shrink-0"
                      >
                        <Settings2 className="size-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <form
                        onSubmit={async (e) => {
                          e.preventDefault();
                          const formData = new FormData(
                            e.target as HTMLFormElement,
                          );
                          const name = formData.get("name");
                          const readOnly = formData.get("readOnly") === "on";
                          if (!name || typeof name !== "string") {
                            return;
                          }
                          await updateRoomMutation({
                            roomId: room.id,
                            name,
                            readOnly,
                          });
                          setOpenSettings((prev) => ({
                            ...prev,
                            [room.id]: false,
                          }));
                        }}
                      >
                        <DialogHeader>
                          <DialogTitle>Update Configuration</DialogTitle>
                          <DialogDescription>
                            Update the name and read only status of the
                            configuration.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="name" className="text-right">
                              Name
                            </Label>
                            <Input
                              id="name"
                              name="name"
                              autoComplete="off"
                              data-1p-ignore
                              className="col-span-3"
                              defaultValue={room.name}
                            />
                            <Label htmlFor="readOnly" className="text-right">
                              Read Only
                            </Label>
                            <Checkbox
                              id="readOnly"
                              name="readOnly"
                              defaultChecked={room.readOnly}
                              className="col-span-3"
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <DeleteButton
                            disabled={updatingRoom || deletingRoom}
                            onDelete={async () => {
                              await deleteRoom({ roomId: room.id });
                              setOpenSettings((prev) => ({
                                ...prev,
                                [room.id]: false,
                              }));
                            }}
                          />
                          <Button
                            type="submit"
                            disabled={updatingRoom || deletingRoom}
                          >
                            Update
                          </Button>
                        </DialogFooter>
                      </form>
                    </DialogContent>
                  </Dialog>
                </div>
                {/* Copy */}
                <div className="flex flex-row items-center justify-center">
                  <Dialog
                    key={room.id}
                    open={copyDialogOpen}
                    onOpenChange={setCopyDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setCopyDialogOpen(true);
                          setRoomToCopy(room);
                        }}
                        className="w-10 h-10 flex items-center justify-center flex-shrink-0"
                      >
                        <Copy className="size-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <form
                        onSubmit={async (e) => {
                          e.preventDefault();
                          if (!roomToCopy) {
                            return;
                          }

                          const formData = new FormData(
                            e.target as HTMLFormElement,
                          );
                          const name = formData.get("name");
                          if (!name || typeof name !== "string") {
                            return;
                          }

                          const newRoom = await copyRoom({
                            name,
                            sourceRoomId: roomToCopy.id,
                          });

                          setCopyDialogOpen(false);
                          setRoomToCopy(null);

                          router.navigate({
                            to: "/org/$orgId/controller/$controllerId",
                            params: {
                              orgId,
                              controllerId: newRoom.id,
                            },
                          });
                        }}
                      >
                        <DialogHeader>
                          <DialogTitle>Copy Room Configuration</DialogTitle>
                          <DialogDescription>
                            Create a copy of{" "}
                            {roomToCopy ? (
                              <strong>{roomToCopy.name}</strong>
                            ) : (
                              "the room configuration"
                            )}
                            .
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="copyName" className="text-right">
                              Name
                            </Label>
                            <Input
                              id="copyName"
                              name="name"
                              autoComplete="off"
                              data-1p-ignore
                              className="col-span-3"
                              defaultValue={
                                roomToCopy ? `${roomToCopy.name} (copy)` : ""
                              }
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button
                            variant="outline"
                            type="button"
                            onClick={() => {
                              setCopyDialogOpen(false);
                              setRoomToCopy(null);
                            }}
                          >
                            Cancel
                          </Button>
                          <Button type="submit" disabled={copyingRoom}>
                            {copyingRoom ? (
                              <Loader2 className="animate-spin mr-2 h-4 w-4" />
                            ) : null}
                            Duplicate and start editing
                          </Button>
                        </DialogFooter>
                      </form>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            );
          })}
        </div>
        <div className="flex flex-row items-center mt-6">
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <Button className="w-full" disabled={roomsLoading || isError}>
                {roomsLoading ? (
                  <Loader2 className="animate-spin" />
                ) : (
                  <>
                    <Plus className="size-4" />
                    <span>New Configuration</span>
                  </>
                )}
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <form
                onSubmit={async (e) => {
                  e.preventDefault();
                  const formData = new FormData(e.target as HTMLFormElement);
                  const name = formData.get("name");
                  if (!name) {
                    return;
                  }
                  const newRoom = await createRoom(name as string);
                  setOpen(false);
                  router.navigate({
                    to: "/org/$orgId/controller/$controllerId",
                    params: {
                      orgId,
                      controllerId: newRoom.id,
                    },
                  });
                }}
              >
                <DialogHeader>
                  <DialogTitle>New Configuration</DialogTitle>
                  <DialogDescription>
                    Create a new configuration for a set of rooms.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">
                      Name
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      autoComplete="off"
                      data-1p-ignore
                      className="col-span-3"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" disabled={creatingRoom}>
                    Create
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}

function DeleteButton({
  disabled,
  onDelete,
}: {
  disabled?: boolean;
  onDelete: () => void;
}) {
  const [shouldConfirm, setShouldConfirm] = useState(false);
  const ref = useClickAway<HTMLButtonElement>(() => {
    setShouldConfirm(false);
  });

  return shouldConfirm ? (
    <Button
      ref={ref}
      variant="destructive-outline"
      disabled={disabled}
      onClick={(event) => {
        event.preventDefault();
        event.stopPropagation();

        setShouldConfirm(false);
        onDelete();
      }}
    >
      Click again to confirm deletion
    </Button>
  ) : (
    <Button
      variant="destructive"
      disabled={disabled}
      onClick={(event) => {
        event.preventDefault();
        event.stopPropagation();

        setShouldConfirm(true);
      }}
    >
      Delete
    </Button>
  );
}
