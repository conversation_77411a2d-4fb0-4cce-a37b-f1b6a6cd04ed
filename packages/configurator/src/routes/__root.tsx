import { useUser } from "@clerk/clerk-react";
import * as <PERSON><PERSON> from "@sentry/react";
import { createRootRoute, Outlet } from "@tanstack/react-router";
import { useEffect } from "react";
import { Toaster } from "../components/ui/toaster";

export const Route = createRootRoute({
  component: () => {
    const { user } = useUser();

    useEffect(() => {
      if (!user?.id) {
        return;
      }

      Sentry.setUser({
        id: user.id,
        email_address: user.emailAddresses[0].emailAddress,
        username: user.fullName ?? user.emailAddresses[0].emailAddress,
      });
    }, [user?.id]);

    return (
      <>
        <Outlet />
        <Toaster />
      </>
    );
  },
});
