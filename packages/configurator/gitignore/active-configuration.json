{"config": {"id": 255, "version": "000000", "lights": [{"id": 0, "dimSpeedMsec": 200, "fixtures": [{"minBrightness": 0, "maxBrightness": 100, "type": 0, "dmx": {"type": 4, "channels": [1]}}]}], "nodeQrMappings": [{"qrCode": "eftmw", "nodeId": 40, "type": 0}, {"qrCode": "ju9ak", "nodeId": 41, "type": 0}, {"qrCode": "vjufi", "nodeId": 42, "type": 0}], "canboConfigs": [{"nodeId": 40, "threePinInputs": [{"connectorId": 1, "type": 0, "toggle": {"upClick": [{"dimSpeedMsec": 0, "lightId": 0, "targetBrightness": 100, "onBrightness": 100, "offBrightness": 0}], "upHold": [{"dimSpeedMsec": 200, "lightId": 0, "targetBrightness": 100, "onBrightness": 100, "offBrightness": 0}], "downClick": [{"dimSpeedMsec": 0, "lightId": 0, "targetBrightness": 0, "onBrightness": 100, "offBrightness": 0}], "downHold": [{"dimSpeedMsec": 400, "lightId": 0, "targetBrightness": 0, "onBrightness": 100, "offBrightness": 0}]}}], "twoPinInputs": []}, {"nodeId": 41, "threePinInputs": [{"connectorId": 3, "type": 1, "momentary": {"upClick": [{"dimSpeedMsec": 200, "lightId": 0, "targetBrightness": 100, "onBrightness": 100, "offBrightness": 0}], "upHold": []}}], "twoPinInputs": []}, {"nodeId": 42, "threePinInputs": [{"connectorId": 1, "type": 0, "toggle": {"upClick": [{"dimSpeedMsec": 200, "lightId": 0, "targetBrightness": 80, "onBrightness": 100, "offBrightness": 0}], "upHold": [], "downClick": [{"dimSpeedMsec": 200, "lightId": 0, "targetBrightness": 20, "onBrightness": 100, "offBrightness": 0}], "downHold": []}}], "twoPinInputs": []}], "rfReedConfigs": []}, "state": {"lights": [{"id": 0, "brightness": 0, "targetValue": 0, "dimSpeedMsec": 200, "lastModifiedTime": 1753366363003, "activeAfterTime": 0}], "buttons": [], "provisionedDevices": [], "reeds": []}}