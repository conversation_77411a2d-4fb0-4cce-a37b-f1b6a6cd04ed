# Somo Configurator

Deployed at https://configurator.somo.ai

## Pre-requisites

You'll need [Node.js](https://nodejs.org/en/) and [pnpm](https://pnpm.io/installation) installed.

Copy the `.env.example` file to `.env` and fill in the values. Ask your coworkers for help.

> [!WARNING]
>
> **The configurator uses the [admin-server](../admin-server/README.md) as a backend.**
>
> By default, it points to your local instance. You can point to a different one by setting `VITE_WS_URL` and `VITE_API_URL` in your `.env`.

## Run locally

> [!TIP]
> To run both the admin-server and the configurator, run: `pnpm dev-configurator`.

1. Install dependencies: `pnpm install`
2. Run the app: `pnpm dev @somo/configurator`

It will start the server and give you an URL to access the app.

## Deployment

We deploy to [Fly.io](https://fly.io/). The deployment is triggered by a GitHub action when you push to the `main` branch.

On prod, the env variables are coming from GitHub secrets when we build the Docker image.
