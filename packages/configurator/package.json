{"name": "@somo/configurator", "version": "0.0.1", "private": true, "type": "module", "exports": {"./package.json": "./package.json", ".": {"development": "./src/main.ts", "types": "./dist/main.d.ts", "import": "./dist/main.js", "default": "./dist/main.js"}}, "main": "./dist/main.js", "module": "./dist/main.js", "types": "./dist/main.d.ts", "scripts": {"build": "tsc -b && vite build", "dev": "vite --host 0.0.0.0 --port 3001", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "5.21.2", "@dnd-kit/core": "6.3.1", "@dnd-kit/sortable": "10.0.0", "@dnd-kit/utilities": "3.2.2", "@hocuspocus/provider": "2.13.5", "@hookform/resolvers": "5.2.0", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "1.1.6", "@sentry/react": "9.39.0", "@sentry/vite-plugin": "3.6.0", "@somo/shared": "workspace:*", "@tanstack/react-query": "5.62.11", "@tanstack/react-router": "1.114.29", "@tiptap/core": "2.11.5", "@tiptap/extension-collaboration": "2.11.5", "@tiptap/extension-document": "2.11.5", "@tiptap/extension-paragraph": "2.11.5", "@tiptap/extension-placeholder": "2.11.5", "@tiptap/extension-text": "2.11.5", "@tiptap/react": "2.11.5", "@uploadthing/react": "7.1.5", "@xyflow/react": "12.3.6", "allotment": "1.20.2", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "crypto-js": "4.2.0", "date-fns": "4.1.0", "eventemitter3": "5.0.1", "fast-deep-equal": "https://github.com/sulf/fast-deep-equal.git#d8e801fe9d970d6c84ef5e62dca388c0cb6f2573", "javascript-time-ago": "2.5.11", "launchdarkly-react-client-sdk": "^3.8.1", "lucide-react": "0.469.0", "mqtt": "5.10.4", "next-themes": "0.4.6", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "7.59.0", "rxjs": "^7.8.2", "semaphore-promise": "1.4.2", "sonner": "2.0.1", "tailwind-merge": "2.6.0", "tailwindcss-animate": "1.0.7", "ts-pattern": "^5.7.0", "uploadthing": "7.5.2", "web-serial-polyfill": "1.0.15", "y-indexeddb": "^9.0.12", "y-prosemirror": "1.2.17", "yjs": "13.6.18", "zod": "4.0.8"}, "devDependencies": {"@bufbuild/protobuf": "^2.4.0", "@tanstack/router-devtools": "1.95.3", "@tanstack/router-plugin": "1.95.3", "@types/crypto-js": "4.2.2", "@types/javascript-time-ago": "2.5.0", "@types/node": "22.10.3", "@types/react": "18.3.18", "@types/react-dom": "18.3.5", "@vitejs/plugin-react": "4.3.4", "autoprefixer": "10.4.20", "globals": "15.14.0", "postcss": "8.4.49", "tailwindcss": "3.4.17"}, "packageManager": "pnpm@9.15.2+sha512.93e57b0126f0df74ce6bff29680394c0ba54ec47246b9cf321f0121d8d9bb03f750a705f24edc3c1180853afd7c2c3b94196d0a3d53d3e069d9e2793ef11f321"}