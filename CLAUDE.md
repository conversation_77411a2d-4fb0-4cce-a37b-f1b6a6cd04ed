# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

Somo AI is a home automation IoT platform with:

- **Hardware**: Smart home devices (switches, dimmers, fans, thermostats) controlled by a Raspberry Pi Pico basestation
- **Communication**: CAN bus, RF, DMX, MQTT protocols
- **Software**: Web apps for configuration and control, backed by embedded firmware

## Architecture

### Monorepo Structure (Nx)

- `/firmware/` - C/C++ embedded code (FreeRTOS on RP2350)
- `/packages/` - TypeScript/React web applications
- `/circuit_boards/` - KiCad PCB designs

### Key Components

1. **Firmware** (`/firmware/basestation/`) - Control logic in C/C++
2. **Configurator** (`/packages/configurator/`) - React app for device setup
3. **Admin Server** (`/packages/admin-server/`) - Express API with Prisma/PostgreSQL
4. **Tablet Controller** (`/packages/tablet-controller/`) - Touch UI for device control

### Cross-Platform Code Sharing

The basestation firmware is compiled to WebAssembly for browser-based simulation in the configurator. Protocol buffers define the configuration schema shared between firmware and web.

## Common Commands

### Development

```bash
# Start development servers
pnpm dev                   # Uses nx to run default dev task
pnpm dev-configurator      # Configurator + admin server
pnpm dev-tablet-controller # Tablet controller
pnpm dev-website           # Marketing website

# Run specific package dev server
npx nx dev [package-name]
```

### Building

```bash
# Build web applications
pnpm build-shared           # Build shared package first
npx nx build [package-name] # Build specific package

# Build firmware (from firmware/basestation)
mkdir build && cd build
cmake ..
cmake --build .

# Generate WASM from firmware
pnpm generate-wasm       # Complete pipeline
pnpm generate-wasm-proto # Generate TypeScript from protobuf
```

### Testing & Linting

```bash
# Testing
pnpm test                  # Run tests
pnpm test-all              # Test all packages
npx nx test [package-name] # Test specific package

# Linting & Type Checking
pnpm lint          # Run linting
pnpm lint-all      # Lint all packages
pnpm typecheck     # TypeScript type checking
pnpm typecheck-all # Type check all packages

# Formatting
pnpm format       # Format with Prettier
pnpm format-check # Check formatting
```

### Protobuf Generation

```bash
pnpm generate-protobuf          # Generate TS from basestation-config.proto
pnpm generate-protobuf-commands # Generate TS from commands.proto
```

## Development Setup Requirements

1. **Node.js** and **pnpm** (v10.13.1)
2. **For WASM generation**:
   - Protobuf v29.3
   - Emscripten SDK v4.0.10
   - CMake v4.0.3
3. **For firmware**: Raspberry Pi Pico SDK

## Key Technical Details

- **Authentication**: Clerk
- **Database**: PostgreSQL with Prisma ORM
- **Real-time**: MQTT and WebSockets
- **Error Tracking**: Sentry
- **Deployment**: Fly.io via GitHub Actions
- **Build Tool**: Vite for web apps
- **Testing**: Vitest
- **Styling**: Tailwind CSS
- **Routing**: TanStack Router

## Working with Nx

```bash
npx nx graph              # Visualize dependencies
npx nx show project [pkg] # Show package details
npx nx sync               # Sync TypeScript references
```

The project uses Husky for pre-commit hooks that run linting and formatting checks.
